const { defineConfig } = require('@vue/cli-service')
const express = require('express')
const path = require('path')



module.exports = defineConfig({
  transpileDependencies: true,
  chainWebpack: config => {
    config.plugin('define').tap(args => {
      // 添加Vue特性标志
      args[0].__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = JSON.stringify(false);
      args[0].__VUE_PROD_DEVTOOLS_GLOBAL_HOOK__ = JSON.stringify(false);
      return args;
    });
  },
  devServer: {
    // 允许系统动态分配端口，不再硬编码端口号
    port: process.env.PORT || 8080,
    proxy: {
      '/api/local': {
        target: 'http://localhost:8091',
        changeOrigin: true
      }
    },
    // 简化路由配置，移除重复的API路由
    setupMiddlewares: (middlewares, devServer) => {
      if (!devServer) {
        throw new Error('webpack-dev-server is not defined');
      }
      
      // 映射 userdata 目录为静态资源
      devServer.app.use(
        '/userdata',
        express.static(path.resolve(__dirname, 'userdata'))
      );
      
      // 添加调试信息路由
      // eslint-disable-next-line no-unused-vars
      devServer.app.get('/dev-info', (req, res) => {
        res.json({
          mode: 'development',
          proxyTarget: 'http://localhost:8089',
          timestamp: new Date().toISOString()
        });
      });
      
      return middlewares;
    },
    // 添加客户端配置，支持在任何端口下访问
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws'
    }
  },
  pluginOptions: {
    electronBuilder: {
      nodeIntegration: false,
      preload: 'src/preload.js',
      // 配置主进程入口
      mainProcessFile: 'src/background.js',
      // 如果你需要将资源绑定到应用程序
      builderOptions: {
        extraResources: ['./public/**']
      }
    }
  },
  configureWebpack: {
    stats: {
      modules: false,
      chunks: false,
      children: false,
      entrypoints: false,
      // 禁用规则克隆相关的日志
      loaders: false,
      rules: false
    }
  }
})
