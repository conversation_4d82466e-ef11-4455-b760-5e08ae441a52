import { ref, provide, inject, readonly } from 'vue';

// 定义注入键名，避免命名冲突
const PROJECT_CONTEXT_KEY = 'projectContext';

/**
 * 提供项目上下文信息的组合式函数
 * 在主应用组件中使用此函数的 Provider 部分
 *
 * @returns {Object} 返回项目上下文的提供者和相关方法
 */
export function provideProjectContext() {
  // 创建响应式状态
  const projectTitle = ref('');
  const chapterTitle = ref('');
  const currentStep = ref('');
  const projectData = ref(null);

  // 更新方法
  const setProjectTitle = (title) => {
    console.log('[ProjectContext] 设置项目标题:', title);
    projectTitle.value = title;
  };

  const setChapterTitle = (title) => {
    console.log('[ProjectContext] 设置章节标题:', title);
    chapterTitle.value = title;
  };

  const setCurrentStep = (step) => {
    console.log('[ProjectContext] 设置当前步骤:', step);
    currentStep.value = step;
  };

  const setProjectData = (data) => {
    console.log('[ProjectContext] 设置项目数据:', data);
    projectData.value = data;
  };

  // 设置项目和章节信息
  const setProjectContext = (context) => {
    console.log('[ProjectContext] 设置项目上下文:', context);
    if (context.projectTitle !== undefined) {
      projectTitle.value = context.projectTitle;
    }
    if (context.chapterTitle !== undefined) {
      chapterTitle.value = context.chapterTitle;
    }
    if (context.currentStep !== undefined) {
      currentStep.value = context.currentStep;
    }
    if (context.projectData !== undefined) {
      projectData.value = context.projectData;
    }
  };

  // 提供给应用的其他部分
  provide(PROJECT_CONTEXT_KEY, {
    // 只读状态
    projectTitle: readonly(projectTitle),
    chapterTitle: readonly(chapterTitle),
    currentStep: readonly(currentStep),
    projectData: readonly(projectData),
    // 更新方法
    setProjectTitle,
    setChapterTitle,
    setCurrentStep,
    setProjectData,
    setProjectContext
  });

  // 返回提供的上下文和方法，以便在提供组件中使用
  return {
    projectTitle,
    chapterTitle,
    currentStep,
    projectData,
    setProjectTitle,
    setChapterTitle,
    setCurrentStep,
    setProjectData,
    setProjectContext
  };
}

/**
 * 注入项目上下文信息的组合式函数
 * 在需要访问项目上下文的组件中使用
 *
 * @returns {Object} 返回项目上下文的状态和方法
 */
export function useProjectContext() {
  const context = inject(PROJECT_CONTEXT_KEY, null);

  if (!context) {
    console.error('[ProjectContext] 项目上下文未提供。请确保在应用根级组件中使用 provideProjectContext()');

    // 返回默认值，避免应用崩溃
    return {
      projectTitle: ref(''),
      chapterTitle: ref(''),
      currentStep: ref(''),
      projectData: ref(null),
      setProjectTitle: () => console.warn('[ProjectContext] 未提供上下文，setProjectTitle 不可用'),
      setChapterTitle: () => console.warn('[ProjectContext] 未提供上下文，setChapterTitle 不可用'),
      setCurrentStep: () => console.warn('[ProjectContext] 未提供上下文，setCurrentStep 不可用'),
      setProjectData: () => console.warn('[ProjectContext] 未提供上下文，setProjectData 不可用'),
      setProjectContext: () => console.warn('[ProjectContext] 未提供上下文，setProjectContext 不可用')
    };
  }

  return context;
}

/**
 * 获取原始SRT文本（未分组状态）
 * 这是一个没有变量的文本，保持原始SRT的序列
 * @returns {string} 原始SRT文本内容
 */
export function getOriginalSrtText() {
  try {
    // 获取项目和章节信息
    const urlParams = new URLSearchParams(window.location.search);
    const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
    const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

    if (!projectTitle || !chapterTitle) {
      console.error('[getOriginalSrtText] 无法获取项目或章节信息');
      return '';
    }

    // 构建localStorage键
    const key = `original_srt_data_${projectTitle}_${chapterTitle}`;

    // 从localStorage获取原始数据
    const dataStr = localStorage.getItem(key);
    if (!dataStr) {
      console.warn('[getOriginalSrtText] localStorage中没有找到原始SRT数据');
      return '';
    }

    // 解析数据
    const data = JSON.parse(dataStr);
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('[getOriginalSrtText] localStorage中的原始SRT数据无效');
      return '';
    }

    console.log('[getOriginalSrtText] 成功加载原始SRT数据，行数:', data.length);

    // 生成带序号的文本
    const textLines = data.map((row, index) => {
      const serialNumber = index + 1;
      const description = row.description || '';
      return `${serialNumber}. ${description}`;
    });

    const result = textLines.join('\n');
    console.log('[getOriginalSrtText] 生成原始文本，总长度:', result.length);

    return result;
  } catch (error) {
    console.error('[getOriginalSrtText] 获取原始SRT文本失败:', error);
    return '';
  }
}

/**
 * 获取ContentCreationStudio分组后的文本
 * 这是一个有变量的函数，根据studio分组的结果，带有studio分组后的序列
 *
 * 数据获取优先级：
 * 1. 传入的studioRows参数（最高优先级）
 * 2. 项目上下文中的实时数据（推荐，最新状态）
 * 3. 文件系统中的project-data.json（备用，持久化数据）
 * 4. localStorage中的备用数据（最后备用）
 *
 * @param {Array} studioRows - ContentCreationStudio的行数据（可选，如果不提供则按优先级自动获取）
 * @returns {Promise<string>} 分组后的文本内容
 */
export async function getStudioGroupedText(studioRows = null) {
  try {
    let rows = studioRows;
    let dataSource = 'parameter';

    // 优先级1: 如果传入了参数，直接使用
    if (rows && Array.isArray(rows) && rows.length > 0) {
      console.log('[getStudioGroupedText] 使用传入的行数据，行数:', rows.length);
      dataSource = 'parameter';
    }
    // 优先级2: 尝试从项目上下文获取（最新的实时数据）
    else {
      try {
        const context = inject(PROJECT_CONTEXT_KEY, null);
        if (context && context.projectData && context.projectData.value && context.projectData.value.data && context.projectData.value.data.rows) {
          rows = context.projectData.value.data.rows;
          dataSource = 'context';
          console.log('[getStudioGroupedText] 从项目上下文获取实时行数据，行数:', rows.length);
        }
      } catch (error) {
        console.warn('[getStudioGroupedText] 无法从项目上下文获取数据，尝试文件系统');
      }

      // 优先级3: 从文件系统获取项目数据（持久化数据）
      if (!rows) {
        const urlParams = new URLSearchParams(window.location.search);
        const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
        const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

        if (projectTitle && chapterTitle) {
          try {
            // 从文件系统读取项目数据
            const filePath = `draft/${projectTitle}/${chapterTitle}/project-data.json`;
            const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(filePath)}`);

            if (response.ok) {
              const result = await response.json();
              if (result.success && result.content) {
                const projectData = JSON.parse(result.content);
                if (projectData.rows && Array.isArray(projectData.rows)) {
                  rows = projectData.rows;
                  dataSource = 'file';
                  console.log('[getStudioGroupedText] 从文件系统获取行数据，行数:', rows.length);
                }
              }
            } else if (response.status !== 404) {
              // 只在非404错误时记录警告，404是正常情况
              console.warn('[getStudioGroupedText] 无法读取项目数据文件:', response.status);
            }
          } catch (fileError) {
            console.warn('[getStudioGroupedText] 从文件系统获取数据失败:', fileError);

            // 优先级4: 最后尝试从localStorage获取（最后备用）
            try {
              const projectDataKey = `project_${projectTitle}_${chapterTitle}`;
              const projectDataStr = localStorage.getItem(projectDataKey);
              if (projectDataStr) {
                const projectData = JSON.parse(projectDataStr);
                if (projectData.data && projectData.data.rows) {
                  rows = projectData.data.rows;
                  dataSource = 'localStorage';
                  console.log('[getStudioGroupedText] 从localStorage备用获取行数据，行数:', rows.length);
                }
              }
            } catch (localStorageError) {
              console.warn('[getStudioGroupedText] 从localStorage备用获取数据失败:', localStorageError);
            }
          }
        }
      }
    }

    if (!rows || !Array.isArray(rows) || rows.length === 0) {
      console.warn('[getStudioGroupedText] 没有找到有效的行数据');
      return '';
    }

    console.log(`[getStudioGroupedText] 开始处理分组后的文本，数据源: ${dataSource}，行数:`, rows.length);

    // 生成带序号的文本，使用当前的行顺序
    const textLines = rows.map((row, index) => {
      const serialNumber = index + 1;
      let description = row.description || '';

      // 处理合并行：将换行符替换为逗号分隔
      if (row.isMerged && description.includes('\n')) {
        description = description.replace(/\n/g, '，');
      }

      // 直接返回描述文本，不添加任何标记
      return `${serialNumber}. ${description}`;
    });

    const result = textLines.join('\n');

    // 统计合并行数量
    const mergedCount = rows.filter(row => row.isMerged).length;

    console.log('[getStudioGroupedText] 生成分组后文本完成:', {
      dataSource,
      totalRows: rows.length,
      mergedRows: mergedCount,
      textLength: result.length
    });

    return result;
  } catch (error) {
    console.error('[getStudioGroupedText] 获取分组后文本失败:', error);
    return '';
  }
}