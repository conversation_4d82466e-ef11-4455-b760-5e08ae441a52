/**
 * ComfyUI设置管理
 * 负责ComfyUI相关设置的保存、加载和验证
 */

import { ref, computed } from 'vue';

export function useComfyUISettings() {
  // 默认设置
  const defaultSettings = {
    serverUrl: 'http://localhost:8188',
    isConnected: false,
    batchSize: 1,
    seedValue: 0,
    currentWorkflow: null,
    workflowName: '',
    nodeMapping: {
      positivePromptNode: '',
      negativePromptNode: '',
      imageSizeNode: '',
      saveImageNode: '',
      samplerNode: '',
      seedNode: '',
      batchSizeNode: ''
    },
    defaultSettings: {
      width: 1024,
      height: 1024,
      steps: 35,
      cfg: 7,
      sampler: 'euler'
    },
    availableWorkflows: [],
    detectedModels: {
      checkpoints: [],
      loras: []
    },
    availableModels: {
      checkpoints: [],
      loras: []
    },
    selectedModels: {
      checkpoint: {
        nodeId: '',
        modelName: ''
      },
      loras: []
    },
    workflowNodes: []
  };

  // 当前设置状态
  const settings = ref({ ...defaultSettings });
  const isLoading = ref(false);
  const error = ref(null);

  // 计算属性：验证设置完整性
  const isConfigurationValid = computed(() => {
    return settings.value.serverUrl && 
           settings.value.nodeMapping.positivePromptNode && 
           settings.value.nodeMapping.saveImageNode;
  });

  // 计算属性：连接状态
  const connectionStatus = computed(() => {
    if (!settings.value.serverUrl) return 'not-configured';
    if (!settings.value.isConnected) return 'disconnected';
    return 'connected';
  });

  /**
   * 测试ComfyUI服务器连接
   * @param {string} serverUrl - 服务器地址
   * @returns {Promise<boolean>} 连接是否成功
   */
  async function testConnection(serverUrl = settings.value.serverUrl) {
    if (!serverUrl) {
      throw new Error('请提供服务器地址');
    }

    try {
      const response = await fetch(`${serverUrl}/system_stats`, {
        method: 'GET',
        timeout: 5000
      });

      if (response.ok) {
        settings.value.isConnected = true;
        return true;
      } else {
        throw new Error(`服务器响应错误: ${response.status}`);
      }
    } catch (err) {
      settings.value.isConnected = false;
      throw new Error(`连接失败: ${err.message}`);
    }
  }

  /**
   * 更新服务器地址
   * @param {string} url - 新的服务器地址
   */
  function updateServerUrl(url) {
    settings.value.serverUrl = url;
    settings.value.isConnected = false; // 重置连接状态
  }

  /**
   * 更新批量生成数量
   * @param {number} size - 批量大小
   */
  function updateBatchSize(size) {
    if (size >= 1 && size <= 10) {
      settings.value.batchSize = size;
    } else {
      throw new Error('批量大小必须在1-10之间');
    }
  }

  /**
   * 设置工作流
   * @param {Object} workflow - 工作流数据
   * @param {string} name - 工作流名称
   * @param {Array} nodes - 节点列表
   */
  function setWorkflow(workflow, name, nodes = []) {
    settings.value.currentWorkflow = workflow;
    settings.value.workflowName = name;
    settings.value.workflowNodes = nodes;
  }

  /**
   * 更新节点映射
   * @param {string} nodeType - 节点类型
   * @param {string} nodeId - 节点ID
   */
  function updateNodeMapping(nodeType, nodeId) {
    if (settings.value.nodeMapping.hasOwnProperty(nodeType)) {
      settings.value.nodeMapping[nodeType] = nodeId;
    } else {
      throw new Error(`未知的节点类型: ${nodeType}`);
    }
  }

  /**
   * 验证节点映射配置
   * @returns {Object} 验证结果
   */
  function validateNodeMapping() {
    const issues = [];
    const warnings = [];

    // 检查必需的节点
    if (!settings.value.nodeMapping.positivePromptNode) {
      issues.push('缺少正向提示词节点');
    }
    if (!settings.value.nodeMapping.saveImageNode) {
      issues.push('缺少图像保存节点');
    }

    // 检查可选但推荐的节点
    if (!settings.value.nodeMapping.negativePromptNode) {
      warnings.push('建议设置负向提示词节点');
    }
    if (!settings.value.nodeMapping.imageSizeNode) {
      warnings.push('建议设置图像尺寸节点');
    }
    if (!settings.value.nodeMapping.samplerNode) {
      warnings.push('建议设置采样器节点');
    }

    return {
      isValid: issues.length === 0,
      issues,
      warnings
    };
  }

  /**
   * 重置设置到默认值
   */
  function resetSettings() {
    settings.value = { ...defaultSettings };
    error.value = null;
  }

  /**
   * 导出设置配置
   * @returns {Object} 设置配置
   */
  function exportSettings() {
    return {
      ...settings.value,
      exportTime: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * 导入设置配置
   * @param {Object} importedSettings - 导入的设置
   */
  function importSettings(importedSettings) {
    try {
      // 验证导入的设置格式
      if (!importedSettings || typeof importedSettings !== 'object') {
        throw new Error('无效的设置格式');
      }

      // 合并设置，保留默认值
      settings.value = {
        ...defaultSettings,
        ...importedSettings,
        nodeMapping: {
          ...defaultSettings.nodeMapping,
          ...(importedSettings.nodeMapping || {})
        },
        defaultSettings: {
          ...defaultSettings.defaultSettings,
          ...(importedSettings.defaultSettings || {})
        }
      };

      console.log('[useComfyUISettings] 设置导入成功');
    } catch (err) {
      error.value = err.message;
      throw err;
    }
  }

  /**
   * 获取生成参数
   * @param {Object} overrides - 覆盖参数
   * @returns {Object} 生成参数
   */
  function getGenerationParams(overrides = {}) {
    return {
      ...settings.value.defaultSettings,
      batchSize: settings.value.batchSize,
      ...overrides
    };
  }

  /**
   * 检查是否准备就绪
   * @returns {boolean} 是否可以开始生成
   */
  function isReadyForGeneration() {
    return isConfigurationValid.value && 
           settings.value.isConnected && 
           settings.value.currentWorkflow;
  }

  return {
    // 状态
    settings,
    isLoading,
    error,

    // 计算属性
    isConfigurationValid,
    connectionStatus,

    // 方法
    testConnection,
    updateServerUrl,
    updateBatchSize,
    setWorkflow,
    updateNodeMapping,
    validateNodeMapping,
    resetSettings,
    exportSettings,
    importSettings,
    getGenerationParams,
    isReadyForGeneration
  };
}
