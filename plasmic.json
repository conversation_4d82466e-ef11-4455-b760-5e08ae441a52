{"platform": "react", "code": {"lang": "js", "scheme": "blackbox", "reactRuntime": "classic"}, "style": {"scheme": "css-modules", "defaultStyleCssFilePath": ""}, "images": {"scheme": "inlined", "publicDir": "../public", "publicUrlPrefix": "/static/"}, "tokens": {"scheme": "theo", "tokensFilePath": "plasmic-tokens.theo.json"}, "srcDir": "src/components", "defaultPlasmicDir": "./plasmic", "projects": [], "globalVariants": {"variantGroups": []}, "wrapPagesWithGlobalContexts": true, "cliVersion": "0.1.337", "$schema": "https://unpkg.com/@plasmicapp/cli@0.1.337/dist/plasmic.schema.json"}