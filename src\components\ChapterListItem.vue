<template>
  <div class="chapter-list-row">
    <div class="chapter-list-cell">
      <div class="chapter-title-container">
        <span v-if="chapter.id !== editingChapterId">
          {{ chapter.title }}
          <span
            class="file-indicators"
            v-if="fileStatus"
          >
            <i
              v-if="fileStatus.hasSrt"
              class="ri-file-text-line file-indicator has-srt"
            />
            <i
              v-if="fileStatus.hasAudio"
              class="ri-music-fill file-indicator has-audio"
            />
          </span>
        </span>
        <input
          v-else
          type="text"
          v-model="localTitle"
          @blur="finishEdit"
          @keyup.enter="finishEdit"
          class="chapter-input"
          style="width:90%;"
        >
      </div>
    </div>
    <div class="chapter-list-cell">
      <div class="chapter-actions">
        <button
          v-if="chapter.id !== editingChapterId"
          class="icon-btn"
          @click="editChapter"
          title="编辑"
        >
          <i class="ri-edit-line" />
        </button>
        <button
          v-else
          class="icon-btn"
          @click="finishEdit"
          title="完成"
        >
          <i class="ri-check-line" />
        </button>
        <button
          class="icon-btn delete"
          @click="$emit('delete', chapter)"
          title="删除"
        >
          <i class="ri-delete-bin-line" />
        </button>
      </div>
    </div>
    <div class="chapter-list-cell">
      <button
        class="icon-btn create"
        @click="$emit('navigate', chapter)"
        title="去创作"
      >
        <i class="ri-quill-pen-line" />
      </button>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue';

export default {
  props: {
    chapter: {
      type: Object,
      default: () => ({})
    },
    editingChapterId: {
      type: [String, Number, null],
      default: null
    },
    editingChapterTitle: {
      type: String,
      default: ''
    },
    fileStatus: {
      type: Object,
      default: () => ({ hasSrt: false, hasAudio: false })
    }
  },
  emits: ['edit', 'finish-edit', 'delete', 'navigate'],
  setup(props, { emit }) {
    const localTitle = ref(props.editingChapterTitle);
    
    watch(() => props.editingChapterTitle, (val) => {
      localTitle.value = val;
    });
    
    function editChapter() {
      // 直接触发编辑事件
      console.log('点击编辑按钮:', props.chapter);
      emit('edit', props.chapter);
    }
    
    function finishEdit() {
      // 确保编辑完成时正确传递完整的章节对象
      console.log('完成编辑:', localTitle.value);
      const updatedChapter = { ...props.chapter };
      updatedChapter.title = localTitle.value;
      // 保存原标题用于文件重命名
      updatedChapter.originalTitle = props.chapter.title;
      emit('finish-edit', updatedChapter);
    }
    
    return {
      localTitle,
      editChapter,
      finishEdit
    };
  }
};
</script>

<style scoped>
/* 使用网格布局的行样式 */
.chapter-list-row {
  display: grid;
  grid-template-columns: 60% 20% 20%;
  width: 100%;
  background: #232136;
  transition: background 0.2s;
}

.chapter-list-row:nth-child(even) {
  background: #2a293e;
}

.chapter-list-row:hover {
  background: #393552;
}

.chapter-list-cell {
  padding: 0.5rem 1.2rem;
  text-align: left;
  border: 1px solid #393552;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

.chapter-title-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.file-indicators {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

.file-indicator {
  font-size: 1rem;
  transition: all 0.3s ease;
}

.file-indicator.has-srt {
  color: #4ade80;
  filter: drop-shadow(0 0 3px #4ade80);
  animation: glow-green 2s infinite alternate;
}

.file-indicator.has-audio {
  color: #4ade80;
  filter: drop-shadow(0 0 3px #4ade80);
  animation: glow-green 2s infinite alternate;
}

@keyframes glow-green {
  0% {
    filter: drop-shadow(0 0 1px #4ade80);
  }
  100% {
    filter: drop-shadow(0 0 6px #4ade80);
  }
}

.chapter-input {
  padding: 4px 8px;
  border: 1px solid #a23a5a;
  border-radius: 4px;
}

.chapter-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  height: 100%;
  margin: 0;
  padding: 0;
}

.icon-btn {
  width: 34px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 1rem;
  background: #2d2a45;
  color: #e0def4;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.icon-btn:hover {
  background: #393552;
  transform: translateY(-2px);
}

.icon-btn.delete {
  color: #eb6f92;
}

.icon-btn.delete:hover {
  background: #eb6f92;
  color: #232136;
}

.icon-btn.create {
  color: #f6c177;
  background: #2d2a45;
}

.icon-btn.create:hover {
  background: #f6c177;
  color: #232136;
}
</style>
