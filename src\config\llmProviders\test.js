/**
 * LLM提供商配置测试脚本
 * 用于验证配置文件的正确性
 */

import { 
  getProviderConfig, 
  getAllProviderConfigs, 
  getProviderList, 
  validateProviderConfig,
  getProviderDefaults,
  getProviderModels,
  getApiKeyForProvider,
  buildApiRequest,
  parseApiResponse
} from './index.js';

/**
 * 测试所有提供商配置
 */
async function testAllProviders() {
  console.log('🧪 开始测试LLM提供商配置...\n');
  
  const providers = ['openrouter', 'google', 'openai', 'anthropic', 'local'];
  let passedTests = 0;
  let totalTests = 0;
  
  for (const providerId of providers) {
    console.log(`📋 测试提供商: ${providerId}`);
    console.log('─'.repeat(50));
    
    const testResults = await testProvider(providerId);
    
    const passed = testResults.filter(r => r.passed).length;
    const total = testResults.length;
    
    console.log(`✅ 通过: ${passed}/${total} 项测试\n`);
    
    passedTests += passed;
    totalTests += total;
  }
  
  console.log('🎯 总体测试结果:');
  console.log('='.repeat(50));
  console.log(`✅ 通过: ${passedTests}/${totalTests} 项测试`);
  console.log(`📊 成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！配置文件正常。');
  } else {
    console.log('⚠️ 部分测试失败，请检查配置文件。');
  }
}

/**
 * 测试单个提供商
 * @param {string} providerId - 提供商ID
 * @returns {Array} 测试结果数组
 */
async function testProvider(providerId) {
  const results = [];
  
  // 测试1: 配置文件是否存在
  try {
    const config = getProviderConfig(providerId);
    if (config) {
      results.push({ test: '配置文件存在', passed: true });
      console.log('  ✅ 配置文件存在');
    } else {
      results.push({ test: '配置文件存在', passed: false });
      console.log('  ❌ 配置文件不存在');
      return results;
    }
  } catch (error) {
    results.push({ test: '配置文件存在', passed: false, error: error.message });
    console.log('  ❌ 配置文件加载失败:', error.message);
    return results;
  }
  
  // 测试2: 基本属性是否完整
  const config = getProviderConfig(providerId);
  const requiredProps = ['name', 'id', 'api', 'defaults', 'models', 'formatRequest', 'parseResponse'];
  
  for (const prop of requiredProps) {
    if (config[prop] !== undefined) {
      results.push({ test: `属性 ${prop} 存在`, passed: true });
      console.log(`  ✅ 属性 ${prop} 存在`);
    } else {
      results.push({ test: `属性 ${prop} 存在`, passed: false });
      console.log(`  ❌ 属性 ${prop} 缺失`);
    }
  }
  
  // 测试3: 默认配置
  try {
    const defaults = getProviderDefaults(providerId);
    if (defaults && typeof defaults === 'object') {
      results.push({ test: '默认配置有效', passed: true });
      console.log('  ✅ 默认配置有效');
    } else {
      results.push({ test: '默认配置有效', passed: false });
      console.log('  ❌ 默认配置无效');
    }
  } catch (error) {
    results.push({ test: '默认配置有效', passed: false, error: error.message });
    console.log('  ❌ 默认配置测试失败:', error.message);
  }
  
  // 测试4: 模型列表
  try {
    const models = getProviderModels(providerId);
    if (Array.isArray(models) && models.length > 0) {
      results.push({ test: '模型列表有效', passed: true });
      console.log(`  ✅ 模型列表有效 (${models.length} 个模型)`);
    } else {
      results.push({ test: '模型列表有效', passed: false });
      console.log('  ❌ 模型列表无效或为空');
    }
  } catch (error) {
    results.push({ test: '模型列表有效', passed: false, error: error.message });
    console.log('  ❌ 模型列表测试失败:', error.message);
  }
  
  // 测试5: 配置验证函数
  try {
    const testConfig = {
      apiKey: 'test-key',
      model: 'test-model',
      temperature: 0.7,
      maxTokens: 4096
    };
    
    const validationErrors = validateProviderConfig(providerId, testConfig);
    if (Array.isArray(validationErrors)) {
      results.push({ test: '配置验证函数', passed: true });
      console.log('  ✅ 配置验证函数正常');
    } else {
      results.push({ test: '配置验证函数', passed: false });
      console.log('  ❌ 配置验证函数返回值格式错误');
    }
  } catch (error) {
    results.push({ test: '配置验证函数', passed: false, error: error.message });
    console.log('  ❌ 配置验证函数测试失败:', error.message);
  }
  
  // 测试6: 请求构建函数
  try {
    const testSettings = {
      apiKey: 'test-key',
      model: 'test-model',
      temperature: 0.7,
      maxOutputTokens: 4096,
      localUrl: 'http://localhost:8080'
    };
    
    const requestConfig = buildApiRequest(providerId, 'test prompt', testSettings);
    
    if (requestConfig && requestConfig.url && requestConfig.method && requestConfig.headers) {
      results.push({ test: '请求构建函数', passed: true });
      console.log('  ✅ 请求构建函数正常');
    } else {
      results.push({ test: '请求构建函数', passed: false });
      console.log('  ❌ 请求构建函数返回值格式错误');
    }
  } catch (error) {
    results.push({ test: '请求构建函数', passed: false, error: error.message });
    console.log('  ❌ 请求构建函数测试失败:', error.message);
  }
  
  // 测试7: 响应解析函数
  try {
    // 创建模拟响应数据
    let mockResponse;
    if (providerId === 'google') {
      mockResponse = {
        candidates: [{ content: { parts: [{ text: 'test response' }] } }]
      };
    } else if (providerId === 'anthropic') {
      mockResponse = {
        content: [{ text: 'test response' }]
      };
    } else {
      mockResponse = {
        choices: [{ message: { content: 'test response' } }]
      };
    }
    
    const parsedResponse = parseApiResponse(providerId, mockResponse);
    
    if (parsedResponse && parsedResponse.content) {
      results.push({ test: '响应解析函数', passed: true });
      console.log('  ✅ 响应解析函数正常');
    } else {
      results.push({ test: '响应解析函数', passed: false });
      console.log('  ❌ 响应解析函数返回值格式错误');
    }
  } catch (error) {
    results.push({ test: '响应解析函数', passed: false, error: error.message });
    console.log('  ❌ 响应解析函数测试失败:', error.message);
  }
  
  return results;
}

/**
 * 测试统一接口函数
 */
function testUnifiedInterface() {
  console.log('🔧 测试统一接口函数...\n');
  
  // 测试获取所有配置
  try {
    const allConfigs = getAllProviderConfigs();
    console.log(`✅ getAllProviderConfigs: 返回 ${Object.keys(allConfigs).length} 个提供商配置`);
  } catch (error) {
    console.log('❌ getAllProviderConfigs 失败:', error.message);
  }
  
  // 测试获取提供商列表
  try {
    const providerList = getProviderList();
    console.log(`✅ getProviderList: 返回 ${providerList.length} 个提供商`);
    
    const popularProviders = getProviderList(true);
    console.log(`✅ getProviderList(popular): 返回 ${popularProviders.length} 个热门提供商`);
  } catch (error) {
    console.log('❌ getProviderList 失败:', error.message);
  }
  
  // 测试API密钥获取
  try {
    const mockSettings = {
      openrouterApiKey: 'test-openrouter-key',
      googleApiKey: 'test-google-key',
      openaiApiKey: 'test-openai-key'
    };
    
    const openrouterKey = getApiKeyForProvider(mockSettings, 'openrouter');
    const googleKey = getApiKeyForProvider(mockSettings, 'google');
    
    if (openrouterKey === 'test-openrouter-key' && googleKey === 'test-google-key') {
      console.log('✅ getApiKeyForProvider: API密钥获取正常');
    } else {
      console.log('❌ getApiKeyForProvider: API密钥获取异常');
    }
  } catch (error) {
    console.log('❌ getApiKeyForProvider 失败:', error.message);
  }
  
  console.log();
}

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testUnifiedInterface();
  await testAllProviders();
}

export {
  testAllProviders,
  testProvider,
  testUnifiedInterface
};
