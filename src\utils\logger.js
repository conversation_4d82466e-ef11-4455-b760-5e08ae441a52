/**
 * 统一日志管理系统
 * 支持不同级别的日志输出和条件过滤
 */

// 日志级别枚举
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
  VERBOSE: 4
};

// 日志类别
const LOG_CATEGORIES = {
  PERFORMANCE: 'PERF',
  BUSINESS: 'BIZ',
  API: 'API',
  DEBUG: 'DEBUG',
  ERROR: 'ERROR'
};

// 当前日志级别（生产环境只显示ERROR和WARN）
const currentLogLevel = process.env.NODE_ENV === 'production' ? LOG_LEVELS.WARN : LOG_LEVELS.INFO;

// 性能监控开关
const enablePerformanceLogging = process.env.NODE_ENV === 'development';

// 🔧 新增：图像生成相关的日志控制
const enableImageGenerationLogs = process.env.NODE_ENV === 'development';
const enableWebSocketLogs = process.env.NODE_ENV === 'development';
const enableQueueLogs = process.env.NODE_ENV === 'development';

// 格式化时间戳
const formatTimestamp = () => {
  const now = new Date();
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}.${now.getMilliseconds().toString().padStart(3, '0')}`;
};

// 格式化日志前缀
const formatPrefix = (level, category = '') => {
  const timestamp = formatTimestamp();
  const categoryStr = category ? `[${category}]` : '';
  return `${timestamp} ${categoryStr}`;
};

const logger = {
  // 错误日志 - 始终显示
  error(message, ...args) {
    if (currentLogLevel >= LOG_LEVELS.ERROR) {
      console.error(`${formatPrefix('ERROR', LOG_CATEGORIES.ERROR)} ${message}`, ...args);
    }
  },

  // 警告日志 - 生产环境也显示
  warn(message, ...args) {
    if (currentLogLevel >= LOG_LEVELS.WARN) {
      console.warn(`${formatPrefix('WARN')} ${message}`, ...args);
    }
  },

  // 业务流程日志 - 重要的业务操作
  business(message, ...args) {
    if (currentLogLevel >= LOG_LEVELS.INFO) {
      console.log(`${formatPrefix('INFO', LOG_CATEGORIES.BUSINESS)} ${message}`, ...args);
    }
  },

  // 性能监控日志 - 专门用于性能分析
  performance(message, duration, metadata = {}) {
    if (enablePerformanceLogging && currentLogLevel >= LOG_LEVELS.INFO) {
      const status = duration > 100 ? '🔴' : duration > 50 ? '🟡' : '🟢';
      console.log(`${formatPrefix('PERF', LOG_CATEGORIES.PERFORMANCE)} ${status} ${message}: ${duration.toFixed(2)}ms`, metadata);
    }
  },

  // API请求日志
  api(message, ...args) {
    if (currentLogLevel >= LOG_LEVELS.INFO) {
      console.log(`${formatPrefix('API', LOG_CATEGORIES.API)} ${message}`, ...args);
    }
  },

  // 调试日志 - 仅开发环境
  debug(message, ...args) {
    if (currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.log(`${formatPrefix('DEBUG', LOG_CATEGORIES.DEBUG)} ${message}`, ...args);
    }
  },

  // 详细日志 - 最详细的调试信息
  verbose(message, ...args) {
    if (currentLogLevel >= LOG_LEVELS.VERBOSE) {
      console.log(`${formatPrefix('VERBOSE')} ${message}`, ...args);
    }
  },

  // 🔧 新增：图像生成专用日志方法
  imageGeneration(message, ...args) {
    if (enableImageGenerationLogs && currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.log(`${formatPrefix('IMG-GEN')} ${message}`, ...args);
    }
  },

  // WebSocket专用日志方法
  websocket(message, ...args) {
    if (enableWebSocketLogs && currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.log(`${formatPrefix('WS')} ${message}`, ...args);
    }
  },

  // 队列处理专用日志方法
  queue(message, ...args) {
    if (enableQueueLogs && currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.log(`${formatPrefix('QUEUE')} ${message}`, ...args);
    }
  },

  // 🔧 移除：删除单行更新日志方法，保持简洁

  // 兼容旧的方法
  log(message, ...args) {
    this.debug(message, ...args);
  },

  info(message, ...args) {
    this.business(message, ...args);
  },

  // 性能监控专用方法
  startTimer(label) {
    if (enablePerformanceLogging) {
      console.time(`⏱️ ${label}`);
    }
  },

  endTimer(label) {
    if (enablePerformanceLogging) {
      console.timeEnd(`⏱️ ${label}`);
    }
  },

  // 分组日志
  group(label) {
    if (currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.group(`📁 ${label}`);
    }
  },

  groupEnd() {
    if (currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.groupEnd();
    }
  }
};

export default logger;