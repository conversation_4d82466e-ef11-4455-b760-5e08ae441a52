/**
 * Studio Actions - 重构后的干净版本
 * 移除了合并/分拆功能，专注于其他核心功能
 */

import { ref } from 'vue';
import { useMergeAndSplit } from './useMergeAndSplit.js';

export function useStudioActions() {
  // Toast通知相关
  const toastMessage = ref('');
  const toastTitle = ref('');
  const toastType = ref('info');
  const serverBaseUrl = ref('http://localhost:8091');

  // 保存状态控制
  const isSaving = ref(false);
  const saveDebounceTimer = ref(null);

  // 引入合并/分拆功能模块
  const mergeAndSplit = useMergeAndSplit();

  // 显示错误消息
  const showErrorMessage = (message) => {
    console.error(message);
    toastMessage.value = message;
    toastTitle.value = '错误';
    toastType.value = 'error';
    return { toastMessage, toastTitle, toastType };
  };

  // 显示成功消息
  const showSuccessMessage = (title, message) => {
    toastMessage.value = message;
    toastTitle.value = title;
    toastType.value = 'success';
    return { toastMessage, toastTitle, toastType };
  };

  // 显示信息消息
  const showInfoMessage = (title, message) => {
    toastMessage.value = message;
    toastTitle.value = title;
    toastType.value = 'info';
    return { toastMessage, toastTitle, toastType };
  };

  // 获取服务器基础URL
  const getServerBaseUrl = async () => {
    try {
      serverBaseUrl.value = 'http://localhost:8091';
      console.log('使用当前应用端口作为API服务器:', serverBaseUrl.value);
      return serverBaseUrl.value;
    } catch (error) {
      console.error('初始化服务器URL出错:', error);
      return serverBaseUrl.value;
    }
  };

  // 构建API URL
  const getApiUrl = (path) => {
    return `${serverBaseUrl.value}/api/${path}`;
  };

  // 执行智能推理
  const performSmartReasoning = () => {
    showInfoMessage('功能提示', '智能推理功能执行中...');
  };

  // 生成配图
  const generateImages = () => {
    showInfoMessage('功能提示', '生成配图功能执行中...');
  };

  // 放大配图
  const enlargeImages = () => {
    showInfoMessage('功能提示', '放大配图功能执行中...');
  };

  // 合并/分拆功能的包装器 - 委托给专门模块
  const mergeUp = (index, rows) => {
    return mergeAndSplit.mergeUp(index, rows, showErrorMessage, showSuccessMessage);
  };

  const splitDown = (index, rows) => {
    return mergeAndSplit.splitDown(index, rows, showErrorMessage, showSuccessMessage);
  };

  // 图片操作
  const selectMainImage = () => {
    showInfoMessage('功能提示', '请选择或上传图片...');
  };

  const toggleImageLock = (isImageLocked) => {
    const newLockState = !isImageLocked;
    showInfoMessage('图片状态', newLockState ? '图片已锁定' : '图片已解锁');
    return newLockState;
  };

  const clearMainImage = () => {
    showSuccessMessage('操作成功', '已清空当前图片');
    return '';
  };

  const selectThumbnail = (index) => {
    showInfoMessage('功能提示', `已选择缩略图 ${index + 1}`);
  };

  const manageImages = () => {
    showInfoMessage('功能提示', '打开图片管理面板...');
  };

  const redrawImage = () => {
    showInfoMessage('重绘中', '正在重新生成当前镜头图片...');
  };

  const inferPrompt = () => {
    showInfoMessage('推理中', '正在根据原文智能推理提示词...');
  };

  /**
   * 检查章节标题是否重名
   */
  const isChapterTitleDuplicate = (chapters, title, excludeId = null) => {
    return chapters.some(c => c.title === title && (!excludeId || c.id !== excludeId));
  };

  /**
   * 生成下一个默认章节标题
   */
  const getNextChapterTitle = (chapters) => {
    let nextNum = chapters.length + 1;
    let defaultTitle = `CH${nextNum}`;
    while (chapters.some(c => c.title === defaultTitle)) {
      nextNum++;
      defaultTitle = `CH${nextNum}`;
    }
    return defaultTitle;
  };

  /**
   * 创建新章节
   */
  const createChapter = async ({ projectId, projectTitle, chapters, getApiUrl }) => {
    const title = getNextChapterTitle(chapters);
    console.log('[createChapter] 创建章节', { projectId, projectTitle, title });
    
    // 创建章节目录
    const createDirResponse = await fetch(getApiUrl('createChapter'), {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ projectId, projectTitle, chapterTitle: title })
    });
    
    if (!createDirResponse.ok) {
      const errorData = await createDirResponse.json().catch(() => ({}));
      console.error('[createChapter] 创建章节目录失败', errorData);
      throw new Error(errorData.error || '创建章节目录失败');
    }
    
    // 创建章节数据
    const chapterObj = {
      id: `chapter_${Date.now()}`,
      title,
      createdAt: new Date().toISOString(),
      status: 'draft',
      data: {}
    };
    
    console.log('[createChapter] 创建章节成功', chapterObj);
    return chapterObj;
  };

  /**
   * 重命名章节
   */
  const renameChapter = async ({ novelData, chapters, chapterId, newTitle, getApiUrl }) => {
    const chapterIndex = chapters.findIndex(c => c.id === chapterId);
    if (chapterIndex === -1) throw new Error('章节未找到');

    const oldTitle = chapters[chapterIndex].title;
    console.log('[renameChapter] 重命名章节', { chapterId, oldTitle, newTitle });

    // 如果标题没有变化，直接返回
    if (oldTitle === newTitle) {
      console.log('[renameChapter] 标题未变化，跳过重命名');
      return novelData;
    }

    // 更新本地数据
    chapters[chapterIndex].title = newTitle;

    // 深拷贝章节，移除data中的循环引用
    const safeChapters = chapters.map(ch => {
      const { id, title, createdAt, updatedAt, status, data } = ch;
      let safeData = {};
      if (data && typeof data === 'object') {
        const { ...rest } = data;
        safeData = rest;
      }
      return { id, title, createdAt, updatedAt, status, data: safeData };
    });

    // 构造更新数据
    const updatedNovel = {
      ...novelData,
      updatedAt: new Date().toISOString(),
      data: {
        ...novelData.data,
        chapters: safeChapters,
        renameChapterInfo: {
          chapterId,
          oldTitle,
          newTitle
        },
        renameChapterFolder: true,
        forceUnique: true
      }
    };

    // 发送请求到服务器
    const response = await fetch(getApiUrl(`projects/${novelData.id}`), {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedNovel)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[renameChapter] 章节重命名失败', errorData);
      throw new Error(errorData.error || '章节重命名失败');
    }

    const result = await response.json();
    console.log('[renameChapter] 章节重命名成功', result);
    return result;
  };

  /**
   * 通用章节创建/重命名方法
   */
  const createOrRenameChapter = async (params) => {
    const { mode, novelData, chapters, chapterId, newTitle, projectId, projectTitle, getApiUrl } = params;
    if (mode === 'create') {
      return await createChapter({ projectId, projectTitle, chapters, getApiUrl });
    } else if (mode === 'rename') {
      return await renameChapter({ novelData, chapters, chapterId, newTitle, getApiUrl });
    } else {
      throw new Error('未知章节操作类型');
    }
  };

  return {
    // 状态
    toastMessage,
    toastTitle,
    toastType,
    serverBaseUrl,
    isSaving,
    saveDebounceTimer,

    // 通用工具方法
    showErrorMessage,
    showSuccessMessage,
    showInfoMessage,
    getServerBaseUrl,
    getApiUrl,

    // AI功能
    performSmartReasoning,
    generateImages,
    enlargeImages,
    inferPrompt,

    // 合并/分拆功能（委托给专门模块）
    mergeUp,
    splitDown,

    // 图片操作
    selectMainImage,
    toggleImageLock,
    clearMainImage,
    selectThumbnail,
    manageImages,
    redrawImage,

    // 章节管理
    isChapterTitleDuplicate,
    getNextChapterTitle,
    createChapter,
    renameChapter,
    createOrRenameChapter,

    // 合并/分拆模块的状态和工具（用于调试）
    mergeAndSplitModule: mergeAndSplit
  };
}
