<template>
  <div class="preset-selector">
    <div class="preset-header">
      <div class="preset-title">
        {{ title }}
      </div>
      <div class="preset-actions">
        <button
          class="preset-action-btn"
          title="保存为新预设"
          @click="saveAsNew"
          v-if="allowCustom"
        >
          <i class="ri-save-line" />
        </button>
      </div>
    </div>
    
    <div class="preset-content">
      <div class="preset-cards">
        <div 
          v-for="(preset, index) in availablePresets" 
          :key="index"
          class="preset-card"
          :class="{ 'active': selectedPresetIndex === index }"
          @click="selectPreset(index)"
        >
          <div class="preset-card-icon">
            <i :class="preset.icon || 'ri-settings-4-line'" />
          </div>
          <div class="preset-card-info">
            <div class="preset-card-name">
              {{ preset.name }}
            </div>
            <div class="preset-card-desc">
              {{ preset.description }}
            </div>
          </div>
          <div
            class="preset-card-actions"
            v-if="preset.canDelete && allowCustom"
          >
            <button
              class="preset-delete-btn"
              @click.stop="deletePreset(index)"
              title="删除预设"
            >
              <i class="ri-delete-bin-line" />
            </button>
          </div>
        </div>
      </div>
      
      <div
        class="preset-details"
        v-if="selectedPreset"
      >
        <div class="preset-detail-header">
          {{ selectedPreset.name }}参数设置
        </div>
        
        <div class="preset-properties">
          <div 
            v-for="(property, key) in selectedPreset.properties" 
            :key="key"
            class="preset-property"
          >
            <label :for="'property-' + key">{{ propertyLabels[key] || key }}</label>
            
            <!-- 不同类型的属性编辑器 -->
            <select
              v-if="propertyTypes[key] === 'select'" 
              :id="'property-' + key" 
              v-model="editingProperties[key]"
              :disabled="!allowCustom"
            >
              <option
                v-for="option in propertyOptions[key] || []" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
            
            <input
              v-else-if="propertyTypes[key] === 'color'" 
              type="color" 
              :id="'property-' + key" 
              v-model="editingProperties[key]"
              :disabled="!allowCustom"
            >
            
            <input
              v-else-if="propertyTypes[key] === 'number'" 
              type="number" 
              :id="'property-' + key" 
              v-model.number="editingProperties[key]"
              :min="propertyOptions[key]?.min"
              :max="propertyOptions[key]?.max"
              :step="propertyOptions[key]?.step || 1"
              :disabled="!allowCustom"
            >
            
            <input
              v-else
              type="text" 
              :id="'property-' + key" 
              v-model="editingProperties[key]"
              :disabled="!allowCustom"
            >
          </div>
        </div>
        
        <div
          class="preset-details-footer"
          v-if="allowCustom"
        >
          <button
            class="preset-apply-btn"
            @click="applyChanges"
          >
            应用更改
          </button>
          <button
            class="preset-reset-btn"
            @click="resetChanges"
          >
            重置
          </button>
        </div>
      </div>
    </div>
    
    <!-- 保存新预设对话框 -->
    <div
      class="modal-backdrop"
      v-if="showSaveModal"
      @click="showSaveModal = false"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          保存为新预设
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="new-preset-name">预设名称</label>
            <input
              type="text"
              id="new-preset-name"
              v-model="newPresetName"
              placeholder="例如：我的动漫风格"
            >
          </div>
          <div class="form-group">
            <label for="new-preset-desc">预设描述</label>
            <textarea
              id="new-preset-desc"
              v-model="newPresetDesc"
              placeholder="简要描述这个预设的特点..."
            />
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="modal-btn cancel"
            @click="showSaveModal = false"
          >
            取消
          </button>
          <button
            class="modal-btn save"
            @click="confirmSaveNew"
            :disabled="!newPresetName"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PresetSelector',
  props: {
    // 预设类型标题
    title: {
      type: String,
      default: '预设'
    },
    // 预设列表
    presets: {
      type: Array,
      default: () => []
    },
    // 属性标签映射
    propertyLabels: {
      type: Object,
      default: () => ({})
    },
    // 属性类型映射 (select, color, number, text)
    propertyTypes: {
      type: Object,
      default: () => ({})
    },
    // 属性选项映射 (用于select类型的选项，或number类型的min/max/step)
    propertyOptions: {
      type: Object,
      default: () => ({})
    },
    // 是否允许自定义
    allowCustom: {
      type: Boolean,
      default: true
    },
    // 当前选中的预设值
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 本地存储的预设列表
      customPresets: [],
      // 当前选中的预设索引
      selectedPresetIndex: 0,
      // 临时编辑的属性
      editingProperties: {},
      // 显示保存对话框
      showSaveModal: false,
      // 新预设名称
      newPresetName: '',
      // 新预设描述
      newPresetDesc: ''
    };
  },
  computed: {
    // 结合内置预设和自定义预设
    availablePresets() {
      return [...this.presets, ...this.customPresets];
    },
    // 当前选中的预设
    selectedPreset() {
      return this.availablePresets[this.selectedPresetIndex] || null;
    }
  },
  watch: {
    // 预设改变时，更新编辑属性
    selectedPreset: {
      handler(newPreset) {
        if (newPreset) {
          this.editingProperties = { ...newPreset.properties };
        }
      },
      immediate: true
    }
  },
  created() {
    // 加载自定义预设
    this.loadCustomPresets();
    
    // 根据props.value找到匹配的预设索引
    this.findMatchingPreset();
  },
  methods: {
    // 选择预设
    selectPreset(index) {
      this.selectedPresetIndex = index;
      this.editingProperties = { ...this.availablePresets[index].properties };
      
      // 触发选择事件
      this.$emit('select', this.availablePresets[index]);
      this.$emit('input', this.availablePresets[index].properties);
    },
    
    // 应用更改
    applyChanges() {
      // 创建一个更新后的预设对象
      const updatedPreset = {
        ...this.selectedPreset,
        properties: { ...this.editingProperties }
      };
      
      // 如果是自定义预设，更新它
      if (this.selectedPresetIndex >= this.presets.length) {
        const customIndex = this.selectedPresetIndex - this.presets.length;
        this.customPresets[customIndex] = updatedPreset;
        this.saveCustomPresets();
      }
      
      // 触发更新事件
      this.$emit('update', updatedPreset);
      this.$emit('input', updatedPreset.properties);
    },
    
    // 重置更改
    resetChanges() {
      this.editingProperties = { ...this.selectedPreset.properties };
    },
    
    // 保存为新预设
    saveAsNew() {
      this.showSaveModal = true;
      // 默认使用当前选择的预设名称加上"自定义"
      this.newPresetName = this.selectedPreset ? `${this.selectedPreset.name} 自定义` : '新预设';
      this.newPresetDesc = '';
    },
    
    // 确认保存新预设
    confirmSaveNew() {
      if (!this.newPresetName) return;
      
      // 创建新预设
      const newPreset = {
        name: this.newPresetName,
        description: this.newPresetDesc || `自定义${this.title}预设`,
        icon: 'ri-user-settings-line',
        properties: { ...this.editingProperties },
        canDelete: true // 自定义预设可删除
      };
      
      // 添加到自定义预设
      this.customPresets.push(newPreset);
      this.saveCustomPresets();
      
      // 选择新预设
      this.selectedPresetIndex = this.availablePresets.length - 1;
      
      // 关闭对话框
      this.showSaveModal = false;
      
      // 触发事件
      this.$emit('preset-created', newPreset);
      this.$emit('input', newPreset.properties);
    },
    
    // 删除预设
    deletePreset(index) {
      // 确保只能删除自定义预设
      if (index < this.presets.length) return;
      
      // 弹出确认
      if (!confirm(`确认删除预设"${this.availablePresets[index].name}"?`)) {
        return;
      }
      
      // 获取自定义预设索引
      const customIndex = index - this.presets.length;
      
      // 删除预设
      this.customPresets.splice(customIndex, 1);
      this.saveCustomPresets();
      
      // 如果删除的是当前选择的预设，选择第一个预设
      if (this.selectedPresetIndex === index) {
        this.selectedPresetIndex = 0;
        this.editingProperties = { ...this.availablePresets[0].properties };
        
        // 触发选择事件
        this.$emit('select', this.availablePresets[0]);
        this.$emit('input', this.availablePresets[0].properties);
      }
      // 如果删除的预设在当前选择的预设之前，更新索引
      else if (this.selectedPresetIndex > index) {
        this.selectedPresetIndex--;
      }
      
      // 触发删除事件
      this.$emit('preset-deleted', index);
    },
    
    // 保存自定义预设到本地存储
    saveCustomPresets() {
      try {
        const storageKey = `custom_${this.title}_presets`;
        localStorage.setItem(storageKey, JSON.stringify(this.customPresets));
      } catch (error) {
        console.error('保存预设失败:', error);
      }
    },
    
    // 从本地存储加载自定义预设
    loadCustomPresets() {
      try {
        const storageKey = `custom_${this.title}_presets`;
        const saved = localStorage.getItem(storageKey);
        
        if (saved) {
          this.customPresets = JSON.parse(saved);
        }
      } catch (error) {
        console.error('加载预设失败:', error);
        this.customPresets = [];
      }
    },
    
    // 根据传入的value查找匹配的预设
    findMatchingPreset() {
      if (!this.value || Object.keys(this.value).length === 0) {
        // 如果没有value，使用第一个预设
        this.selectPreset(0);
        return;
      }
      
      // 尝试找到完全匹配的预设
      const matchIndex = this.availablePresets.findIndex(preset => {
        const presetProps = preset.properties;
        // 检查所有值是否匹配
        return Object.keys(this.value).every(key => 
          presetProps[key] === this.value[key]
        );
      });
      
      if (matchIndex >= 0) {
        this.selectPreset(matchIndex);
      } else {
        // 没有完全匹配，使用第一个预设并覆盖其属性
        this.selectPreset(0);
        this.editingProperties = { ...this.value };
      }
    }
  }
}
</script>

<style scoped>
.preset-selector {
  background-color: #1e1e2e;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #313244;
}

.preset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #181825;
  border-bottom: 1px solid #313244;
}

.preset-title {
  font-weight: bold;
  color: #cba6f7;
  font-size: 1rem;
}

.preset-actions {
  display: flex;
  gap: 8px;
}

.preset-action-btn {
  background-color: #313244;
  border: none;
  color: #cdd6f4;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-action-btn:hover {
  background-color: #45475a;
  color: #f5c2e7;
}

.preset-content {
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

@media (min-width: 768px) {
  .preset-content {
    flex-direction: row;
  }
  
  .preset-cards {
    width: 40%;
    max-width: 300px;
  }
  
  .preset-details {
    flex: 1;
  }
}

.preset-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preset-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #313244;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  gap: 12px;
}

.preset-card:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

.preset-card.active {
  border-color: #cba6f7;
  background-color: #45475a;
}

.preset-card-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #1e1e2e;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cba6f7;
  font-size: 1.2rem;
}

.preset-card-info {
  flex: 1;
  overflow: hidden;
}

.preset-card-name {
  font-weight: 600;
  color: #f5c2e7;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preset-card-desc {
  font-size: 0.8rem;
  color: #a6adc8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preset-card-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.preset-card:hover .preset-card-actions {
  opacity: 1;
}

.preset-delete-btn {
  background: none;
  border: none;
  color: #f38ba8;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.preset-delete-btn:hover {
  background-color: rgba(243, 139, 168, 0.2);
}

.preset-details {
  background-color: #181825;
  border-radius: 6px;
  padding: 16px;
}

.preset-detail-header {
  font-weight: 600;
  color: #cba6f7;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #313244;
}

.preset-properties {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.preset-property {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.preset-property label {
  font-size: 0.9rem;
  color: #bac2de;
}

.preset-property input,
.preset-property select {
  background-color: #313244;
  border: 1px solid #45475a;
  color: #cdd6f4;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.preset-property input:focus,
.preset-property select:focus {
  outline: none;
  border-color: #cba6f7;
}

.preset-property input:disabled,
.preset-property select:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.preset-details-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #313244;
}

.preset-apply-btn,
.preset-reset-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-apply-btn {
  background-color: #cba6f7;
  color: #181825;
  border: none;
}

.preset-apply-btn:hover {
  background-color: #f5c2e7;
  transform: translateY(-2px);
}

.preset-reset-btn {
  background-color: #313244;
  color: #cdd6f4;
  border: none;
}

.preset-reset-btn:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

/* 模态对话框 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(24, 24, 37, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.modal-content {
  background-color: #1e1e2e;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 16px;
  background-color: #181825;
  color: #cba6f7;
  font-weight: bold;
  border-bottom: 1px solid #313244;
}

.modal-body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.9rem;
  color: #bac2de;
}

.form-group input,
.form-group textarea {
  background-color: #313244;
  border: 1px solid #45475a;
  color: #cdd6f4;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.modal-footer {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #313244;
}

.modal-btn {
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-btn.cancel {
  background-color: #313244;
  color: #cdd6f4;
  border: none;
}

.modal-btn.save {
  background-color: #cba6f7;
  color: #181825;
  border: none;
}

.modal-btn:hover {
  transform: translateY(-2px);
}

.modal-btn.cancel:hover {
  background-color: #45475a;
}

.modal-btn.save:hover {
  background-color: #f5c2e7;
}

.modal-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}
</style> 