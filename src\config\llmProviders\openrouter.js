/**
 * OpenRouter LLM 提供商配置
 * 支持多种模型的统一接口
 */

export const openrouterConfig = {
  // 提供商基本信息
  name: 'OpenRouter',
  id: 'openrouter',
  description: '通过OpenRouter访问多种AI模型',
  
  // API配置
  api: {
    baseUrl: 'https://openrouter.ai/api/v1',
    endpoint: '/chat/completions',
    headers: {
      'Content-Type': 'application/json',
      'HTTP-Referer': () => location.origin,
      'X-Title': 'Voice Comic Generator'
    },
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  
  // 默认配置
  defaults: {
    model: 'anthropic/claude-3-sonnet',
    temperature: 0.7,
    maxTokens: 4096,
    timeout: 30000
  },
  
  // 支持的模型列表
  models: [
    {
      id: 'anthropic/claude-3-sonnet',
      name: 'Claude 3 Sonnet',
      description: '平衡性能和成本的高质量模型',
      maxTokens: 200000,
      pricing: { input: 0.003, output: 0.015 }
    },
    {
      id: 'anthropic/claude-3-haiku',
      name: 'Claude 3 Haiku',
      description: '快速响应的轻量级模型',
      maxTokens: 200000,
      pricing: { input: 0.00025, output: 0.00125 }
    },
    {
      id: 'deepseek/deepseek-r1:free',
      name: 'DeepSeek R1 (Free)',
      description: '免费的高性能推理模型',
      maxTokens: 65536,
      pricing: { input: 0, output: 0 }
    },
    {
      id: 'google/gemini-2.0-flash-exp:free',
      name: 'Gemini 2.0 Flash (Free)',
      description: '免费的快速多模态模型',
      maxTokens: 1000000,
      pricing: { input: 0, output: 0 }
    }
  ],
  
  // 请求格式化函数
  formatRequest: (prompt, settings) => {
    // 检查是否是结构化的prompt（包含instruction和content）
    let messages;
    if (typeof prompt === 'object' && prompt.instruction && prompt.content) {
      messages = [
        { role: 'system', content: prompt.instruction },
        { role: 'user', content: prompt.content }
      ];
    } else {
      // 向后兼容：如果是字符串prompt，直接使用
      messages = [{ role: 'user', content: prompt }];
    }

    return {
      model: settings.model,
      messages: messages,
      temperature: settings.temperature || 0.7,
      max_tokens: settings.maxOutputTokens || 4096,
      reasoning: { effort: "high", exclude: true }
    };
  },
  
  // 响应解析函数
  parseResponse: (data) => {
    if (!data.choices || !data.choices[0]) {
      throw new Error('API响应格式无效');
    }
    
    return {
      content: data.choices[0].message.content.trim(),
      usage: data.usage,
      model: data.model
    };
  },
  
  // 错误处理
  handleError: (error, response) => {
    if (response && response.status === 401) {
      return new Error('OpenRouter API密钥无效或已过期');
    }
    if (response && response.status === 429) {
      return new Error('OpenRouter API请求频率限制，请稍后重试');
    }
    if (response && response.status === 402) {
      return new Error('OpenRouter账户余额不足');
    }
    return new Error(`OpenRouter API错误: ${error.message}`);
  },
  
  // 配置验证
  validateConfig: (config) => {
    const errors = [];
    
    if (!config.apiKey) {
      errors.push('缺少OpenRouter API密钥');
    }
    
    if (!config.model) {
      errors.push('未指定模型');
    }
    
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('温度值应在0-2之间');
    }
    
    return errors;
  }
};

export default openrouterConfig;
