<template>
  <div class="character-analyzer-demo">
    <h2 class="demo-title">
      角色分析器示例
    </h2>

    <LLMCommunicator
      title="角色分析器"
      placeholder="输入小说或剧本片段，AI将提取角色信息..."
      button-text="分析角色"
      analyze-type="character-analysis"
      :default-prompt="characterPromptTemplate"
      :show-advanced="true"
      :show-raw-output="false"
      :textarea-rows="10"
      @processing-complete="handleAnalysisComplete"
    >
      <!-- 自定义的结果显示模板 -->
      <template #formatted-result="{ data }">
        <div
          v-if="data && data.characters && data.characters.length > 0"
          class="character-results"
        >
          <div
            v-for="(character, index) in data.characters"
            :key="index"
            class="character-card"
          >
            <div class="character-header">
              <span class="character-name">{{ character.name || '未命名角色' }}</span>
            </div>
            <div class="character-details">
              <div class="character-detail">
                <span class="detail-label">视觉特征:</span>
                <span class="detail-value">{{ character.visualTraits || '未提供' }}</span>
              </div>
              <div
                v-if="character.personality"
                class="character-detail"
              >
                <span class="detail-label">性格:</span>
                <span class="detail-value">{{ character.personality }}</span>
              </div>
              <div
                v-if="character.role"
                class="character-detail"
              >
                <span class="detail-label">角色定位:</span>
                <span class="detail-value">{{ character.role }}</span>
              </div>
            </div>
          </div>
        </div>
        <div
          v-else-if="data && data.setting"
          class="setting-result"
        >
          <h3>背景设定</h3>
          <div class="setting-detail">
            <div class="setting-name">
              {{ data.setting.name || data.setting.era || '未命名设定' }}
            </div>
            <p class="setting-description">
              {{ data.setting.description || '未提供描述' }}
            </p>
          </div>
        </div>
        <div
          v-else
          class="no-data-message"
        >
          暂无有效分析结果，请尝试提供更多文本内容
        </div>
      </template>
    </LLMCommunicator>
  </div>
</template>

<script>
import LLMCommunicator from './LLMCommunicator.vue'

export default {
  name: 'CharacterAnalyzerDemo',
  components: {
    LLMCommunicator
  },
  data() {
    return {
      characterPromptTemplate: `请分析以下文本中的角色信息，提取每个角色的基本信息：

文本内容：
{text}

请按照以下JSON格式返回分析结果：
{
  "characters": [
    {
      "name": "角色姓名",
      "visualTraits": "外貌特征描述",
      "personality": "性格特点",
      "role": "在故事中的作用"
    }
  ]
}`
    }
  },
  methods: {
    handleAnalysisComplete(result) {
      console.log('角色分析完成:', result)
      // 可以在这里添加额外的处理逻辑
    }
  }
}
</script>

<style scoped>
.character-analyzer-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-title {
  color: #e0e0e0;
  text-align: center;
  margin-bottom: 20px;
}

.character-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.character-card {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
}

.character-header {
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.character-name {
  font-size: 18px;
  font-weight: bold;
  color: #3e8fb0;
}

.character-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #888;
  font-weight: bold;
}

.detail-value {
  color: #e0e0e0;
  line-height: 1.4;
}

.setting-result {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.setting-result h3 {
  color: #3e8fb0;
  margin-bottom: 16px;
}

.setting-name {
  font-size: 18px;
  font-weight: bold;
  color: #e0e0e0;
  margin-bottom: 8px;
}

.setting-description {
  color: #ccc;
  line-height: 1.6;
}

.no-data-message {
  text-align: center;
  color: #888;
  padding: 40px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  margin-top: 20px;
}
</style>
