/* 卡片标题悬停炫酷动画 */
.text-hover-bling {
  animation: blink-link-cool 1.2s linear infinite;
  background: linear-gradient(90deg, #a23a5a, #29908b, #a23a5a 80%);
  background-size: 200% auto;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  filter: drop-shadow(0 0 6px #a23a5a88);
  transform: scale(1.08);
  transition: transform 0.18s;
}
@keyframes blink-link-cool {
  0% {
    background-position: 0% 50%;
    filter: drop-shadow(0 0 6px #a23a5a88);
  }
  50% {
    background-position: 100% 50%;
    filter: drop-shadow(0 0 16px #29908bcc);
  }
  100% {
    background-position: 0% 50%;
    filter: drop-shadow(0 0 6px #a23a5a88);
  }
} 