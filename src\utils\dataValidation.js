/**
 * 数据验证工具模块
 * 从 ContentCreationStudio.vue 中提取的数据验证相关功能
 * 
 * 功能：
 * - 图像数据验证
 * - 图像数据结构修复
 * - 生成图像冗余数据清理
 * - 纯工具函数，无状态依赖
 */

/**
 * 简单的图像数据验证
 * 验证每行的图像数据完整性和一致性
 * @param {Array} rows - 行数据数组
 * @returns {Object} 验证结果
 */
export function simpleImageDataValidation(rows) {
  console.log('🎨 [图片验证] 开始验证图片数据...');

  if (!Array.isArray(rows)) {
    console.warn('🎨 [图片验证] 无效的行数据，不是数组');
    return { isValid: false, errors: ['行数据不是数组'] };
  }

  const validationResults = [];
  let totalErrors = 0;

  rows.forEach((row, index) => {
    const hasMainImage = !!(row.imageSrc &&
      row.imageSrc !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

    const thumbnailsCount = row.thumbnails ? row.thumbnails.length : 0;
    const isValid = hasMainImage || thumbnailsCount === 0;

    const result = {
      rowIndex: index,
      hasMainImage: hasMainImage ? '✅' : '❌',
      thumbnailsCount,
      status: isValid ? '正常' : '异常'
    };

    validationResults.push(result);

    if (!isValid) {
      totalErrors++;
    }

    console.log(`🎨 [图片验证] 行${index + 1}:`, result);
  });

  console.log('🎨 [图片验证] 验证完成');

  return {
    isValid: totalErrors === 0,
    totalRows: rows.length,
    errorCount: totalErrors,
    results: validationResults
  };
}

/**
 * 修复图像数据结构一致性
 * 修复缩略图数据结构，移除多余字段，添加缺失字段
 * @param {Array} rows - 行数据数组
 * @returns {Object} 修复结果
 */
export function fixImageDataStructure(rows) {
  console.log('🔧 [数据修复] 开始修复图片数据结构一致性');

  if (!Array.isArray(rows)) {
    console.warn('🔧 [数据修复] 无效的行数据，不是数组');
    return { fixedCount: 0, errors: ['行数据不是数组'] };
  }

  let fixedCount = 0;
  const fixedDetails = [];

  rows.forEach((row, rowIndex) => {
    let rowFixed = false;
    const rowDetails = {
      rowIndex,
      fixes: []
    };

    // 修复可选图数据结构
    if (row.thumbnails && Array.isArray(row.thumbnails)) {
      row.thumbnails.forEach((thumbnail, thumbIndex) => {
        if (thumbnail && typeof thumbnail === 'object') {
          let thumbFixed = false;

          // 移除多余字段
          if (thumbnail.name !== undefined) {
            delete thumbnail.name;
            thumbFixed = true;
            rowDetails.fixes.push(`第${thumbIndex + 1}张图：移除name字段`);
          }
          if (thumbnail.size !== undefined) {
            delete thumbnail.size;
            thumbFixed = true;
            rowDetails.fixes.push(`第${thumbIndex + 1}张图：移除size字段`);
          }
          if (thumbnail.type !== undefined) {
            delete thumbnail.type;
            thumbFixed = true;
            rowDetails.fixes.push(`第${thumbIndex + 1}张图：移除type字段`);
          }

          // 添加缺失的 ismain 字段
          if (thumbnail.ismain === undefined) {
            thumbnail.ismain = false;
            thumbFixed = true;
            rowDetails.fixes.push(`第${thumbIndex + 1}张图：添加ismain字段`);
          }

          // 修复错误的字段名
          if (thumbnail.isMain !== undefined) {
            thumbnail.ismain = thumbnail.isMain;
            delete thumbnail.isMain;
            thumbFixed = true;
            rowDetails.fixes.push(`第${thumbIndex + 1}张图：修复isMain字段名为ismain`);
          }

          if (thumbFixed) {
            console.log(`🔧 [数据修复] 修复第${rowIndex + 1}行第${thumbIndex + 1}张可选图数据结构`);
            rowFixed = true;
          }
        }
      });

      // 移除无效的缩略图项
      const originalLength = row.thumbnails.length;
      row.thumbnails = row.thumbnails.filter(thumb =>
        thumb &&
        typeof thumb === 'object' &&
        thumb.src &&
        thumb.src.trim() !== ''
      );

      if (row.thumbnails.length < originalLength) {
        const removedCount = originalLength - row.thumbnails.length;
        console.log(`🔧 [数据修复] 第${rowIndex + 1}行移除了${removedCount}个无效缩略图`);
        rowDetails.fixes.push(`移除${removedCount}个无效缩略图`);
        rowFixed = true;
      }
    }

    if (rowFixed) {
      fixedCount++;
      fixedDetails.push(rowDetails);
    }
  });

  const result = {
    fixedCount,
    totalRows: rows.length,
    fixedDetails
  };

  if (fixedCount > 0) {
    console.log(`🔧 [数据修复] 完成，修复了${fixedCount}行的图片数据结构`);
  } else {
    console.log('🔧 [数据修复] 没有发现需要修复的数据结构问题');
  }

  return result;
}

/**
 * 清理生成图像冗余数据
 * 移除generatedImages字段和thumbnails中的主图重复
 * @param {Array} rows - 行数据数组
 * @returns {Object} 清理结果
 */
export function cleanupGeneratedImagesData(rows) {
  console.log('🧹 [数据清理] 开始清理generatedImages冗余数据...');

  if (!Array.isArray(rows)) {
    console.warn('🧹 [数据清理] 无效的行数据，不是数组');
    return { cleanedCount: 0, errors: ['行数据不是数组'] };
  }

  let cleanedCount = 0;
  const cleanedDetails = [];

  rows.forEach((row, index) => {
    const rowDetails = {
      rowIndex: index,
      cleanedItems: []
    };
    let rowCleaned = false;

    // 清理generatedImages字段
    if (row.generatedImages) {
      delete row.generatedImages;
      rowCleaned = true;
      rowDetails.cleanedItems.push('移除generatedImages字段');
      console.log(`🧹 [数据清理] 已清理行${index + 1}的generatedImages数据`);
    }

    // 同时清理thumbnails中的主图重复
    if (row.thumbnails && row.imageSrc) {
      const originalLength = row.thumbnails.length;
      row.thumbnails = row.thumbnails.filter(thumb => thumb.src !== row.imageSrc);
      if (row.thumbnails.length < originalLength) {
        const removedCount = originalLength - row.thumbnails.length;
        rowDetails.cleanedItems.push(`从thumbnails中移除${removedCount}个重复的主图`);
        rowCleaned = true;
        console.log(`🧹 [数据清理] 行${index + 1}: 从thumbnails中移除了重复的主图`);
      }
    }

    if (rowCleaned) {
      cleanedCount++;
      cleanedDetails.push(rowDetails);
    }
  });

  const result = {
    cleanedCount,
    totalRows: rows.length,
    cleanedDetails
  };

  if (cleanedCount > 0) {
    console.log(`🧹 [数据清理] 完成，清理了${cleanedCount}行的冗余数据`);
  } else {
    console.log('🧹 [数据清理] 没有发现需要清理的数据');
  }

  return result;
}

/**
 * 数据验证工具集合
 * 提供统一的接口访问所有验证工具
 */
export const dataValidationUtils = {
  validateImageData: simpleImageDataValidation,
  fixImageStructure: fixImageDataStructure,
  cleanupRedundantData: cleanupGeneratedImagesData
};

/**
 * 默认导出，包含所有验证工具
 */
export default {
  simpleImageDataValidation,
  fixImageDataStructure,
  cleanupGeneratedImagesData,
  dataValidationUtils
};
