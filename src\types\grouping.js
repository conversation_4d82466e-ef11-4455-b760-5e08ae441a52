/**
 * 分组指令接口定义
 * 
 * @typedef {Object} GroupingInstruction
 * @property {string} type - 指令类型: 'merge' 或 'keep'
 * @property {number} originalIndex - 原始索引
 * @property {number[]} [mergedIndices] - 合并项的原始索引数组 (仅用于 type='merge')
 */

/**
 * 验证分组指令是否有效
 * @param {GroupingInstruction[]} instructions - 分组指令数组
 * @returns {boolean} 是否有效
 */
export function validateGroupingInstructions(instructions) {
  if (!Array.isArray(instructions) || instructions.length === 0) {
    console.error('分组指令必须是非空数组');
    return false;
  }

  for (const instruction of instructions) {
    if (!instruction.type || !['merge', 'keep'].includes(instruction.type)) {
      console.error('无效的指令类型:', instruction.type);
      return false;
    }

    if (typeof instruction.originalIndex !== 'number') {
      console.error('缺少有效的原始索引:', instruction);
      return false;
    }

    if (instruction.type === 'merge') {
      if (!Array.isArray(instruction.mergedIndices) || instruction.mergedIndices.length === 0) {
        console.error('合并指令缺少有效的合并索引:', instruction);
        return false;
      }
    }
  }

  return true;
}