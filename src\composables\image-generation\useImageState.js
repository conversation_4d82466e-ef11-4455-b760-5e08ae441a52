/**
 * 图像生成状态管理
 * 负责生成状态、进度、错误等状态的管理
 */

import { ref, reactive, computed } from 'vue';
import comfyuiImageGeneration from '../../services/comfyuiImageGeneration.js';

export function useImageState() {
  // 响应式状态
  const isGenerating = ref(false);
  const isCancelling = ref(false);
  const generationProgress = ref(0);
  const generationStage = ref('');
  const generationMessage = ref('');
  const generationError = ref('');
  const currentTaskId = ref(null);

  // 响应式的服务可用性状态
  const isServiceAvailable = ref(false);

  // 生成历史记录
  const generationHistory = reactive(new Map());

  // 创建一个包装函数来追踪 isGenerating 的变化
  const setIsGenerating = (value, location = 'unknown') => {
    const oldValue = isGenerating.value;

    // 只在值真正发生变化时更新和记录
    if (oldValue !== value) {
      isGenerating.value = value;
      // 🔧 优化：只在开发模式下显示详细的状态变化
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ImageState] setIsGenerating: ${oldValue} -> ${value} (${location})`);
      } else if (value) {
        // 生产模式下只显示开始生成的日志
        console.log(`[ImageState] 开始图像生成`);
      }
    }
  };

  // 更新服务状态
  const updateServiceStatus = () => {
    const available = comfyuiImageGeneration.isAvailable();
    if (isServiceAvailable.value !== available) {
      isServiceAvailable.value = available;
      console.log(`[状态] 服务可用性: ${available}`);
    }
  };

  // 计算属性：是否可以生成
  const canGenerate = computed(() => {
    // 始终返回true，让按钮保持可点击状态
    // 具体的服务可用性检查将在点击时进行，并通过Toast通知用户
    return true;
  });

  // 计算属性：服务状态
  const serviceStatus = computed(() => {
    return comfyuiImageGeneration.getStatus();
  });

  /**
   * 初始化服务
   */
  const initializeService = async () => {
    try {
      const success = await comfyuiImageGeneration.initialize();
      updateServiceStatus();
      
      if (success) {
        console.log('🎨 [图像生成] 服务初始化成功');
      } else {
        console.warn('🎨 [图像生成] 服务初始化失败');
      }
      
      return success;
    } catch (error) {
      console.error('🎨 [图像生成] 服务初始化错误:', error);
      updateServiceStatus();
      return false;
    }
  };

  /**
   * 处理生成进度
   */
  const handleProgress = (progressData) => {
    generationProgress.value = progressData.progress || 0;
    generationStage.value = progressData.stage || '';
    generationMessage.value = progressData.message || '';
    
    console.log('📊 [状态] 进度更新:', {
      progress: generationProgress.value,
      stage: generationStage.value,
      message: generationMessage.value
    });
  };

  /**
   * 处理错误
   */
  const handleError = (error, context = '', userInputData = null) => {
    const errorMessage = error.message || error.toString();
    generationError.value = errorMessage;
    
    console.error(`❌ [状态] 错误处理 (${context}):`, {
      error: errorMessage,
      userInputData,
      stack: error.stack
    });

    // 更新服务状态
    updateServiceStatusFromError(error);
  };

  /**
   * 根据错误更新服务状态
   */
  const updateServiceStatusFromError = (error) => {
    const errorMessage = error.message || error.toString();

    // 检查是否是连接相关错误
    const isConnectionError = errorMessage.includes('连接') || 
                             errorMessage.includes('网络') ||
                             errorMessage.includes('timeout') ||
                             errorMessage.includes('ECONNREFUSED');

    if (isConnectionError) {
      isServiceAvailable.value = false;
      console.log('[状态] 检测到连接错误，标记服务不可用');
    }

    // 检查是否是服务恢复
    const isServiceRecovered = errorMessage.includes('成功') || 
                              errorMessage.includes('连接已建立');

    if (isServiceRecovered) {
      updateServiceStatus(); // 重新检查服务状态
    }
  };

  /**
   * 重置生成状态
   */
  const resetGenerationState = () => {
    generationProgress.value = 0;
    generationStage.value = '';
    generationMessage.value = '';
    generationError.value = '';
    currentTaskId.value = null;
    
    console.log('[ImageState] resetGenerationState: Generation state has been reset.');
  };

  /**
   * 获取行的生成历史
   */
  const getRowGenerationHistory = (projectTitle, chapterTitle, rowIndex) => {
    const historyKey = `${projectTitle}/${chapterTitle}/${rowIndex}`;
    return generationHistory.get(historyKey) || null;
  };

  /**
   * 添加生成历史记录
   */
  const addGenerationHistory = (projectTitle, chapterTitle, rowIndex, historyData) => {
    const historyKey = `${projectTitle}/${chapterTitle}/${rowIndex}`;
    const existingHistory = generationHistory.get(historyKey) || [];
    
    const newEntry = {
      timestamp: new Date().toISOString(),
      ...historyData
    };
    
    existingHistory.push(newEntry);
    generationHistory.set(historyKey, existingHistory);
    
    console.log('📝 [状态] 添加生成历史:', {
      key: historyKey,
      entry: newEntry
    });
  };

  /**
   * 清除生成历史
   */
  const clearGenerationHistory = () => {
    generationHistory.clear();
    console.log('🗑️ [状态] 生成历史已清除');
  };

  /**
   * 强制重置状态
   */
  const forceResetState = () => {
    console.log('🔧 [状态] 执行强制状态重置...');

    // 重置前端状态
    setIsGenerating(false, '强制重置');
    isCancelling.value = false;
    resetGenerationState();

    // 重置后端状态
    try {
      comfyuiImageGeneration.forceResetState();
    } catch (error) {
      console.warn('⚠️ [状态] 后端状态重置失败:', error);
    }

    // 更新服务状态
    updateServiceStatus();

    console.log('[ImageState] forceResetState: Forced state reset completed.');
  };

  /**
   * 🆕 完整的状态重置 - 包括行级状态
   */
  const completeStateReset = (rows = null) => {
    console.log('🔧 [状态] 执行完整状态重置');

    // 1. 重置全局状态
    forceResetState();

    // 2. 重置行级状态（如果提供了rows）
    if (rows && Array.isArray(rows)) {
      console.log('🔧 [状态] 重置行级生成状态，行数:', rows.length);
      rows.forEach((row, index) => {
        if (row) {
          row.isGenerating = false;
          row.isQueued = false;
          row.generationProgress = 0;
          row.generationMessage = '';

          // 清理队列相关状态
          if (row.queueTaskId) {
            row.queueTaskId = '';
            row.queuePosition = 0;
          }

          console.log(`🔧 [状态] 重置第${index + 1}行状态完成`);
        }
      });
    }

    console.log('[ImageState] completeStateReset: Full state reset completed (including row-level).');
    return true;
  };

  /**
   * 验证状态一致性
   */
  const validateStateConsistency = () => {
    const backendValidation = comfyuiImageGeneration.validateStateConsistency();
    const frontendState = {
      isGenerating: isGenerating.value,
      isCancelling: isCancelling.value,
      currentTaskId: currentTaskId.value,
      hasProgress: generationProgress.value > 0
    };

    const isConsistent = frontendState.isGenerating === backendValidation.isGenerating;

    console.log('🔍 [状态] 状态一致性验证:', {
      frontend: frontendState,
      backend: backendValidation,
      isConsistent
    });

    return {
      isConsistent,
      frontend: frontendState,
      backend: backendValidation,
      recommendations: isConsistent ? [] : ['考虑执行状态同步或重置']
    };
  };

  /**
   * 获取完整状态快照
   */
  const getStateSnapshot = () => {
    return {
      isGenerating: isGenerating.value,
      isCancelling: isCancelling.value,
      generationProgress: generationProgress.value,
      generationStage: generationStage.value,
      generationMessage: generationMessage.value,
      generationError: generationError.value,
      currentTaskId: currentTaskId.value,
      isServiceAvailable: isServiceAvailable.value,
      serviceStatus: serviceStatus.value,
      historyCount: generationHistory.size,
      timestamp: new Date().toISOString()
    };
  };

  return {
    // 响应式状态
    isGenerating,
    isCancelling,
    generationProgress,
    generationStage,
    generationMessage,
    generationError,
    currentTaskId,
    isServiceAvailable,
    generationHistory,

    // 计算属性
    canGenerate,
    serviceStatus,

    // 状态管理方法
    setIsGenerating,
    updateServiceStatus,
    initializeService,
    handleProgress,
    handleError,
    updateServiceStatusFromError,
    resetGenerationState,
    forceResetState,
    completeStateReset,
    validateStateConsistency,
    getStateSnapshot,

    // 历史记录管理
    getRowGenerationHistory,
    addGenerationHistory,
    clearGenerationHistory
  };
}
