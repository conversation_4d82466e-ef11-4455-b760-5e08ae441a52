/**
 * ComfyUI错误恢复和用户体验管理
 * 提供错误处理、重试机制、用户友好的错误提示等功能
 */

import { reactive, computed } from 'vue';

export function useComfyUIErrorRecovery() {
  // 🆕 错误状态管理
  const errorState = reactive({
    hasError: false,
    errorType: null, // 'memory', 'connection', 'timeout', 'unknown'
    errorMessage: '',
    originalError: null,
    canRetry: false,
    retryCount: 0,
    maxRetries: 3,
    isRetrying: false,
    lastErrorTime: null,
    userInput: {
      saved: false,
      data: null
    }
  });

  // 🆕 服务状态
  const serviceState = reactive({
    status: 'disconnected', // 'disconnected', 'connecting', 'connected', 'error', 'maintenance'
    isAvailable: false,
    lastConnectionCheck: null,
    connectionQuality: 'unknown', // 'good', 'poor', 'bad', 'unknown'
    memoryOptimizationLevel: 0,
    circuitBreakerOpen: false,
    estimatedRecoveryTime: null
  });

  // 🆕 用户友好的错误信息映射
  const errorMessages = {
    memory: {
      title: '显存不足',
      message: '您的显卡内存不足以完成当前设置的图像生成。',
      suggestions: [
        '降低图像分辨率（推荐：从1024x1024降至768x768）',
        '减少批次大小（推荐：设置为1）',
        '关闭其他占用显存的程序',
        '使用更简单的模型或工作流'
      ],
      autoFix: '系统将自动降低设置并重试'
    },
    connection: {
      title: 'ComfyUI连接失败',
      message: '无法连接到ComfyUI服务器，请检查服务状态。',
      suggestions: [
        '确保ComfyUI正在运行（访问 http://localhost:8188）',
        '检查防火墙设置是否阻止了连接',
        '重启ComfyUI服务器',
        '检查端口8188是否被其他程序占用'
      ],
      autoFix: '系统将自动重试连接'
    },
    timeout: {
      title: '生成超时',
      message: '图像生成时间过长，可能是由于复杂的提示词或服务器负载过高。',
      suggestions: [
        '简化提示词内容',
        '降低图像质量设置',
        '检查ComfyUI服务器性能',
        '等待服务器负载降低后重试'
      ],
      autoFix: '系统将清理超时任务并允许重试'
    },
    unknown: {
      title: '未知错误',
      message: '发生了未预期的错误，请查看详细信息。',
      suggestions: [
        '检查ComfyUI控制台是否有错误信息',
        '验证工作流配置是否正确',
        '重启ComfyUI服务器',
        '联系技术支持'
      ],
      autoFix: '请手动重试或联系支持'
    }
  };

  // 🆕 计算属性
  const currentErrorInfo = computed(() => {
    if (!errorState.hasError || !errorState.errorType) {
      return null;
    }
    return errorMessages[errorState.errorType] || errorMessages.unknown;
  });

  const canRetryNow = computed(() => {
    return errorState.canRetry && 
           !errorState.isRetrying && 
           errorState.retryCount < errorState.maxRetries &&
           !serviceState.circuitBreakerOpen;
  });

  const statusText = computed(() => {
    switch (serviceState.status) {
      case 'connected':
        return '已连接';
      case 'connecting':
        return '连接中...';
      case 'disconnected':
        return '未连接';
      case 'error':
        return '连接错误';
      case 'maintenance':
        return '维护中';
      default:
        return '未知状态';
    }
  });

  const statusColor = computed(() => {
    switch (serviceState.status) {
      case 'connected':
        return 'success';
      case 'connecting':
        return 'warning';
      case 'disconnected':
        return 'info';
      case 'error':
        return 'danger';
      case 'maintenance':
        return 'warning';
      default:
        return 'secondary';
    }
  });

  // 🆕 错误处理方法 - 使用toast通知代替模态弹窗
  function handleError(error, context = '', userInputData = null) {
    console.error('🚨 [错误恢复] 处理错误:', error, '上下文:', context);

    // 保存用户输入数据
    if (userInputData) {
      errorState.userInput.data = userInputData;
      errorState.userInput.saved = true;
    }

    errorState.hasError = true;
    errorState.originalError = error;
    errorState.errorMessage = error.message || error.toString();
    errorState.lastErrorTime = new Date();

    // 分析错误类型
    errorState.errorType = analyzeErrorType(error);

    // 设置重试能力
    errorState.canRetry = isRetryableError(errorState.errorType);

    // 更新服务状态
    updateServiceStatus(errorState.errorType);

    // 🆕 使用toast通知显示错误信息，而不是模态弹窗
    showErrorToast(errorState.errorType, error);
  }

  // 🆕 显示错误toast通知
  function showErrorToast(errorType, error) {
    console.log('🔔 [Toast] showErrorToast被调用:', { errorType, error: error.message });

    const errorInfo = errorMessages[errorType] || errorMessages.unknown;

    console.log('🔔 [Toast] 错误信息映射:', { errorType, errorInfo });

    // 简化的错误消息，适合toast显示
    let toastMessage = errorInfo.message || error.message || '图像生成过程中发生未知错误';

    // 根据错误类型添加简短的解决建议
    if (errorType === 'memory') {
      toastMessage += ' 建议降低图像分辨率或批次大小。';
    } else if (errorType === 'connection') {
      toastMessage += ' 请检查ComfyUI服务器状态。';
    } else if (errorType === 'timeout') {
      toastMessage += ' 请简化提示词或稍后重试。';
    } else if (errorType === 'unknown') {
      toastMessage += ' 请检查ComfyUI设置和连接状态。';
    }

    // 🆕 确保消息不为空
    const finalTitle = errorInfo.title || '图像生成失败';
    const finalMessage = toastMessage || '发生未知错误';

    // 🔍 调试toast参数
    console.log('🔔 [Toast] 准备显示错误通知:', {
      type: 'error',
      title: finalTitle,
      message: finalMessage,
      errorType: errorType,
      originalError: error.message,
      hasTitle: !!finalTitle,
      hasMessage: !!finalMessage,
      titleLength: finalTitle.length,
      messageLength: finalMessage.length
    });

    // 触发toast通知（通过全局事件或状态管理）
    if (typeof window !== 'undefined' && window.showToast) {
      console.log('🔔 [Toast] 调用全局showToast方法，参数:', {
        type: 'error',
        title: finalTitle,
        message: finalMessage,
        duration: 6000
      });

      window.showToast({
        type: 'error',
        title: finalTitle,
        message: finalMessage,
        duration: 6000 // 错误消息显示6秒
      });
    } else {
      console.warn('🔔 [Toast] 全局showToast方法不可用，使用备用方案');
      // 备用方案：使用console输出
      console.error(`🚨 ${finalTitle}: ${finalMessage}`);
    }
  }

  function analyzeErrorType(error) {
    const message = error.message || error.toString();
    
    if (isMemoryError(message)) return 'memory';
    if (isConnectionError(message)) return 'connection';
    if (isTimeoutError(message)) return 'timeout';
    
    return 'unknown';
  }

  function isMemoryError(message) {
    const patterns = [
      'CUDA out of memory',
      'OutOfMemoryError',
      'insufficient memory',
      'memory allocation failed'
    ];
    return patterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()));
  }

  function isConnectionError(message) {
    const patterns = [
      'Failed to fetch',
      'ECONNREFUSED',
      'CONNECTION_ERROR',
      'ComfyUI服务器连接失败',
      'net::ERR_CONNECTION_REFUSED'
    ];
    return patterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()));
  }

  function isTimeoutError(message) {
    const patterns = ['timeout', 'AbortError', '超时'];
    return patterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()));
  }

  function isRetryableError(errorType) {
    return ['memory', 'connection', 'timeout'].includes(errorType);
  }

  function updateServiceStatus(errorType) {
    switch (errorType) {
      case 'connection':
        serviceState.status = 'disconnected';
        serviceState.isAvailable = false;
        break;
      case 'memory':
        serviceState.status = 'connected'; // 连接正常，但需要优化
        serviceState.memoryOptimizationLevel++;
        break;
      case 'timeout':
        serviceState.status = 'error';
        break;
      default:
        serviceState.status = 'error';
    }
  }

  // 🆕 重试方法
  async function retry(retryCallback) {
    if (!canRetryNow.value) {
      console.warn('🚨 [错误恢复] 当前不能重试');
      return false;
    }

    errorState.isRetrying = true;
    errorState.retryCount++;

    try {
      console.log(`🔄 [错误恢复] 开始重试 (${errorState.retryCount}/${errorState.maxRetries})`);
      
      const result = await retryCallback();
      
      // 重试成功，清理错误状态
      clearError();
      return result;
    } catch (error) {
      console.error('🚨 [错误恢复] 重试失败:', error);
      
      // 更新错误信息
      handleError(error, '重试失败');
      return false;
    } finally {
      errorState.isRetrying = false;
    }
  }

  // 🆕 清理错误状态
  function clearError() {
    errorState.hasError = false;
    errorState.errorType = null;
    errorState.errorMessage = '';
    errorState.originalError = null;
    errorState.canRetry = false;
    errorState.retryCount = 0;
    errorState.isRetrying = false;
    errorState.lastErrorTime = null;
  }

  // 🆕 恢复用户输入
  function restoreUserInput() {
    if (errorState.userInput.saved && errorState.userInput.data) {
      const data = errorState.userInput.data;
      errorState.userInput.saved = false;
      errorState.userInput.data = null;
      return data;
    }
    return null;
  }

  // 🆕 更新服务状态
  function updateServiceState(status, additionalInfo = {}) {
    serviceState.status = status;
    serviceState.lastConnectionCheck = new Date();
    
    if (additionalInfo.isAvailable !== undefined) {
      serviceState.isAvailable = additionalInfo.isAvailable;
    }
    
    if (additionalInfo.connectionQuality) {
      serviceState.connectionQuality = additionalInfo.connectionQuality;
    }
    
    if (additionalInfo.circuitBreakerOpen !== undefined) {
      serviceState.circuitBreakerOpen = additionalInfo.circuitBreakerOpen;
    }
    
    if (additionalInfo.estimatedRecoveryTime) {
      serviceState.estimatedRecoveryTime = additionalInfo.estimatedRecoveryTime;
    }
  }

  return {
    // 状态
    errorState,
    serviceState,
    
    // 计算属性
    currentErrorInfo,
    canRetryNow,
    statusText,
    statusColor,
    
    // 方法
    handleError,
    retry,
    clearError,
    restoreUserInput,
    updateServiceState
  };
}
