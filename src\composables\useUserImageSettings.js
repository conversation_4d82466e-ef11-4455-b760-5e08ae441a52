/**
 * 用户图像设置管理
 * 管理用户的图像生成相关设置，包括上下文行数等
 */

import { ref, computed } from 'vue';

/**
 * 用户图像设置 composable
 * @returns {Object} 设置管理相关的方法和状态
 */
export function useUserImageSettings() {
  // 内部状态
  const isLoading = ref(false);
  const error = ref(null);

  // 默认设置
  const defaultSettings = {
    contextLines: 10, // 默认上下文行数
    lastUpdated: Date.now(),
    version: '1.0'
  };

  /**
   * 获取项目和章节信息
   * @returns {Object} 项目信息
   */
  function getProjectInfo() {
    const urlParams = new URLSearchParams(window.location.search);
    const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
    const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');
    
    return { projectTitle, chapterTitle };
  }

  /**
   * 构建设置文件路径
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @returns {string} 设置文件路径
   */
  function getSettingsFilePath(projectTitle, chapterTitle) {
    return `draft/${projectTitle}/${chapterTitle}/UserImageSettings.json`;
  }

  /**
   * 加载用户图像设置
   * @param {string} projectTitle - 项目标题（可选）
   * @param {string} chapterTitle - 章节标题（可选）
   * @returns {Promise<Object>} 用户设置
   */
  async function loadUserImageSettings(projectTitle = null, chapterTitle = null) {
    try {
      isLoading.value = true;
      error.value = null;

      // 获取项目信息
      const projectInfo = projectTitle && chapterTitle ? 
        { projectTitle, chapterTitle } : 
        getProjectInfo();

      if (!projectInfo.projectTitle || !projectInfo.chapterTitle) {
        console.warn('[useUserImageSettings] 无法获取项目或章节信息，使用默认设置');
        return { ...defaultSettings };
      }

      const settingsPath = getSettingsFilePath(projectInfo.projectTitle, projectInfo.chapterTitle);
      console.log(`[useUserImageSettings] 正在加载设置: ${settingsPath}`);

      const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(settingsPath)}&t=${Date.now()}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          console.log('[useUserImageSettings] 设置文件不存在，使用默认设置');
          return { ...defaultSettings };
        } else {
          throw new Error(`读取设置文件失败: ${response.status}`);
        }
      }

      const result = await response.json();
      let settings;

      if (result.success && result.content) {
        settings = JSON.parse(result.content);
      } else {
        throw new Error('设置文件格式无效');
      }

      // 验证设置数据并合并默认值
      const validatedSettings = {
        ...defaultSettings,
        ...settings,
        lastUpdated: settings.lastUpdated || Date.now()
      };

      console.log('[useUserImageSettings] 设置加载成功:', validatedSettings);
      return validatedSettings;

    } catch (err) {
      console.error('[useUserImageSettings] 加载设置失败:', err);
      error.value = err.message;
      return { ...defaultSettings };
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * 保存用户图像设置
   * @param {Object} settings - 要保存的设置
   * @param {string} projectTitle - 项目标题（可选）
   * @param {string} chapterTitle - 章节标题（可选）
   * @returns {Promise<boolean>} 保存是否成功
   */
  async function saveUserImageSettings(settings, projectTitle = null, chapterTitle = null) {
    try {
      isLoading.value = true;
      error.value = null;

      // 获取项目信息
      const projectInfo = projectTitle && chapterTitle ? 
        { projectTitle, chapterTitle } : 
        getProjectInfo();

      if (!projectInfo.projectTitle || !projectInfo.chapterTitle) {
        throw new Error('无法获取项目或章节信息');
      }

      const settingsPath = getSettingsFilePath(projectInfo.projectTitle, projectInfo.chapterTitle);
      console.log(`[useUserImageSettings] 正在保存设置: ${settingsPath}`);

      // 准备要保存的设置数据
      const settingsToSave = {
        ...settings,
        lastUpdated: Date.now(),
        version: '1.0'
      };

      const response = await fetch('/api/local/save-text-file', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filePath: settingsPath,
          content: JSON.stringify(settingsToSave, null, 2)
        })
      });

      if (!response.ok) {
        throw new Error(`保存设置文件失败: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || '保存设置失败');
      }

      console.log('[useUserImageSettings] 设置保存成功');
      return true;

    } catch (err) {
      console.error('[useUserImageSettings] 保存设置失败:', err);
      error.value = err.message;
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * 获取上下文行数设置
   * @param {string} projectTitle - 项目标题（可选）
   * @param {string} chapterTitle - 章节标题（可选）
   * @returns {Promise<number>} 上下文行数
   */
  async function getContextLines(projectTitle = null, chapterTitle = null) {
    try {
      const settings = await loadUserImageSettings(projectTitle, chapterTitle);
      return settings.contextLines || defaultSettings.contextLines;
    } catch (err) {
      console.error('[useUserImageSettings] 获取上下文行数失败:', err);
      return defaultSettings.contextLines;
    }
  }

  /**
   * 设置上下文行数
   * @param {number} contextLines - 上下文行数
   * @param {string} projectTitle - 项目标题（可选）
   * @param {string} chapterTitle - 章节标题（可选）
   * @returns {Promise<boolean>} 设置是否成功
   */
  async function setContextLines(contextLines, projectTitle = null, chapterTitle = null) {
    try {
      // 验证输入
      if (!Number.isInteger(contextLines) || contextLines < 1 || contextLines > 50) {
        throw new Error('上下文行数必须是1-50之间的整数');
      }

      // 加载当前设置
      const currentSettings = await loadUserImageSettings(projectTitle, chapterTitle);
      
      // 更新上下文行数
      const updatedSettings = {
        ...currentSettings,
        contextLines
      };

      // 保存设置
      const success = await saveUserImageSettings(updatedSettings, projectTitle, chapterTitle);
      
      if (success) {
        console.log(`[useUserImageSettings] 上下文行数已更新为: ${contextLines}`);
      }
      
      return success;

    } catch (err) {
      console.error('[useUserImageSettings] 设置上下文行数失败:', err);
      error.value = err.message;
      return false;
    }
  }

  /**
   * 重置设置为默认值
   * @param {string} projectTitle - 项目标题（可选）
   * @param {string} chapterTitle - 章节标题（可选）
   * @returns {Promise<boolean>} 重置是否成功
   */
  async function resetToDefaults(projectTitle = null, chapterTitle = null) {
    try {
      const success = await saveUserImageSettings({ ...defaultSettings }, projectTitle, chapterTitle);
      
      if (success) {
        console.log('[useUserImageSettings] 设置已重置为默认值');
      }
      
      return success;

    } catch (err) {
      console.error('[useUserImageSettings] 重置设置失败:', err);
      error.value = err.message;
      return false;
    }
  }

  return {
    // 状态
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    defaultSettings,

    // 方法
    loadUserImageSettings,
    saveUserImageSettings,
    getContextLines,
    setContextLines,
    resetToDefaults,
    getProjectInfo,
    getSettingsFilePath
  };
}
