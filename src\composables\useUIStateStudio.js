/**
 * UI状态管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的UI状态管理相关功能
 * 
 * 功能：
 * - 队列面板状态管理
 * - 模态框状态管理
 * - 加载状态管理
 * - 选择状态管理
 * - UI交互状态管理
 */

import { reactive } from 'vue';

export function useUIStateStudio() {
  // UI状态管理
  const uiState = reactive({
    // 队列面板状态
    isQueuePanelCollapsed: false,
    phantomStateCheckInterval: null,
    
    // 模态框状态
    showImagePromptDrawer: false,
    showGlobalReasoningDrawer: false,
    showImagePromptTestModal: false,
    
    // 加载状态
    isLoading: true,
    loadingMessage: '正在加载SRT数据，请稍候...',
    
    // 选择状态
    selectedRowCount: 0,
    
    // 批量更新控制
    isBatchUpdating: false,
    pendingSaveOperations: new Set(),
    
    // 图像提示词相关状态
    currentPromptData: null,
    fullPromptPreview: '',
    isImagePromptLoading: false,
    globalReasoningText: ''
  });

  /**
   * 切换队列面板折叠状态
   */
  const toggleQueuePanel = () => {
    uiState.isQueuePanelCollapsed = !uiState.isQueuePanelCollapsed;
    console.log('🎛️ [UI状态] 队列面板折叠状态:', uiState.isQueuePanelCollapsed);
  };

  /**
   * 设置加载状态
   * @param {boolean} loading - 是否加载中
   * @param {string} message - 加载消息
   */
  const setLoadingState = (loading, message = '') => {
    uiState.isLoading = loading;
    if (message) {
      uiState.loadingMessage = message;
    }
    console.log('🎛️ [UI状态] 设置加载状态:', { loading, message });
  };

  /**
   * 更新选择状态
   * @param {Array} rows - 行数据
   */
  const updateSelectionState = (rows) => {
    if (!rows || !Array.isArray(rows)) {
      uiState.selectedRowCount = 0;
      return;
    }
    
    uiState.selectedRowCount = rows.filter(row => row.isSelected).length;
  };

  /**
   * 开始批量更新模式
   */
  const startBatchUpdate = () => {
    console.log('[Performance] 开始批量更新模式');
    uiState.isBatchUpdating = true;
    uiState.pendingSaveOperations.clear();
  };

  /**
   * 结束批量更新模式
   * @param {Function} saveCallback - 保存回调函数
   */
  const endBatchUpdate = (saveCallback) => {
    console.log('[Performance] 结束批量更新模式，执行统一保存');
    uiState.isBatchUpdating = false;

    // 如果有待处理的保存操作，执行一次统一保存
    if (uiState.pendingSaveOperations.size > 0) {
      console.log('[Performance] 执行批量保存，操作类型:', Array.from(uiState.pendingSaveOperations));
      uiState.pendingSaveOperations.clear();

      // 调用保存回调
      if (saveCallback && typeof saveCallback === 'function') {
        saveCallback();
      }
    }
  };

  /**
   * 打开图像提示词抽屉
   * @param {Object} context - 组件上下文
   */
  const openImagePromptDrawer = (context) => {
    console.log('🎛️ [UI状态] 打开图像提示词抽屉');
    
    if (!context.rows || context.rows.length === 0) {
      context.showErrorMessage('没有可用的行数据');
      return;
    }

    // 准备上下文文本
    uiState.globalReasoningText = context.getFormattedTextForReasoning ? context.getFormattedTextForReasoning() : '';

    // 打开抽屉
    uiState.showImagePromptDrawer = true;
  };

  /**
   * 关闭图像提示词抽屉
   */
  const closeImagePromptDrawer = () => {
    console.log('🎛️ [UI状态] 关闭图像提示词抽屉');
    uiState.showImagePromptDrawer = false;
    uiState.globalReasoningText = '';
  };

  /**
   * 打开图像提示词测试模态框
   * @param {Object} promptData - 提示词数据
   * @param {string} fullPrompt - 完整提示词
   */
  const openImagePromptTestModal = (promptData, fullPrompt) => {
    console.log('🎛️ [UI状态] 打开图像提示词测试模态框');
    uiState.currentPromptData = promptData;
    uiState.fullPromptPreview = fullPrompt;
    uiState.showImagePromptTestModal = true;
  };

  /**
   * 关闭图像提示词测试模态框
   */
  const closeImagePromptTestModal = () => {
    console.log('🎛️ [UI状态] 关闭图像提示词测试模态框');
    uiState.showImagePromptTestModal = false;
    uiState.currentPromptData = null;
    uiState.fullPromptPreview = '';
    uiState.isImagePromptLoading = false;
  };

  /**
   * 设置图像提示词加载状态
   * @param {boolean} loading - 是否加载中
   */
  const setImagePromptLoading = (loading) => {
    uiState.isImagePromptLoading = loading;
    console.log('🎛️ [UI状态] 设置图像提示词加载状态:', loading);
  };

  /**
   * 启动虚假状态监控
   */
  const startPhantomStateMonitoring = () => {
    console.log('🔧 [状态监控] 虚假状态监控已禁用（避免无限循环）');
    // 暂时禁用定时器监控
    // uiState.phantomStateCheckInterval = setInterval(() => {
    //   // 监控逻辑已禁用
    // }, 5000);
  };

  /**
   * 停止虚假状态监控
   */
  const stopPhantomStateMonitoring = () => {
    if (uiState.phantomStateCheckInterval) {
      clearInterval(uiState.phantomStateCheckInterval);
      uiState.phantomStateCheckInterval = null;
      console.log('🔧 [状态监控] 已停止虚假状态监控');
    }
  };

  /**
   * 重置所有UI状态
   */
  const resetAllUIStates = () => {
    console.log('🎛️ [UI状态] 重置所有UI状态');
    
    // 重置模态框状态
    uiState.showImagePromptDrawer = false;
    uiState.showGlobalReasoningDrawer = false;
    uiState.showImagePromptTestModal = false;
    
    // 重置加载状态
    uiState.isLoading = false;
    uiState.loadingMessage = '';
    
    // 重置选择状态
    uiState.selectedRowCount = 0;
    
    // 重置批量更新状态
    uiState.isBatchUpdating = false;
    uiState.pendingSaveOperations.clear();
    
    // 重置图像提示词状态
    uiState.currentPromptData = null;
    uiState.fullPromptPreview = '';
    uiState.isImagePromptLoading = false;
    uiState.globalReasoningText = '';
    
    // 停止监控
    stopPhantomStateMonitoring();
  };

  /**
   * 获取当前UI状态摘要
   */
  const getUIStateSummary = () => {
    return {
      isLoading: uiState.isLoading,
      selectedRowCount: uiState.selectedRowCount,
      hasOpenModals: uiState.showImagePromptDrawer || uiState.showGlobalReasoningDrawer || uiState.showImagePromptTestModal,
      isBatchUpdating: uiState.isBatchUpdating,
      isQueuePanelCollapsed: uiState.isQueuePanelCollapsed
    };
  };

  return {
    // 响应式状态
    uiState,
    
    // 队列面板管理
    toggleQueuePanel,
    
    // 加载状态管理
    setLoadingState,
    
    // 选择状态管理
    updateSelectionState,
    
    // 批量更新管理
    startBatchUpdate,
    endBatchUpdate,
    
    // 模态框管理
    openImagePromptDrawer,
    closeImagePromptDrawer,
    openImagePromptTestModal,
    closeImagePromptTestModal,
    setImagePromptLoading,
    
    // 状态监控
    startPhantomStateMonitoring,
    stopPhantomStateMonitoring,
    
    // 状态重置和工具
    resetAllUIStates,
    getUIStateSummary
  };
}
