🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:36.199Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:36.199Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:36.200Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:36.200Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:36.200Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:36.200Z'}
🧹 [数据清理] 移除了8824字节的临时状态数据
[数据持久化] 保存project_data 完成: {operation: '保存project_data', duration: '344ms', timestamp: '2025-07-04T04:35:36.652Z', saveCount: 9, success: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:37.133Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:37.133Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:37.133Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:37.133Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:37.134Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:37.134Z'}
🧹 [数据清理] 移除了8823字节的临时状态数据
[数据持久化] 保存project_data 完成: {operation: '保存project_data', duration: '28ms', timestamp: '2025-07-04T04:35:37.267Z', saveCount: 10, success: true, …}
🔥 [SecondaryNavbar] 按钮被点击: generate-images
🔥 [SecondaryNavbar] 发送action事件: generate-images
🎯 [ContentCreationStudio] 接收到导航操作: generate-images
🎯 [ContentCreationStudio] 调用 projectContextStudio.handleNavAction
🚀 [useProjectContextStudio] handleNavAction 被调用
🚀 [useProjectContextStudio] 导航操作: generate-images
🚀 [useProjectContextStudio] context.batchOperationUI 存在: true
🎨 [导航] 批量生图按钮被点击
🎨 [ContentCreationStudio] 执行批量生图功能
🔍 [调试] performBatchImageGeneration 执行前状态: {batchState: RefImpl, buttonText: '2. 批量生图'}
🎨 [批量生图] 开始批量生图操作: {selectedCount: 2, indices: Array(2)}
🔍 [调试] isBatchGenerating 计算: {isActive: false, batchState: Proxy(Object)}
🔍 [调试] 批量生图前状态检查: {currentBatchState: Proxy(Object), isBatchGenerating: false, selectedRowIndices: Array(2)}
🎨 [调试] 执行开始批量生图
🎨 [批量生图] 启动批量生图任务: {selectedCount: 2, indices: Array(2)}
🔍 [调试] 设置批量状态前: {oldState: {…}}
🔍 [调试] isBatchGenerating 计算: {isActive: true, batchState: Proxy(Object)}
🔍 [调试] 设置批量状态后: {newState: {…}, isBatchGenerating: true}
🎨 [批量生图] 启动行0的生图任务
🎨 [批量生图] 为行生成图像: 0
🎨 [图像生成] 请求为行生成图像: {rowIndex: 0, promptLength: 547, projectTitle: '1', chapterTitle: '法力无边'}
🔍 [DEBUG] generateImageForRow调用时的状态: {isGenerating: false, currentTask: null, taskQueueLength: 0, timestamp: '2025-07-04T04:35:39.007Z'}
🔍 [队列检查] 当前状态: {前端isGenerating: false, 后端isGenerating: false, currentTask: undefined, hasActiveTask: null, 状态一致: true, …}
🔍 [队列决策] 是否需要排队: {shouldQueue: false, frontendIsGenerating: false, backendIsGenerating: false, hasActiveTask: null, hasQueuedTasks: false, …}
🔧 [并发保护] 立即设置isGenerating状态，防止并发任务
[ImageState] setIsGenerating: false -> true (generateImageForRow-开始执行)
🎨 [图像生成] 直接执行图像生成
🎨 [图像执行] 开始执行图像生成: {rowIndex: 0, promptLength: 547, projectTitle: '1', chapterTitle: '法力无边'}
🔢 [图像执行] 应用层面批量生成: 1 次API调用 (来自用户配置)
🎨 [图像执行] 第 1/1 次API调用
🎨 [ComfyUIClient] 开始生成图像...
🔧 [ComfyUIClient] 队列任务，允许强制执行
📝 [ComfyUIClient] 设置正向提示词到节点 78: One male, 24 years old, long black hair, dark gray Chinese cloths,, Medium Shot from an Eye-level Sh...
📝 [ComfyUIClient] 设置负向提示词到节点 79: Bad quality,worst quality,normal quality,low-res,sketch,poor design,deformed,disfigured,soft,bad com...
🔧 [ComfyUIClient] 应用设置到工作流: {nodeMapping: {…}, defaultSettings: {…}, batchSize: 1}
📐 [ComfyUIClient] 设置宽度: 1280
📐 [ComfyUIClient] 设置高度: 720
📊 [ComfyUIClient] 应用层面批量生成: 1次API调用
⚙️ [ComfyUIClient] 设置步数: 35
⚙️ [ComfyUIClient] 设置CFG: 7
⚙️ [ComfyUIClient] 设置采样器: euler
🎲 [ComfyUIClient] 设置种子: 352853
🔧 [ComfyUIClient] 工作流构建完成
🔌 [ComfyUIClient] 开始连接...
🔌 [WebSocketManager] 已经连接，跳过连接
🎨 [批量生图] 启动行1的生图任务
🎨 [批量生图] 为行生成图像: 1
🎨 [图像生成] 请求为行生成图像: {rowIndex: 1, promptLength: 716, projectTitle: '1', chapterTitle: '法力无边'}
🔍 [DEBUG] generateImageForRow调用时的状态: {isGenerating: true, currentTask: null, taskQueueLength: 0, timestamp: '2025-07-04T04:35:39.010Z'}
🔍 [队列检查] 当前状态: {前端isGenerating: true, 后端isGenerating: false, currentTask: undefined, hasActiveTask: null, 状态一致: false, …}
🔍 [队列决策] 是否需要排队: {shouldQueue: true, frontendIsGenerating: true, backendIsGenerating: false, hasActiveTask: null, hasQueuedTasks: false, …}
🔧 [队列决策] 检测到需要排队，立即设置临时状态
🔧 [批量生图] 进度更新: {rowIndex: 1, stage: 'queuing', progress: 0, isQueued: true, isProcessing: false, …}
🔧 [批量生图] 设置排队状态: {rowIndex: 1, taskId: 'temp_1751603739010', queuePosition: 1, message: '正在加入队列...', beforeState: {…}}
🔧 [批量生图] 排队状态已设置: {rowIndex: 1, afterState: {…}}
📋 [队列管理] 有任务正在执行，将新任务加入队列
[ImageQueue] 添加任务: task_1751603739016_ldjutkp (第2行)，队列长度: 1
🔧 [队列通知] 通知UI任务已进入队列: {taskId: 'task_1751603739016_ldjutkp', queuePosition: 1, rowIndex: 1}
🔧 [批量生图] 进度更新: {rowIndex: 1, stage: 'queued', progress: 0, isQueued: true, isProcessing: false, …}
🔧 [批量生图] 设置排队状态: {rowIndex: 1, taskId: 'task_1751603739016_ldjutkp', queuePosition: 1, message: '排队中 (1)', beforeState: {…}}
🔧 [批量生图] 排队状态已设置: {rowIndex: 1, afterState: {…}}
🔧 [队列触发] 新任务已加入队列，触发队列处理检查
📊 [批量生图] 批量状态变化: {from: false, to: true, buttonState: 'generating'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.018Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.018Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.019Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.019Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.019Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.019Z'}
🔧 [批量生图] 进度更新: {rowIndex: 1, stage: 'queuing', progress: 0, isQueued: true, isProcessing: false, …}
🔧 [批量生图] 设置排队状态: {rowIndex: 1, taskId: 'temp_1751603739010', queuePosition: 1, message: '正在加入队列...', beforeState: {…}}
🔧 [批量生图] 排队状态已设置: {rowIndex: 1, afterState: {…}}
🔧 [批量生图] 进度更新: {rowIndex: 1, stage: 'queued', progress: 0, isQueued: true, isProcessing: false, …}
🔧 [批量生图] 设置排队状态: {rowIndex: 1, taskId: 'task_1751603739016_ldjutkp', queuePosition: 1, message: '排队中 (1)', beforeState: {…}}
🔧 [批量生图] 排队状态已设置: {rowIndex: 1, afterState: {…}}
🎨 [批量生图] 第 2 行生成完成: {isQueued: true, taskId: 'task_1751603739016_ldjutkp', queuePosition: 1, success: true}
📤 [ComfyUIClient] 提交工作流...
📤 [HttpApiClient] 提交工作流...
📤 [HttpApiClient] 请求体: {serverUrl: 'http://localhost:8188', workflowNodeCount: 16, client_id: 'client_gg19xrab6_1751603739021'}
🌐 [HttpApiClient] 发送请求: POST /api/local/comfyui/prompt
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.021Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.021Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.022Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.022Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.022Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.022Z'}
✅ [批量生图] 行1生图任务启动成功: {isQueued: true, taskId: 'task_1751603739016_ldjutkp', queuePosition: 1, success: true}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.023Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.023Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.023Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.023Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.023Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.023Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.031Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.031Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.032Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.032Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.032Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.032Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.033Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.033Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.033Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.033Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.033Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.033Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.034Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.034Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.034Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.034Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.034Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.034Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.035Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.035Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.035Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.035Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.035Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.035Z'}
🔧 [队列处理] 检查队列触发条件: {isGenerating: true, isCancelling: false, currentTask: undefined, queueLength: 1, waitingTasksCount: 1, …}
[QueueProcessor] 队列处理条件不满足: isGenerating=true, isCancelling=false, currentTask=undefined, waitingTasks=1
🌐 [HttpApiClient] 响应状态: 200 OK
✅ [HttpApiClient] 工作流提交成功: {success: true, status: 200, headers: {…}, data: {…}}
📊 [PromptStateManager] 添加任务: eb9cdc56-1859-4381-a46a-b9f31e877736
✅ [ComfyUIClient] 工作流提交成功: eb9cdc56-1859-4381-a46a-b9f31e877736
🔧 [ComfyUIClient] 设置当前任务ID: eb9cdc56-1859-4381-a46a-b9f31e877736
📊 [ComfyUIClient] 状态更新: {status: {…}}
📊 [ComfyUIClient] 状态更新: {status: {…}}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 3, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.897Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.897Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.898Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.898Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.898Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.898Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.908Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:39.908Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.908Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:39.908Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:39.908Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:39.908Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 6, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.399Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:40.399Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.400Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:40.400Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.400Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:40.400Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.409Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:40.409Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.409Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:40.409Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.410Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:40.410Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 9, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.898Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:40.898Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.899Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:40.899Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.899Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:40.899Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.910Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:40.910Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.910Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:40.910Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:40.911Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:40.911Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 11, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.413Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:41.413Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.413Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:41.413Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.414Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:41.414Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.423Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:41.423Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.424Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:41.424Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.424Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:41.424Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 14, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.910Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:41.910Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.910Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:41.910Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.910Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:41.910Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.924Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:41.924Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.924Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:41.924Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:41.925Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:41.925Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 17, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.413Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:42.413Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.414Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:42.414Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.414Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:42.414Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.423Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:42.423Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.424Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:42.424Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.424Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:42.424Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 20, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.928Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:42.928Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.928Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:42.928Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.928Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:42.928Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.939Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:42.939Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.940Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:42.940Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:42.940Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:42.940Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 23, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.423Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:43.423Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.423Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:43.423Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.423Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:43.423Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.433Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:43.433Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.434Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:43.434Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.434Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:43.434Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 26, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.923Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:43.923Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.923Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:43.923Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.923Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:43.923Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.933Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:43.933Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.933Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:43.933Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:43.934Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:43.934Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 29, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.440Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:44.440Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.440Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:44.440Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.440Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:44.440Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.456Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:44.456Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.456Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:44.456Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.456Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:44.456Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 31, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.939Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:44.939Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.939Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:44.939Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.939Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:44.939Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.949Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:44.949Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.950Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:44.950Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:44.950Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:44.950Z'}
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 34, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.440Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.440Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.440Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.440Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.440Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.440Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.454Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.454Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.455Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.455Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.455Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.455Z'}
🔥 [SecondaryNavbar] 按钮被点击: generate-images
🔥 [SecondaryNavbar] 发送action事件: generate-images
🎯 [ContentCreationStudio] 接收到导航操作: generate-images
🎯 [ContentCreationStudio] 调用 projectContextStudio.handleNavAction
🚀 [useProjectContextStudio] handleNavAction 被调用
🚀 [useProjectContextStudio] 导航操作: generate-images
🚀 [useProjectContextStudio] context.batchOperationUI 存在: true
🎨 [导航] 批量生图按钮被点击
🎨 [ContentCreationStudio] 执行批量生图功能
🔍 [调试] performBatchImageGeneration 执行前状态: {batchState: RefImpl, buttonText: '2. 批量生图'}
🎨 [批量生图] 开始批量生图操作: {selectedCount: 2, indices: Array(2)}
🔍 [调试] 批量生图前状态检查: {currentBatchState: Proxy(Object), isBatchGenerating: true, selectedRowIndices: Array(2)}
🚫 [调试] 执行取消批量生图
🚫 [批量生图] 开始取消批量生图任务
🚫 [批量生图] 取消任务详情: {totalSelectedCount: 2}
🚫 [批量生图] 取消行0的生成任务
🚫 [图像生成] 取消生成任务，行索引: 0
🔧 [取消状态] 设置取消标志并立即重置行状态
🎨 [图像生成] 开始取消生成...
[ImageState] setIsGenerating: true -> false (取消生成-立即重置)
🔧 [取消状态] 当前任务信息: {hasCurrentTask: false, taskId: undefined, queueLength: 1}
🎨 [图像执行] 开始取消生成...
🛑 [ComfyUIClient] 开始取消生成任务: null
🛑 [ComfyUIClient] 发送interrupt命令到: http://localhost:8188
🛑 [HttpApiClient] 发送中断命令到: http://localhost:8188
🛑 [HttpApiClient] 请求体: {serverUrl: 'http://localhost:8188'}
🚫 [批量生图] 取消行1的排队任务
🚫 [批量操作管理] 取消第 2 行的排队任务
🚫 [图像生成] 取消生成任务，行索引: 1
🔧 [取消状态] 设置取消标志并立即重置行状态
🔧 [队列取消] 取消排队任务: {rowIndex: 1, taskId: 'task_1751603739016_ldjutkp', queuePosition: 1}
📋 [队列管理] 取消排队任务: {taskId: 'task_1751603739016_ldjutkp', rowIndex: 1}
[ImageQueue] removeFromQueue: Task task_1751603739016_ldjutkp (Row 1, Status: waiting) removed. Queue length: 0
📋 [队列管理] 任务已移除，当前队列长度: 0
🔧 [队列同步] 强制更新队列统计: Proxy(Object) {total: 0, waiting: 0, processing: 0, completed: 0, cancelled: 0}
🔧 [状态管理] 行2 排队任务取消，自动取消选中状态
👁️ [批量生图] 检测到单行操作影响: {rowIndex: 1, operation: 'cancel_queue', isBatchActive: true, selectedIndices: 2}
🚫 [批量生图] 行1被单独取消 (cancel_queue)
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.553Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.553Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.554Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.554Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.554Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.554Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.555Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.555Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.556Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.556Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.556Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.556Z'}
🔄 [批量操作管理] 按钮状态已同步: {旧状态: false, 新状态: false, currentOperation: null, UI状态详情: {…}}
🔍 [批量生图] 实际状态检查: {totalTasks: 2, activeCount: 0, completedCount: 2, cancelledOrFailedCount: 0, finishedCount: 2, …}
🎉 [批量生图] 所有任务完成，准备重置状态 {isAllFinished: true, hasNoActiveTasks: true, activeCount: 0, finishedCount: 2, totalTasks: 2}
✅ [批量生图] 行1取消操作完成
🧹 [批量生图] 执行状态重置和清理
🔄 [批量生图] 重置批量生图状态
✅ [批量生图] 状态重置和清理完成
📊 [批量生图] 批量状态变化: {from: true, to: false, buttonState: 'idle'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.560Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.560Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.560Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.560Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.561Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.561Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.561Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.561Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.561Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.561Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.562Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.562Z'}
🛑 [HttpApiClient] 响应状态: 200 OK
✅ [HttpApiClient] 中断命令发送成功: {success: true, status: 200, headers: {…}, data: ''}
✅ [ComfyUIClient] 成功发送取消命令到ComfyUI
🔧 [ComfyUIClient] 状态已清理，取消完成
✅ [图像执行] 取消生成完成: {success: true, interruptSent: true, queueCleared: true, verificationPassed: true, errors: Array(0)}
[ImageState] resetGenerationState: Generation state has been reset.
✅ [图像生成] 取消生成完成: {success: true, interruptSent: true, queueCleared: true, verificationPassed: true, errors: Array(0)}
✅ [图像生成] 任务取消成功
✅ [批量生图] 行0取消操作完成
✅ [批量生图] 批量取消完成
🔄 [批量生图] 重置批量生图状态
🔍 [调试] performBatchImageGeneration 执行后状态: {batchState: RefImpl, buttonText: '2. 批量生图'}
🔧 [ContentCreationStudio] 强制更新按钮状态
🔍 [调试] nextTick后状态: {batchState: RefImpl, buttonText: '2. 批量生图'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.573Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:45.573Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.573Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:45.573Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:45.573Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:45.573Z'}
🔧 [队列恢复] 取消完成，检查是否有其他任务需要处理
🔧 [队列处理] 检查队列触发条件: {isGenerating: false, isCancelling: false, currentTask: undefined, queueLength: 0, waitingTasksCount: 0, …}
[QueueProcessor] 队列处理条件不满足: isGenerating=false, isCancelling=false, currentTask=undefined, waitingTasks=0
⚠️ [WebSocketManager] 执行中断: eb9cdc56-1859-4381-a46a-b9f31e877736
📨 [MessageParser] 处理关键消息: execution_interrupted
🛑 [ComfyUIClient] 执行中断: eb9cdc56-1859-4381-a46a-b9f31e877736
📊 [PromptStateManager] 状态更新: eb9cdc56-1859-4381-a46a-b9f31e877736 queued -> interrupted
🔧 [ComfyUIClient] 触发中断进度回调: eb9cdc56-1859-4381-a46a-b9f31e877736
🔧 [批量生图] 进度更新: {rowIndex: 0, stage: 'generating', progress: 0, isQueued: undefined, isProcessing: true, …}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.247Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:46.247Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.247Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:46.247Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.248Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:46.248Z'}
📊 [ComfyUIClient] 状态更新: {status: {…}}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.258Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:46.258Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.258Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:46.258Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.258Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:46.258Z'}
🔧 [延迟修复] 检测到状态未正确重置，强制重置
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'smart-reasoning', actionButtonText: '1. 批量推理', actionName: '1. 批量推理', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.573Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'smart-reasoning', result: '1. 批量推理', timestamp: '2025-07-04T04:35:46.573Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'generate-images', actionButtonText: '2. 批量生图', actionName: '2. 批量生图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.574Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'generate-images', result: '2. 批量生图', timestamp: '2025-07-04T04:35:46.574Z'}
🔍 [SecondaryNavbar] getActionButtonText被调用: {actionId: 'enlarge-images', actionButtonText: undefined, actionName: '3. 放大配图', propValue: '2. 批量生图', timestamp: '2025-07-04T04:35:46.574Z'}
🔍 [SecondaryNavbar] getActionButtonText返回: {actionId: 'enlarge-images', result: '3. 放大配图', timestamp: '2025-07-04T04:35:46.574Z'}
