<template>
  <div
    v-if="visible"
    class="modal-overlay"
    @click="handleOverlayClick"
  >
    <div
      class="modal-container"
      @click.stop
    >
      <!-- 模态窗口头部 -->
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="ri-image-2-line" />
          图片管理 - 第{{ rowIndex + 1 }}行
        </h3>
        <button
          class="close-button"
          @click="closeModal"
          title="关闭"
        >
          <i class="ri-close-line" />
        </button>
      </div>

      <!-- 模态窗口内容 -->
      <div
        class="modal-content"
        :class="{ 'drag-over': isDragOver && !isLocked }"
        @drop.prevent="handleDrop"
        @dragover.prevent="handleDragOver"
        @dragleave="handleDragLeave"
        @dragenter.prevent="handleDragEnter"
      >
        <!-- 锁定状态提示 -->
        <div
          v-if="isLocked"
          class="locked-notice"
        >
          <i class="ri-lock-line" />
          <span>该行已锁定，仅可预览图片，无法进行编辑操作</span>
        </div>

        <!-- 拖拽提示覆盖层 -->
        <div
          v-if="isDragOver && !isLocked"
          class="drag-overlay"
        >
          <div class="drag-overlay-content">
            <i class="ri-upload-cloud-2-line" />
            <p class="drag-text">释放以上传图片</p>
            <p class="drag-hint">拖拽到主图位置替换主图，拖拽到可选图位置添加图片</p>
          </div>
        </div>

        <!-- 主图区域 -->
        <div class="main-image-section">
          <h4 class="section-title">主图</h4>
          <div
            class="main-image-container drop-zone"
            :class="{
              'drag-over': isDragOverMainImage && !isLocked,
              'locked': isLocked
            }"
            @drop.prevent.stop="handleDropOnMainImage"
            @dragover.prevent.stop="handleDragOverMainImage"
            @dragleave.stop="handleDragLeaveMainImage"
            @dragenter.prevent.stop="handleDragEnterMainImage"
          >
            <div
              v-if="mainImage && mainImage.src"
              class="image-item main-image"
              @click="previewImage(mainImage)"
            >
              <img
                :src="mainImage.src"
                :alt="mainImage.alt || '主图'"
                class="image-preview"
              >
              <div class="image-overlay">
                <button
                  v-if="!isLocked"
                  class="delete-button"
                  @click.stop="deleteMainImage"
                  title="删除主图"
                >
                  <i class="ri-delete-bin-line" />
                </button>
                <button
                  class="preview-button"
                  @click.stop="previewImage(mainImage)"
                  title="预览图片"
                >
                  <i class="ri-eye-line" />
                </button>
              </div>
            </div>
            <div
              v-else
              class="empty-slot main-image"
            >
              <i class="ri-image-add-line" />
              <span>{{ isLocked ? '暂无主图' : '拖拽图片到此处设为主图' }}</span>
            </div>
            <!-- 主图拖拽提示 -->
            <div
              v-if="isDragOverMainImage && !isLocked"
              class="drop-hint"
            >
              <i class="ri-image-line" />
              <span>设为主图</span>
            </div>
          </div>
        </div>

        <!-- 可选图区域 -->
        <div class="optional-images-section">
          <h4 class="section-title">可选图 ({{ optionalImages.length }})</h4>
          <div class="optional-images-scroll-container">
            <div class="optional-images-grid">
              <div
                v-for="(image, index) in optionalImages"
                :key="index"
                class="image-item optional-image drop-zone"
                :class="{
                  'empty': !image || !image.src,
                  'drag-over': isDragOverOptionalImage === index && !isLocked,
                  'locked': isLocked
                }"
                @click="image && image.src ? previewImage(image) : null"
                @drop.prevent.stop="handleDropOnOptionalImage($event, index)"
                @dragover.prevent.stop="handleDragOverOptionalImage"
                @dragleave.stop="handleDragLeaveOptionalImage"
                @dragenter.prevent.stop="handleDragEnterOptionalImage($event, index)"
              >
                <img
                  v-if="image && image.src"
                  :src="image.src"
                  :alt="image.alt || `可选图${index + 1}`"
                  class="image-preview"
                >
                <div
                  v-else
                  class="empty-slot"
                >
                  <i class="ri-image-add-line" />
                  <span>{{ isLocked ? `位置${index + 1}` : `拖拽图片到位置${index + 1}` }}</span>
                </div>
                <div
                  v-if="image && image.src"
                  class="image-overlay"
                >
                  <button
                    v-if="!isLocked"
                    class="delete-button"
                    @click.stop="deleteOptionalImage(index)"
                    title="删除图片"
                  >
                    <i class="ri-delete-bin-line" />
                  </button>
                  <button
                    class="preview-button"
                    @click.stop="previewImage(image)"
                    title="预览图片"
                  >
                    <i class="ri-eye-line" />
                  </button>
                </div>
                <div class="image-index">{{ index + 1 }}</div>
                <!-- 可选图拖拽提示 -->
                <div
                  v-if="isDragOverOptionalImage === index && !isLocked"
                  class="drop-hint"
                >
                  <i class="ri-image-line" />
                  <span>添加到位置{{ index + 1 }}</span>
                </div>
              </div>
              <!-- 添加新位置按钮（当图片少于最大数量时） -->
              <div
                v-if="optionalImages.length < maxOptionalImages && !isLocked"
                class="image-item optional-image drop-zone add-new"
                :class="{ 'drag-over': isDragOverOptionalImage === optionalImages.length }"
                @drop.prevent.stop="handleDropOnOptionalImage($event, optionalImages.length)"
                @dragover.prevent.stop="handleDragOverOptionalImage"
                @dragleave.stop="handleDragLeaveOptionalImage"
                @dragenter.prevent.stop="handleDragEnterOptionalImage($event, optionalImages.length)"
              >
                <div class="empty-slot">
                  <i class="ri-add-line" />
                  <span>拖拽添加新图片</span>
                </div>
                <!-- 新位置拖拽提示 -->
                <div
                  v-if="isDragOverOptionalImage === optionalImages.length"
                  class="drop-hint"
                >
                  <i class="ri-image-line" />
                  <span>添加新图片</span>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- 模态窗口底部 -->
      <div class="modal-footer">
        <button
          class="footer-button secondary"
          @click="closeModal"
        >
          关闭
        </button>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <ImagePreviewModal
      :visible="showPreviewModal"
      :image-src="previewImageSrc"
      :image-alt="previewImageAlt"
      @close="closePreview"
    />

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      :visible="showDeleteConfirm"
      :message="deleteMessage"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script>
import ImagePreviewModal from './ImagePreviewModal.vue';
import DeleteConfirmDialog from './DeleteConfirmDialog.vue';

export default {
  name: 'ImageManagementModal',
  components: {
    ImagePreviewModal,
    DeleteConfirmDialog
  },
  emits: {
    'close': null,
    'delete-main-image': null,
    'delete-optional-image': (index) => typeof index === 'number',
    'upload-images': (files) => Array.isArray(files),
    'upload-error': (message) => typeof message === 'string',
    'replace-main-image': (file) => file instanceof File,
    'replace-optional-image': (data) => typeof data === 'object' && data.file instanceof File && typeof data.index === 'number'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowIndex: {
      type: Number,
      required: true
    },
    mainImage: {
      type: Object,
      default: () => null
    },
    optionalImages: {
      type: Array,
      default: () => []
    },
    isLocked: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDragOver: false,
      dragCounter: 0, // 用于跟踪拖拽进入/离开事件
      isDragOverMainImage: false,
      isDragOverOptionalImage: -1, // -1表示没有拖拽，>=0表示拖拽到对应索引位置
      mainImageDragCounter: 0,
      optionalImageDragCounters: {},
      maxOptionalImages: 20, // 最大可选图数量
      showPreviewModal: false,
      previewImageSrc: '',
      previewImageAlt: '',
      showDeleteConfirm: false,
      deleteType: '', // 'main' or 'optional'
      deleteIndex: -1,
      deleteMessage: ''
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 窗口打开时重置状态
        this.resetState();
      }
    }
  },
  computed: {
    // 移除displayOptionalImages，直接使用optionalImages
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    handleOverlayClick() {
      this.closeModal();
    },
    previewImage(image) {
      if (image && image.src) {
        this.previewImageSrc = image.src;
        this.previewImageAlt = image.alt || '图片预览';
        this.showPreviewModal = true;
      }
    },
    closePreview() {
      this.showPreviewModal = false;
      this.previewImageSrc = '';
      this.previewImageAlt = '';
    },
    deleteMainImage() {
      this.deleteType = 'main';
      this.deleteMessage = '确定要删除主图吗？';
      this.showDeleteConfirm = true;
    },
    deleteOptionalImage(index) {
      this.deleteType = 'optional';
      this.deleteIndex = index;
      this.deleteMessage = `确定要删除第${index + 1}张可选图吗？`;
      this.showDeleteConfirm = true;
    },
    confirmDelete() {
      this.showDeleteConfirm = false;
      if (this.deleteType === 'main') {
        this.$emit('delete-main-image');
      } else if (this.deleteType === 'optional') {
        this.$emit('delete-optional-image', this.deleteIndex);
      }
      this.resetDeleteState();
    },
    cancelDelete() {
      this.showDeleteConfirm = false;
      this.resetDeleteState();
    },
    resetDeleteState() {
      this.deleteType = '';
      this.deleteIndex = -1;
      this.deleteMessage = '';
    },
    // 全局拖拽事件处理
    handleDragEnter(event) {
      event.preventDefault();
      this.dragCounter++;
      this.isDragOver = true;
      console.log('🎨 [ImageManagementModal] 全局拖拽进入，计数器:', this.dragCounter);
    },
    handleDragOver(event) {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'copy';
    },
    handleDragLeave(event) {
      event.preventDefault();
      this.dragCounter--;
      if (this.dragCounter === 0) {
        this.isDragOver = false;
      }
      console.log('🎨 [ImageManagementModal] 全局拖拽离开，计数器:', this.dragCounter);
    },

    // 主图拖拽事件处理
    handleDragEnterMainImage(event) {
      event.preventDefault();
      this.mainImageDragCounter++;
      this.isDragOverMainImage = true;
      console.log('🎨 [ImageManagementModal] 主图拖拽进入');
    },
    handleDragOverMainImage(event) {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'copy';
    },
    handleDragLeaveMainImage(event) {
      event.preventDefault();
      this.mainImageDragCounter--;
      if (this.mainImageDragCounter === 0) {
        this.isDragOverMainImage = false;
      }
      console.log('🎨 [ImageManagementModal] 主图拖拽离开');
    },
    handleDropOnMainImage(event) {
      event.preventDefault();
      this.isDragOverMainImage = false;
      this.mainImageDragCounter = 0;

      console.log('🎨 [ImageManagementModal] 主图位置拖拽放下');
      this.processDroppedFiles(event, 'main');
    },

    // 可选图拖拽事件处理
    handleDragEnterOptionalImage(event, index) {
      event.preventDefault();
      if (!this.optionalImageDragCounters[index]) {
        this.optionalImageDragCounters[index] = 0;
      }
      this.optionalImageDragCounters[index]++;
      this.isDragOverOptionalImage = index;
      console.log('🎨 [ImageManagementModal] 可选图拖拽进入位置:', index);
    },
    handleDragOverOptionalImage(event) {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'copy';
    },
    handleDragLeaveOptionalImage(event) {
      event.preventDefault();
      const index = this.isDragOverOptionalImage;
      if (index >= 0 && this.optionalImageDragCounters[index]) {
        this.optionalImageDragCounters[index]--;
        if (this.optionalImageDragCounters[index] === 0) {
          this.isDragOverOptionalImage = -1;
        }
      }
      console.log('🎨 [ImageManagementModal] 可选图拖拽离开位置:', index);
    },
    handleDropOnOptionalImage(event, index) {
      event.preventDefault();
      this.isDragOverOptionalImage = -1;
      this.optionalImageDragCounters[index] = 0;

      console.log('🎨 [ImageManagementModal] 可选图位置拖拽放下，位置:', index);
      this.processDroppedFiles(event, 'optional', index);
    },
    handleDrop(event) {
      event.preventDefault();
      this.isDragOver = false;
      this.dragCounter = 0;

      console.log('🎨 [ImageManagementModal] 全局拖拽放下事件触发');
      this.processDroppedFiles(event, 'general');
    },

    // 统一的文件处理方法
    processDroppedFiles(event, dropType, targetIndex = -1) {
      const files = Array.from(event.dataTransfer.files);
      console.log('🎨 [ImageManagementModal] 处理拖拽文件:', { files, dropType, targetIndex });

      // 更严格的图片文件过滤
      const imageFiles = files.filter(file => {
        const isImage = file.type.startsWith('image/');
        const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const isSupported = supportedTypes.includes(file.type.toLowerCase());
        const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB限制

        console.log('🎨 [ImageManagementModal] 文件检查:', {
          name: file.name,
          type: file.type,
          size: file.size,
          isImage,
          isSupported,
          isValidSize
        });

        return isImage && isSupported && isValidSize;
      });

      console.log('🎨 [ImageManagementModal] 过滤后的图片文件:', imageFiles);

      if (imageFiles.length > 0) {
        // 根据拖拽类型发射不同的事件
        if (dropType === 'main') {
          console.log('🎨 [ImageManagementModal] 发射replace-main-image事件');
          this.$emit('replace-main-image', imageFiles[0]); // 主图只取第一个文件
        } else if (dropType === 'optional') {
          console.log('🎨 [ImageManagementModal] 发射replace-optional-image事件，位置:', targetIndex);
          this.$emit('replace-optional-image', { file: imageFiles[0], index: targetIndex });
        } else {
          console.log('🎨 [ImageManagementModal] 发射upload-images事件，文件数量:', imageFiles.length);
          this.$emit('upload-images', imageFiles);
        }
      } else if (files.length > 0) {
        // 如果有文件但都不是有效图片，显示错误提示
        console.warn('🎨 [ImageManagementModal] 没有有效的图片文件');
        this.$emit('upload-error', '请拖拽有效的图片文件（JPG、PNG、GIF、WebP，最大10MB）');
      }
    },
    resetState() {
      this.isDragOver = false;
      this.dragCounter = 0;
      this.isDragOverMainImage = false;
      this.isDragOverOptionalImage = -1;
      this.mainImageDragCounter = 0;
      this.optionalImageDragCounters = {};
      this.showPreviewModal = false;
      this.previewImageSrc = '';
      this.previewImageAlt = '';
      this.showDeleteConfirm = false;
      this.deleteType = '';
      this.deleteIndex = -1;
      this.deleteMessage = '';
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background-color: #1e1e1e;
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  border: 1px solid #333;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #333;
  background-color: #252525;
  border-radius: 8px 8px 0 0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #e0e0e0;
  font-size: 18px;
  font-weight: 600;
}

.modal-title i {
  color: #f59e0b;
  font-size: 20px;
}

.close-button {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button:hover {
  background-color: #333;
  color: #e0e0e0;
}

.close-button i {
  font-size: 18px;
}

.modal-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  transition: all 0.3s ease;
}

.modal-content.drag-over {
  background-color: rgba(62, 143, 176, 0.05);
  border: 2px dashed #3e8fb0;
  border-radius: 8px;
}

/* 拖拽覆盖层 */
.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(62, 143, 176, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  border-radius: 8px;
  backdrop-filter: blur(2px);
}

.drag-overlay-content {
  text-align: center;
  color: #3e8fb0;
  padding: 40px;
  border: 2px dashed #3e8fb0;
  border-radius: 12px;
  background-color: rgba(30, 30, 30, 0.9);
}

.drag-overlay-content i {
  font-size: 64px;
  margin-bottom: 16px;
  display: block;
}

.drag-text {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.drag-hint {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

.locked-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: rgba(220, 38, 38, 0.1);
  border: 1px solid #dc2626;
  border-radius: 6px;
  color: #fca5a5;
  font-size: 14px;
}

.locked-notice i {
  color: #dc2626;
  font-size: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-image-section {
  display: flex;
  flex-direction: column;
}

.main-image-container {
  display: flex;
  justify-content: flex-start;
}

.optional-images-section {
  display: flex;
  flex-direction: column;
}

.optional-images-scroll-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
}

/* 自定义滚动条样式 */
.optional-images-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.optional-images-scroll-container::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.optional-images-scroll-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.optional-images-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #666;
}

.optional-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding-bottom: 16px;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  background-color: #111;
  border: 2px dashed #333;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 拖拽区域样式 */
.drop-zone {
  position: relative;
}

.drop-zone.drag-over {
  border-color: #3e8fb0;
  border-style: solid;
  background-color: rgba(62, 143, 176, 0.1);
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(62, 143, 176, 0.3);
}

.drop-zone.locked {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 拖拽提示 */
.drop-hint {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(62, 143, 176, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  z-index: 10;
  border-radius: 6px;
}

.drop-hint i {
  font-size: 32px;
  margin-bottom: 8px;
}

.drop-hint span {
  font-size: 14px;
  text-align: center;
}

.image-item:hover {
  border-color: #3e8fb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(62, 143, 176, 0.2);
}

.image-item.main-image {
  width: 200px;
  height: 200px;
}

.image-item.add-new {
  border-style: dotted;
  border-color: #555;
  background-color: #0a0a0a;
}

.image-item.add-new:hover {
  border-color: #666;
  background-color: #1a1a1a;
}

.image-item.add-new.drag-over {
  border-color: #3e8fb0;
  border-style: solid;
  background-color: rgba(62, 143, 176, 0.1);
}

.image-item.empty {
  cursor: default;
}

.image-item.empty:hover {
  border-color: #333;
  transform: none;
  box-shadow: none;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

.empty-slot i {
  font-size: 24px;
  color: #555;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.delete-button,
.preview-button {
  background-color: rgba(0, 0, 0, 0.8);
  border: none;
  color: white;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.delete-button {
  background-color: rgba(220, 38, 38, 0.8);
}

.delete-button:hover {
  background-color: #dc2626;
  transform: scale(1.1);
}

.preview-button {
  background-color: rgba(62, 143, 176, 0.8);
}

.preview-button:hover {
  background-color: #3e8fb0;
  transform: scale(1.1);
}

.image-index {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #f59e0b;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.upload-area {
  border: 2px dashed #555;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background-color: #111;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #666;
  background-color: #1a1a1a;
}

.upload-area.drag-over {
  border-color: #3e8fb0;
  background-color: rgba(62, 143, 176, 0.15);
  border-style: solid;
  transform: scale(1.02);
  box-shadow: 0 4px 20px rgba(62, 143, 176, 0.3);
}

.upload-area.drag-over::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(62, 143, 176, 0.1) 50%, transparent 70%);
  border-radius: 6px;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-content i {
  font-size: 48px;
  color: #666;
  transition: all 0.3s ease;
}

.upload-area.drag-over .upload-content i {
  color: #3e8fb0;
  transform: scale(1.2);
}

.upload-text {
  margin: 0;
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.upload-area.drag-over .upload-text {
  color: #3e8fb0;
  font-weight: 600;
}

.upload-hint {
  margin: 0;
  color: #999;
  font-size: 14px;
  transition: all 0.3s ease;
}

.upload-area.drag-over .upload-hint {
  color: #bbb;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #333;
  background-color: #252525;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-radius: 0 0 8px 8px;
}

.footer-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.footer-button.secondary {
  background-color: #333;
  color: #e0e0e0;
}

.footer-button.secondary:hover {
  background-color: #444;
}
</style>
