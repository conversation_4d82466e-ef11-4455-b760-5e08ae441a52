<template>
  <div class="grid-cell image-cell">
    <div class="image-wrapper" :class="{ 'locked-row': isLocked }">
      <div
        class="display-window"
        @click="selectImage"
        @contextmenu.prevent="showPreview"
      >
        <div
          class="image-container"
          v-if="hasImage"
        >
          <img
            :src="imageSrc"
            :alt="imageAlt"
            class="preview-image"
          >
          <!-- 删除按钮 -->
          <ImageDeleteButton
            :show-delete="hasImage && !isLocked"
            @delete="handleDeleteImage"
          />
        </div>
        <div
          v-else
          class="placeholder-text"
        >
          点击选择图片
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      :visible="showDeleteConfirm"
      message="确定要删除这张主图吗？"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />

    <!-- 图片预览模态框 -->
    <ImagePreviewModal
      :visible="showPreviewModal"
      :image-src="imageSrc"
      :image-alt="imageAlt"
      @close="closePreview"
    />
  </div>
</template>

<script>
import ImageDeleteButton from '../common/ImageDeleteButton.vue';
import DeleteConfirmDialog from '../common/DeleteConfirmDialog.vue';
import ImagePreviewModal from '../common/ImagePreviewModal.vue';

export default {
  name: 'ImageCell',
  components: {
    ImageDeleteButton,
    DeleteConfirmDialog,
    ImagePreviewModal
  },
  emits: {
    'select-image': null,
    'delete-image': null,
    'show-locked-message': null
  },
  props: {
    imageSrc: {
      type: String,
      default: ''
    },
    imageAlt: {
      type: String,
      default: ''
    },
    isLocked: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDeleteConfirm: false,
      showPreviewModal: false
    };
  },
  computed: {
    hasImage() {
      return this.imageSrc && this.imageSrc !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    }
  },
  methods: {
    selectImage() {
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }
      this.$emit('select-image');
    },

    handleDeleteImage() {
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }
      this.showDeleteConfirm = true;
    },

    async confirmDelete() {
      this.showDeleteConfirm = false;
      this.$emit('delete-image');
    },

    cancelDelete() {
      this.showDeleteConfirm = false;
    },

    showPreview() {
      if (this.hasImage) {
        this.showPreviewModal = true;
      }
    },

    closePreview() {
      this.showPreviewModal = false;
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.image-cell {
  /* 🔧 主图列需要足够空间 */
  width: 20%;
  min-width: 150px;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  background-color: #252525;
  display: flex;
  flex-direction: column;
}

/* 锁定行的视觉效果 */
.image-wrapper.locked-row {
  border: 2px solid #dc2626;
  border-radius: 4px;
  background-color: rgba(220, 38, 38, 0.1);
  position: relative;
}

.image-wrapper.locked-row::before {
  content: '🔒';
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  background-color: #dc2626;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.display-window {
  flex: 1;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 5px;
  border: 1px dashed #333;
}

.display-window:hover {
  box-shadow: 0 0 0 2px rgba(62, 143, 176, 0.3);
}

/* 锁定状态下禁用悬停效果 */
.image-wrapper.locked-row .display-window {
  cursor: not-allowed;
}

.image-wrapper.locked-row .display-window:hover {
  box-shadow: none;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: #1a1a1a;
}

.placeholder-text {
  color: #555;
  font-size: 0.8rem;
}
</style>