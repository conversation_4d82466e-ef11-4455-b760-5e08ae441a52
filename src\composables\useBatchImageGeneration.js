/**
 * 批量图像生成功能 - 整合现有单行生图和排队系统
 * 
 * 功能特性：
 * - 批量生图按钮状态管理
 * - 批量取消功能
 * - 任务完成后状态重置
 * - 单行操作影响检测
 * - UI状态同步
 */

import { ref, computed, watch, nextTick } from 'vue';

export function useBatchImageGeneration() {
  // 🎯 核心状态管理 - 简化版本，移除内部计数器
  const batchGenerationState = ref({
    isActive: false,           // 是否正在进行批量生图
    selectedRowIndices: [],    // 参与批量生图的行索引
    startTime: null,           // 批量任务开始时间
    buttonState: 'idle'        // 按钮状态: idle, generating, cancelling
  });

  // 🎯 计算属性
  const isBatchGenerating = computed(() => {
    const isActive = batchGenerationState.value.isActive;
    console.log('🔍 [调试] isBatchGenerating 计算:', {
      isActive,
      batchState: batchGenerationState.value
    });
    return isActive;
  });
  
  // 🔧 创建进度计算函数 - 需要传入context来访问行状态
  const calculateBatchProgress = (context) => {
    const state = batchGenerationState.value;
    if (!state.isActive || !context || !context.rows || state.selectedRowIndices.length === 0) {
      return 0;
    }

    const selectedRowIndices = state.selectedRowIndices;
    const totalTasks = selectedRowIndices.length;

    let completedCount = 0;
    let activeCount = 0;

    selectedRowIndices.forEach(rowIndex => {
      const row = context.rows[rowIndex];
      if (!row) return;

      if (row.imageSrc && row.imageSrc.trim() !== '') {
        completedCount++;
      } else if (row.isGenerating || row.isQueued) {
        activeCount++;
      }
    });

    // 🔧 如果任务刚启动，没有活跃任务也没有完成任务，返回0%
    if (completedCount === 0 && activeCount === 0 && state.buttonState === 'generating') {
      console.log('🔧 [批量生图] 任务刚启动，进度为0%');
      return 0;
    }

    return Math.round((completedCount / totalTasks) * 100);
  };

  const calculateActiveBatchTaskCount = (context) => {
    const state = batchGenerationState.value;

    console.log('🔍 [调试] calculateActiveBatchTaskCount 被调用:', {
      isActive: state.isActive,
      hasContext: !!context,
      hasRows: !!(context && context.rows),
      selectedRowIndicesLength: state.selectedRowIndices?.length || 0
    });

    if (!state.isActive || !context || !context.rows) {
      console.log('🔍 [调试] calculateActiveBatchTaskCount 早期返回0');
      return 0;
    }

    const selectedRowIndices = state.selectedRowIndices;
    let activeCount = 0;
    let completedCount = 0;

    selectedRowIndices.forEach(rowIndex => {
      const row = context.rows[rowIndex];
      if (!row) return;

      if (row.isGenerating || row.isQueued) {
        activeCount++;
      } else if (row.imageSrc && row.imageSrc.trim() !== '') {
        completedCount++;
      }
    });

    console.log('🔍 [调试] calculateActiveBatchTaskCount 统计结果:', {
      activeCount,
      completedCount,
      buttonState: state.buttonState,
      selectedRowIndicesLength: selectedRowIndices.length
    });

    // 🔧 关键修复：如果没有活跃任务但也没有完成任务，
    // 说明任务刚启动，行状态还没更新，返回选中的行数作为活跃任务数
    if (activeCount === 0 && completedCount === 0 && state.buttonState === 'generating') {
      console.log('🔧 [批量生图] 任务刚启动，使用选中行数作为活跃任务数:', selectedRowIndices.length);
      return selectedRowIndices.length;
    }

    console.log('🔍 [调试] calculateActiveBatchTaskCount 最终返回:', activeCount);
    return activeCount;
  };

  // 保留简化的computed属性用于兼容性
  const batchProgress = computed(() => batchGenerationState.value.isActive ? -1 : 0);
  const activeBatchTaskCount = computed(() => batchGenerationState.value.isActive ? -1 : 0);

  const batchButtonText = computed(() => {
    const state = batchGenerationState.value;

    if (!state) return '批量生图';

    // 🔧 修复：基于实际状态而不仅仅是buttonState
    switch (state.buttonState) {
      case 'cancelling':
        return '正在取消...';
      case 'generating': {
        // 🔧 注意：这里无法直接访问context来检查活跃任务数
        // 实际的按钮文本应该由ContentCreationStudio中的accurateBatchButtonText提供
        const progress = batchProgress.value || 0;
        return `取消批量生图 (${progress}%)`;
      }
      default:
        // 🔧 默认状态：当没有活跃任务时显示初始文本
        return '批量生图';
    }
  });

  const batchButtonClass = computed(() => {
    const state = batchGenerationState.value;

    if (!state) {
      return {
        'batch-generate-btn': true,
        'generating': false,
        'cancelling': false,
        'disabled': false
      };
    }

    return {
      'batch-generate-btn': true,
      'generating': state.buttonState === 'generating',
      'cancelling': state.buttonState === 'cancelling',
      'disabled': state.buttonState === 'cancelling'
    };
  });

  // 🎯 核心功能函数

  /**
   * 启动批量生图任务
   */
  const startBatchGeneration = async (context, selectedRowIndices) => {
    console.log('🎨 [批量生图] 启动批量生图任务:', {
      selectedCount: selectedRowIndices.length,
      indices: selectedRowIndices
    });

    try {
      // 🔍 记录状态设置前的状态
      console.log('🔍 [调试] 设置批量状态前:', {
        oldState: { ...batchGenerationState.value }
      });

      // 初始化批量生图状态
      batchGenerationState.value = {
        isActive: true,
        selectedRowIndices: [...selectedRowIndices],
        startTime: Date.now(),
        buttonState: 'generating'
      };

      // 🔍 记录状态设置后的状态
      console.log('🔍 [调试] 设置批量状态后:', {
        newState: { ...batchGenerationState.value },
        isBatchGenerating: isBatchGenerating.value
      });

      // 为每个选中的行启动生图任务
      const promises = selectedRowIndices.map(async (rowIndex) => {
        return await startSingleRowGeneration(context, rowIndex);
      });

      // 等待所有任务启动（不等待完成）
      await Promise.allSettled(promises);

      console.log('✅ [批量生图] 所有任务已启动');

      // 🔧 启动状态监控
      startStatusMonitoring(context);

      // 🔧 立即触发一次状态检查，确保UI能立即更新
      nextTick(() => {
        console.log('🔧 [批量生图] 立即检查状态以更新UI');
        // 这里不调用checkBatchCompletion，因为任务刚启动不应该完成
        // 只是为了触发响应式更新
      });

    } catch (error) {
      console.error('❌ [批量生图] 启动批量生图失败:', error);
      resetBatchState();
      throw error;
    }
  };

  /**
   * 启动单个行的生图任务 - 简化版本
   */
  const startSingleRowGeneration = async (context, rowIndex) => {
    const row = context.rows[rowIndex];
    if (!row || !row.keywords?.trim()) {
      console.warn('⚠️ [批量生图] 跳过无效行:', rowIndex);
      return;
    }

    try {
      console.log(`🎨 [批量生图] 启动行${rowIndex}的生图任务`);

      // 🔧 简化：直接调用现有的单行生图功能，不维护内部状态
      // 状态跟踪完全依赖实际的行状态和单行操作影响监听
      const result = await context.imageGenerationStudio.generateImageForRow(context, rowIndex);

      console.log(`✅ [批量生图] 行${rowIndex}生图任务启动成功:`, result);

    } catch (error) {
      console.error(`❌ [批量生图] 行${rowIndex}生图启动失败:`, error);

      // 启动失败时触发状态检查
      nextTick(() => {
        checkBatchCompletion(context);
      });
    }
  };



  /**
   * 取消批量生图任务
   */
  const cancelBatchGeneration = async (context) => {
    console.log('🚫 [批量生图] 开始取消批量生图任务');

    try {
      // 设置取消状态
      batchGenerationState.value.buttonState = 'cancelling';

      // 获取所有需要取消的任务 - 基于选中的行索引
      const selectedRowIndices = batchGenerationState.value.selectedRowIndices;

      console.log('🚫 [批量生图] 取消任务详情:', {
        totalSelectedCount: selectedRowIndices.length
      });

      // 取消所有相关行的任务
      const cancelPromises = selectedRowIndices.map(async (rowIndex) => {
        try {
          const row = context.rows[rowIndex];
          
          // 取消正在生成的任务
          if (row.isGenerating) {
            console.log(`🚫 [批量生图] 取消行${rowIndex}的生成任务`);
            await context.imageGenerationStudio.handleCancelGeneration(context, rowIndex);
          }
          
          // 取消排队中的任务
          if (row.isQueued) {
            console.log(`🚫 [批量生图] 取消行${rowIndex}的排队任务`);
            await context.batchOperationsStudio.handleCancelQueue(context, rowIndex);
          }

          // 🔧 移除内部状态标记，改为依赖实际行状态
          console.log(`✅ [批量生图] 行${rowIndex}取消操作完成`);
          
        } catch (error) {
          console.error(`❌ [批量生图] 取消行${rowIndex}失败:`, error);
        }
      });

      // 等待所有取消操作完成
      await Promise.allSettled(cancelPromises);

      console.log('✅ [批量生图] 批量取消完成');

      // 重置状态
      resetBatchState();

    } catch (error) {
      console.error('❌ [批量生图] 批量取消失败:', error);
      resetBatchState();
      throw error;
    }
  };

  /**
   * 检查批量任务完成状态 - 重写版本，基于实际行状态
   */
  const checkBatchCompletion = (context) => {
    const state = batchGenerationState.value;

    if (!state.isActive || !context || !context.rows) {
      return;
    }

    const selectedRowIndices = state.selectedRowIndices;
    const totalTasks = selectedRowIndices.length;

    // 🔧 关键修复：基于实际行状态检查，而不是内部计数器
    const actualRowStates = selectedRowIndices.map(rowIndex => {
      const row = context.rows[rowIndex];
      if (!row) {
        return { rowIndex, status: 'missing', isActive: false };
      }

      // 检查行的实际状态
      const isGenerating = row.isGenerating || false;
      const isQueued = row.isQueued || false;
      const hasImage = row.imageSrc && row.imageSrc.trim() !== '';

      let status;
      let isActive;

      if (isGenerating) {
        status = 'generating';
        isActive = true;
      } else if (isQueued) {
        status = 'queued';
        isActive = true;
      } else if (hasImage) {
        status = 'completed';
        isActive = false;
      } else {
        // 既不在生成也不在排队，且没有图片，可能是取消或失败
        status = 'cancelled_or_failed';
        isActive = false;
      }

      return { rowIndex, status, isActive, isGenerating, isQueued, hasImage };
    });

    // 统计实际状态
    const activeCount = actualRowStates.filter(state => state.isActive).length;
    const completedCount = actualRowStates.filter(state => state.status === 'completed').length;
    const cancelledOrFailedCount = actualRowStates.filter(state => state.status === 'cancelled_or_failed').length;
    const finishedCount = completedCount + cancelledOrFailedCount;

    console.log('🔍 [批量生图] 实际状态检查:', {
      totalTasks,
      activeCount,
      completedCount,
      cancelledOrFailedCount,
      finishedCount,
      isAllFinished: finishedCount >= totalTasks && activeCount === 0,
      actualStates: actualRowStates
    });

    // 🔧 关键修复：只有当所有任务都真正完成且没有活跃任务时才重置
    const isAllFinished = finishedCount >= totalTasks && activeCount === 0;

    // 🔧 额外检查：如果批量任务已激活但没有任何活跃任务，也应该重置
    const hasNoActiveTasks = activeCount === 0 && state.isActive;

    if (isAllFinished || hasNoActiveTasks) {
      const reason = isAllFinished ? '所有任务完成' : '无活跃任务';
      console.log(`🎉 [批量生图] ${reason}，准备重置状态`, {
        isAllFinished,
        hasNoActiveTasks,
        activeCount,
        finishedCount,
        totalTasks
      });

      // 延迟重置状态，确保UI更新
      nextTick(() => {
        resetBatchStateWithCleanup(context);
      });
    } else {
      console.log('🔄 [批量生图] 批量任务仍在进行中，保持状态', {
        activeCount,
        finishedCount,
        totalTasks
      });
    }
  };

  /**
   * 重置批量生图状态 - 简化版本
   */
  const resetBatchState = () => {
    console.log('🔄 [批量生图] 重置批量生图状态');

    // 🔧 停止状态监控
    stopStatusMonitoring();

    batchGenerationState.value = {
      isActive: false,
      selectedRowIndices: [],
      startTime: null,
      buttonState: 'idle'
    };
  };

  /**
   * 带清理的状态重置
   */
  const resetBatchStateWithCleanup = (context) => {
    console.log('🧹 [批量生图] 执行状态重置和清理');

    try {
      // 取消选中所有相关行
      if (context && context.rows) {
        batchGenerationState.value.selectedRowIndices.forEach(rowIndex => {
          if (context.rows[rowIndex]) {
            context.rows[rowIndex].isSelected = false;
          }
        });

        // 强制更新UI
        if (context.$forceUpdate) {
          context.$forceUpdate();
        }
      }

      // 重置状态
      resetBatchState();

      console.log('✅ [批量生图] 状态重置和清理完成');

    } catch (error) {
      console.error('❌ [批量生图] 状态重置失败:', error);
      resetBatchState(); // 确保至少状态被重置
    }
  };

  /**
   * 监听单行操作影响 - 重写版本，基于实际状态检查
   */
  const handleSingleRowOperationImpact = (rowIndex, operation, context) => {
    if (!batchGenerationState.value.isActive) return;

    const selectedIndices = batchGenerationState.value.selectedRowIndices;
    if (!selectedIndices.includes(rowIndex)) return;

    console.log('👁️ [批量生图] 检测到单行操作影响:', {
      rowIndex,
      operation,
      isBatchActive: batchGenerationState.value.isActive,
      selectedIndices: selectedIndices.length
    });

    // 🔧 关键修复：不再依赖内部状态计数器，而是触发实际状态检查
    switch (operation) {
      case 'cancel_generation':
      case 'cancel_queue':
        console.log(`🚫 [批量生图] 行${rowIndex}被单独取消 (${operation})`);
        // 移除旧的计数器逻辑，改为触发状态检查
        break;

      case 'generation_complete':
        console.log(`✅ [批量生图] 行${rowIndex}生成完成`);
        // 移除旧的计数器逻辑，改为触发状态检查
        break;

      case 'generation_error':
        console.log(`❌ [批量生图] 行${rowIndex}生成失败`);
        // 移除旧的计数器逻辑，改为触发状态检查
        break;
    }

    // 🔧 关键修复：每次单行操作后都检查整体批量状态
    // 延迟检查，确保行状态已经更新
    nextTick(() => {
      if (context) {
        checkBatchCompletion(context);
      }
    });
  };

  /**
   * 获取批量生图统计信息 - 基于实际行状态
   */
  const getBatchStats = (context) => {
    const state = batchGenerationState.value;

    if (!state.isActive || !context || !context.rows) {
      return {
        isActive: false,
        totalTasks: 0,
        activeTasks: 0,
        completedTasks: 0,
        cancelledTasks: 0,
        progress: 0,
        buttonState: 'idle',
        duration: 0
      };
    }

    const selectedRowIndices = state.selectedRowIndices;
    let activeTasks = 0;
    let completedTasks = 0;
    let cancelledTasks = 0;

    selectedRowIndices.forEach(rowIndex => {
      const row = context.rows[rowIndex];
      if (!row) return;

      if (row.isGenerating || row.isQueued) {
        activeTasks++;
      } else if (row.imageSrc && row.imageSrc.trim() !== '') {
        completedTasks++;
      } else {
        cancelledTasks++;
      }
    });

    const progress = selectedRowIndices.length > 0
      ? Math.round(((completedTasks + cancelledTasks) / selectedRowIndices.length) * 100)
      : 0;

    return {
      isActive: state.isActive,
      totalTasks: selectedRowIndices.length,
      activeTasks,
      completedTasks,
      cancelledTasks,
      progress,
      buttonState: state.buttonState,
      duration: state.startTime ? Date.now() - state.startTime : 0,
      // 🧪 测试用详细信息
      detailedStates: selectedRowIndices.map(rowIndex => {
        const row = context.rows[rowIndex];
        if (!row) {
          return { rowIndex, isGenerating: false, isQueued: false, hasImage: false, status: 'missing' };
        }

        let status;
        if (row.isGenerating) {
          status = 'generating';
        } else if (row.isQueued) {
          status = 'queued';
        } else if (row.imageSrc && row.imageSrc.trim() !== '') {
          status = 'completed';
        } else {
          status = 'cancelled_or_failed';
        }

        return {
          rowIndex,
          isGenerating: row.isGenerating || false,
          isQueued: row.isQueued || false,
          hasImage: !!(row.imageSrc && row.imageSrc.trim() !== ''),
          status
        };
      })
    };
  };

  // 🎯 监听器设置 - 修复版本，移除对activeTasks的依赖
  watch(
    () => batchGenerationState.value.isActive,
    (newActive, oldActive) => {
      console.log('📊 [批量生图] 批量状态变化:', {
        from: oldActive,
        to: newActive,
        buttonState: batchGenerationState.value.buttonState
      });
    }
  );

  // 🔧 添加定期状态检查，确保状态能够及时更新
  let statusCheckInterval = null;

  const startStatusMonitoring = (context) => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
    }

    statusCheckInterval = setInterval(() => {
      if (batchGenerationState.value.isActive && context) {
        // 定期检查批量任务状态
        checkBatchCompletion(context);
      }
    }, 2000); // 每2秒检查一次
  };

  const stopStatusMonitoring = () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
      statusCheckInterval = null;
    }
  };

  return {
    // 状态
    batchGenerationState,
    isBatchGenerating,
    batchProgress,
    activeBatchTaskCount,
    batchButtonText,
    batchButtonClass,

    // 核心功能
    startBatchGeneration,
    cancelBatchGeneration,
    resetBatchState,
    resetBatchStateWithCleanup,

    // 监听和处理
    handleSingleRowOperationImpact,
    checkBatchCompletion,

    // 🔧 新增：基于context的计算函数
    calculateBatchProgress,
    calculateActiveBatchTaskCount,

    // 🔧 状态监控
    startStatusMonitoring,
    stopStatusMonitoring,

    // 工具函数
    getBatchStats
  };
}