/**
 * 调试工具模块
 * 从 ContentCreationStudio.vue 中提取的调试相关功能
 * 
 * 功能：
 * - 批量操作状态调试
 * - 数据保存流程调试
 * - 全局调试方法设置
 * - 各种测试和验证工具
 */

/**
 * 调试批量操作状态
 * 检查批量推理和图像生成的状态详情
 * @param {Object} context - 组件上下文，包含必要的状态和方法
 * @returns {Object} 调试信息
 */
export function debugBatchOperationStates(context) {
  console.log('🔍 [批量操作调试] 当前状态详情:');

  const debugInfo = {
    timestamp: new Date().toISOString(),
    uiState: null,
    stateManager: null,
    selectedRows: null
  };

  if (context.batchOperationUI) {
    const uiState = {
      推理状态: {
        isProcessing: context.batchOperationUI.isBatchInferenceProcessing.value,
        canCancel: context.batchOperationUI.batchInferenceState.value.canCancel,
        progress: context.batchOperationUI.batchInferenceState.value.progress,
        buttonText: context.batchOperationUI.batchInferenceState.value.buttonText
      },
      生图状态: {
        isProcessing: context.batchOperationUI.isBatchImageGenerationProcessing.value,
        canCancel: context.batchOperationUI.batchImageGenerationState.value.canCancel,
        progress: context.batchOperationUI.batchImageGenerationState.value.progress,
        buttonText: context.batchOperationUI.batchImageGenerationState.value.buttonText
      }
    };
    
    console.log('📊 [UI状态]', uiState);
    debugInfo.uiState = uiState;
  }

  if (context.batchStateManager) {
    const stateManagerInfo = context.batchStateManager.getStateStats();
    console.log('📊 [状态管理器]', stateManagerInfo);
    debugInfo.stateManager = stateManagerInfo;
  }

  // 检查选中的行
  const selectedRowsInfo = {
    selectedRowsCount: context.selectedRows?.length || 0,
    selectedRows: context.selectedRows?.map((row, index) => ({
      index,
      hasDescription: !!(row.description && row.description.trim()),
      hasKeywords: !!(row.keywords && row.keywords.trim())
    })) || []
  };
  
  console.log('📊 [选中状态]', selectedRowsInfo);
  debugInfo.selectedRows = selectedRowsInfo;

  return debugInfo;
}

/**
 * 调试数据保存流程
 * 验证项目数据保存的完整性和一致性
 * @param {Object} context - 组件上下文，包含必要的状态和方法
 * @returns {Promise<Object>} 调试结果
 */
export async function debugDataSaveFlow(context) {
  console.log('🔧 [调试] 开始验证数据保存流程');

  const debugResult = {
    timestamp: new Date().toISOString(),
    success: false,
    steps: {},
    errors: []
  };

  try {
    // 1. 检查项目数据状态
    const projectDataStatus = {
      hasProjectData: !!context.projectData,
      hasDataProperty: !!(context.projectData && context.projectData.data),
      hasRowsProperty: !!(context.projectData && context.projectData.data && context.projectData.data.rows),
      rowsCount: context.rows ? context.rows.length : 0,
      currentManageImageRowIndex: context.currentManageImageRowIndex
    };
    
    console.log('🔧 [调试] 1. 项目数据状态检查:', projectDataStatus);
    debugResult.steps.projectDataStatus = projectDataStatus;

    // 2. 检查当前管理的行数据
    if (context.currentManageImageRowIndex >= 0 && context.currentManageImageRowIndex < context.rows.length) {
      const currentRow = context.rows[context.currentManageImageRowIndex];
      const currentRowData = {
        rowIndex: context.currentManageImageRowIndex,
        hasMainImage: !!(currentRow.imageSrc && currentRow.imageSrc !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'),
        mainImageSrcLength: currentRow.imageSrc ? currentRow.imageSrc.length : 0,
        mainImageAlt: currentRow.imageAlt,
        hasThumbnails: !!currentRow.thumbnails,
        thumbnailsCount: currentRow.thumbnails ? currentRow.thumbnails.length : 0,
        thumbnailsData: currentRow.thumbnails ? currentRow.thumbnails.map((thumb, i) => ({
          index: i,
          name: thumb ? thumb.name : 'null',
          hasData: !!thumb,
          srcLength: thumb && thumb.src ? thumb.src.length : 0
        })) : []
      };
      
      console.log('🔧 [调试] 2. 当前管理行数据:', currentRowData);
      debugResult.steps.currentRowData = currentRowData;
    }

    // 3. 检查保存方法是否可用
    const saveMethodStatus = {
      hasSaveProjectDataImmediately: typeof context.saveProjectDataImmediately === 'function',
      hasDataPersistence: !!context.projectData
    };
    
    console.log('🔧 [调试] 3. 保存方法检查:', saveMethodStatus);
    debugResult.steps.saveMethodStatus = saveMethodStatus;

    // 4. 测试保存流程
    console.log('🔧 [调试] 4. 测试保存流程...');
    if (context.saveProjectDataImmediately) {
      await context.saveProjectDataImmediately();
      console.log('🔧 [调试] 4. 保存流程测试完成');
      debugResult.steps.saveTest = { success: true };
    } else {
      debugResult.steps.saveTest = { success: false, error: '保存方法不可用' };
    }

    // 5. 检查图片数据结构一致性
    console.log('🔧 [调试] 5. 图片数据结构一致性检查...');
    const structureCheck = checkImageDataStructure(context.rows);
    console.log('🔧 [调试] 5. 数据结构检查完成，发现问题数量:', structureCheck.issuesCount);
    debugResult.steps.structureCheck = structureCheck;

    // 6. 检查文件系统状态（浏览器环境中可能不可用）
    console.log('🔧 [调试] 6. 文件系统检查...');
    const fileSystemCheck = await checkFileSystemStatus(context);
    debugResult.steps.fileSystemCheck = fileSystemCheck;

    debugResult.success = true;
    
    if (context.showSuccessMessage) {
      context.showSuccessMessage('调试完成', '数据保存流程验证完成，请查看控制台输出');
    }

  } catch (error) {
    console.error('🔧 [调试] 验证过程出错:', error);
    debugResult.errors.push(error.message);
    
    if (context.showErrorMessage) {
      context.showErrorMessage('调试失败', error.message);
    }
  }

  return debugResult;
}

/**
 * 检查图片数据结构一致性
 * @param {Array} rows - 行数据数组
 * @returns {Object} 检查结果
 */
function checkImageDataStructure(rows) {
  let issuesCount = 0;
  const issues = [];

  if (!Array.isArray(rows)) {
    return { issuesCount: 0, issues: [], error: '行数据不是数组' };
  }

  rows.forEach((row, rowIndex) => {
    if (row.thumbnails && Array.isArray(row.thumbnails)) {
      row.thumbnails.forEach((thumbnail, thumbIndex) => {
        if (thumbnail && typeof thumbnail === 'object') {
          const rowIssues = [];

          // 检查多余字段
          if (thumbnail.name !== undefined) rowIssues.push('多余的name字段');
          if (thumbnail.size !== undefined) rowIssues.push('多余的size字段');
          if (thumbnail.type !== undefined) rowIssues.push('多余的type字段');

          // 检查缺失字段
          if (thumbnail.ismain === undefined && thumbnail.isMain === undefined) {
            rowIssues.push('缺失ismain字段');
          }

          // 检查字段名错误
          if (thumbnail.isMain !== undefined) rowIssues.push('错误的isMain字段名');

          if (rowIssues.length > 0) {
            const issueInfo = {
              rowIndex: rowIndex + 1,
              thumbIndex: thumbIndex + 1,
              issues: rowIssues
            };
            
            console.log(`🔧 [调试] 5. 第${rowIndex + 1}行第${thumbIndex + 1}张图片数据结构问题:`, rowIssues);
            issues.push(issueInfo);
            issuesCount++;
          }
        }
      });
    }
  });

  return { issuesCount, issues };
}

/**
 * 检查文件系统状态
 * @param {Object} context - 组件上下文
 * @returns {Promise<Object>} 文件系统检查结果
 */
async function checkFileSystemStatus(context) {
  try {
    const fs = window.require('fs');
    const path = window.require('path');
    const projectFilePath = path.join(process.cwd(), 'project-data.json');

    if (fs.existsSync(projectFilePath)) {
      const fileStats = fs.statSync(projectFilePath);
      const fileContent = fs.readFileSync(projectFilePath, 'utf8');
      const parsedData = JSON.parse(fileContent);

      const fileStatus = {
        exists: true,
        size: fileStats.size,
        lastModified: fileStats.mtime,
        hasData: !!parsedData.data,
        hasRows: !!(parsedData.data && parsedData.data.rows),
        rowsCount: parsedData.data && parsedData.data.rows ? parsedData.data.rows.length : 0
      };

      console.log('🔧 [调试] 6. project-data.json 文件状态:', fileStatus);

      // 检查特定行的数据
      if (context.currentManageImageRowIndex >= 0 && parsedData.data && parsedData.data.rows) {
        const savedRow = parsedData.data.rows[context.currentManageImageRowIndex];
        if (savedRow) {
          const savedRowData = {
            rowIndex: context.currentManageImageRowIndex,
            hasMainImage: !!(savedRow.imageSrc && savedRow.imageSrc !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'),
            mainImageSrcLength: savedRow.imageSrc ? savedRow.imageSrc.length : 0,
            mainImageAlt: savedRow.imageAlt,
            hasThumbnails: !!savedRow.thumbnails,
            thumbnailsCount: savedRow.thumbnails ? savedRow.thumbnails.length : 0
          };

          console.log('🔧 [调试] 6. 文件中保存的当前行数据:', savedRowData);
          fileStatus.savedRowData = savedRowData;
        }
      }

      return fileStatus;
    } else {
      console.log('🔧 [调试] 6. project-data.json 文件不存在');
      return { exists: false };
    }
  } catch (fsError) {
    console.log('🔧 [调试] 6. 无法访问文件系统 (可能在浏览器环境):', fsError.message);
    return { error: fsError.message, browserEnvironment: true };
  }
}

/**
 * 设置全局调试方法
 * 将调试方法绑定到window对象，供开发者在控制台使用
 * @param {Object} context - 组件上下文，包含必要的状态和方法
 */
export function setupGlobalDebugMethods(context) {
  // 🔍 设置全局调试方法
  window.debugBatchOperations = () => {
    return debugBatchOperationStates(context);
  };

  window.resetBatchOperations = () => {
    if (context.resetBatchOperationStates) {
      context.resetBatchOperationStates();
      console.log('✅ [全局调试] 批量操作状态已重置');
    } else {
      console.warn('⚠️ [全局调试] resetBatchOperationStates 方法不可用');
    }
  };

  // 🔧 测试响应式对象访问
  window.testReactiveAccess = () => {
    console.log('🧪 [响应式测试] 测试Vue 3响应式对象访问:');
    if (context.batchOperationUI && context.batchOperationUI.isBatchInferenceProcessing) {
      console.log('原始对象:', context.batchOperationUI.isBatchInferenceProcessing);
      console.log('访问.value:', context.batchOperationUI.isBatchInferenceProcessing.value);
      console.log('类型检查:', typeof context.batchOperationUI.isBatchInferenceProcessing.value);
      console.log('布尔值检查:', !!context.batchOperationUI.isBatchInferenceProcessing.value);
    } else {
      console.warn('⚠️ [响应式测试] batchOperationUI 不可用');
    }
  };

  // 🔧 测试Toast系统
  window.testToast = () => {
    console.log('🧪 [Toast测试] 测试Toast系统:');

    if (context.showToast) {
      // 测试新的调用方式
      context.showToast({
        type: 'warning',
        title: '测试标题',
        message: '这是一个测试消息'
      });
      console.log('🧪 [Toast测试] Toast调用完成');
    } else {
      console.warn('⚠️ [Toast测试] showToast 方法不可用');
    }
  };

  // 🔧 测试批量验证
  window.testBatchValidation = () => {
    console.log('🧪 [验证测试] 测试批量验证:');

    if (context.batchOperationUI && context.batchOperationUI.validateUserSelection && context.showToast) {
      const result = context.batchOperationUI.validateUserSelection(
        [], // 空数组，应该触发Toast
        context.showToast,
        'inference'
      );
      console.log('🧪 [验证测试] 验证结果:', result);
    } else {
      console.warn('⚠️ [验证测试] 批量验证方法不可用');
    }
  };

  // 🔧 直接测试Toast组件
  window.testToastDirect = () => {
    console.log('🧪 [Toast直接测试] 直接测试Toast组件:');

    if (context.toastNotification) {
      // 使用新的Toast通知系统
      context.toastNotification.showToast({
        type: 'warning',
        title: '直接测试',
        message: '这是直接测试Toast组件的消息'
      });
      console.log('🧪 [Toast直接测试] 使用新的Toast系统调用完成');
    } else {
      console.warn('⚠️ [Toast直接测试] toastNotification 不可用');
    }
  };

  // 🔧 测试showWarningMessage方法
  window.testWarningMessage = () => {
    console.log('🧪 [Warning测试] 测试showWarningMessage方法:');
    if (context.showWarningMessage) {
      context.showWarningMessage('测试警告', '这是一个测试警告消息');
    } else {
      console.warn('⚠️ [Warning测试] showWarningMessage 方法不可用');
    }
  };

  // 🔧 测试队列状态显示
  window.testQueueDisplay = () => {
    console.log('🔧 [队列显示测试] 测试队列状态显示:');

    if (context.imageGeneration && context.imageGeneration.queueStats) {
      // 检查队列统计
      const queueStats = context.imageGeneration.queueStats;
      console.log('🔧 [队列显示测试] 当前队列统计:', queueStats);

      // 检查行状态
      if (context.rows) {
        context.rows.forEach((row, index) => {
          if (row.isQueued || row.isGenerating) {
            console.log(`🔧 [队列显示测试] 第${index + 1}行状态:`, {
              isQueued: row.isQueued,
              isGenerating: row.isGenerating,
              queueTaskId: row.queueTaskId,
              queuePosition: row.queuePosition,
              generationMessage: row.generationMessage,
              generationProgress: row.generationProgress
            });
          }
        });
      }
    } else {
      console.warn('⚠️ [队列显示测试] imageGeneration 或 queueStats 不可用');
    }
  };

  // 🔧 测试ComfyUI后端取消功能
  window.testComfyUICancel = async () => {
    console.log('🔧 [ComfyUI取消测试] 测试ComfyUI后端取消功能:');

    try {
      if (context.imageGeneration && context.imageGeneration.comfyuiImageGeneration) {
        // 直接调用ComfyUI的取消方法
        const cancelResult = await context.imageGeneration.comfyuiImageGeneration.notifyComfyUICancel([]);
        console.log('🔧 [ComfyUI取消测试] 取消结果:', cancelResult);

        if (cancelResult.success && cancelResult.interrupted) {
          console.log('✅ [ComfyUI取消测试] ComfyUI后端取消成功');
        } else {
          console.warn('⚠️ [ComfyUI取消测试] ComfyUI后端取消可能不完整');
        }
      } else {
        console.warn('⚠️ [ComfyUI取消测试] ComfyUI图像生成服务不可用');
      }
    } catch (error) {
      console.error('❌ [ComfyUI取消测试] 测试失败:', error);
    }
  };

  // 🔧 测试队列面板统计准确性
  window.testQueueStatsAccuracy = () => {
    console.log('🔧 [队列统计测试] 测试队列面板统计准确性:');

    if (context.imageGeneration) {
      const queueStats = context.imageGeneration.queueStats;
      const taskQueue = context.imageGeneration.taskQueue;

      console.log('📊 [队列统计测试] 当前队列统计:', queueStats);
      console.log('📋 [队列统计测试] 实际任务队列:', taskQueue);

      if (taskQueue && Array.isArray(taskQueue)) {
        // 验证统计准确性
        const actualWaiting = taskQueue.filter(t => t.status === 'waiting').length;
        const actualProcessing = taskQueue.filter(t => t.status === 'processing').length;
        const actualCompleted = taskQueue.filter(t => t.status === 'completed').length;

        console.log('🔍 [队列统计测试] 统计验证:', {
          waiting: { reported: queueStats.waiting, actual: actualWaiting, match: queueStats.waiting === actualWaiting },
          processing: { reported: queueStats.processing, actual: actualProcessing, match: queueStats.processing === actualProcessing },
          completed: { reported: queueStats.completed, actual: actualCompleted, match: queueStats.completed === actualCompleted }
        });

        if (queueStats.waiting === actualWaiting &&
            queueStats.processing === actualProcessing &&
            queueStats.completed === actualCompleted) {
          console.log('✅ [队列统计测试] 队列统计准确');
        } else {
          console.warn('⚠️ [队列统计测试] 队列统计不准确，需要修复');
        }
      } else {
        console.warn('⚠️ [队列统计测试] 任务队列不可用');
      }
    } else {
      console.warn('⚠️ [队列统计测试] imageGeneration 不可用');
    }
  };

  // 🔧 测试数据保存流程
  window.testDataSaveFlow = async () => {
    console.log('🔧 [数据保存测试] 开始测试数据保存流程:');
    return await debugDataSaveFlow(context);
  };

  console.log('🔍 [调试] 全局调试方法已设置: debugBatchOperations(), resetBatchOperations(), testToast(), testBatchValidation(), testToastDirect(), testWarningMessage(), testReactiveAccess(), testQueueDisplay(), testComfyUICancel(), testQueueStatsAccuracy(), testDataSaveFlow()');
}

/**
 * 调试工具集合
 * 提供统一的接口访问所有调试工具
 */
export const debugUtils = {
  debugBatchStates: debugBatchOperationStates,
  debugSaveFlow: debugDataSaveFlow,
  setupGlobalMethods: setupGlobalDebugMethods
};

/**
 * 默认导出，包含所有调试工具
 */
export default {
  debugBatchOperationStates,
  debugDataSaveFlow,
  setupGlobalDebugMethods,
  debugUtils
};
