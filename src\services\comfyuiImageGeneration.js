/**
 * ComfyUI WebSocket Monitor: 模块化客户端框架
 * 基于事件驱动的ComfyUI实时监控系统
 *
 * 设计原则：
 * - 模块化：功能分解为独立的可管理模块
 * - 事件驱动：通过事件/回调响应WebSocket消息
 * - 状态管理：清晰跟踪任务生命周期
 * - 可配置性：允许配置连接参数和行为
 * - 可扩展性：易于添加新处理逻辑
 */

// ===== 核心模块导入 =====
import { WebSocketManager } from './comfyui/WebSocketManager.js';
import { MessageParser } from './comfyui/MessageParser.js';
import { PromptStateManager } from './comfyui/PromptStateManager.js';

import { HttpApiClient } from './comfyui/HttpApiClient.js';

// ===== 任务状态枚举 =====
export const PromptStatus = {
  QUEUED: 'queued',
  RUNNING: 'running',
  COMPLETED: 'completed',
  ERROR: 'error',
  INTERRUPTED: 'interrupted',
  CACHED_COMPLETE: 'cached_complete'
};

/**
 * I. 顶级协调器模块: ComfyUIClient
 *
 * 职责：
 * - 作为整个监视器系统的入口点和主要协调者
 * - 初始化和管理所有其他核心模块
 * - 提供高级API给用户
 * - 聚合来自其他模块的事件，通过定义好的回调接口暴露给用户
 */
class ComfyUIClient {
  constructor(config = {}) {
    // 配置参数
    this.config = {
      host: config.host || 'localhost',
      port: config.port || 8188,
      client_id: config.client_id || this.generateClientId(),
      reconnect_attempts: config.reconnect_attempts || 5,
      reconnect_delay: config.reconnect_delay || 2000,
      request_timeout: config.request_timeout || 30000,
      auto_fetch_outputs: config.auto_fetch_outputs || true,
      output_directory: config.output_directory || null
    };

    // 用户回调函数
    this.callbacks = {
      onConnect: null,
      onDisconnect: null,
      onStatusUpdate: null,
      onPromptSubmitted: null,
      onPromptStart: null,
      onPromptProgress: null,
      onPromptNodeExecuting: null,
      onPromptNodeExecuted: null,
      onPromptOutputReceived: null,
      onPromptPreviewImage: null,
      onPromptComplete: null,
      onPromptError: null,
      onPromptInterrupted: null,
      onSystemError: null
    };

    // ComfyUI配置
    this.comfyuiConfig = {
      serverUrl: 'http://localhost:8188',
      batchSize: 1,
      currentWorkflow: null,
      nodeMapping: {},
      defaultSettings: {},
      workflowName: ''
    };

    // 初始化核心模块
    this.initializeModules();
  }

  /**
   * 初始化所有核心模块
   */
  initializeModules() {
    console.log('🎨 [ComfyUIClient] 初始化核心模块...');

    // 初始化HTTP API客户端
    this.httpClient = new HttpApiClient({
      base_url: `http://${this.config.host}:${this.config.port}`,
      request_timeout: this.config.request_timeout
    });

    // 初始化任务状态管理器
    this.stateManager = new PromptStateManager();



    // 初始化消息解析器
    this.messageParser = new MessageParser();

    // 初始化WebSocket管理器
    this.wsManager = new WebSocketManager({
      ws_url: `ws://${this.config.host}:${this.config.port}/ws?clientId=${this.config.client_id}`,
      reconnect_attempts: this.config.reconnect_attempts,
      reconnect_delay: this.config.reconnect_delay
    });

    // 设置模块间的事件绑定
    this.bindModuleEvents();

    console.log('✅ [ComfyUIClient] 核心模块初始化完成');
  }

  /**
   * 绑定模块间的事件
   */
  bindModuleEvents() {
    // WebSocket事件
    this.wsManager.on('open', () => this.handleWebSocketOpen());
    this.wsManager.on('message', (message) => this.handleWebSocketMessage(message));
    this.wsManager.on('error', (error) => this.handleWebSocketError(error));
    this.wsManager.on('close', (code, reason) => this.handleWebSocketClose(code, reason));



    console.log('🔗 [ComfyUIClient] 模块事件绑定完成');
  }

  /**
   * 生成客户端ID
   */
  generateClientId() {
    return 'client_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
  }

  // ===== 公共API方法 =====

  /**
   * 连接到ComfyUI服务器
   */
  async connect() {
    console.log('🔌 [ComfyUIClient] 开始连接...');
    try {
      await this.wsManager.connect();
      return true;
    } catch (error) {
      console.error('❌ [ComfyUIClient] 连接失败:', error);
      this.triggerCallback('onSystemError', error.message, { error });
      return false;
    }
  }

  /**
   * 断开连接
   */
  async disconnect() {
    console.log('🔌 [ComfyUIClient] 断开连接...');
    await this.wsManager.disconnect();
  }

  /**
   * 提交工作流
   * @param {Object} workflow_api_json - 工作流API JSON
   * @returns {Promise<string|null>} prompt_id 或 null
   */
  async submitWorkflow(workflow_api_json) {
    try {
      console.log('📤 [ComfyUIClient] 提交工作流...');

      const result = await this.httpClient.postPrompt(workflow_api_json);

      // 🔧 修复：后端代理包装了响应，prompt_id在data字段中
      let prompt_id = null;
      if (result && result.data && result.data.prompt_id) {
        prompt_id = result.data.prompt_id;
      } else if (result && result.prompt_id) {
        // 兼容直接返回的情况
        prompt_id = result.prompt_id;
      }

      if (prompt_id) {

        // 添加到状态管理器
        this.stateManager.addPrompt(prompt_id, workflow_api_json);

        // 触发回调
        this.triggerCallback('onPromptSubmitted', prompt_id, workflow_api_json);

        console.log(`✅ [ComfyUIClient] 工作流提交成功: ${prompt_id}`);
        return prompt_id;
      } else {
        throw new Error('提交失败，未返回prompt_id');
      }
    } catch (error) {
      console.error('❌ [ComfyUIClient] 提交工作流失败:', error);
      this.triggerCallback('onSystemError', error.message, { error });
      return null;
    }
  }

  /**
   * 获取任务状态
   * @param {string} prompt_id
   * @returns {Object|null} PromptState 或 null
   */
  getPromptStatus(prompt_id) {
    return this.stateManager.getPrompt(prompt_id);
  }

  /**
   * 获取所有任务状态
   * @returns {Object} prompt_id -> PromptState 的映射
   */
  getAllPromptsStatus() {
    return this.stateManager.getAllPrompts();
  }

  /**
   * 取消任务（可选/高级功能）
   * @param {string} prompt_id
   */
  async cancelPrompt(prompt_id) {
    try {
      console.log(`🛑 [ComfyUIClient] 取消任务: ${prompt_id}`);
      // 这里可以实现取消逻辑，如果ComfyUI支持的话
      // 目前先更新本地状态
      this.stateManager.setError(prompt_id, {
        message: '用户取消',
        cancelled: true
      });
    } catch (error) {
      console.error('❌ [ComfyUIClient] 取消任务失败:', error);
    }
  }

  // ===== 回调管理方法 =====

  /**
   * 设置回调函数
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    const callbackName = `on${eventName.charAt(0).toUpperCase()}${eventName.slice(1)}`;
    if (Object.prototype.hasOwnProperty.call(this.callbacks, callbackName)) {
      this.callbacks[callbackName] = callback;
    } else {
      console.warn(`⚠️ [ComfyUIClient] 未知的事件名称: ${eventName}`);
    }
  }

  /**
   * 移除事件回调
   * @param {string} eventName - 事件名称
   */
  off(eventName) {
    const callbackName = `on${eventName.charAt(0).toUpperCase()}${eventName.slice(1)}`;
    if (Object.prototype.hasOwnProperty.call(this.callbacks, callbackName)) {
      this.callbacks[callbackName] = null;
    } else {
      console.warn(`⚠️ [ComfyUIClient] 未知的事件名称: ${eventName}`);
    }
  }

  /**
   * 触发回调函数
   * @param {string} callbackName - 回调名称
   * @param {...any} args - 参数
   */
  triggerCallback(callbackName, ...args) {
    const callback = this.callbacks[callbackName];
    if (callback && typeof callback === 'function') {
      try {
        callback(...args);
      } catch (error) {
        console.error(`❌ [ComfyUIClient] 回调执行失败 ${callbackName}:`, error);
      }
    }
  }

  // ===== WebSocket事件处理方法 =====

  /**
   * 处理WebSocket连接打开
   */
  handleWebSocketOpen() {
    console.log('✅ [ComfyUIClient] WebSocket连接已建立');
    this.triggerCallback('onConnect');
  }

  /**
   * 处理WebSocket消息
   * @param {string|Blob} message - 原始消息
   */
  async handleWebSocketMessage(message) {
    try {
      // 使用消息解析器处理消息
      await this.messageParser.parseAndDispatch(message, {
        onStatusUpdate: (data) => this.handleStatusUpdate(data),
        onExecutionStart: (data) => this.handleExecutionStart(data),
        onExecuting: (data) => this.handleExecuting(data),
        onProgress: (data) => this.handleProgress(data),
        onExecuted: (data) => this.handleExecuted(data),
        onExecutionCached: (data) => this.handleExecutionCached(data),
        onExecutionError: (data) => this.handleExecutionError(data),
        onExecutionInterrupted: (data) => this.handleExecutionInterrupted(data),
        onPreview: (data) => this.handlePreview(data),
        // 🆕 新增错误状态处理器
        onServerShutdown: (data) => this.handleServerShutdown(data),
        onOutOfMemory: (data) => this.handleOutOfMemory(data),
        onUserCancelled: (data) => this.handleUserCancelled(data),
        onConnectionLost: (data) => this.handleConnectionLost(data),
        onQueueFull: (data) => this.handleQueueFull(data),
        onPotentialError: (data) => this.handlePotentialError(data),
        onUnknownMessage: (data) => this.handleUnknownMessage(data)
      });
    } catch (error) {
      console.error('❌ [ComfyUIClient] 处理WebSocket消息失败:', error);
      this.triggerCallback('onSystemError', error.message, { error });
    }
  }

  /**
   * 处理WebSocket错误
   * @param {Error} error - 错误对象
   */
  handleWebSocketError(error) {
    console.error('❌ [ComfyUIClient] WebSocket错误:', error);
    this.triggerCallback('onSystemError', error.message, { error });
  }

  /**
   * 处理WebSocket关闭
   * @param {number} code - 关闭代码
   * @param {string} reason - 关闭原因
   */
  handleWebSocketClose(code, reason) {
    console.log(`🔌 [ComfyUIClient] WebSocket连接已关闭: ${code} - ${reason}`);
    this.triggerCallback('onDisconnect', reason);
  }

  // ===== 消息处理方法 =====

  /**
   * 处理状态更新消息
   * @param {Object} data - 状态数据
   */
  handleStatusUpdate(data) {
    // 🔧 优化：只在开发模式下显示详细状态更新
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) console.log('📊 [ComfyUIClient] 状态更新:', data);

    // 🔧 关键修复：检查队列状态，可能触发完成事件
    if (data.status && data.status.exec_info) {
      const { queue_remaining } = data.status.exec_info;

      // 如果队列为空且有当前任务，可能表示任务已完成
      if (queue_remaining === 0 && this.currentPromptId) {
        console.log('🔔 [ComfyUIClient] 队列为空，检查任务完成:', this.currentPromptId);

        // 🔧 关键修复：基于队列状态的强制完成
        setTimeout(async () => {
          if (this.currentPromptId) {
            const promptState = this.stateManager.getPrompt(this.currentPromptId);
            if (promptState && promptState.status !== 'completed') {
              console.log('🔧 [ComfyUIClient] 队列空闲但任务未完成，强制触发完成检查');

              // 🎯 新策略：尝试获取ComfyUI的历史记录来获取输出
              try {
                console.log('🔍 [ComfyUIClient] 尝试从ComfyUI历史记录获取输出数据');
                const historyData = await this.httpClient.getHistory(this.currentPromptId);

                if (historyData && historyData[this.currentPromptId]) {
                  const promptHistory = historyData[this.currentPromptId];
                  console.log('📚 [ComfyUIClient] 找到历史记录:', promptHistory);

                  // 🔧 增强调试：检查历史记录结构
                  console.log('🔍 [ComfyUIClient] 历史记录结构分析:', {
                    hasOutputs: !!promptHistory.outputs,
                    outputsKeys: promptHistory.outputs ? Object.keys(promptHistory.outputs) : [],
                    hasStatus: !!promptHistory.status,
                    status: promptHistory.status
                  });

                  // 处理历史记录中的输出
                  if (promptHistory.outputs) {
                    let foundImages = false;
                    for (const [nodeId, nodeOutput] of Object.entries(promptHistory.outputs)) {
                      console.log(`🔍 [ComfyUIClient] 处理节点输出: ${nodeId}`, nodeOutput);

                      // 🔧 检查是否包含图像
                      if (nodeOutput && nodeOutput.images && Array.isArray(nodeOutput.images)) {
                        foundImages = true;
                        console.log(`🖼️ [ComfyUIClient] 历史记录中发现图像输出: 节点${nodeId}, ${nodeOutput.images.length}张图片`);
                      }

                      this.stateManager.markNodeExecuted(this.currentPromptId, nodeId, nodeOutput);

                      // 🎯 使用新的简化处理逻辑
                      if (this.isSaveImageNode(nodeId)) {
                        console.log(`🎯 [ComfyUIClient] 历史记录中的SaveImage节点: ${nodeId}`);
                        this.handleSaveImageComplete(this.currentPromptId, nodeId, nodeOutput);
                      }
                    }

                    if (!foundImages) {
                      console.log('⚠️ [ComfyUIClient] 历史记录中未找到任何图像输出');
                    }
                  } else {
                    console.log('⚠️ [ComfyUIClient] 历史记录中没有outputs字段');
                  }
                } else {
                  console.log('⚠️ [ComfyUIClient] 未找到对应的历史记录');
                }
              } catch (error) {
                console.warn('⚠️ [ComfyUIClient] 获取历史记录失败:', error.message);
              }

              console.log('🎉 [ComfyUIClient] 队列为空且进度100%，强制标记任务完成');
              this.handlePromptComplete(this.currentPromptId);
            }
          }
        }, 2000); // 增加到2秒，确保ComfyUI完全完成
      }
    }

    this.triggerCallback('onStatusUpdate', data);
  }

  /**
   * 处理执行开始消息
   * @param {Object} data - 执行开始数据
   */
  handleExecutionStart(data) {
    const { prompt_id } = data;
    console.log(`🚀 [ComfyUIClient] 执行开始: ${prompt_id}`);

    this.stateManager.updateStatus(prompt_id, PromptStatus.RUNNING);
    this.triggerCallback('onPromptStart', prompt_id);
  }

  /**
   * 处理节点执行消息
   * @param {Object} data - 节点执行数据
   */
  handleExecuting(data) {
    const { prompt_id, node } = data;
    // 🔧 优化：只在开发模式下显示节点执行详情
    if (process.env.NODE_ENV === 'development') {
      console.log(`⚙️ [ComfyUIClient] 执行节点: ${prompt_id} -> ${node}`);
    }

    this.stateManager.setExecutingNode(prompt_id, node);
    this.triggerCallback('onPromptNodeExecuting', prompt_id, node);
  }

  /**
   * 处理进度消息
   * @param {Object} data - 进度数据
   */
  handleProgress(data) {
    const { prompt_id, node, value, max } = data;
    const percent = max > 0 ? Math.round((value / max) * 100) : 0;

    // 🔧 优化：减少进度日志频率，只显示关键进度点
    if (percent % 25 === 0 || percent === 100) {
      console.log(`📈 [ComfyUIClient] 进度: ${prompt_id} -> ${percent}%`);
    }

    this.stateManager.setNodeProgress(prompt_id, node, value, max);
    this.triggerCallback('onPromptProgress', prompt_id, {
      node_id: node,
      value,
      max,
      percent
    });
  }

  /**
   * 🎯 重新设计：简化的节点执行完成处理
   * @param {Object} data - 执行完成数据
   */
  handleExecuted(data) {
    const { prompt_id, node, output } = data;
    console.log(`✅ [ComfyUIClient] 节点执行完成: ${prompt_id} -> ${node}`);

    // 更新状态管理器
    this.stateManager.markNodeExecuted(prompt_id, node, output);

    // 🎯 核心逻辑：只处理SaveImage节点
    if (this.isSaveImageNode(node)) {
      console.log(`🎯 [ComfyUIClient] SaveImage节点完成，处理最终图像: ${node}`);
      this.handleSaveImageComplete(prompt_id, node, output);
    } else {
      console.log(`⏭️ [ComfyUIClient] 忽略非SaveImage节点: ${node}`);
    }

    // 检查任务是否完全完成
    this.checkPromptCompletion(prompt_id);
  }

  /**
   * 处理缓存执行消息
   * @param {Object} data - 缓存执行数据
   */
  handleExecutionCached(data) {
    const { prompt_id, nodes } = data;
    console.log(`💾 [ComfyUIClient] 缓存执行: ${prompt_id} -> ${nodes.length} 个节点`);

    nodes.forEach(node => {
      this.stateManager.markNodeCached(prompt_id, node);
    });

    // 检查任务是否完成
    if (this.stateManager.isPromptComplete(prompt_id)) {
      this.handlePromptComplete(prompt_id);
    }
  }

  /**
   * 处理执行错误消息
   * @param {Object} data - 错误数据
   */
  handleExecutionError(data) {
    const { prompt_id, node_id, node_type, exception_message, traceback } = data;
    console.error(`❌ [ComfyUIClient] 执行错误: ${prompt_id} -> ${node_type}(${node_id})`);

    const errorData = {
      node_id,
      node_type,
      exception_message,
      traceback
    };

    this.stateManager.setError(prompt_id, errorData);

    // 🔧 关键修复：如果这是当前任务，清理状态
    if (this.currentPromptId === prompt_id) {
      console.log(`🔧 [ComfyUIClient] 清理错误任务状态: ${prompt_id}`);
      this.currentPromptId = null;
      this.isGenerating = false;
    }

    // 🔧 关键修复：触发进度回调，通知前端任务失败
    if (this.callbacks.onPromptProgress) {
      console.log(`🔧 [ComfyUIClient] 触发错误进度回调: ${prompt_id}`);
      this.callbacks.onPromptProgress(prompt_id, {
        stage: 'error',
        progress: 0,
        message: errorData.exception_message || '生成失败',
        isCompleted: true,
        isProcessing: false,
        isError: true,
        isCancelled: false
      });
    }

    this.triggerCallback('onPromptError', prompt_id, errorData);
  }

  /**
   * 处理执行中断消息
   * @param {Object} data - 中断数据
   */
  handleExecutionInterrupted(data) {
    const { prompt_id } = data;
    console.log(`🛑 [ComfyUIClient] 执行中断: ${prompt_id}`);

    this.stateManager.updateStatus(prompt_id, PromptStatus.INTERRUPTED);

    // 🔧 关键修复：如果这是当前任务，清理状态
    if (this.currentPromptId === prompt_id) {
      console.log(`🔧 [ComfyUIClient] 清理中断任务状态: ${prompt_id}`);
      this.currentPromptId = null;
      this.isGenerating = false;
    }

    // 🔧 关键修复：触发进度回调，通知前端任务中断
    if (this.callbacks.onPromptProgress) {
      console.log(`🔧 [ComfyUIClient] 触发中断进度回调: ${prompt_id}`);
      this.callbacks.onPromptProgress(prompt_id, {
        stage: 'interrupted',
        progress: 0,
        message: '任务已中断',
        isCompleted: true,
        isProcessing: false,
        isError: false,
        isCancelled: true
      });
    }

    this.triggerCallback('onPromptInterrupted', prompt_id, data);
  }

  /**
   * 处理预览图像消息
   * @param {Object} data - 预览数据
   */
  handlePreview(data) {
    const { prompt_id, node, image_data, format } = data;
    console.log(`🖼️ [ComfyUIClient] 预览图像: ${prompt_id} -> ${node}`);

    this.triggerCallback('onPromptPreviewImage', prompt_id, node, image_data, format);
  }

  /**
   * 🆕 处理服务器关闭事件
   * @param {Object} data - 关闭数据
   */
  handleServerShutdown(data) {
    console.error('🛑 [ComfyUIClient] ComfyUI服务器正在关闭');

    // 清理所有进行中的任务
    this.stateManager.clearAllTasks();

    // 触发服务器关闭回调
    this.triggerCallback('onServerShutdown', data);

    // 显示用户友好的错误信息
    this.showUserError('服务器关闭', 'ComfyUI服务器正在关闭，请稍后重试', 'server_shutdown');
  }

  /**
   * 🆕 处理内存不足事件
   * @param {Object} data - 内存错误数据
   */
  handleOutOfMemory(data) {
    console.error('💾 [ComfyUIClient] ComfyUI内存不足');

    // 标记相关任务为失败
    if (data.prompt_id) {
      this.stateManager.setError(data.prompt_id, {
        type: 'out_of_memory',
        message: '显存不足，无法完成图像生成'
      });
    }

    // 触发内存不足回调
    this.triggerCallback('onOutOfMemory', data);

    // 显示内存优化建议
    this.showUserError('显存不足', '显卡内存不足，建议降低图像分辨率或批次大小', 'memory_error');
  }

  /**
   * 🆕 处理用户取消事件
   * @param {Object} data - 取消数据
   */
  handleUserCancelled(data) {
    console.log('🚫 [ComfyUIClient] 用户取消了任务');

    if (data.prompt_id) {
      this.stateManager.updateStatus(data.prompt_id, PromptStatus.CANCELLED);
    }

    this.triggerCallback('onUserCancelled', data);
  }

  /**
   * 🆕 处理连接丢失事件
   * @param {Object} data - 连接数据
   */
  handleConnectionLost(data) {
    console.error('🔌 [ComfyUIClient] 与ComfyUI的连接丢失');

    // 触发连接丢失回调
    this.triggerCallback('onConnectionLost', data);

    // 尝试重新连接
    this.scheduleReconnection();

    this.showUserError('连接丢失', '与ComfyUI服务器的连接丢失，正在尝试重新连接', 'connection_error');
  }

  /**
   * 🆕 处理队列满事件
   * @param {Object} data - 队列数据
   */
  handleQueueFull(data) {
    console.warn('📋 [ComfyUIClient] ComfyUI队列已满');

    this.triggerCallback('onQueueFull', data);

    this.showUserError('队列已满', 'ComfyUI任务队列已满，请稍后重试', 'queue_full');
  }

  /**
   * 🆕 处理潜在错误事件
   * @param {Object} data - 错误数据
   */
  handlePotentialError(data) {
    const { detectedPatterns } = data;
    console.warn('🔍 [ComfyUIClient] 检测到潜在错误:', detectedPatterns);

    // 根据检测到的模式采取相应行动
    for (const pattern of detectedPatterns) {
      switch (pattern) {
        case 'memory_error':
          this.handleOutOfMemory({ message: '检测到内存错误' });
          break;
        case 'model_error':
          this.showUserError('模型错误', '模型加载失败，请检查模型文件是否存在', 'model_error');
          break;
        case 'connection_error':
          this.handleConnectionLost({ message: '检测到连接错误' });
          break;
        case 'interruption':
          this.handleUserCancelled({ message: '检测到任务中断' });
          break;
        case 'permission_error':
          this.showUserError('权限错误', '访问被拒绝，请检查文件权限', 'permission_error');
          break;
      }
    }

    this.triggerCallback('onPotentialError', data);
  }

  /**
   * 🆕 处理未知消息事件
   * @param {Object} data - 消息数据
   */
  handleUnknownMessage(data) {
    console.log('📨 [ComfyUIClient] 收到未知消息类型:', data);
    this.triggerCallback('onUnknownMessage', data);
  }

  /**
   * 🆕 显示用户友好的错误信息
   * @param {string} title - 错误标题
   * @param {string} message - 错误消息
   * @param {string} type - 错误类型
   */
  showUserError(title, message, type) {
    const errorData = {
      title,
      message,
      type,
      timestamp: new Date().toISOString()
    };

    console.error(`❌ [ComfyUIClient] ${title}: ${message}`);
    this.triggerCallback('onUserError', errorData);
  }

  /**
   * 🆕 安排重新连接
   */
  scheduleReconnection() {
    if (this.webSocketManager) {
      // 启用重连并尝试连接
      this.webSocketManager.enableReconnecting();
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('❌ [ComfyUIClient] 重新连接失败:', error);
        });
      }, 3000);
    }
  }



  /**
   * 🎯 简化的SaveImage节点检测
   * @param {string} node_id - 节点ID
   * @returns {boolean} 是否为SaveImage节点
   */
  isSaveImageNode(node_id) {
    // 检查工作流中的节点类型
    const workflow = this.comfyuiConfig?.currentWorkflow;
    if (workflow && workflow[node_id] && workflow[node_id].class_type === 'SaveImage') {
      console.log(`✅ [ComfyUIClient] 确认SaveImage节点: ${node_id}`);
      return true;
    }

    console.log(`⏭️ [ComfyUIClient] 非SaveImage节点: ${node_id}`);
    return false;
  }

  /**
   * 🎯 处理SaveImage节点完成
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Object} output - 输出数据
   */
  handleSaveImageComplete(prompt_id, node_id, output) { // eslint-disable-line no-unused-vars
    console.log(`🎯 [ComfyUIClient] 处理SaveImage节点完成: ${node_id}`);

    // 检查是否有图像输出
    if (!output || !output.images || !Array.isArray(output.images) || output.images.length === 0) {
      console.log(`⚠️ [ComfyUIClient] SaveImage节点无图像输出: ${node_id}`);
      return;
    }

    console.log(`🖼️ [ComfyUIClient] SaveImage节点输出 ${output.images.length} 张图像`);

    // 处理每张图像
    output.images.forEach((imageData, index) => {
      console.log(`📸 [ComfyUIClient] 处理图像 ${index + 1}: ${imageData.filename}`);

      // 🔧 确保filename字段存在
      const filename = imageData.filename || imageData.name || `ComfyUI_${Date.now()}.png`;

      // 🔧 构建ComfyUI图片URL
      const imageUrl = this.buildImageUrl(
        filename,
        imageData.subfolder || '',
        imageData.type || 'output'
      );

      console.log(`🖼️ [ComfyUIClient] 构建ComfyUI图片URL: ${imageUrl}`);

      // 触发图像生成完成回调
      if (this.callbacks.onImageGenerated) {
        console.log(`🎨 [ComfyUIClient] 触发图像生成回调: ${filename}`);
        // 🔧 修复：使用正确的rowIndex，从当前任务参数中获取
        const rowIndex = this.currentRowIndex || 0;
        this.callbacks.onImageGenerated({
          filename: filename,
          path: filename,
          url: imageUrl,
          imageUrl: imageUrl,
          subfolder: imageData.subfolder || '',
          type: imageData.type || 'output',
          timestamp: new Date().toISOString(),
          rowIndex: rowIndex // 🔧 添加rowIndex到数据中
        });
      }

      // 🔧 新增：强制检查任务完成状态（防止遗漏）
      setTimeout(() => {
        if (this.currentPromptId) {
          console.log(`🔧 [ComfyUIClient] 延迟检查任务完成状态: ${this.currentPromptId}`);
          this.checkPromptCompletion(this.currentPromptId);
        }
      }, 1000); // 1秒后检查
    });
  }

  /**
   * 🎯 检查任务完成状态
   * @param {string} prompt_id - 任务ID
   */
  checkPromptCompletion(prompt_id) {
    const promptState = this.stateManager.prompts.get(prompt_id);
    console.log(`🔍 [ComfyUIClient] 检查任务完成状态: ${prompt_id}`, {
      存在状态: !!promptState,
      当前状态: promptState?.status,
      总节点数: promptState?.node_graph_info?.total_nodes,
      已执行节点: promptState?.executed_nodes?.size,
      已缓存节点: promptState?.cached_nodes?.size,
      完成节点总数: (promptState?.executed_nodes?.size || 0) + (promptState?.cached_nodes?.size || 0)
    });

    const isComplete = this.stateManager.isPromptComplete(prompt_id);
    console.log(`🔍 [ComfyUIClient] 任务完成检查结果: ${prompt_id} -> ${isComplete}`);

    if (isComplete) {
      console.log(`🎉 [ComfyUIClient] 任务完成: ${prompt_id}`);
      this.handlePromptComplete(prompt_id);
    }
  }

  /**
   * 🆕 统一处理图像输出
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Object} output - 输出数据
   * @param {string} source - 来源描述
   */




  /**
   * 🔧 构建图像URL
   * @param {string} filename - 文件名
   * @param {string} subfolder - 子文件夹
   * @param {string} type - 文件类型
   * @returns {string} 图像URL
   */
  buildImageUrl(filename, subfolder = '', type = 'output') {
    const params = new URLSearchParams({
      filename,
      type,
      serverUrl: this.comfyuiConfig?.serverUrl || 'http://localhost:8188'
    });

    if (subfolder) {
      params.append('subfolder', subfolder);
    }

    // 使用正确的后端代理路由
    const imageUrl = `http://localhost:8091/api/local/comfyui/view?${params.toString()}`;
    console.log(`🖼️ [ComfyUIClient] 构建图像URL: ${imageUrl}`);

    return imageUrl;
  }

  /**
   * 处理任务完成
   * @param {string} prompt_id - 任务ID
   */
  handlePromptComplete(prompt_id) {
    console.log(`🎉 [ComfyUIClient] 任务完成: ${prompt_id}`);

    this.stateManager.updateStatus(prompt_id, PromptStatus.COMPLETED);
    const finalOutputs = this.stateManager.getFinalOutputs(prompt_id);

    // 🔧 关键修复：如果这是当前任务，清理状态
    if (this.currentPromptId === prompt_id) {
      console.log(`🔧 [ComfyUIClient] 清理当前任务状态: ${prompt_id}`);
      this.currentPromptId = null;
      this.isGenerating = false;
    }

    // 🔧 关键修复：触发进度回调，通知前端任务完成
    if (this.callbacks.onPromptProgress) {
      console.log(`🔧 [ComfyUIClient] 触发完成进度回调: ${prompt_id}`);
      this.callbacks.onPromptProgress(prompt_id, {
        stage: 'completed',
        progress: 100,
        message: '图像生成完成',
        isCompleted: true,
        isProcessing: false,
        isError: false,
        isCancelled: false
      });
    }

    this.triggerCallback('onPromptComplete', prompt_id, finalOutputs);

    // 🔧 新增：延迟触发队列检查，确保前端状态已更新
    setTimeout(() => {
      console.log(`🔧 [ComfyUIClient] 延迟触发队列检查: ${prompt_id}`);
      if (this.callbacks.onQueueCheck) {
        this.callbacks.onQueueCheck();
      }
    }, 300);
  }

  // ===== 兼容性API方法 =====
  // 为了与现有的useImageGeneration composable兼容

  /**
   * 初始化服务（兼容性方法）
   */
  async initialize() {
    try {
      console.log('🎨 [ComfyUIClient] 兼容性初始化...');

      // 加载ComfyUI配置
      await this.loadComfyUISettings();

      // 验证配置完整性
      const isValid = this.validateConfiguration();
      if (!isValid) {
        console.warn('⚠️ [ComfyUIClient] 配置验证失败，但继续初始化');
      }

      return true;
    } catch (error) {
      console.error('❌ [ComfyUIClient] 初始化失败:', error);
      return false;
    }
  }

  /**
   * 加载ComfyUI设置
   */
  async loadComfyUISettings() {
    try {
      console.log('📁 [ComfyUIClient] 加载ComfyUI设置...');

      const response = await fetch('/api/local/load-user-settings?filename=comfyui-settings.json');

      if (!response.ok) {
        console.warn('⚠️ [ComfyUIClient] 无法加载ComfyUI设置，使用默认配置');
        return;
      }

      const result = await response.json();

      if (!result.success || !result.settings) {
        console.warn('⚠️ [ComfyUIClient] 服务器返回无效的ComfyUI设置数据');
        return;
      }

      const comfyuiSettings = result.settings.comfyui;
      if (!comfyuiSettings) {
        console.warn('⚠️ [ComfyUIClient] 设置文件中缺少comfyui配置');
        return;
      }

      // 更新配置
      this.comfyuiConfig = {
        serverUrl: comfyuiSettings.serverUrl || 'http://localhost:8188',
        batchSize: comfyuiSettings.batchSize || 1,
        currentWorkflow: comfyuiSettings.currentWorkflow,
        nodeMapping: comfyuiSettings.nodeMapping || {},
        defaultSettings: comfyuiSettings.defaultSettings || {},
        workflowName: comfyuiSettings.workflowName || ''
      };

      // 更新HTTP客户端配置
      if (this.httpClient) {
        this.httpClient.updateConfig({
          base_url: this.comfyuiConfig.serverUrl
        });
      }

      // 更新WebSocket管理器配置
      if (this.wsManager) {
        const wsUrl = this.comfyuiConfig.serverUrl.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws';
        this.wsManager.config.ws_url = wsUrl;
      }

      console.log('✅ [ComfyUIClient] ComfyUI设置加载成功');
      console.log('📊 [ComfyUIClient] 配置信息:', {
        serverUrl: this.comfyuiConfig.serverUrl,
        batchSize: this.comfyuiConfig.batchSize,
        workflowName: this.comfyuiConfig.workflowName,
        hasWorkflow: !!this.comfyuiConfig.currentWorkflow,
        nodeMapping: Object.keys(this.comfyuiConfig.nodeMapping)
      });

    } catch (error) {
      console.error('❌ [ComfyUIClient] 加载ComfyUI设置失败:', error);
    }
  }

  /**
   * 验证配置完整性
   */
  validateConfiguration() {
    try {
      const issues = [];

      // 检查基本配置
      if (!this.comfyuiConfig.serverUrl) {
        issues.push('缺少服务器地址');
      }

      if (!this.comfyuiConfig.currentWorkflow) {
        issues.push('缺少工作流配置');
      }

      // 检查节点映射
      const requiredMappings = ['positivePromptNode', 'saveImageNode'];
      for (const mapping of requiredMappings) {
        if (!this.comfyuiConfig.nodeMapping[mapping]) {
          issues.push(`缺少必需的节点映射: ${mapping}`);
        }
      }

      if (issues.length > 0) {
        console.warn('⚠️ [ComfyUIClient] 配置验证发现问题:', issues);
        return false;
      }

      console.log('✅ [ComfyUIClient] 配置验证通过');
      return true;
    } catch (error) {
      console.error('❌ [ComfyUIClient] 配置验证失败:', error);
      return false;
    }
  }

  /**
   * 检查服务是否可用（兼容性方法）
   */
  isAvailable() {
    // 检查配置是否完整
    return !!(this.comfyuiConfig.serverUrl &&
              this.comfyuiConfig.currentWorkflow &&
              this.comfyuiConfig.nodeMapping.positivePromptNode &&
              this.comfyuiConfig.nodeMapping.saveImageNode);
  }

  /**
   * 取消当前生成任务
   * @param {string} promptId - 要取消的任务ID
   * @returns {Promise<boolean>} 取消是否成功
   */
  async cancelGeneration(promptId = null) {
    try {
      console.log('🛑 [ComfyUIClient] 开始取消生成任务:', promptId);

      // 如果没有指定promptId，使用当前正在执行的任务
      const targetPromptId = promptId || this.currentPromptId;

      if (!targetPromptId) {
        console.warn('⚠️ [ComfyUIClient] 没有找到要取消的任务ID');
        return false;
      }

      // 发送interrupt命令到ComfyUI
      console.log('🛑 [ComfyUIClient] 发送interrupt命令到:', this.comfyuiConfig.serverUrl);
      const result = await this.httpClient.interrupt();

      if (result && result.success) {
        console.log('✅ [ComfyUIClient] 成功发送取消命令到ComfyUI');

        // 清理当前状态
        this.currentPromptId = null;
        this.isGenerating = false;

        // 触发取消事件
        this.callbacks.onCancelled && this.callbacks.onCancelled(targetPromptId);

        console.log('🔧 [ComfyUIClient] 状态已清理，取消完成');

        // 🔧 返回UI期望的格式
        return {
          success: true,
          interruptSent: true,
          queueCleared: true,
          verificationPassed: true,
          errors: []
        };
      } else {
        console.error('❌ [ComfyUIClient] 发送取消命令失败:', result);

        // 🔧 返回UI期望的格式
        return {
          success: false,
          interruptSent: false,
          queueCleared: false,
          verificationPassed: false,
          errors: [result?.error || '取消命令发送失败']
        };
      }
    } catch (error) {
      console.error('❌ [ComfyUIClient] 取消生成时出错:', error);
      return false;
    }
  }

  /**
   * 强制重置状态
   */
  forceStateReset() {
    console.log('🔧 [ComfyUIClient] 执行强制状态重置');
    this.currentPromptId = null;
    this.isGenerating = false;
    this.callbacks.onStateReset && this.callbacks.onStateReset();
  }

  /**
   * 验证状态一致性
   */
  validateStateConsistency() {
    console.log('🔍 [ComfyUIClient] 验证状态一致性');
    return {
      isGenerating: this.isGenerating,
      currentPromptId: this.currentPromptId,
      isConnected: this.webSocketManager?.isConnected || false
    };
  }

  /**
   * 测试工作流构建和提交
   * @param {string} testPrompt - 测试提示词
   */
  async testWorkflowSubmission(testPrompt = "1 girl") {
    try {
      console.log('🧪 [ComfyUIClient] 开始测试工作流提交...');

      // 确保已初始化
      if (!this.comfyuiConfig.currentWorkflow) {
        await this.initialize();
      }

      // 构建测试工作流
      const testWorkflow = this.buildWorkflow(testPrompt, "");

      console.log('🧪 [ComfyUIClient] 测试工作流构建完成，节点数:', Object.keys(testWorkflow).length);

      // 连接WebSocket
      await this.connect();

      // 提交工作流
      const prompt_id = await this.submitWorkflow(testWorkflow);

      if (prompt_id) {
        // 🔧 设置当前任务ID，以便取消功能可以使用
        this.currentPromptId = prompt_id;
        this.isGenerating = true;

        // 🔧 监听任务完成事件，自动清理状态
        const cleanupHandler = (completed_prompt_id) => {
          if (completed_prompt_id === prompt_id) {
            console.log('🔧 [ComfyUIClient] 测试任务完成，清理状态:', completed_prompt_id);
            this.currentPromptId = null;
            this.isGenerating = false;
            // 移除监听器
            this.off('promptComplete', cleanupHandler);
            this.off('promptError', cleanupHandler);
          }
        };

        this.on('promptComplete', cleanupHandler);
        this.on('promptError', cleanupHandler);

        console.log('✅ [ComfyUIClient] 测试工作流提交成功，任务ID:', prompt_id);
        return { success: true, prompt_id, workflow: testWorkflow };
      } else {
        throw new Error('工作流提交失败');
      }

    } catch (error) {
      console.error('❌ [ComfyUIClient] 测试工作流提交失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止WebSocket重连
   */
  stopReconnecting() {
    if (this.webSocketManager) {
      this.webSocketManager.stopReconnecting();
      console.log('🛑 [ComfyUIClient] WebSocket重连已停止');
    }
  }

  /**
   * 重新启用WebSocket重连
   */
  enableReconnecting() {
    if (this.webSocketManager) {
      this.webSocketManager.enableReconnecting();
      console.log('🔄 [ComfyUIClient] WebSocket重连已重新启用');
    }
  }

  /**
   * 获取WebSocket状态
   */
  getWebSocketStatus() {
    if (this.webSocketManager) {
      return this.webSocketManager.getStatus();
    }
    return { isConnected: false, message: 'WebSocket管理器未初始化' };
  }

  /**
   * 获取服务状态（兼容性方法）
   */
  getStatus() {
    return {
      isAvailable: this.isAvailable(),
      isConnected: this.wsManager ? this.wsManager.isConnected : false,
      config: this.config
    };
  }

  /**
   * 生成图像（兼容性方法）
   * @param {Object} params - 生成参数
   */
  async generateImage(params) {
    try {
      console.log('🎨 [ComfyUIClient] 开始生成图像...');

      // 提取参数
      const {
        positivePrompt,
        negativePrompt = '',
        projectTitle,
        chapterTitle,
        rowIndex = 0, // 🔧 新增：行索引参数
        onProgress,
        onError,
        onImageGenerated,
        isQueueTask = false // 🔧 新增：标识是否为队列任务
      } = params;

      // 🆕 设置当前项目和章节信息，用于SaveImage节点配置
      this.currentProjectTitle = projectTitle;
      this.currentChapterTitle = chapterTitle;
      this.currentRowIndex = rowIndex; // 🔧 新增：设置当前行索引

      // 🔧 关键修复：如果不是队列任务，检查是否需要排队
      if (!isQueueTask) {
        console.log('🔍 [ComfyUIClient] 检查是否需要排队...');

        // 检查当前是否有任务在执行
        if (this.isGenerating) {
          console.log('❌ [ComfyUIClient] 当前有任务在执行，拒绝新任务');
          throw new Error('当前有图像生成任务正在执行，请等待完成后再试');
        }
      } else {
        console.log('🔧 [ComfyUIClient] 队列任务，允许强制执行');
      }

      // 构建简单的工作流（这里需要根据实际需求构建）
      const workflow = this.buildWorkflow(positivePrompt, negativePrompt);

      // 设置回调
      if (onProgress) {
        this.on('promptProgress', (_, progressInfo) => {
          onProgress({
            stage: 'generating',
            progress: progressInfo.percent,
            message: `生成进度: ${progressInfo.percent}%`,
            isProcessing: true,
            isCompleted: false,
            isError: false,
            isCancelled: false
          });
        });
      }

      // 🆕 设置图片生成回调（通过handleSaveImageComplete统一处理）
      if (onImageGenerated) {
        this.callbacks.onImageGenerated = onImageGenerated;
      }

      // 连接并提交工作流
      await this.connect();
      const prompt_id = await this.submitWorkflow(workflow);

      if (!prompt_id) {
        throw new Error('工作流提交失败');
      }

      // 🔧 设置当前任务ID，以便取消功能可以使用
      this.currentPromptId = prompt_id;
      this.isGenerating = true;
      console.log('🔧 [ComfyUIClient] 设置当前任务ID:', prompt_id);

      // 等待任务完成 - 使用Promise而不是覆盖全局回调
      return new Promise((resolve, reject) => {
        // 🔧 修复：保存原有回调，避免覆盖
        const originalOnPromptComplete = this.callbacks.onPromptComplete;
        const originalOnPromptError = this.callbacks.onPromptError;

        // 创建临时回调处理器
        const tempCompleteHandler = (completed_prompt_id, finalOutputs) => {
          if (completed_prompt_id === prompt_id) {
            console.log('🎉 [ComfyUIClient] 图像生成完成 (Promise回调)');

            // 🔧 重要：不在这里清理状态，让handlePromptComplete处理
            // 状态清理由handlePromptComplete统一处理，避免重复清理

            // 恢复原有回调
            this.callbacks.onPromptComplete = originalOnPromptComplete;
            this.callbacks.onPromptError = originalOnPromptError;

            resolve([{ success: true, outputs: finalOutputs }]);
          } else if (originalOnPromptComplete) {
            // 转发给原有回调
            originalOnPromptComplete(completed_prompt_id, finalOutputs);
          }
        };

        const tempErrorHandler = (error_prompt_id, errorData) => {
          if (error_prompt_id === prompt_id) {
            console.log('❌ [ComfyUIClient] 图像生成失败 (Promise回调)');

            // 🔧 重要：不在这里清理状态，让handlePromptComplete处理
            // 状态清理由错误处理统一处理

            // 恢复原有回调
            this.callbacks.onPromptComplete = originalOnPromptComplete;
            this.callbacks.onPromptError = originalOnPromptError;

            const error = new Error(errorData.exception_message || '生成失败');
            if (onError) onError(error);
            reject(error);
          } else if (originalOnPromptError) {
            // 转发给原有回调
            originalOnPromptError(error_prompt_id, errorData);
          }
        };

        // 设置临时回调
        this.callbacks.onPromptComplete = tempCompleteHandler;
        this.callbacks.onPromptError = tempErrorHandler;
      });

    } catch (error) {
      console.error('❌ [ComfyUIClient] 生成图像失败:', error);
      if (params.onError) params.onError(error);
      throw error;
    }
  }

  /**
   * 构建工作流
   * @param {string} positivePrompt - 正向提示词
   * @param {string} negativePrompt - 负向提示词
   */
  buildWorkflow(positivePrompt, negativePrompt = '') {
    try {
      // 检查是否有配置的工作流
      if (!this.comfyuiConfig.currentWorkflow) {
        throw new Error('未配置ComfyUI工作流，请在设置中上传工作流文件');
      }

      // 深拷贝工作流以避免修改原始配置
      const workflow = JSON.parse(JSON.stringify(this.comfyuiConfig.currentWorkflow));

      // 应用提示词到工作流
      this.applyPromptsToWorkflow(workflow, positivePrompt, negativePrompt);

      // 应用其他设置
      this.applySettingsToWorkflow(workflow);



      console.log('🔧 [ComfyUIClient] 工作流构建完成');
      return workflow;

    } catch (error) {
      console.error('❌ [ComfyUIClient] 构建工作流失败:', error);

      // 诊断工作流问题
      if (this.comfyuiConfig.currentWorkflow) {
        const issues = this.diagnoseWorkflow(this.comfyuiConfig.currentWorkflow);
        if (issues.length > 0) {
          console.error('🔍 [ComfyUIClient] 工作流诊断发现问题:', issues);
        }
      }

      // 抛出错误而不是使用备用工作流
      throw new Error(`工作流构建失败: ${error.message}`);
    }
  }

  /**
   * 应用提示词到工作流
   * @param {Object} workflow - 工作流对象
   * @param {string} positivePrompt - 正向提示词
   * @param {string} negativePrompt - 负向提示词
   */
  applyPromptsToWorkflow(workflow, positivePrompt, negativePrompt) {
    const nodeMapping = this.comfyuiConfig.nodeMapping;

    // 设置正向提示词
    if (nodeMapping.positivePromptNode && workflow[nodeMapping.positivePromptNode]) {
      const positiveNode = workflow[nodeMapping.positivePromptNode];
      const originalText = positiveNode.inputs.text || '';

      // 将用户提示词与原有提示词合并
      const combinedPositivePrompt = positivePrompt ? `${positivePrompt}, ${originalText}` : originalText;
      positiveNode.inputs.text = combinedPositivePrompt;

      console.log(`📝 [ComfyUIClient] 设置正向提示词到节点 ${nodeMapping.positivePromptNode}:`, combinedPositivePrompt.substring(0, 100) + '...');
    } else {
      console.warn(`⚠️ [ComfyUIClient] 未找到正向提示词节点: ${nodeMapping.positivePromptNode}`);
    }

    // 设置负向提示词
    if (nodeMapping.negativePromptNode && workflow[nodeMapping.negativePromptNode]) {
      const negativeNode = workflow[nodeMapping.negativePromptNode];
      const originalText = negativeNode.inputs.text || '';

      // 将用户提示词与原有提示词合并
      const combinedNegativePrompt = negativePrompt ? `${negativePrompt}, ${originalText}` : originalText;
      negativeNode.inputs.text = combinedNegativePrompt;

      console.log(`📝 [ComfyUIClient] 设置负向提示词到节点 ${nodeMapping.negativePromptNode}:`, combinedNegativePrompt.substring(0, 100) + '...');
    } else {
      console.warn(`⚠️ [ComfyUIClient] 未找到负向提示词节点: ${nodeMapping.negativePromptNode}`);
    }
  }

  /**
   * 应用其他设置到工作流
   * @param {Object} workflow - 工作流对象
   */
  applySettingsToWorkflow(workflow) {
    const nodeMapping = this.comfyuiConfig.nodeMapping;
    const defaultSettings = this.comfyuiConfig.defaultSettings;

    console.log('🔧 [ComfyUIClient] 应用设置到工作流:', {
      nodeMapping,
      defaultSettings,
      batchSize: this.comfyuiConfig.batchSize
    });

    // 设置图像尺寸
    if (nodeMapping.imageSizeNode && workflow[nodeMapping.imageSizeNode]) {
      const node = workflow[nodeMapping.imageSizeNode];
      if (defaultSettings.width) {
        node.inputs.width = defaultSettings.width;
        console.log(`📐 [ComfyUIClient] 设置宽度: ${defaultSettings.width}`);
      }
      if (defaultSettings.height) {
        node.inputs.height = defaultSettings.height;
        console.log(`📐 [ComfyUIClient] 设置高度: ${defaultSettings.height}`);
      }
    }

    // 🔧 批次大小处理：不在ComfyUI工作流中设置batch_size，而是在应用层面调用多次API
    if (nodeMapping.batchSizeNode && workflow[nodeMapping.batchSizeNode]) {
      // 确保ComfyUI工作流中的batch_size始终为1，因为我们在应用层面处理批量
      workflow[nodeMapping.batchSizeNode].inputs.batch_size = 1;
      console.log(`📊 [ComfyUIClient] ComfyUI工作流batch_size设置为1 (应用层面批量: ${this.comfyuiConfig.batchSize}次)`);
    } else {
      console.log(`📊 [ComfyUIClient] 应用层面批量生成: ${this.comfyuiConfig.batchSize}次API调用`);
    }

    // 设置采样器参数
    if (nodeMapping.samplerNode && workflow[nodeMapping.samplerNode]) {
      const node = workflow[nodeMapping.samplerNode];
      if (defaultSettings.steps) {
        node.inputs.steps = defaultSettings.steps;
        console.log(`⚙️ [ComfyUIClient] 设置步数: ${defaultSettings.steps}`);
      }
      if (defaultSettings.cfg) {
        node.inputs.cfg = defaultSettings.cfg;
        console.log(`⚙️ [ComfyUIClient] 设置CFG: ${defaultSettings.cfg}`);
      }
      if (defaultSettings.sampler) {
        node.inputs.sampler_name = defaultSettings.sampler;
        console.log(`⚙️ [ComfyUIClient] 设置采样器: ${defaultSettings.sampler}`);
      }
    }



    // 设置随机种子
    if (nodeMapping.seedNode && workflow[nodeMapping.seedNode]) {
      const seedValue = this.comfyuiConfig.seedValue || Math.floor(Math.random() * 1000000);
      workflow[nodeMapping.seedNode].inputs.seed = seedValue;
      console.log(`🎲 [ComfyUIClient] 设置种子: ${seedValue}`);
    }

    // 为所有KSampler节点设置随机种子（如果没有专门的种子节点）
    if (!nodeMapping.seedNode) {
      for (const [nodeId, node] of Object.entries(workflow)) {
        if (node.class_type === 'KSampler' && node.inputs.seed !== undefined) {
          const randomSeed = Math.floor(Math.random() * 1000000000);
          node.inputs.seed = randomSeed;
          console.log(`🎲 [ComfyUIClient] 为KSampler节点 ${nodeId} 设置随机种子: ${randomSeed}`);
        }
      }
    }
  }

  /**
   * 构建备用工作流
   */
  buildFallbackWorkflow() {
    console.warn('⚠️ [ComfyUIClient] 使用备用工作流，请检查ComfyUI设置中的工作流配置');

    // 抛出错误而不是使用可能无效的备用工作流
    throw new Error('ComfyUI工作流配置无效。请在设置中上传有效的工作流文件，并确保：\n' +
      '1. 工作流文件是从ComfyUI导出的有效JSON\n' +
      '2. 节点映射配置正确\n' +
      '3. 指定的模型文件在ComfyUI服务器上存在');
  }

  /**
   * 诊断工作流问题
   * @param {Object} workflow - 工作流对象
   */
  diagnoseWorkflow(workflow) {
    const issues = [];

    if (!workflow || typeof workflow !== 'object') {
      issues.push('工作流为空或格式无效');
      return issues;
    }

    // 检查是否有节点
    const nodeIds = Object.keys(workflow);
    if (nodeIds.length === 0) {
      issues.push('工作流中没有节点');
      return issues;
    }

    // 检查常见的节点类型
    const nodeTypes = {};
    for (const [nodeId, node] of Object.entries(workflow)) {
      if (!node.class_type) {
        issues.push(`节点 ${nodeId} 缺少 class_type`);
        continue;
      }

      nodeTypes[node.class_type] = nodeTypes[node.class_type] || [];
      nodeTypes[node.class_type].push(nodeId);

      // 检查CheckpointLoader节点的模型文件
      if (node.class_type === 'CheckpointLoaderSimple') {
        const modelName = node.inputs?.ckpt_name;
        if (!modelName) {
          issues.push(`节点 ${nodeId} (CheckpointLoaderSimple) 缺少模型文件名`);
        } else if (modelName === 'model.safetensors') {
          issues.push(`节点 ${nodeId} 使用默认模型名 "${modelName}"，请确保该模型在ComfyUI服务器上存在`);
        }
      }
    }

    // 检查必需的节点类型
    if (!nodeTypes['CheckpointLoaderSimple'] && !nodeTypes['CheckpointLoader']) {
      issues.push('工作流中缺少模型加载节点 (CheckpointLoaderSimple)');
    }

    if (!nodeTypes['SaveImage'] && !nodeTypes['PreviewImage']) {
      issues.push('工作流中缺少图像保存节点 (SaveImage)');
    }

    console.log('🔍 [ComfyUIClient] 工作流诊断结果:', {
      nodeCount: nodeIds.length,
      nodeTypes: Object.keys(nodeTypes),
      issues: issues
    });

    return issues;
  }
}

// ===== 导出 =====
export { ComfyUIClient };

// 为了兼容现有代码，创建一个默认实例
const comfyuiImageGeneration = new ComfyUIClient();

// 添加到全局对象以便在浏览器控制台中测试
if (typeof window !== 'undefined') {
  window.comfyuiImageGeneration = comfyuiImageGeneration;

  window.testComfyUI = async (prompt = "1 girl") => {
    console.log('🧪 开始测试ComfyUI工作流提交...');
    try {
      const result = await comfyuiImageGeneration.testWorkflowSubmission(prompt);
      console.log('🧪 测试结果:', result);
      return result;
    } catch (error) {
      console.error('🧪 测试失败:', error);
      return { success: false, error: error.message };
    }
  };

  // WebSocket控制函数
  window.stopComfyUIReconnect = () => {
    comfyuiImageGeneration.stopReconnecting();
    console.log('🛑 ComfyUI WebSocket重连已停止');
  };

  window.enableComfyUIReconnect = () => {
    comfyuiImageGeneration.enableReconnecting();
    console.log('🔄 ComfyUI WebSocket重连已重新启用');
  };

  window.getComfyUIStatus = () => {
    const status = comfyuiImageGeneration.getWebSocketStatus();
    console.log('📊 ComfyUI WebSocket状态:', status);
    return status;
  };
}

export default comfyuiImageGeneration;

