<template>
  <div
    class="image-preview-overlay"
    v-if="visible"
    @click="handleClose"
  >
    <div
      class="image-preview-container"
      @click.stop
    >
      <div class="preview-header">
        <h3>图片预览</h3>
        <button
          class="close-btn"
          @click="handleClose"
        >
          ×
        </button>
      </div>
      
      <div class="preview-content">
        <div
          class="image-container"
          ref="imageContainer"
          @wheel.prevent="handleWheel"
        >
          <img
            ref="previewImage"
            :src="imageSrc"
            :alt="imageAlt"
            class="preview-image"
            @load="handleImageLoad"
            :style="imageStyle"
          >
        </div>
      </div>
      
      <div class="preview-controls">
        <button @click="resetZoom">
          重置
        </button>
        <button @click="toggleFullscreen">
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </button>
        <span class="scale-info">{{ Math.round(scale * 100) }}%</span>
        <span class="zoom-hint">使用鼠标滚轮缩放</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreviewModal',
  emits: ['close'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    imageSrc: {
      type: String,
      default: ''
    },
    imageAlt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      scale: 1,
      minScale: 0.1,
      maxScale: 5,
      isFullscreen: false,
      imageLoaded: false
    };
  },
  computed: {
    imageStyle() {
      return {
        transform: `scale(${this.scale})`,
        transformOrigin: 'center center'
      };
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetZoom();
        document.addEventListener('keydown', this.handleKeydown);
      } else {
        document.removeEventListener('keydown', this.handleKeydown);
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    
    handleImageLoad() {
      this.imageLoaded = true;
      this.resetZoom();
    },
    
    zoomIn() {
      this.scale = Math.min(this.scale * 1.2, this.maxScale);
    },
    
    zoomOut() {
      this.scale = Math.max(this.scale / 1.2, this.minScale);
    },
    
    resetZoom() {
      this.scale = 1;
    },
    
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        this.$refs.imageContainer.requestFullscreen();
        this.isFullscreen = true;
      } else {
        document.exitFullscreen();
        this.isFullscreen = false;
      }
    },
    
    handleKeydown(event) {
      switch (event.key) {
        case 'Escape':
          this.handleClose();
          break;
        case '+':
        case '=':
          this.zoomIn();
          break;
        case '-':
          this.zoomOut();
          break;
        case '0':
          this.resetZoom();
          break;
      }
    },

    handleWheel(event) {
      // 阻止页面滚动
      event.preventDefault();

      // 获取滚轮方向
      const delta = event.deltaY;

      // 计算缩放步长（更小的步长使缩放更平滑）
      const zoomStep = 0.1;

      // 计算新的缩放比例
      let newScale;
      if (delta < 0) {
        // 向上滚动，放大
        newScale = Math.min(this.scale + zoomStep, this.maxScale);
      } else {
        // 向下滚动，缩小
        newScale = Math.max(this.scale - zoomStep, this.minScale);
      }

      // 如果缩放比例没有变化，直接返回
      if (newScale === this.scale) {
        return;
      }

      // 获取鼠标在容器中的位置（为将来的以鼠标为中心缩放预留）
      // const container = this.$refs.imageContainer;
      // const rect = container.getBoundingClientRect();
      // const mouseX = event.clientX - rect.left;
      // const mouseY = event.clientY - rect.top;

      // 更新缩放比例
      this.scale = newScale;

      // 为了更好的用户体验，保持图片居中
      // 在实际应用中，可以根据需要实现更复杂的以鼠标为中心的缩放
      this.$nextTick(() => {
        // 确保图片在缩放后保持在视图中心
        if (this.$refs.previewImage) {
          this.$refs.previewImage.style.transformOrigin = 'center center';
        }
      });
    }
  },
  
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleKeydown);
  }
}
</script>

<style scoped>
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.image-preview-container {
  background: #2a2a2a;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  color: #e0e0e0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #444;
}

.preview-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #e0e0e0;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.image-container {
  max-width: 100%;
  max-height: 100%;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.2s ease;
}

.preview-controls {
  display: flex;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #444;
  align-items: center;
}

.preview-controls button {
  padding: 8px 12px;
  background: #444;
  border: none;
  border-radius: 4px;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
}

.preview-controls button:hover:not(:disabled) {
  background: #555;
}

.preview-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.scale-info {
  font-size: 14px;
  color: #ccc;
  margin-right: 15px;
}

.zoom-hint {
  margin-left: auto;
  font-size: 12px;
  color: #999;
  font-style: italic;
}
</style>
