/**
 * IV. 核心模块: PromptStateManager
 * 
 * 职责：
 * - 维护所有已提交 prompt_id 的状态
 * - 提供接口来创建、更新和查询每个 prompt 的状态
 * - 跟踪节点的执行情况，以判断 prompt 是否整体完成
 */

import { PromptStatus } from '../comfyuiImageGeneration.js';

export class PromptStateManager {
  constructor() {
    // 存储所有任务状态
    this.prompts = new Map(); // prompt_id -> PromptState
    
    console.log('📊 [PromptStateManager] 初始化完成');
  }

  /**
   * 添加新的任务
   * @param {string} prompt_id - 任务ID
   * @param {Object} workflow_api_json - 工作流JSON
   */
  addPrompt(prompt_id, workflow_api_json) {
    const promptState = {
      prompt_id,
      status: PromptStatus.QUEUED,
      workflow_api_json,
      node_graph_info: this.parseNodeGraph(workflow_api_json),
      executing_node_id: null,
      progress_details: null,
      executed_nodes: new Set(),
      cached_nodes: new Set(),
      outputs: {},
      error_info: null,
      start_time: null,
      end_time: null
    };

    this.prompts.set(prompt_id, promptState);
    console.log(`📊 [PromptStateManager] 添加任务: ${prompt_id}`);
    return promptState;
  }

  /**
   * 更新任务状态
   * @param {string} prompt_id - 任务ID
   * @param {string} new_status - 新状态
   */
  updateStatus(prompt_id, new_status) {
    const promptState = this.prompts.get(prompt_id);
    if (!promptState) {
      console.warn(`⚠️ [PromptStateManager] 任务不存在: ${prompt_id}`);
      return false;
    }

    const oldStatus = promptState.status;
    promptState.status = new_status;

    // 设置时间戳
    if (new_status === PromptStatus.RUNNING && !promptState.start_time) {
      promptState.start_time = new Date();
    }

    if ([PromptStatus.COMPLETED, PromptStatus.ERROR, PromptStatus.INTERRUPTED].includes(new_status)) {
      promptState.end_time = new Date();
    }

    console.log(`📊 [PromptStateManager] 状态更新: ${prompt_id} ${oldStatus} -> ${new_status}`);
    return true;
  }

  /**
   * 设置当前执行的节点
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   */
  setExecutingNode(prompt_id, node_id) {
    const promptState = this.prompts.get(prompt_id);
    if (promptState) {
      promptState.executing_node_id = node_id;
    }
  }

  /**
   * 设置节点进度
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {number} value - 当前值
   * @param {number} max_val - 最大值
   */
  setNodeProgress(prompt_id, node_id, value, max_val) {
    const promptState = this.prompts.get(prompt_id);
    if (promptState) {
      promptState.progress_details = {
        node_id,
        value,
        max: max_val,
        percent: max_val > 0 ? Math.round((value / max_val) * 100) : 0
      };
    }
  }

  /**
   * 标记节点执行完成
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Object} output_data - 输出数据
   */
  markNodeExecuted(prompt_id, node_id, output_data) {
    const promptState = this.prompts.get(prompt_id);
    if (promptState) {
      promptState.executed_nodes.add(node_id);
      if (output_data) {
        promptState.outputs[node_id] = output_data;
      }
    }
  }

  /**
   * 标记节点缓存执行
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   */
  markNodeCached(prompt_id, node_id) {
    const promptState = this.prompts.get(prompt_id);
    if (promptState) {
      promptState.cached_nodes.add(node_id);
    }
  }

  /**
   * 设置错误信息
   * @param {string} prompt_id - 任务ID
   * @param {Object} error_data - 错误数据
   */
  setError(prompt_id, error_data) {
    const promptState = this.prompts.get(prompt_id);
    if (promptState) {
      promptState.status = PromptStatus.ERROR;
      promptState.error_info = error_data;
      promptState.end_time = new Date();
    }
  }

  /**
   * 获取任务状态
   * @param {string} prompt_id - 任务ID
   * @returns {Object|null} 任务状态或null
   */
  getPrompt(prompt_id) {
    return this.prompts.get(prompt_id) || null;
  }

  /**
   * 获取所有任务状态
   * @returns {Object} prompt_id -> PromptState 的映射
   */
  getAllPrompts() {
    const result = {};
    for (const [prompt_id, promptState] of this.prompts) {
      result[prompt_id] = promptState;
    }
    return result;
  }

  /**
   * 判断任务是否完成
   * @param {string} prompt_id - 任务ID
   * @returns {boolean} 是否完成
   */
  isPromptComplete(prompt_id) {
    const promptState = this.prompts.get(prompt_id);
    if (!promptState) {
      return false;
    }

    // 如果已经是终止状态，直接返回
    if ([PromptStatus.COMPLETED, PromptStatus.ERROR, PromptStatus.INTERRUPTED].includes(promptState.status)) {
      return true;
    }

    // 检查所有节点是否都已执行或缓存
    const totalNodes = promptState.node_graph_info.total_nodes;
    const completedNodes = promptState.executed_nodes.size + promptState.cached_nodes.size;
    
    return completedNodes >= totalNodes;
  }

  /**
   * 获取最终输出
   * @param {string} prompt_id - 任务ID
   * @returns {Object} 最终输出
   */
  getFinalOutputs(prompt_id) {
    const promptState = this.prompts.get(prompt_id);
    return promptState ? promptState.outputs : {};
  }

  /**
   * 解析节点图信息
   * @param {Object} workflow_api_json - 工作流JSON
   * @returns {Object} 节点图信息
   */
  parseNodeGraph(workflow_api_json) {
    const nodes = Object.keys(workflow_api_json || {});
    return {
      total_nodes: nodes.length,
      node_ids: nodes,
      save_nodes: nodes.filter(id => {
        const node = workflow_api_json[id];
        return node && (
          node.class_type === 'SaveImage' ||
          node.class_type === 'PreviewImage'
        );
      })
    };
  }

  /**
   * 清理已完成的任务
   * @param {number} maxAge - 最大保留时间（毫秒）
   */
  cleanup(maxAge = 300000) { // 默认5分钟
    const now = Date.now();
    const toRemove = [];

    for (const [prompt_id, promptState] of this.prompts) {
      if (promptState.end_time && (now - promptState.end_time.getTime()) > maxAge) {
        toRemove.push(prompt_id);
      }
    }

    toRemove.forEach(prompt_id => {
      this.prompts.delete(prompt_id);
      console.log(`🗑️ [PromptStateManager] 清理过期任务: ${prompt_id}`);
    });

    return toRemove.length;
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      total: this.prompts.size,
      by_status: {}
    };

    // 初始化状态计数
    Object.values(PromptStatus).forEach(status => {
      stats.by_status[status] = 0;
    });

    // 统计各状态数量
    for (const promptState of this.prompts.values()) {
      stats.by_status[promptState.status]++;
    }

    return stats;
  }
}
