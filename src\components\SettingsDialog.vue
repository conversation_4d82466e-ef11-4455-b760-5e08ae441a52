<template>
  <BaseDialog
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    dialog-class="settings-dialog"
    title="系统设置"
  >
    <div class="settings-container">
      <!-- 左侧导航 -->
      <div class="settings-sidebar">
        <div class="sidebar-menu">
          <div 
            v-for="(item, index) in activeMenuItems" 
            :key="index" 
            :class="['menu-item', { active: activeMenu === item.id }]"
            @click="activeMenu = item.id"
          >
            <i :class="item.icon" />
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容区 -->
      <div class="settings-content">
        <!-- LLM 设置 -->
        <div
          v-if="activeMenu === 'llm'"
          class="settings-panel"
        >
          <h2 class="panel-title">
            大语言模型设置
          </h2>
          
          <div class="settings-form">
            <div class="form-row">
              <div class="form-group">
                <label for="llm-provider">服务提供商</label>
                <select
                  id="llm-provider"
                  v-model="settings.llm.provider"
                  class="form-input"
                >
                  <option value="openrouter">
                    Open Router
                  </option>
                  <option value="google">
                    Google Gemini
                  </option>
                  <option value="openai">
                    OpenAI
                  </option>
                  <option value="anthropic">
                    Anthropic
                  </option>
                  <option value="local">
                    本地模型
                  </option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="default-model">默认模型</label>
                <select
                  id="default-model"
                  v-model="settings.llm.defaultModel"
                  class="form-input"
                >
                  <template v-if="settings.llm.provider === 'openrouter'">
                    <option value="deepseek/deepseek-r1:free">
                      DeepSeek R1 (免费)
                    </option>
                    <option value="qwen/qwen3-235b-a22b:free">
                      Qwen3-235B-A22B (免费)
                    </option>
                    <option value="tngtech/deepseek-r1t-chimera:free">
                      DeepSeek R1T Chimera (免费)
                    </option>
                    <option value="thudm/glm-4-9b:free">
                      GLM-4-9B (免费)
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'google'">
                    <option
                      v-for="model in activeGeminiModels"
                      :key="model.id"
                      :value="model.id"
                    >
                      {{ model.displayName }}
                    </option>
                    <!-- 如果没有获取到模型列表，显示以下默认选项 -->
                    <option
                      v-if="!activeGeminiModels.length"
                      value="gemini-pro"
                    >
                      Gemini Pro
                    </option>
                    <option
                      v-if="!activeGeminiModels.length"
                      value="gemini-1.5-pro"
                    >
                      Gemini 1.5 Pro
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'openai'">
                    <option value="gpt-3.5-turbo">
                      GPT-3.5 Turbo
                    </option>
                    <option value="gpt-4">
                      GPT-4
                    </option>
                    <option value="gpt-4-turbo">
                      GPT-4 Turbo
                    </option>
                    <option value="gpt-4o">
                      GPT-4o
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'anthropic'">
                    <option value="claude-3-haiku-20240307">
                      Claude 3 Haiku
                    </option>
                    <option value="claude-3-sonnet-20240229">
                      Claude 3 Sonnet
                    </option>
                    <option value="claude-3-opus-20240229">
                      Claude 3 Opus
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'local'">
                    <option value="local-model">
                      本地默认模型
                    </option>
                    <option value="llama3">
                      Llama 3
                    </option>
                    <option value="mistral">
                      Mistral
                    </option>
                  </template>
                </select>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="temperature">温度 (创造性)</label>
                <div class="slider-container">
                  <input 
                    type="range" 
                    id="temperature" 
                    v-model.number="settings.llm.temperature" 
                    min="0" 
                    max="1" 
                    step="0.1" 
                    class="form-slider"
                  >
                  <span class="slider-value">{{ settings.llm.temperature }}</span>
                </div>
                <div class="form-hint">
                  较低的值使结果更确定，较高的值使结果更多样化和创造性
                </div>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="max-output-tokens">最大输出Token数</label>
                <input
                  type="number"
                  id="max-output-tokens"
                  v-model.number="settings.llm.maxOutputTokens"
                  class="form-input"
                  min="1"
                  max="65536"
                  placeholder="4096"
                >
                <div class="form-hint">
                  控制每次AI生成的最大输出Token数。建议4096~65536，过大可能导致API报错或消耗过多配额。
                </div>
              </div>
            </div>
            
            <!-- API密钥输入 -->
            <div class="form-row">
              <div class="form-group api-key-group">
                <label for="api-key">API密钥</label>
                <div class="api-input-group">
                  <div class="input-wrapper">
                    <input 
                      :type="showPassword ? 'text' : 'password'" 
                      id="api-key" 
                      v-model="getCurrentApiKey" 
                      class="form-input with-icon"
                      :placeholder="`请输入${settings.llm.provider}的API密钥`"
                    >
                    <button 
                      class="password-toggle-button" 
                      type="button" 
                      @click="showPassword = !showPassword"
                      :title="showPassword ? '隐藏密钥' : '显示密钥'"
                    >
                      <i :class="showPassword ? 'ri-eye-off-line' : 'ri-eye-line'" />
                    </button>
                  </div>
                  <button
                    class="test-api-button"
                    @click="testApiKey"
                    :disabled="apiTestStatus === 'loading'"
                  >
                    <span v-if="apiTestStatus === 'loading'">
                      <span class="loading-indicator" /> 测试中...
                    </span>
                    <span v-else-if="apiTestStatus === 'success'">
                      <span class="status-indicator success" /> 已连接
                    </span>
                    <span v-else-if="apiTestStatus === 'error'">
                      <span class="status-indicator error" /> 测试
                    </span>
                    <span v-else>
                      <i class="ri-link-m" /> 测试连接
                    </span>
                  </button>
                  <button
                    v-if="settings.llm.provider === 'openrouter'"
                    class="test-api-button"
                    @click="checkOpenRouterCredit"
                  >
                    <i class="ri-money-dollar-circle-line" /> 查看余额
                  </button>
                </div>
                
                <!-- API测试结果消息 -->
                <div 
                  v-if="apiTestStatus && apiTestMessage" 
                  :class="['api-test-message', apiTestStatus]"
                >
                  <i :class="apiTestStatus === 'success' ? 'ri-checkbox-circle-line' : 'ri-error-warning-line'" />
                  <span>{{ apiTestMessage }}</span>
                </div>
                
                <div class="form-hint">
                  请输入您的API密钥，用于连接到{{ settings.llm.provider }}服务。系统不会将您的密钥发送给第三方。
                </div>
                <div
                  v-if="settings.llm.provider === 'openrouter' && openrouterCreditInfo"
                  class="form-hint"
                  style="white-space: pre-line;"
                >
                  {{ openrouterCreditInfo }}
                </div>
              </div>
            </div>
            
            <div
              class="form-row"
              v-if="settings.llm.provider === 'local'"
            >
              <div class="form-group api-key-group">
                <label for="local-url">本地模型API地址</label>
                <input 
                  type="text" 
                  id="local-url" 
                  v-model="settings.llm.localUrl" 
                  class="form-input"
                  placeholder="http://localhost:8080"
                >
              </div>
            </div>
          </div>
          
          <!-- Google Gemini 模型选择 -->
          <div
            v-if="settings.llm.provider === 'google'"
            class="model-selection"
          >
            <h3>选择 Gemini 模型</h3>
            <div
              v-if="isLoadingModels"
              class="loading-models"
            >
              <div class="loading-spinner" />
              <span>正在加载可用模型...</span>
            </div>
            <div
              v-else-if="activeGeminiModels.length === 0"
              class="no-models-message"
            >
              未找到可用的Gemini模型，请检查API密钥权限。
            </div>
            <div
              v-else
              class="model-list"
            >
              <div
                v-for="model in activeGeminiModels" 
                :key="model.id" 
                class="model-card"
                :class="{ 'selected': settings.llm.defaultModel === model.id }"
                @click="selectModel(model.id)"
              >
                <h4>{{ model.displayName || model.name }}</h4>
                <p>{{ model.description }}</p>
                <div class="capabilities">
                  <span
                    v-for="cap in model.capabilities"
                    :key="cap"
                    class="capability-tag"
                  >
                    {{ cap }}
                  </span>
                </div>
                <div class="model-limits">
                  <span>输入上限: {{ model.inputTokenLimit.toLocaleString() }} tokens</span>
                  <span>输出上限: {{ model.outputTokenLimit.toLocaleString() }} tokens</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="action-buttons">
            <button
              class="save-button"
              @click="saveSettings"
            >
              保存设置
            </button>
            <button
              class="cancel-button"
              @click="resetSettings"
            >
              重置
            </button>
          </div>
        </div>
        
        <!-- ComfyUI 设置 - 全新设计 -->
        <div
          v-else-if="activeMenu === 'comfyui'"
          class="settings-panel comfyui-modern"
        >
          <!-- 紧凑型头部 -->
          <div class="compact-header">
            <i class="ri-cpu-line header-icon" />
            <div class="header-text">
              <h2 class="panel-title">
                ComfyUI 配置
              </h2>
              <span class="panel-subtitle">配置服务器连接和工作流设置</span>
            </div>
          </div>

          <!-- 紧凑型配置进度 -->
          <div class="compact-progress">
            <div
              class="progress-item"
              :class="{ 'completed': settings.comfyui.isConnected, 'active': !settings.comfyui.isConnected }"
            >
              <i class="ri-server-line" />
              <span>连接服务器</span>
            </div>
            <i
              class="ri-arrow-right-s-line progress-arrow"
              :class="{ 'completed': settings.comfyui.isConnected }"
            />
            <div
              class="progress-item"
              :class="{ 'completed': settings.comfyui.currentWorkflow, 'active': settings.comfyui.isConnected && !settings.comfyui.currentWorkflow }"
            >
              <i class="ri-upload-cloud-line" />
              <span>上传工作流</span>
            </div>
            <i
              class="ri-arrow-right-s-line progress-arrow"
              :class="{ 'completed': settings.comfyui.currentWorkflow }"
            />
            <div
              class="progress-item"
              :class="{ 'completed': isNodeMappingComplete, 'active': settings.comfyui.currentWorkflow && !isNodeMappingComplete }"
            >
              <i class="ri-node-tree" />
              <span>配置节点</span>
            </div>
          </div>

          <!-- 第一步：服务器连接 -->
          <div
            class="config-card"
            :class="{ 'expanded': !settings.comfyui.isConnected }"
          >
            <div
              class="card-header"
              @click="toggleCard('connection')"
            >
              <div class="card-title">
                <div class="card-icon">
                  <i class="ri-server-line" />
                </div>
                <div class="card-info">
                  <span class="card-name">服务器连接</span>
                  <span class="card-desc">配置ComfyUI服务器地址和连接</span>
                </div>
                <div
                  class="connection-status"
                  :class="settings.comfyui.isConnected ? 'connected' : 'disconnected'"
                >
                  <i :class="settings.comfyui.isConnected ? 'ri-checkbox-circle-fill' : 'ri-error-warning-line'" />
                  <span>{{ settings.comfyui.isConnected ? '已连接' : '未连接' }}</span>
                </div>
              </div>
              <i
                class="ri-arrow-down-s-line card-arrow"
                :class="{ 'rotated': expandedCards.connection }"
              />
            </div>

            <div
              v-show="expandedCards.connection || !settings.comfyui.isConnected"
              class="card-content"
            >
              <div class="server-config">
                <div class="input-group modern">
                  <label class="input-label">
                    <i class="ri-global-line" />
                    <span>服务器地址</span>
                  </label>
                  <div class="input-with-action">
                    <input
                      v-model="settings.comfyui.serverUrl"
                      type="text"
                      placeholder="http://localhost:8188"
                      class="modern-input"
                      @change="onComfyUISettingChange('serverUrl', $event.target.value)"
                      @keyup.enter="testComfyUIConnection"
                    >
                    <button
                      class="action-btn test-btn"
                      :class="{ 'loading': comfyuiTestStatus === 'loading', 'success': comfyuiTestStatus === 'success' }"
                      @click="testComfyUIConnection"
                      :disabled="!settings.comfyui.serverUrl"
                    >
                      <i
                        v-if="comfyuiTestStatus === 'loading'"
                        class="ri-loader-4-line spinning"
                      />
                      <i
                        v-else-if="comfyuiTestStatus === 'success'"
                        class="ri-check-line"
                      />
                      <i
                        v-else
                        class="ri-play-line"
                      />
                      <span>{{ comfyuiTestStatus === 'loading' ? '测试中' : '测试连接' }}</span>
                    </button>
                  </div>
                  <div
                    v-if="comfyuiTestMessage"
                    class="status-message"
                    :class="comfyuiTestStatus"
                  >
                    <i :class="comfyuiTestStatus === 'success' ? 'ri-check-line' : 'ri-error-warning-line'" />
                    <span>{{ comfyuiTestMessage }}</span>
                  </div>
                </div>

                <div class="quick-actions">
                  <div
                    class="quick-action-item"
                    @click="setDefaultServer"
                  >
                    <i class="ri-home-line" />
                    <span>使用默认地址</span>
                  </div>
                  <div
                    class="quick-action-item"
                    @click="showServerHelp"
                  >
                    <i class="ri-question-line" />
                    <span>连接帮助</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二步：工作流管理 -->
          <div
            class="config-card compact"
            :class="{ 'expanded': settings.comfyui.isConnected && !settings.comfyui.currentWorkflow, 'disabled': !settings.comfyui.isConnected }"
          >
            <div
              class="card-header"
              @click="toggleCard('workflow')"
            >
              <div class="card-title">
                <div class="card-icon">
                  <i class="ri-file-code-line" />
                </div>
                <div class="card-info">
                  <span class="card-name">工作流管理</span>
                </div>
                <div
                  v-if="settings.comfyui.currentWorkflow"
                  class="workflow-badge"
                >
                  <i class="ri-check-line" />
                  <span>已配置</span>
                </div>
              </div>
              <i
                class="ri-arrow-down-s-line card-arrow"
                :class="{ 'rotated': expandedCards.workflow }"
              />
            </div>

            <div
              v-show="expandedCards.workflow || (settings.comfyui.isConnected && !settings.comfyui.currentWorkflow)"
              class="card-content compact"
            >
              <!-- 紧凑型工作流显示 -->
              <div
                v-if="settings.comfyui.currentWorkflow"
                class="compact-workflow"
              >
                <div class="workflow-summary">
                  <div class="workflow-info">
                    <i class="ri-file-text-line" />
                    <span class="workflow-name">{{ settings.comfyui.workflowName || '未命名工作流' }}</span>
                    <span class="workflow-nodes">{{ settings.comfyui.workflowNodes?.length || 0 }}节点</span>
                  </div>
                  <div class="workflow-actions">
                    <button
                      class="compact-btn"
                      @click="reloadWorkflow"
                      title="重新解析"
                    >
                      <i class="ri-refresh-line" />
                    </button>
                    <button
                      class="compact-btn danger"
                      @click="removeWorkflow"
                      title="移除"
                    >
                      <i class="ri-delete-bin-line" />
                    </button>
                  </div>
                </div>
              </div>

              <!-- 紧凑型上传区域 -->
              <div
                v-else
                class="compact-upload"
              >
                <div
                  class="upload-zone"
                  :class="{ 'drag-over': isDragOver, 'uploading': isParsingWorkflow }"
                  @click="triggerFileInput"
                  @dragover.prevent="isDragOver = true"
                  @dragleave.prevent="isDragOver = false"
                  @drop="handleWorkflowDrop"
                >
                  <input
                    ref="workflowFileInput"
                    type="file"
                    accept=".json"
                    style="display: none"
                    @change="handleWorkflowFileSelect"
                  >
                  <div class="upload-content">
                    <i
                      v-if="isParsingWorkflow"
                      class="ri-loader-4-line spinning upload-icon"
                    />
                    <i
                      v-else
                      class="ri-upload-cloud-2-line upload-icon"
                    />
                    <div class="upload-text">
                      <span class="upload-title">
                        {{ isParsingWorkflow ? '解析中...' : '上传工作流' }}
                      </span>
                      <span class="upload-hint">拖拽JSON文件或点击选择</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第三步：节点映射配置 -->
          <div
            v-if="settings.comfyui.currentWorkflow"
            class="config-card compact"
            :class="{ 'expanded': settings.comfyui.currentWorkflow && !isNodeMappingComplete }"
          >
            <div
              class="card-header"
              @click="toggleCard('nodeMapping')"
            >
              <div class="card-title">
                <div class="card-icon">
                  <i class="ri-node-tree" />
                </div>
                <div class="card-info">
                  <span class="card-name">节点映射配置</span>
                </div>
                <div
                  v-if="isNodeMappingComplete"
                  class="workflow-badge"
                >
                  <i class="ri-check-line" />
                  <span>已完成</span>
                </div>
              </div>
              <i
                class="ri-arrow-down-s-line card-arrow"
                :class="{ 'rotated': expandedCards.nodeMapping }"
              />
            </div>

            <div
              v-show="expandedCards.nodeMapping || (settings.comfyui.currentWorkflow && !isNodeMappingComplete)"
              class="card-content compact"
            >
              <!-- 紧凑型节点映射 -->
              <div class="compact-node-mapping">
                <!-- 必需节点组 -->
                <div class="node-group required">
                  <div class="group-header">
                    <i class="ri-star-line" />
                    <span>必需节点</span>
                  </div>
                  <div class="node-grid">
                    <div class="node-item">
                      <label class="node-label">
                        正向提示词 <span class="required">*</span>
                      </label>
                      <select
                        v-model="settings.comfyui.nodeMapping.positivePromptNode"
                        class="compact-select"
                        @change="onNodeMappingChange('positivePromptNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('CLIPTextEncode')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                    <div class="node-item">
                      <label class="node-label">
                        图像保存 <span class="required">*</span>
                      </label>
                      <select
                        v-model="settings.comfyui.nodeMapping.saveImageNode"
                        class="compact-select"
                        @change="onNodeMappingChange('saveImageNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('SaveImage')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- 可选节点组 -->
                <div class="node-group optional">
                  <div class="group-header">
                    <i class="ri-settings-3-line" />
                    <span>可选节点</span>
                  </div>
                  <div class="node-grid">
                    <div class="node-item">
                      <label class="node-label">负向提示词</label>
                      <select
                        v-model="settings.comfyui.nodeMapping.negativePromptNode"
                        class="compact-select"
                        @change="onNodeMappingChange('negativePromptNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('CLIPTextEncode')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                    <div class="node-item">
                      <label class="node-label">图像尺寸</label>
                      <select
                        v-model="settings.comfyui.nodeMapping.imageSizeNode"
                        class="compact-select"
                        @change="onNodeMappingChange('imageSizeNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('EmptyLatentImage')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                    <div class="node-item">
                      <label class="node-label">采样器</label>
                      <select
                        v-model="settings.comfyui.nodeMapping.samplerNode"
                        class="compact-select"
                        @change="onNodeMappingChange('samplerNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('KSampler')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                    <div class="node-item">
                      <label class="node-label">批次大小</label>
                      <select
                        v-model="settings.comfyui.nodeMapping.batchSizeNode"
                        class="compact-select"
                        @change="onNodeMappingChange('batchSizeNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('EmptyLatentImage')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                    <div class="node-item">
                      <label class="node-label">随机种子</label>
                      <select
                        v-model="settings.comfyui.nodeMapping.seedNode"
                        class="compact-select"
                        @change="onNodeMappingChange('seedNode', $event.target.value)"
                      >
                        <option value="">
                          选择节点
                        </option>
                        <option
                          v-for="node in comfyuiWorkflow.getNodeOptions.value('KSampler')"
                          :key="node.value"
                          :value="node.value"
                        >
                          {{ node.label }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- 配置状态 -->
                <div class="mapping-status">
                  <div
                    v-if="isNodeMappingComplete"
                    class="status-success"
                  >
                    <i class="ri-check-line" />
                    <span>配置完成，可以开始生成图像</span>
                  </div>
                  <div
                    v-else
                    class="status-warning"
                  >
                    <i class="ri-alert-line" />
                    <span>请配置必需的节点</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 🆕 第四步：模型和LoRA选择 -->
          <div
            v-if="shouldShowModelSelection"
            class="config-card compact"
            :class="{ 'expanded': expandedCards.models }"
          >
            <div
              class="card-header"
              @click="toggleCard('models')"
            >
              <div class="card-title">
                <div class="card-icon">
                  <i class="ri-cpu-line" />
                </div>
                <div class="card-info">
                  <span class="card-name">模型选择</span>
                  <span class="card-desc">选择要使用的模型和LoRA</span>
                </div>
                <div
                  v-if="hasSelectedModels"
                  class="workflow-badge"
                >
                  <i class="ri-check-line" />
                  <span>已配置</span>
                </div>
              </div>
              <i
                class="ri-arrow-down-s-line card-arrow"
                :class="{ 'rotated': expandedCards.models }"
              />
            </div>

            <div
              v-show="expandedCards.models"
              class="card-content compact"
            >
              <!-- 🆕 模型选择说明 -->
              <div class="model-selection-info">
                <div class="info-item">
                  <i class="ri-information-line" />
                  <span>选择要使用的模型将覆盖工作流中的默认设置</span>
                </div>
                <div class="info-item">
                  <i class="ri-refresh-line" />
                  <span>如果模型列表为空，请点击"刷新模型列表"按钮</span>
                </div>
              </div>

              <div class="model-selection">
                <!-- 🆕 空状态提示 -->
                <div
                  v-if="settings.comfyui.detectedModels.checkpoints.length === 0 && settings.comfyui.detectedModels.loras.length === 0"
                  class="empty-models-state"
                >
                  <div class="empty-icon">
                    <i class="ri-cpu-line" />
                  </div>
                  <div class="empty-title">未检测到模型节点</div>
                  <div class="empty-description">
                    当前工作流中没有检测到Checkpoint或LoRA节点。<br>
                    请确保工作流包含这些节点类型，或重新上传工作流。
                  </div>
                  <button
                    class="action-btn refresh-btn"
                    @click="refreshAvailableModels"
                    :disabled="isLoadingModels"
                  >
                    <i
                      v-if="isLoadingModels"
                      class="ri-loader-4-line spinning"
                    />
                    <i
                      v-else
                      class="ri-refresh-line"
                    />
                    <span>{{ isLoadingModels ? '刷新中...' : '刷新检测' }}</span>
                  </button>
                </div>

                <!-- Checkpoint模型选择 -->
                <div
                  v-if="settings.comfyui.detectedModels.checkpoints.length > 0"
                  class="model-group"
                >
                  <div class="group-header">
                    <i class="ri-cpu-line" />
                    <span>主模型 (Checkpoint)</span>
                  </div>
                  <div class="model-grid">
                    <div
                      v-for="checkpoint in settings.comfyui.detectedModels.checkpoints"
                      :key="checkpoint.nodeId"
                      class="model-item"
                    >
                      <label class="model-label">
                        {{ checkpoint.nodeName }}
                      </label>
                      <select
                        v-model="checkpoint.selectedModel"
                        class="compact-select"
                        @change="onModelSelectionChange('checkpoint', checkpoint.nodeId, $event.target.value)"
                      >
                        <option value="">
                          选择模型
                        </option>
                        <option
                          v-for="model in settings.comfyui.availableModels.checkpoints"
                          :key="model"
                          :value="model"
                        >
                          {{ formatComfyUIModelName(model) }}
                        </option>
                      </select>
                      <div class="current-model">
                        当前: {{ formatComfyUIModelName(checkpoint.modelName) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- LoRA模型选择 -->
                <div
                  v-if="settings.comfyui.detectedModels.loras.length > 0"
                  class="model-group"
                >
                  <div class="group-header">
                    <i class="ri-magic-line" />
                    <span>LoRA模型</span>
                  </div>
                  <div class="model-grid">
                    <div
                      v-for="lora in settings.comfyui.detectedModels.loras"
                      :key="lora.nodeId"
                      class="model-item lora-item"
                    >
                      <label class="model-label">
                        {{ lora.nodeName }}
                      </label>
                      <select
                        v-model="lora.selectedLora"
                        class="compact-select"
                        @change="onModelSelectionChange('lora', lora.nodeId, $event.target.value)"
                      >
                        <option value="">
                          保持原始LoRA
                        </option>
                        <option value="DISABLED" class="disable-option">
                          🚫 禁用此LoRA
                        </option>
                        <option
                          v-for="loraModel in settings.comfyui.availableModels.loras"
                          :key="loraModel"
                          :value="loraModel"
                        >
                          {{ formatComfyUIModelName(loraModel) }}
                        </option>
                      </select>
                      <div class="current-model">
                        <span v-if="lora.selectedLora === 'DISABLED'" class="disabled-status">
                          🚫 已禁用
                        </span>
                        <span v-else>
                          当前: {{ formatComfyUIModelName(lora.loraName) }}
                        </span>
                      </div>
                      <div
                        v-if="lora.selectedLora !== 'DISABLED'"
                        class="lora-strength"
                      >
                        <div class="strength-item">
                          <label>模型强度:</label>
                          <input
                            type="number"
                            v-model.number="lora.strengthModel"
                            min="0"
                            max="2"
                            step="0.1"
                            class="strength-input"
                            @input="onLoraStrengthChange(lora.nodeId, 'model', $event.target.value)"
                          >
                        </div>
                        <div class="strength-item">
                          <label>CLIP强度:</label>
                          <input
                            type="number"
                            v-model.number="lora.strengthClip"
                            min="0"
                            max="2"
                            step="0.1"
                            class="strength-input"
                            @input="onLoraStrengthChange(lora.nodeId, 'clip', $event.target.value)"
                          >
                        </div>
                      </div>
                      <div
                        v-else
                        class="disabled-notice"
                      >
                        <i class="ri-information-line" />
                        <span>此LoRA已禁用，强度将设置为0</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 模型刷新按钮 -->
                <div class="model-actions">
                  <button
                    class="action-btn refresh-btn"
                    @click="refreshAvailableModels"
                    :disabled="isLoadingModels"
                  >
                    <i
                      v-if="isLoadingModels"
                      class="ri-loader-4-line spinning"
                    />
                    <i
                      v-else
                      class="ri-refresh-line"
                    />
                    <span>{{ isLoadingModels ? '刷新中...' : '刷新模型列表' }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 第五步：生成设置 -->
          <div class="config-card compact">
            <div
              class="card-header"
              @click="toggleCard('generation')"
            >
              <div class="card-title">
                <div class="card-icon">
                  <i class="ri-image-line" />
                </div>
                <div class="card-info">
                  <span class="card-name">生成设置</span>
                </div>
              </div>
              <i
                class="ri-arrow-down-s-line card-arrow"
                :class="{ 'rotated': expandedCards.generation }"
              />
            </div>

            <div
              v-show="expandedCards.generation"
              class="card-content compact"
            >
              <div class="generation-settings">
                <div class="setting-item">
                  <label class="setting-label">每次生成图片数量</label>
                  <select
                    v-model="settings.comfyui.batchSize"
                    class="compact-select"
                    @change="onComfyUISettingChange('batchSize', $event.target.value)"
                  >
                    <option
                      v-for="n in 10"
                      :key="n"
                      :value="n"
                    >
                      {{ n }}张
                    </option>
                  </select>
                </div>

                <!-- 🔥 新增：分辨率设置 -->
                <div class="setting-item">
                  <label class="setting-label">图像分辨率</label>
                  <div class="resolution-inputs">
                    <div class="resolution-input-group">
                      <label class="resolution-label">宽度</label>
                      <input
                        type="number"
                        v-model.number="settings.comfyui.defaultSettings.width"
                        min="256"
                        max="4096"
                        step="64"
                        class="compact-input resolution-input"
                        @input="onComfyUISettingChange('defaultSettings.width', $event.target.value)"
                      >
                      <span class="resolution-unit">px</span>
                    </div>
                    <div class="resolution-separator">
                      ×
                    </div>
                    <div class="resolution-input-group">
                      <label class="resolution-label">高度</label>
                      <input
                        type="number"
                        v-model.number="settings.comfyui.defaultSettings.height"
                        min="256"
                        max="4096"
                        step="64"
                        class="compact-input resolution-input"
                        @input="onComfyUISettingChange('defaultSettings.height', $event.target.value)"
                      >
                      <span class="resolution-unit">px</span>
                    </div>
                  </div>
                  <div class="setting-hint">
                    推荐使用64的倍数，默认1024×1024像素
                  </div>
                  <div class="resolution-presets">
                    <button
                      class="preset-button"
                      @click="setResolutionPreset(1024, 1024)"
                      :class="{ active: settings.comfyui.defaultSettings.width === 1024 && settings.comfyui.defaultSettings.height === 1024 }"
                    >
                      1024×1024
                    </button>
                    <button
                      class="preset-button"
                      @click="setResolutionPreset(1280, 720)"
                      :class="{ active: settings.comfyui.defaultSettings.width === 1280 && settings.comfyui.defaultSettings.height === 720 }"
                    >
                      1280×720
                    </button>
                    <button
                      class="preset-button"
                      @click="setResolutionPreset(768, 1024)"
                      :class="{ active: settings.comfyui.defaultSettings.width === 768 && settings.comfyui.defaultSettings.height === 1024 }"
                    >
                      768×1024
                    </button>
                    <button
                      class="preset-button"
                      @click="setResolutionPreset(1024, 768)"
                      :class="{ active: settings.comfyui.defaultSettings.width === 1024 && settings.comfyui.defaultSettings.height === 768 }"
                    >
                      1024×768
                    </button>
                  </div>
                </div>

                <div class="setting-item">
                  <label class="setting-label">随机种子设置</label>
                  <input
                    type="number"
                    v-model.number="settings.comfyui.seedValue"
                    min="0"
                    max="2147483647"
                    class="compact-input"
                    placeholder="0 = 随机种子"
                    @input="onComfyUISettingChange('seedValue', $event.target.value)"
                  >
                  <div class="setting-hint">
                    设置为 0 表示使用随机种子，其他值为固定种子
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 默认占位符 -->
        <div
          v-else
          class="settings-panel"
        >
          <h2 class="panel-title">
            选择设置项
          </h2>
          <div class="placeholder-text">
            请从左侧选择要配置的设置项
          </div>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from './BaseDialog.vue';
import { useComfyUIWorkflow } from '../composables/useComfyUIWorkflow.js';

export default {
  name: 'SettingsDialog',
  components: {
    BaseDialog
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'save-settings'],
  setup() {
    // 初始化ComfyUI工作流管理
    const comfyuiWorkflow = useComfyUIWorkflow();

    return {
      comfyuiWorkflow
    };
  },
  data() {
    return {
      activeMenu: 'llm',
      apiTestStatus: '', // 'loading', 'success', 'error'
      apiTestMessage: '', // 添加测试结果消息
      showPassword: false, // 控制密码显示
      openrouterCreditInfo: '', // 新增：余额和速率信息
      // ComfyUI相关状态
      comfyuiTestStatus: '', // 'loading', 'success', 'error'
      comfyuiTestMessage: '',
      isDragOver: false,
      isParsingWorkflow: false,
      autoSaveTimer: null, // 自动保存定时器
      isInitializing: true, // 防止初始化时触发保存
      // 新的UI状态
      expandedCards: {
        connection: true,
        workflow: false,
        nodeMapping: false,
        models: false, // 🆕 模型选择卡片
        generation: false
      },
      // 🆕 模型加载状态
      isLoadingModels: false,
      activeMenuItems: [
        { id: 'llm', label: '大语言模型', icon: 'ri-robot-line' },
        { id: 'comfyui', label: 'ComfyUI设置', icon: 'ri-image-line' }
      ],
      settings: {
        llm: {
          provider: 'openrouter',
          defaultModel: 'deepseek/deepseek-r1:free',
          temperature: 0.7,
          localUrl: 'http://localhost:8080',
          // 为每个提供商添加独立的API密钥
          openrouterApiKey: '',
          googleApiKey: '',
          openaiApiKey: '',
          anthropicApiKey: '',
          localApiKey: '',
          // Gemini模型列表 - 将通过API动态获取
          geminiModels: [],
          maxOutputTokens: 4096
        },
        comfyui: {
          serverUrl: 'http://localhost:8188',
          isConnected: false,
          batchSize: 1,
          seedValue: 0,
          currentWorkflow: null,
          workflowName: '',
          nodeMapping: {
            positivePromptNode: '',
            negativePromptNode: '',
            imageSizeNode: '',
            saveImageNode: '',
            samplerNode: '',
            batchSizeNode: '',
            seedNode: ''
          },
          defaultSettings: {
            width: 1024,
            height: 1024,
            steps: 35,
            cfg: 7,
            sampler: 'euler'
          },
          availableWorkflows: [],
          detectedModels: {
            checkpoints: [],
            loras: []
          },
          availableModels: {
            checkpoints: [],
            loras: []
          },
          selectedModels: {
            checkpoint: {
              nodeId: '',
              modelName: ''
            },
            loras: []
          },
          workflowNodes: []
        }
      },
      defaultSettings: {
        theme: 'dark',
        language: 'zh-CN',
        autoSave: true,
        llm: {
          provider: 'openrouter',
          defaultModel: 'deepseek/deepseek-r1:free',
          temperature: 0.7,
          localUrl: 'http://localhost:8080',
          // 为每个提供商添加独立的API密钥
          openrouterApiKey: '',
          googleApiKey: '',
          openaiApiKey: '',
          anthropicApiKey: '',
          localApiKey: '',
          // Gemini模型列表 - 将通过API动态获取
          geminiModels: [],
          maxOutputTokens: 4096
        },
        comfyui: {
          serverUrl: 'http://localhost:8188',
          isConnected: false,
          batchSize: 1,
          seedValue: 0,
          currentWorkflow: null,
          workflowName: '',
          nodeMapping: {
            positivePromptNode: '',
            negativePromptNode: '',
            imageSizeNode: '',
            saveImageNode: '',
            samplerNode: '',
            batchSizeNode: '',
            seedNode: ''
          },
          defaultSettings: {
            width: 1024,
            height: 1024,
            steps: 35,
            cfg: 7,
            sampler: 'euler'
          },
          availableWorkflows: [],
          workflowNodes: []
        }
      }
    };
  },
  computed: {
    getCurrentApiKey: {
      get() {
        switch(this.settings.llm.provider) {
          case 'openrouter': return this.settings.llm.openrouterApiKey;
          case 'google': return this.settings.llm.googleApiKey;
          case 'openai': return this.settings.llm.openaiApiKey;
          case 'anthropic': return this.settings.llm.anthropicApiKey;
          case 'local': return this.settings.llm.localApiKey;
          default: return '';
        }
      },
      set(value) {
        switch(this.settings.llm.provider) {
          case 'openrouter': this.settings.llm.openrouterApiKey = value; break;
          case 'google': this.settings.llm.googleApiKey = value; break;
          case 'openai': this.settings.llm.openaiApiKey = value; break;
          case 'anthropic': this.settings.llm.anthropicApiKey = value; break;
          case 'local': this.settings.llm.localApiKey = value; break;
        }
      }
    },
    // 计算可用的Gemini模型列表，过滤掉已弃用的模型
    activeGeminiModels() {
      // 如果模型列表为空，返回空数组
      if (!this.settings.llm.geminiModels || this.settings.llm.geminiModels.length === 0) {
        return [];
      }
      
      // 过滤掉已弃用的模型
      return this.settings.llm.geminiModels.filter(model => {
        // 如果描述中包含deprecated，则认为该模型已弃用
        const isDeprecated = model.description && 
                           model.description.toLowerCase().includes('deprecated');
        
        // 只返回未弃用的模型
        return !isDeprecated;
      }).sort((a, b) => {
        // 优先显示较新的模型
        if (a.id.includes('1.5-pro') && !b.id.includes('1.5-pro')) return -1;
        if (!a.id.includes('1.5-pro') && b.id.includes('1.5-pro')) return 1;
        if (a.id.includes('1.5') && !b.id.includes('1.5')) return -1;
        if (!a.id.includes('1.5') && b.id.includes('1.5')) return 1;
        return 0;
      });
    },

    // 检查节点映射是否完成
    isNodeMappingComplete() {
      const mapping = this.settings.comfyui.nodeMapping;
      return mapping.positivePromptNode && mapping.saveImageNode;
    },

    // 🆕 检查是否有选择的模型
    hasSelectedModels() {
      const checkpoints = this.settings.comfyui.detectedModels.checkpoints || [];
      const loras = this.settings.comfyui.detectedModels.loras || [];

      const hasSelectedCheckpoint = checkpoints.some(cp => cp.selectedModel);
      const hasSelectedLora = loras.some(lora => lora.selectedLora);

      return hasSelectedCheckpoint || hasSelectedLora;
    },

    // 🆕 调试：检查模型选择卡片是否应该显示
    shouldShowModelSelection() {
      const hasWorkflow = !!this.settings.comfyui.currentWorkflow;
      const hasCheckpoints = (this.settings.comfyui.detectedModels.checkpoints || []).length > 0;
      const hasLoras = (this.settings.comfyui.detectedModels.loras || []).length > 0;

      console.log('[调试] 模型选择卡片显示条件:', {
        hasWorkflow,
        hasCheckpoints,
        hasLoras,
        checkpointsCount: (this.settings.comfyui.detectedModels.checkpoints || []).length,
        lorasCount: (this.settings.comfyui.detectedModels.loras || []).length
      });

      return hasWorkflow && (hasCheckpoints || hasLoras);
    }
  },
  methods: {
    updateModelValue(value) {
      this.$emit('update:modelValue', value);
    },

    // 新的UI交互方法
    toggleCard(cardName) {
      this.expandedCards[cardName] = !this.expandedCards[cardName];
    },

    setDefaultServer() {
      this.settings.comfyui.serverUrl = 'http://localhost:8188';
      this.onComfyUISettingChange('serverUrl', this.settings.comfyui.serverUrl);
    },

    showServerHelp() {
      alert(`ComfyUI服务器连接帮助：

1. 确保ComfyUI已启动并运行在指定端口
2. 默认地址为 http://localhost:8188
3. 如果使用其他端口，请相应修改地址
4. 确保没有防火墙阻止连接
5. 如果使用远程服务器，请确保网络连通性

常见问题：
- 连接超时：检查ComfyUI是否正在运行
- 端口错误：确认ComfyUI启动时的端口号
- 跨域问题：确保ComfyUI允许跨域访问`);
    },

    formatDate(dateString) {
      if (!dateString) return '未知时间';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    },

    reloadWorkflow() {
      if (this.settings.comfyui.currentWorkflow) {
        // 重新解析当前工作流
        this.comfyuiWorkflow.parseWorkflow(
          this.settings.comfyui.currentWorkflow,
          this.settings.comfyui.workflowName
        );
        this.showSaveNotification('工作流已重新解析');
      }
    },

    removeWorkflow() {
      if (confirm('确定要移除当前工作流吗？这将清除所有节点映射配置。')) {
        this.settings.comfyui.currentWorkflow = null;
        this.settings.comfyui.workflowName = '';
        this.settings.comfyui.workflowNodes = [];
        this.settings.comfyui.nodeMapping = {
          positivePromptNode: '',
          negativePromptNode: '',
          imageSizeNode: '',
          saveImageNode: '',
          samplerNode: '',
          batchSizeNode: '',
          seedNode: ''
        };
        this.saveComfyUISettingsWithCustomNotification('工作流已移除');
      }
    },

    // 🔥 新增：设置分辨率预设
    setResolutionPreset(width, height) {
      this.settings.comfyui.defaultSettings.width = width;
      this.settings.comfyui.defaultSettings.height = height;

      // 触发保存
      this.onComfyUISettingChange('defaultSettings.width', width);
      this.onComfyUISettingChange('defaultSettings.height', height);

      console.log(`[分辨率设置] 设置为 ${width}×${height}`);
    },

    // 🆕 格式化ComfyUI模型名称
    formatComfyUIModelName(modelPath) {
      if (!modelPath) return '未选择';

      // 提取文件名（去掉路径）
      const fileName = modelPath.split(/[/\\]/).pop();

      // 去掉文件扩展名
      const nameWithoutExt = fileName.replace(/\.(safetensors|ckpt|pt|pth)$/i, '');

      // 如果名称太长，截断并添加省略号
      if (nameWithoutExt.length > 40) {
        return nameWithoutExt.substring(0, 37) + '...';
      }

      return nameWithoutExt;
    },

    // 🆕 处理模型选择变化
    onModelSelectionChange(type, nodeId, selectedValue) {
      console.log(`[模型选择] ${type} 节点 ${nodeId} 选择了: ${selectedValue}`);

      if (type === 'checkpoint') {
        // 更新Checkpoint选择
        const checkpoint = this.settings.comfyui.detectedModels.checkpoints.find(cp => cp.nodeId === nodeId);
        if (checkpoint) {
          checkpoint.selectedModel = selectedValue;
        }
      } else if (type === 'lora') {
        // 更新LoRA选择
        const lora = this.settings.comfyui.detectedModels.loras.find(lr => lr.nodeId === nodeId);
        if (lora) {
          lora.selectedLora = selectedValue;
        }
      }

      // 保存设置
      this.saveComfyUISettingsWithCustomNotification(`${type === 'checkpoint' ? '主模型' : 'LoRA'}选择已更新`);
    },

    // 🆕 处理LoRA强度变化
    onLoraStrengthChange(nodeId, strengthType, value) {
      console.log(`[LoRA强度] 节点 ${nodeId} ${strengthType}强度: ${value}`);

      const lora = this.settings.comfyui.detectedModels.loras.find(lr => lr.nodeId === nodeId);
      if (lora) {
        if (strengthType === 'model') {
          lora.strengthModel = parseFloat(value) || 1.0;
        } else if (strengthType === 'clip') {
          lora.strengthClip = parseFloat(value) || 1.0;
        }

        // 保存设置
        this.saveComfyUISettingsWithCustomNotification('LoRA强度已更新');
      }
    },

    // 🆕 刷新可用模型列表
    async refreshAvailableModels() {
      if (!this.settings.comfyui.isConnected) {
        this.showSaveNotification('请先连接ComfyUI服务器', 'error');
        return;
      }

      this.isLoadingModels = true;

      try {
        console.log('[模型刷新] 开始获取可用模型列表...');

        const response = await fetch('/api/local/comfyui/models', {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          this.settings.comfyui.availableModels = {
            checkpoints: result.data.checkpoints || [],
            loras: result.data.loras || []
          };

          console.log('[模型刷新] 获取成功:', this.settings.comfyui.availableModels);

          const checkpointCount = result.data.checkpoints?.length || 0;
          const loraCount = result.data.loras?.length || 0;

          if (checkpointCount === 0 && loraCount === 0) {
            this.showSaveNotification('模型列表已刷新，但未找到任何模型。请检查ComfyUI模型目录。', 'warning');
          } else {
            this.showSaveNotification(`模型列表已刷新 (${checkpointCount} 个主模型, ${loraCount} 个LoRA)`, 'success');
          }

          // 保存更新的模型列表
          await this.saveComfyUISettingsWithCustomNotification('可用模型列表已更新');
        } else {
          throw new Error(result.error || '获取模型列表失败');
        }
      } catch (error) {
        console.error('[模型刷新] 失败:', error);
        this.showSaveNotification(`刷新模型列表失败: ${error.message}`, 'error');
      } finally {
        this.isLoadingModels = false;
      }
    },
    // 获取Google模型列表
    async fetchGoogleModels() {
      // 如果对话框未打开，直接返回，不执行API调用
      if (!this.modelValue) {
        console.log('对话框未打开，跳过获取模型列表');
        return [];
      }
      
      if (!this.settings.llm.googleApiKey) {
        this.showApiError('请先输入API密钥以获取可用模型');
        return [];
      }
      
      this.isLoadingModels = true;
      console.log('正在获取Google模型列表...');
      
      try {
        const url = `https://generativelanguage.googleapis.com/v1beta/models?key=${this.settings.llm.googleApiKey}`;
        const response = await fetch(url);
        
        if (!response.ok) throw new Error(`API请求失败: ${response.status}`);
        
        const data = await response.json();
        console.log('收到模型数据:', JSON.stringify(data).substring(0, 300) + '...');
        
        if (data && data.models && Array.isArray(data.models)) {
          const geminiModels = this.filterGeminiModels(data.models);
          console.log('找到', geminiModels.length, '个Gemini模型');
          
          this.settings.llm.geminiModels = this.processGeminiModels(geminiModels);
          console.log('处理后的模型列表:', this.settings.llm.geminiModels);
          
          this.updateDefaultGeminiModel();
          
          this.showApiSuccess('获取模型列表成功');
          return this.settings.llm.geminiModels;
        } else {
          throw new Error('服务器响应中没有模型数据');
        }
      } catch (error) {
        this.showApiError(`获取模型列表失败: ${error.message}`);
        return [];
      } finally {
        this.isLoadingModels = false;
      }
    },
    
    // 过滤Gemini模型
    filterGeminiModels(models) {
      return models.filter(model => 
        model.name.includes('gemini') && 
        model.supportedGenerationMethods && 
        model.supportedGenerationMethods.includes('generateContent')
      );
    },
    
    // 处理Gemini模型信息
    processGeminiModels(geminiModels) {
      // 模型列表预处理
      const processedModels = geminiModels.map(model => {
        const id = model.name.split('/').pop(); 
        
        // 强制使用根据ID生成的displayName，以确保与ID的一致性
        const finalDisplayName = this.formatModelName(id); 

        // 如果原始Google API提供的displayName与我们生成的finalDisplayName不同，
        // 打印一个警告。这有助于我们了解Google API返回的数据何时可能存在不一致。
        if (model.displayName && model.displayName !== finalDisplayName) {
          console.warn(
            `模型ID "${id}": Google API 提供的原始 displayName ("${model.displayName}") ` +
            `与我们基于ID生成的名称 ("${finalDisplayName}") 不一致。将统一使用后者。`
          );
        }

        return {
          name: model.name, 
          id,              
          displayName: finalDisplayName, 
          description: model.description || this.getModelDescription(id), 
          capabilities: this.getModelCapabilities(id),
          inputTokenLimit: model.inputTokenLimit || 30720, 
          outputTokenLimit: model.outputTokenLimit || 2048  
        };
      });
      
      console.log('处理后的模型列表 (强制使用基于ID的displayName):', processedModels);
      return processedModels;
    },
    
    // 格式化模型名称
    formatModelName(id) {
      const nameParts = id.split('-');
      return nameParts.map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' ');
    },
    
    // 获取模型描述
    getModelDescription(id) {
      if (id.includes('pro')) {
        return '高性能模型，适用于复杂任务和推理';
      } else if (id.includes('flash')) {
        return '快速响应模型，适合需要低延迟的应用场景';
      } else if (id.includes('vision')) {
        return '支持图像理解的多模态模型';
      }
      return 'Gemini语言模型';
    },
    
    // 获取模型能力
    getModelCapabilities(id) {
      const capabilities = ['文本生成', '对话'];
      if (id.includes('pro')) {
        capabilities.push('代码生成', '分析', '长文本处理');
      } else if (id.includes('flash')) {
        capabilities.push('快速响应');
      } else if (id.includes('vision')) {
        capabilities.push('图像理解');
      }
      return capabilities;
    },
    
    // 更新默认Gemini模型
    updateDefaultGeminiModel() {
      if (this.settings.llm.provider !== 'google') return;
      
      // 如果没有可用模型，不进行操作
      if (!this.settings.llm.geminiModels || this.settings.llm.geminiModels.length === 0) {
        console.log('没有可用的Gemini模型，无法设置默认模型');
        return;
      }
      
      // 优先排序模型，将最新的模型放在前面
      const sortedModels = [...this.settings.llm.geminiModels].sort((a, b) => {
        // 优先选择 1.5-pro 模型
        if (a.id.includes('1.5-pro') && !b.id.includes('1.5-pro')) return -1;
        if (!a.id.includes('1.5-pro') && b.id.includes('1.5-pro')) return 1;
        // 其次是 1.5 系列模型
        if (a.id.includes('1.5') && !b.id.includes('1.5')) return -1;
        if (!a.id.includes('1.5') && b.id.includes('1.5')) return 1;
        // 再其次是pro模型
        if (a.id.includes('pro') && !b.id.includes('pro')) return -1;
        if (!a.id.includes('pro') && b.id.includes('pro')) return 1;
        return 0;
      });
      
      // 检查当前默认模型是否在可用模型列表中
      const defaultModelIsAvailable = sortedModels.some(
        model => model.id === this.settings.llm.defaultModel
      );
      
      // 如果默认模型不可用，选择排序后的第一个模型
      if (!defaultModelIsAvailable) {
        const newDefaultModel = sortedModels[0].id;
        console.log(`当前默认模型不可用，将设置新的默认模型: ${newDefaultModel}`);
        this.settings.llm.defaultModel = newDefaultModel;
      } else {
        console.log(`当前默认模型可用: ${this.settings.llm.defaultModel}`);
      }
    },
    
    // 测试OpenRouter API
    async testOpenRouterApi(apiKey) {
      const endpoint = 'https://openrouter.ai/api/v1/chat/completions';
      const requestData = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': location.origin,
          'X-Title': 'Voice Comic Generator',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.settings.llm.defaultModel,
          messages: [{ role: 'user', content: 'Please reply with "Hello" only.' }],
          max_tokens: 10
        })
      };
      
      const response = await fetch(endpoint, requestData);
      const data = await response.json();
      
      if (response.ok && data.choices && data.choices[0] && data.choices[0].message) {
        const aiResponse = data.choices[0].message.content.trim();
        this.showApiSuccess(`API测试成功! AI回复: "${aiResponse}"`);
      } else {
        const errorMessage = data.error ? 
          (data.error.message || `错误: ${data.error.type || data.error.code || '未知'}`) : 
          `请求失败: ${response.status} ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    
    // 测试Google API
    async testGoogleApi(apiKey) {
      try {
        // 先获取最新的模型列表
        await this.fetchGoogleModels();
        
        if (this.settings.llm.geminiModels.length === 0) {
          throw new Error('无法获取可用的Gemini模型');
        }
        
        // 选择一个可用模型进行测试
        const testModel = this.selectTestModel();
        console.log('选择模型进行测试:', testModel);
        
        const responseData = await this.executeGoogleModelTest(testModel, apiKey);
        await this.processGoogleApiResponse(responseData.response, responseData.data);
      } catch (error) {
        console.error('测试Google API失败:', error);
        throw new Error(`测试Google API失败: ${error.message}`);
      }
    },
    
    // 选择测试用的Google模型
    selectTestModel() {
      console.log('开始选择测试模型...');
      
      // 第一步：先检查我们是否有模型列表
      if (!this.settings.llm.geminiModels || !Array.isArray(this.settings.llm.geminiModels) || this.settings.llm.geminiModels.length === 0) {
        console.error('没有可用的模型列表进行测试');
        throw new Error('没有可用的模型列表');
      }
      
      // 列出所有可用模型的ID进行调试
      console.log('所有可用模型:', this.settings.llm.geminiModels.map(m => m.id));
      
      // 首先尝试使用用户选择的模型
      let testModel = this.settings.llm.geminiModels.find(m => m.id === this.settings.llm.defaultModel);
      console.log('默认模型查询结果:', testModel ? testModel.id : '未找到');
      
      // 如果没有找到用户选择的模型，按优先级选择一个稳定的模型
      if (!testModel) {
        // 先尝试稳定版本的模型，而不是实验性模型
        testModel = 
          // 1.5 Pro 系列，首选
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro-latest') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro-001') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro-002') ||
          
          // Flash 系列，次选
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-flash') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-flash-latest') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-flash-001') ||
          
          // 其他可用模型
          this.settings.llm.geminiModels.find(m => m.id.includes('1.5-pro')) ||
          this.settings.llm.geminiModels.find(m => m.id.includes('1.5-flash')) ||
          this.settings.llm.geminiModels.find(m => m.id.includes('gemini-pro'));
        
        // 如果依然没找到，使用第一个可用模型
        if (!testModel) {
          testModel = this.settings.llm.geminiModels[0];
          console.log('没有找到匹配的模型，使用第一个可用模型:', testModel.id);
        } else {
          console.log('选择到替代模型:', testModel.id);
        }
      } else {
        console.log('使用用户选择的默认模型:', testModel.id);
      }
      
      return testModel;
    },
    
    // 执行Google模型测试
    async executeGoogleModelTest(testModel, apiKey) {
      // 打印测试模型信息便于调试
      console.log('测试模型详情:', JSON.stringify(testModel));
      
      // 确保模型路径正确，如果缺少"models/"前缀则添加
      let modelPath = testModel.name;
      if (!modelPath.startsWith('models/')) {
        modelPath = `models/${testModel.id}`;
      }
      
      console.log('使用模型路径:', modelPath);
      const endpoint = `https://generativelanguage.googleapis.com/v1beta/${modelPath}:generateContent?key=${apiKey}`;
      console.log('请求端点:', endpoint);
      
      const requestBody = {
        contents: [{ 
          parts: [{ text: '简单测试，请直接回复"测试成功"' }] 
        }],
        generationConfig: {
          temperature: 0,
          maxOutputTokens: 50,
        }
      };
      
      console.log('发送请求体:', JSON.stringify(requestBody));
      
      const requestData = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      };
      
      try {
        const response = await fetch(endpoint, requestData);
        const data = await response.json();
        console.log('API响应:', JSON.stringify(data));
        return { response, data };
      } catch (error) {
        console.error('测试请求错误:', error);
        throw error;
      }
    },
    
    // 处理Google API响应
    async processGoogleApiResponse(response, data) {
      console.log('处理响应状态码:', response.status);
      
      // 如果状态码非200，则认为是错误
      if (!response.ok) {
        const errorMessage = data.error ? 
          data.error.message || `错误: ${data.error.code || '未知'}` : 
          `请求失败: ${response.status} ${response.statusText}`;
        console.error('响应错误:', errorMessage);
        throw new Error(errorMessage);
      }
      
      // 打印完整的响应数据，便于调试
      console.log('收到完整响应数据:', JSON.stringify(data));
      
      // 如果响应码是200，我们就认为 API 连接测试成功
      // 即使没有具体的内容返回，只要服务器响应正常就认为测试成功
      if (data.candidates && Array.isArray(data.candidates)) {
        let aiResponse = '';
        
        // 尝试从不同格式的响应中提取文本
        if (data.candidates[0]) {
          if (data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
            // 新版API响应格式
            aiResponse = data.candidates[0].content.parts[0].text || '';
            console.log('从响应中提取的文本(新格式):', aiResponse);
          } else if (data.candidates[0].text) {
            // 旧版API响应格式
            aiResponse = data.candidates[0].text;
            console.log('从响应中提取的文本(旧格式):', aiResponse);
          }
        }
        
        // 即使得到空字符串也认为测试成功 - 因为 API 连接正常工作
        if (aiResponse) {
          // 如果有内容则显示内容
          const successMsg = aiResponse.includes('测试成功') ? 
            `API测试成功! AI回复: "${aiResponse}"` : 
            `API测试成功! (得到回复: "${aiResponse.substring(0, 30)}${aiResponse.length > 30 ? '...' : ''}")`;
          this.showApiSuccess(successMsg);
        } else {
          // 空响应也算成功，因为我们在测试 API 连接，而不是模型输出
          this.showApiSuccess(`API测试成功! (请求成功但没有返回内容)`);
        }
        return;
      }
      
      // 只有当没有candidates字段时才认为测试失败
      console.warn('没有收到正确的API响应格式');
      throw new Error('测试失败: API响应格式异常');
    },
    
    // 测试本地API
    async testLocalApi() {
      const endpoint = '/api/generate';
      const requestData = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'local',
          prompt: '你好，请回复"你好"',
          options: { max_tokens: 10, temperature: 0.7 },
          history: []
        })
      };
      
      const response = await fetch(endpoint, requestData);
      const data = await response.json();
      
      if (response.ok && data.choices && data.choices[0] && data.choices[0].message) {
        const aiResponse = data.choices[0].message.content.trim();
        this.showApiSuccess(`API测试成功! AI回复: "${aiResponse}"`);
      } else {
        const errorMessage = data.error ? 
          (data.error.message || `错误: ${data.error.type || data.error.code || '未知'}`) : 
          `请求失败: ${response.status} ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    
    // 显示API成功消息
    showApiSuccess(message) {
      this.apiTestStatus = 'success';
      this.apiTestMessage = message;
      setTimeout(() => {
        if (this.apiTestStatus === 'success') {
          this.apiTestStatus = '';
          this.apiTestMessage = '';
        }
      }, 5000);
    },
    
    // 显示API错误消息
    showApiError(message) {
      this.apiTestStatus = 'error';
      this.apiTestMessage = message;
    },
    async testApiKey() {
      const apiKey = this.getCurrentApiKey;
      
      if (!apiKey) {
        this.showApiError('请先输入API密钥');
        return;
      }
      
      this.apiTestStatus = 'loading';
      this.apiTestMessage = '正在测试API密钥...';
      
      try {
        switch (this.settings.llm.provider) {
          case 'openrouter':
            await this.testOpenRouterApi(apiKey);
            break;
            
          case 'google':
            await this.testGoogleApi(apiKey);
            break;
            
          case 'local':
            await this.testLocalApi();
            break;
          
          default:
            throw new Error(`未知的API提供商: ${this.settings.llm.provider}`);
        }
      } catch (error) {
        this.showApiError(`测试出错: ${error.message}`);
      }
    },
    async saveSettings() {
      try {
        console.log('[SettingsDialog saveSettings] 开始手动保存设置');

        // 分别保存不同类型的设置
        await this.saveGeneralSettings();
        await this.saveComfyUISettings();

        // 清除localStorage中的设置，确保下次从服务器获取最新设置
        console.log('清除localStorage中的设置缓存，确保下次使用最新设置');
        localStorage.removeItem('usersettings');

        // 发出设置已保存的事件
        this.$emit('save-settings', this.settings);

        // 显示成功消息
        alert('设置保存成功！\n- 通用设置已保存到 usersettings.json\n- ComfyUI设置已保存到 comfyui-settings.json');

        // 关闭对话框
        this.updateModelValue(false);
      } catch (error) {
        console.error('保存设置出错:', error);

        // 显示错误消息
        alert(`保存设置失败: ${error.message}`);
      }
    },

    // 自动保存设置（防抖处理）
    autoSaveSettings() {
      // 清除之前的定时器
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      // 设置新的定时器，延迟1秒后保存
      this.autoSaveTimer = setTimeout(async () => {
        try {
          console.log('[SettingsDialog] 执行自动保存设置');

          // 分别保存不同类型的设置
          await this.saveGeneralSettings();
          await this.saveComfyUISettings();

          // 发出设置已保存的事件
          this.$emit('save-settings', this.settings);
        } catch (error) {
          console.error('[SettingsDialog] 自动保存设置失败:', error);
        }
      }, 1000); // 1秒延迟
    },

    // 保存通用设置（LLM等）
    async saveGeneralSettings() {
      try {
        const generalSettings = {
          llm: { ...this.settings.llm }
        };

        // 从保存对象中移除 geminiModels
        if (generalSettings.llm && generalSettings.llm.geminiModels) {
          delete generalSettings.llm.geminiModels;
        }

        const response = await fetch('/api/local/save-user-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            settings: generalSettings,
            filename: 'usersettings.json'
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log('[SettingsDialog] 通用设置保存成功');

            // 🆕 保存成功后重新加载相关服务设置
            await this.reloadServiceSettings('usersettings.json');
          }
        }
      } catch (error) {
        console.error('[SettingsDialog] 保存通用设置失败:', error);
      }
    },

    // 保存ComfyUI专用设置
    async saveComfyUISettings() {
      try {
        const comfyuiSettings = {
          comfyui: { ...this.settings.comfyui },
          lastUpdated: new Date().toISOString(),
          version: '1.0'
        };

        console.log('💾 [ComfyUI设置] 开始保存到 comfyui-settings.json...');

        const response = await fetch('/api/local/save-user-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            settings: comfyuiSettings,
            filename: 'comfyui-settings.json'
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log('✅ [ComfyUI设置] 保存成功！文件路径:', result.path);
            console.log('✅ [ComfyUI设置] 保存时间:', comfyuiSettings.lastUpdated);

            // 在页面上显示保存成功提示
            this.showSaveNotification('ComfyUI设置已自动保存');
          } else {
            console.error('❌ [ComfyUI设置] 服务器返回失败:', result.error || '未知错误');
            throw new Error(result.error || '保存失败');
          }
        } else {
          console.error('❌ [ComfyUI设置] HTTP请求失败:', response.status, response.statusText);
          const errorText = await response.text();
          console.error('❌ [ComfyUI设置] 错误详情:', errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ [ComfyUI设置] 保存失败:', error);
        throw error; // 重新抛出错误，让调用者处理
      }
    },

    // 保存ComfyUI设置并显示自定义通知
    async saveComfyUISettingsWithCustomNotification(notificationMessage) {
      try {
        const comfyuiSettings = {
          comfyui: { ...this.settings.comfyui },
          lastUpdated: new Date().toISOString(),
          version: '1.0'
        };

        console.log('💾 [ComfyUI设置] 开始保存到 comfyui-settings.json...');

        const response = await fetch('/api/local/save-user-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            settings: comfyuiSettings,
            filename: 'comfyui-settings.json'
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log('✅ [ComfyUI设置] 保存成功！文件路径:', result.path);
            console.log('✅ [ComfyUI设置] 保存时间:', comfyuiSettings.lastUpdated);

            // 显示自定义通知
            this.showSaveNotification(notificationMessage);
          } else {
            console.error('❌ [ComfyUI设置] 服务器返回失败:', result.error || '未知错误');
            throw new Error(result.error || '保存失败');
          }
        } else {
          console.error('❌ [ComfyUI设置] HTTP请求失败:', response.status, response.statusText);
          const errorText = await response.text();
          console.error('❌ [ComfyUI设置] 错误详情:', errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ [ComfyUI设置] 保存失败:', error);
        throw error; // 重新抛出错误，让调用者处理
      }
    },

    // 显示保存通知
    showSaveNotification(message, type = 'success') {
      // 创建一个临时的通知元素
      const notification = document.createElement('div');
      notification.textContent = message;

      // 根据类型设置不同的样式
      const styles = {
        success: {
          background: '#2dc0f0',
          color: '#11111b',
          shadow: 'rgba(45, 192, 240, 0.3)'
        },
        error: {
          background: '#f38ba8',
          color: '#11111b',
          shadow: 'rgba(243, 139, 168, 0.3)'
        },
        warning: {
          background: '#f9e2af',
          color: '#11111b',
          shadow: 'rgba(249, 226, 175, 0.3)'
        }
      };

      const notificationStyle = styles[type] || styles.success;

      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${notificationStyle.background};
        color: ${notificationStyle.color};
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 4px 12px ${notificationStyle.shadow};
        animation: slideIn 0.3s ease;
      `;

      // 添加动画样式
      const styleElement = document.createElement('style');
      styleElement.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
      `;
      document.head.appendChild(styleElement);

      document.body.appendChild(notification);

      // 3秒后移除通知
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
        if (styleElement.parentNode) {
          styleElement.parentNode.removeChild(styleElement);
        }
      }, 3000);
    },

    // 检查ComfyUI设置是否有关键变化
    hasComfyUIChanges(newValue, oldValue) {
      if (!newValue || !oldValue) {
        console.log('🔧 [ComfyUI变化检测] 缺少新值或旧值:', { newValue: !!newValue, oldValue: !!oldValue });
        return false;
      }

      console.log('🔧 [ComfyUI变化检测] 开始详细检查...');
      console.log('🔧 [ComfyUI变化检测] 新值:', JSON.stringify(newValue, null, 2));
      console.log('🔧 [ComfyUI变化检测] 旧值:', JSON.stringify(oldValue, null, 2));

      // 检查关键字段
      const keyFields = [
        'serverUrl',
        'batchSize',
        'seedValue',
        'workflowName'
      ];

      // 检查基本字段变化
      for (const field of keyFields) {
        if (newValue[field] !== oldValue[field]) {
          console.log(`🔧 [ComfyUI变化检测] ${field} 发生变化:`, oldValue[field], '->', newValue[field]);
          return true;
        }
      }

      // 检查nodeMapping变化
      const newMapping = newValue.nodeMapping || {};
      const oldMapping = oldValue.nodeMapping || {};

      console.log('🔧 [ComfyUI变化检测] nodeMapping比较:');
      console.log('🔧 [ComfyUI变化检测] 新nodeMapping:', JSON.stringify(newMapping, null, 2));
      console.log('🔧 [ComfyUI变化检测] 旧nodeMapping:', JSON.stringify(oldMapping, null, 2));

      const mappingFields = [
        'positivePromptNode',
        'negativePromptNode',
        'imageSizeNode',
        'saveImageNode',
        'samplerNode',
        'batchSizeNode',
        'seedNode'
      ];

      for (const field of mappingFields) {
        if (newMapping[field] !== oldMapping[field]) {
          console.log(`🔧 [ComfyUI变化检测] nodeMapping.${field} 发生变化:`, oldMapping[field], '->', newMapping[field]);
          return true;
        }
      }

      // 检查currentWorkflow变化（简单检查）
      const newWorkflowStr = JSON.stringify(newValue.currentWorkflow);
      const oldWorkflowStr = JSON.stringify(oldValue.currentWorkflow);
      if (newWorkflowStr !== oldWorkflowStr) {
        console.log('🔧 [ComfyUI变化检测] currentWorkflow 发生变化');
        return true;
      }

      console.log('🔧 [ComfyUI变化检测] 未发现任何关键变化');
      return false;
    },

    // 处理节点映射变化
    async onNodeMappingChange(fieldName, value) {
      console.log(`🔧 [节点映射变化] ${fieldName} 更改为:`, value);

      // 检查是否在初始化状态
      if (this.isInitializing) {
        console.log('🔧 [节点映射变化] 初始化中，跳过保存');
        return;
      }

      try {
        // 绕过watch监听器，直接保存ComfyUI设置
        console.log('🔧 [节点映射变化] 用户操作，直接保存...');
        await this.saveComfyUISettingsWithCustomNotification(`节点映射已更新: ${fieldName}`);
      } catch (error) {
        console.error('❌ [节点映射变化] 保存失败:', error);
        this.showSaveNotification(`保存失败: ${error.message}`, 'error');
      }
    },

    // 处理ComfyUI设置变化
    async onComfyUISettingChange(fieldName, value) {
      console.log(`🔧 [ComfyUI设置变化] ${fieldName} 更改为:`, value);

      // 检查是否在初始化状态
      if (this.isInitializing) {
        console.log('🔧 [ComfyUI设置变化] 初始化中，跳过保存');
        return;
      }

      try {
        // 绕过watch监听器，直接保存ComfyUI设置
        console.log('🔧 [ComfyUI设置变化] 用户操作，直接保存...');
        await this.saveComfyUISettingsWithCustomNotification(`ComfyUI设置已更新: ${fieldName}`);

        // 🆕 使用通用设置重新加载器
        await this.reloadServiceSettings('comfyui-settings.json', [fieldName]);
      } catch (error) {
        console.error('❌ [ComfyUI设置变化] 保存失败:', error);
        this.showSaveNotification(`保存失败: ${error.message}`, 'error');
      }
    },

    // 🆕 通用设置重新加载方法
    async reloadServiceSettings(settingsFileName, changedFields = []) {
      try {
        console.log(`🔄 [设置重载] 重新加载设置文件: ${settingsFileName}`, { changedFields });

        // 动态导入通用设置重新加载器
        const { default: settingsReloader } = await import('@/services/settingsReloader.js');

        // 根据设置文件重新加载相关服务
        const results = await settingsReloader.reloadBySettingsFile(settingsFileName, changedFields);

        const successServices = Object.entries(results).filter(([, success]) => success).map(([name]) => name);
        const failedServices = Object.entries(results).filter(([, success]) => !success).map(([name]) => name);

        if (successServices.length > 0) {
          console.log('✅ [设置重载] 成功重新加载服务:', successServices);
        }

        if (failedServices.length > 0) {
          console.warn('⚠️ [设置重载] 重新加载失败的服务:', failedServices);
        }

        return results;
      } catch (error) {
        console.error('❌ [设置重载] 重新加载设置时出错:', error);
        return {};
      }
    },

    resetSettings() {
      this.settings = JSON.parse(JSON.stringify(this.defaultSettings));
    },
    async loadSavedSettings() {
      try {
        console.log('[SettingsDialog] 开始加载保存的设置');

        // 分别加载通用设置和ComfyUI设置
        await this.loadGeneralSettings();
        await this.loadComfyUISettings();

        console.log('[SettingsDialog] 所有设置加载完成');
      } catch (error) {
        console.error('加载设置时出错:', error);
        alert(`加载设置失败: ${error.message}`);
      }
    },

    // 加载通用设置（LLM等）
    async loadGeneralSettings() {
      try {
        const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');

        if (!response.ok) {
          console.warn('无法加载通用设置，使用默认设置');
          return;
        }

        const result = await response.json();

        if (!result.success || !result.settings) {
          console.warn('服务器返回无效的通用设置数据');
          return;
        }

        const userSettings = result.settings;

        // 特别处理: 确保缓存的 geminiModels 不会被加载
        if (userSettings.llm && userSettings.llm.geminiModels) {
          console.log('忽略保存的模型列表，将使用API获取最新模型');
          delete userSettings.llm.geminiModels;
        }

        // 保存当前的geminiModels列表
        const currentGeminiModels = this.settings.llm.geminiModels || [];

        // 合并LLM设置
        if (userSettings.llm) {
          this.settings.llm = {
            ...this.defaultSettings.llm,
            ...userSettings.llm,
            geminiModels: currentGeminiModels
          };
        }

        console.log('[SettingsDialog] 通用设置加载成功');
      } catch (error) {
        console.error('[SettingsDialog] 加载通用设置失败:', error);
      }
    },

    // 加载ComfyUI专用设置
    async loadComfyUISettings() {
      try {
        const response = await fetch('/api/local/load-user-settings?filename=comfyui-settings.json');

        if (!response.ok) {
          console.warn('无法加载ComfyUI设置，使用默认设置');
          return;
        }

        const result = await response.json();

        if (!result.success || !result.settings) {
          console.warn('服务器返回无效的ComfyUI设置数据');
          return;
        }

        const comfyuiSettings = result.settings;

        // 合并ComfyUI设置
        if (comfyuiSettings.comfyui) {
          this.settings.comfyui = {
            ...this.defaultSettings.comfyui,
            ...comfyuiSettings.comfyui,
            // 确保nodeMapping正确合并
            nodeMapping: {
              ...this.defaultSettings.comfyui.nodeMapping,
              ...(comfyuiSettings.comfyui.nodeMapping || {})
            }
          };

          console.log('🔧 [ComfyUI设置] 加载的节点映射:', this.settings.comfyui.nodeMapping);

          // 如果有保存的工作流数据，需要重新初始化工作流解析器
          if (this.settings.comfyui.currentWorkflow && this.settings.comfyui.workflowNodes) {
            console.log('🔧 [ComfyUI设置] 检测到保存的工作流，重新初始化解析器...');
            try {
              // 重新设置工作流解析器的状态
              this.comfyuiWorkflow.workflowNodes.value = this.settings.comfyui.workflowNodes;
              this.comfyuiWorkflow.nodeMapping.value = { ...this.settings.comfyui.nodeMapping };

              console.log('🔧 [ComfyUI设置] 工作流解析器状态已恢复');
              console.log('🔧 [ComfyUI设置] 工作流名称:', this.settings.comfyui.workflowName);
              console.log('🔧 [ComfyUI设置] 节点数量:', this.settings.comfyui.workflowNodes?.length || 0);
            } catch (error) {
              console.error('🔧 [ComfyUI设置] 恢复工作流解析器状态失败:', error);
            }
          }
        }

        console.log('[SettingsDialog] ComfyUI设置加载成功');
        console.log('[SettingsDialog] 检测到的模型:', this.settings.comfyui.detectedModels);
      } catch (error) {
        console.error('[SettingsDialog] 加载ComfyUI设置失败:', error);
      }
    },
    selectModel(modelId) {
      this.settings.llm.defaultModel = modelId;
      console.log('已选择模型:', modelId);
    },
    async checkOpenRouterCredit() {
      const apiKey = this.settings.llm.openrouterApiKey;
      if (!apiKey) {
        this.openrouterCreditInfo = '请先输入OpenRouter的API密钥';
        return;
      }
      try {
        const response = await fetch('https://openrouter.ai/api/v1/auth/key', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });
        if (!response.ok) throw new Error('请求失败: ' + response.status);
        const data = await response.json();
        if (data && data.data) {
          const { limit, usage, is_free_tier, rate_limit } = data.data;
          let msg = '';
          if (limit === null) {
            msg = '当前API Key额度为无限制 (unlimited)。';
          } else {
            const remain = limit - usage;
            msg = `OpenRouter API Key 剩余额度：${remain} / ${limit} (已用 ${usage})`;
          }
          if (is_free_tier) {
            msg += '\n(当前为免费额度)';
          }
          if (rate_limit && typeof rate_limit.requests === 'number' && rate_limit.interval) {
            msg += `\n速率限制：每${rate_limit.interval}最多${rate_limit.requests}次请求`;
          }
          this.openrouterCreditInfo = msg;
        } else {
          this.openrouterCreditInfo = '未能获取到余额信息';
        }
      } catch (e) {
        this.openrouterCreditInfo = '查询余额失败: ' + e.message;
      }
    },

    // ===== ComfyUI 相关方法 =====

    /**
     * 测试ComfyUI连接
     */
    async testComfyUIConnection() {
      this.comfyuiTestStatus = 'loading';
      this.comfyuiTestMessage = '';

      try {
        console.log('[ComfyUI] 开始测试连接:', this.settings.comfyui.serverUrl);

        // 通过后端代理测试连接，避免CORS问题
        const response = await fetch('/api/local/comfyui/test-connection', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            serverUrl: this.settings.comfyui.serverUrl
          })
        });

        const result = await response.json();
        console.log('[ComfyUI] 代理响应:', result);

        if (result.success) {
          this.comfyuiTestStatus = 'success';
          this.comfyuiTestMessage = `连接成功！ComfyUI运行正常`;
          this.settings.comfyui.isConnected = true;

          // 显示系统信息
          if (result.systemStats) {
            console.log('[ComfyUI] 系统状态:', result.systemStats);
          }
        } else {
          throw new Error(result.error || '连接测试失败');
        }
      } catch (error) {
        console.error('[ComfyUI] 连接测试失败:', error);

        this.comfyuiTestStatus = 'error';
        this.settings.comfyui.isConnected = false;

        if (error.message.includes('Failed to fetch')) {
          this.comfyuiTestMessage = '无法连接到后端服务器，请检查应用是否正常运行';
        } else {
          this.comfyuiTestMessage = `连接失败: ${error.message}`;
        }
      }
    },

    /**
     * 触发文件选择
     */
    triggerFileInput() {
      this.$refs.workflowFileInput?.click();
    },

    /**
     * 处理工作流文件选择
     */
    async handleWorkflowFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        await this.processWorkflowFile(file);
      }
    },

    /**
     * 处理工作流文件拖拽
     */
    async handleWorkflowDrop(event) {
      event.preventDefault();
      this.isDragOver = false;

      const files = event.dataTransfer.files;
      if (files.length > 0) {
        const file = files[0];
        if (file.type === 'application/json' || file.name.endsWith('.json')) {
          await this.processWorkflowFile(file);
        } else {
          alert('请上传JSON格式的工作流文件');
        }
      }
    },

    /**
     * 处理工作流文件
     */
    async processWorkflowFile(file) {
      this.isParsingWorkflow = true;

      try {
        const text = await file.text();
        const workflowData = JSON.parse(text);

        // 使用composable解析工作流
        const result = await this.comfyuiWorkflow.parseWorkflow(workflowData, file.name);

        // 更新设置数据
        this.settings.comfyui.currentWorkflow = workflowData;
        this.settings.comfyui.workflowName = file.name;
        this.settings.comfyui.workflowNodes = this.comfyuiWorkflow.workflowNodes.value;
        this.settings.comfyui.nodeMapping = { ...this.comfyuiWorkflow.nodeMapping.value };

        // 🆕 更新检测到的模型信息
        this.settings.comfyui.detectedModels = { ...this.comfyuiWorkflow.detectedModels.value };

        // 🆕 如果连接到ComfyUI，自动获取可用模型列表
        if (this.settings.comfyui.isConnected) {
          console.log('🔧 [工作流上传] 自动获取可用模型列表...');
          try {
            await this.refreshAvailableModels();
          } catch (error) {
            console.warn('⚠️ [工作流上传] 获取模型列表失败，但不影响工作流解析:', error.message);
          }
        } else {
          console.log('ℹ️ [工作流上传] ComfyUI未连接，跳过模型列表获取');
        }

        // 🆕 自动展开模型选择卡片（如果有检测到的模型）
        if (this.settings.comfyui.detectedModels.checkpoints.length > 0 ||
            this.settings.comfyui.detectedModels.loras.length > 0) {
          this.expandedCards.models = true;
        }

        // 立即保存ComfyUI设置
        console.log('🔧 [工作流上传] 工作流解析完成，立即保存设置...');
        await this.saveComfyUISettingsWithCustomNotification('工作流已上传并保存');

        // 🆕 显示增强的成功消息
        const detectedInfo = [];
        if (result.detectedModels?.checkpoints?.length > 0) {
          detectedInfo.push(`${result.detectedModels.checkpoints.length}个Checkpoint模型`);
        }
        if (result.detectedModels?.loras?.length > 0) {
          detectedInfo.push(`${result.detectedModels.loras.length}个LoRA模型`);
        }

        const successMessage = `工作流解析成功！
文件: ${result.fileName}
节点数量: ${result.nodeCount}
自动检测到的节点: ${Object.keys(result.autoDetected).length}个
${detectedInfo.length > 0 ? '检测到的模型: ' + detectedInfo.join(', ') : ''}`;

        alert(successMessage);

      } catch (error) {
        console.error('解析工作流文件失败:', error);
        alert(`解析工作流文件失败: ${error.message}`);
      } finally {
        this.isParsingWorkflow = false;
      }
    }
  },
  watch: {
    'settings.llm.provider': {
      handler(newValue) {
        console.log('提供商变更为:', newValue);
        if (newValue === 'google' && this.settings.llm.googleApiKey) {
          // 当切换到Google且有API密钥时，获取可用模型
          console.log('自动获取Google模型列表...');
          this.fetchGoogleModels();
        }
        // 只有在非初始化状态时才自动保存设置
        if (!this.isInitializing) {
          console.log('🔧 [LLM设置] 用户更改提供商，自动保存');
          this.autoSaveSettings();
        } else {
          console.log('🔧 [LLM设置] 初始化中，跳过自动保存');
        }
      }
    },
    'modelValue': {
      handler(newValue) {
        // 当对话框显示时，重新加载设置
        if (newValue) {
          console.log('[SettingsDialog] 对话框显示，重新加载设置');
          console.log('[SettingsDialog] 加载前的ComfyUI节点映射:', JSON.stringify(this.settings.comfyui.nodeMapping, null, 2));

          // 设置初始化标志，防止加载过程中触发保存
          this.isInitializing = true;

          this.loadSavedSettings().then(() => {
            console.log('[SettingsDialog] 加载后的ComfyUI节点映射:', JSON.stringify(this.settings.comfyui.nodeMapping, null, 2));

            // 延迟启用自动保存
            setTimeout(() => {
              this.isInitializing = false;
              console.log('🔧 [SettingsDialog] 对话框加载完成，启用自动保存机制');
            }, 500);
          });

          // 如果当前提供商是Google并且有API密钥，则获取模型列表
          if (this.settings.llm.provider === 'google' && this.settings.llm.googleApiKey) {
            console.log('对话框显示，自动获取Google模型列表...');
            this.fetchGoogleModels();
          }
        }
      }
    },
    'settings.llm.googleApiKey': {
      handler(newValue) {
        if (newValue && this.settings.llm.provider === 'google') {
          console.log('Google API密钥已更新，自动获取模型列表...');
          this.fetchGoogleModels();
        }
        // 只有在非初始化状态时才自动保存设置
        if (!this.isInitializing) {
          console.log('🔧 [LLM设置] 用户更改API密钥，自动保存');
          this.autoSaveSettings();
        } else {
          console.log('🔧 [LLM设置] 初始化中，跳过自动保存');
        }
      }
    },
    // ComfyUI设置使用即时保存机制，不再使用watch监听器
    // 监听LLM设置变化并自动保存
    'settings.llm': {
      handler(newValue, oldValue) {
        // 避免初始化时触发保存
        if (oldValue && JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
          console.log('[SettingsDialog] LLM设置发生变化，自动保存');
          this.autoSaveSettings();
        }
      },
      deep: true
    }
  },
  mounted() {
    // 添加标志防止初始化时触发保存
    this.isInitializing = true;

    this.loadSavedSettings().then(() => {
      // 加载完成后，延迟一段时间再启用自动保存
      setTimeout(() => {
        this.isInitializing = false;
        console.log('🔧 [SettingsDialog] 初始化完成，启用自动保存机制');
      }, 500);
    });

    // 如果已经选择了Google并且有API密钥，则获取模型列表
    if (this.settings.llm.provider === 'google' && this.settings.llm.googleApiKey) {
      console.log('组件挂载时自动获取Google模型列表...');
      this.fetchGoogleModels();
    }

    // 如果是Google提供商但默认模型ID不在geminiModels列表中，则设置为第一个可用的非弃用模型
    if (this.settings.llm.provider === 'google') {
      if (this.activeGeminiModels.length > 0) {
        const modelIds = this.activeGeminiModels.map(m => m.id);
        if (!modelIds.includes(this.settings.llm.defaultModel)) {
          this.settings.llm.defaultModel = modelIds[0];
          console.log('设置默认模型为第一个可用的非弃用模型:', this.settings.llm.defaultModel);
        }
      } else if (this.settings.llm.googleApiKey) {
        // 如果有API密钥但没有可用模型，尝试获取模型列表
        this.fetchGoogleModels();
      }
    }

    // 🆕 如果ComfyUI已连接且有工作流，自动获取模型列表
    if (this.settings.comfyui.isConnected && this.settings.comfyui.currentWorkflow) {
      console.log('🔧 [组件挂载] 自动获取ComfyUI可用模型列表...');
      setTimeout(async () => {
        try {
          await this.refreshAvailableModels();
          // 如果有检测到的模型，自动展开模型选择卡片
          if (this.settings.comfyui.detectedModels.checkpoints.length > 0 ||
              this.settings.comfyui.detectedModels.loras.length > 0) {
            this.expandedCards.models = true;
          }
        } catch (error) {
          console.warn('⚠️ [组件挂载] 获取ComfyUI模型列表失败:', error.message);
        }
      }, 1000); // 延迟1秒，确保组件完全初始化
    }
  },

  beforeUnmount() {
    // 清理自动保存定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }
};
</script>

<style scoped>
.settings-dialog {
  max-width: 85vw;
  width: 85vw;
  height: 90vh;
  margin: auto;
  overflow: hidden;
}

.settings-container {
  display: flex;
  height: 100%;
  background-color: #1d1d2c;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
}

.settings-sidebar {
  width: 240px;
  min-width: 240px;
  background-color: #181825;
  border-right: 1px solid #292945;
  padding: 1.8rem 0;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0 1.8rem;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 1rem 1.8rem;
  color: #a6adc8;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-item i {
  font-size: 1.4rem;
}

.menu-item:hover {
  background-color: #292945;
  color: #cdd6f4;
}

.menu-item.active {
  background-color: #292945;
  color: #f5c2e7;
  border-left: 3px solid #f5c2e7;
}

.settings-content {
  flex: 1;
  padding: 3rem 4rem;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  min-width: 0; /* 防止flex子项溢出 */
}

.settings-panel {
  width: 100%;
  margin: 0 auto;
}

.panel-title {
  font-size: 1.8rem;
  color: #f5c2e7;
  margin-bottom: 2rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid #292945;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.form-row {
  display: flex;
  gap: 3rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.form-group {
  flex: 1;
  min-width: 300px;
  margin-bottom: 1rem;
}

.api-key-group {
  flex: 2;
  min-width: 400px;
}

.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  color: #cdd6f4;
  font-weight: bold;
  font-size: 1.1rem;
}

.form-input {
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  padding: 1.2rem;
  background-color: #232338;
  border: 1px solid #333344;
  border-radius: 8px;
  color: #cdd6f4;
  font-size: 1.2rem;
}

.form-input:focus {
  border-color: #f5c2e7;
  outline: none;
  box-shadow: 0 0 0 2px rgba(245, 194, 231, 0.2);
}

.form-hint {
  font-size: 0.95rem;
  color: #7f849c;
  margin-top: 0.8rem;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.form-slider {
  flex: 1;
  -webkit-appearance: none;
  height: 8px;
  background: #333344;
  border-radius: 4px;
  outline: none;
}

.form-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #f5c2e7;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-slider::-moz-range-thumb {
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #f5c2e7;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.form-slider::-webkit-slider-thumb:hover {
  background: #2dc0f0;
  transform: scale(1.2);
}

.slider-value {
  color: #f5c2e7;
  font-weight: bold;
  min-width: 2.5rem;
  text-align: center;
  font-size: 1.1rem;
}

.action-buttons {
  display: flex;
  gap: 1.5rem;
  margin-top: 3rem;
  justify-content: flex-end;
}

.save-button, .cancel-button {
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: bold;
  font-size: 1.05rem;
  cursor: pointer;
  transition: all 0.2s;
}

.save-button {
  background-color: #2dc0f0;
  color: #11111b;
  border: none;
}

.save-button:hover {
  background-color: #3ad7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(45, 192, 240, 0.3);
}

.cancel-button {
  background-color: #333344;
  color: #cdd6f4;
  border: none;
}

.cancel-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(69, 71, 90, 0.3);
}

.placeholder-text {
  color: #7f849c;
  font-style: italic;
  margin-top: 3rem;
  text-align: center;
  font-size: 1.1rem;
}

.api-input-group {
  display: flex;
  gap: 1rem;
  width: 100%;
  align-items: center;
  position: relative;
}

.test-api-button {
  background-color: #333344;
  color: #cdd6f4;
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.test-api-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

.loading-indicator {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #cdd6f4;
  animation: spin 1s ease-in-out infinite;
}

.status-indicator {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 50%;
}

.status-indicator.success {
  background-color: #2dc0f0;
  box-shadow: 0 0 10px #2dc0f0;
  animation: pulse 2s infinite;
}

.status-indicator.error {
  background-color: #f38ba8;
  box-shadow: 0 0 10px #f38ba8;
  animation: pulse 2s infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.input-wrapper {
  position: relative;
  width: 100%;
}
.form-input.with-icon {
  padding-right: 2.5rem; /* 为图标留出空间 */
}
.password-toggle-button {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #7f849c;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  transition: color 0.2s;
}
.password-toggle-button:hover {
  color: #f5c2e7;
}
.api-test-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.7rem;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  font-size: 0.95rem;
}

.api-test-message.success {
  background-color: rgba(45, 192, 240, 0.15);
  color: #2dc0f0;
}

.api-test-message.error {
  background-color: rgba(243, 139, 168, 0.15);
  color: #f38ba8;
}

.api-test-message i {
  font-size: 1.1rem;
}

/* 响应式布局调整 */
@media (max-width: 1200px) {
  .settings-dialog {
    width: 95vw;
  }
  
  .settings-content {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
    min-width: 0;
    padding: 1rem 0;
  }
  
  .settings-content {
    padding: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-group {
    min-width: 100%;
  }
}

.model-selection {
  margin: 20px 0;
}

.model-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.model-card {
  border: 1px solid #333344;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #232338;
}

.model-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.model-card.selected {
  border-color: #f5c2e7;
  background-color: rgba(245, 194, 231, 0.1);
}

.model-card h4 {
  margin: 0 0 10px 0;
  color: #cdd6f4;
}

.model-card p {
  margin: 0 0 15px 0;
  color: #a6adc8;
  font-size: 0.9em;
}

.capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.capability-tag {
  background-color: #333344;
  color: #cdd6f4;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
}

.model-limits {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 0.8em;
  color: #a6adc8;
}

.loading-models {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #cdd6f4;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.no-models-message {
  padding: 1rem;
  background-color: rgba(243, 139, 168, 0.15);
  border-radius: 8px;
  color: #f38ba8;
  text-align: center;
  margin-top: 1rem;
}

/* ComfyUI 设置样式 */
.setting-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #232338;
  border-radius: 8px;
  border: 1px solid #333344;
}

.group-title {
  font-size: 1.3rem;
  color: #f5c2e7;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #333344;
}

.input-group {
  margin-bottom: 1rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #cdd6f4;
  font-weight: 500;
}

.setting-input, .setting-select {
  width: 100%;
  padding: 0.8rem;
  background-color: #181825;
  border: 1px solid #333344;
  border-radius: 6px;
  color: #cdd6f4;
  font-size: 1rem;
}

.setting-input:focus, .setting-select:focus {
  border-color: #f5c2e7;
  outline: none;
  box-shadow: 0 0 0 2px rgba(245, 194, 231, 0.2);
}

.input-with-button {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.input-with-button .setting-input {
  flex: 1;
}

.test-button {
  padding: 0.8rem 1.2rem;
  background-color: #333344;
  color: #cdd6f4;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  white-space: nowrap;
  transition: all 0.2s;
}

.test-button:hover {
  background-color: #45475a;
}

.test-button.loading {
  background-color: #f5c2e7;
  color: #11111b;
}

.test-message {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.test-message.success {
  background-color: rgba(45, 192, 240, 0.15);
  color: #2dc0f0;
}

.test-message.error {
  background-color: rgba(243, 139, 168, 0.15);
  color: #f38ba8;
}

.upload-area {
  border: 2px dashed #333344;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #181825;
}

.upload-area:hover, .upload-area.drag-over {
  border-color: #f5c2e7;
  background-color: rgba(245, 194, 231, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-icon {
  font-size: 2rem;
  color: #7f849c;
}

.upload-area:hover .upload-icon {
  color: #f5c2e7;
}

.upload-hint {
  font-size: 0.8rem;
  color: #7f849c;
}

.workflow-info {
  padding: 1rem;
  background-color: #181825;
  border-radius: 6px;
  border: 1px solid #333344;
}

.workflow-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #cdd6f4;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.workflow-stats {
  color: #7f849c;
  font-size: 0.9rem;
}

.mapping-description {
  color: #a6adc8;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.required {
  color: #f38ba8;
  font-weight: bold;
}

.mapping-validation {
  margin-top: 1rem;
  padding: 0.8rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.validation-success {
  background-color: rgba(45, 192, 240, 0.15);
  color: #2dc0f0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.validation-warning {
  background-color: rgba(249, 226, 175, 0.15);
  color: #f9e2af;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.node-description {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(45, 192, 240, 0.1);
  border-radius: 4px;
  color: #a6adc8;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.node-description i {
  color: #2dc0f0;
}

/* ComfyUI 现代化样式 */
.comfyui-modern {
  max-width: none;
  padding: 0;
}

/* 紧凑型头部 */
.compact-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #89b4fa 0%, #74c7ec 100%);
  border-radius: 8px;
  margin-bottom: 20px;
}

.compact-header .header-icon {
  font-size: 24px;
  color: #11111b;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compact-header .header-text {
  flex: 1;
}

.compact-header .panel-title {
  color: #11111b;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.compact-header .panel-subtitle {
  color: rgba(17, 17, 27, 0.7);
  font-size: 13px;
  margin: 2px 0 0 0;
  font-weight: 400;
}

/* 紧凑型进度指示器 */
.compact-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: #313244;
  border-radius: 8px;
  border: 1px solid #45475a;
  gap: 12px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #6c7086;
  background: transparent;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.progress-item.active {
  background: #89b4fa;
  color: #11111b;
  opacity: 1;
}

.progress-item.completed {
  background: #a6e3a1;
  color: #11111b;
  opacity: 1;
}

.progress-item i {
  font-size: 14px;
}

.progress-arrow {
  font-size: 16px;
  color: #45475a;
  transition: all 0.3s ease;
}

.progress-arrow.completed {
  color: #a6e3a1;
}

/* 配置卡片 */
.config-card {
  background: #1e1e2e;
  border: 1px solid #45475a;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.config-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.config-card.expanded {
  border-color: #89b4fa;
  box-shadow: 0 4px 20px rgba(137, 180, 250, 0.1);
}

.card-header {
  padding: 20px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid transparent;
}

.card-header:hover {
  background: #313244;
}

.config-card.expanded .card-header {
  border-bottom-color: #45475a;
  background: #313244;
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: #45475a;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #89b4fa;
}

.card-info {
  flex: 1;
  margin-left: 16px;
}

.card-name {
  font-weight: 600;
  font-size: 16px;
  color: #cdd6f4;
  display: block;
}

.card-desc {
  font-size: 14px;
  color: #a6adc8;
  margin-top: 2px;
  display: block;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.connection-status.connected {
  background: rgba(166, 227, 161, 0.2);
  color: #a6e3a1;
}

.connection-status.disconnected {
  background: rgba(243, 139, 168, 0.2);
  color: #f38ba8;
}

.workflow-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background: rgba(166, 227, 161, 0.2);
  color: #a6e3a1;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.card-arrow {
  font-size: 20px;
  color: #a6adc8;
  transition: transform 0.3s ease;
}

.card-arrow.rotated {
  transform: rotate(180deg);
}

.card-content {
  padding: 24px;
}

/* 现代化输入组件 */
.input-group.modern {
  margin-bottom: 24px;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #cdd6f4;
  margin-bottom: 12px;
  font-size: 14px;
}

.input-with-action {
  display: flex;
  gap: 12px;
  align-items: stretch;
}

.modern-input {
  flex: 1;
  padding: 12px 16px;
  background: #313244;
  border: 2px solid #45475a;
  border-radius: 8px;
  color: #cdd6f4;
  font-size: 14px;
  transition: all 0.3s ease;
}

.modern-input:focus {
  outline: none;
  border-color: #89b4fa;
  box-shadow: 0 0 0 3px rgba(137, 180, 250, 0.1);
}

.action-btn {
  padding: 12px 20px;
  background: #89b4fa;
  color: #11111b;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.action-btn:hover {
  background: #74c7ec;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.action-btn.loading {
  background: #f9e2af;
  color: #11111b;
}

.action-btn.success {
  background: #a6e3a1;
  color: #11111b;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.status-message.success {
  background: rgba(166, 227, 161, 0.15);
  color: #a6e3a1;
  border: 1px solid rgba(166, 227, 161, 0.3);
}

.status-message.error {
  background: rgba(243, 139, 168, 0.15);
  color: #f38ba8;
  border: 1px solid rgba(243, 139, 168, 0.3);
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #45475a;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #a6adc8;
}

.quick-action-item:hover {
  background: #585b70;
  color: #cdd6f4;
}

/* 工作流相关样式 */
.current-workflow {
  margin-bottom: 24px;
}

.workflow-info-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #313244;
  border: 1px solid #45475a;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.workflow-info-card:hover {
  border-color: #89b4fa;
}

.workflow-icon {
  width: 48px;
  height: 48px;
  background: #89b4fa;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #11111b;
}

.workflow-details {
  flex: 1;
}

.workflow-name {
  font-size: 16px;
  font-weight: 600;
  color: #cdd6f4;
  margin-bottom: 8px;
}

.workflow-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #a6adc8;
}

.workflow-actions {
  display: flex;
  gap: 8px;
}

.icon-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #45475a;
  color: #a6adc8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.icon-btn:hover {
  background: #585b70;
  color: #cdd6f4;
}

.icon-btn.danger:hover {
  background: #f38ba8;
  color: #11111b;
}

/* 上传区域 */
.upload-section {
  text-align: center;
}

.modern-upload-area {
  border: 2px dashed #45475a;
  border-radius: 16px;
  padding: 48px 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #1e1e2e;
  position: relative;
  overflow: hidden;
}

.modern-upload-area:hover,
.modern-upload-area.drag-over {
  border-color: #89b4fa;
  background: #313244;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(137, 180, 250, 0.15);
}

.modern-upload-area.uploading {
  border-color: #f9e2af;
  background: rgba(249, 226, 175, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
  color: #89b4fa;
  transition: all 0.3s ease;
}

.modern-upload-area:hover .upload-icon {
  transform: scale(1.1);
}

.upload-title {
  font-size: 20px;
  font-weight: 600;
  color: #cdd6f4;
  margin: 0;
}

.upload-description {
  font-size: 14px;
  color: #a6adc8;
  margin: 0;
  line-height: 1.5;
}

.upload-formats {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.format-tag {
  padding: 4px 12px;
  background: #45475a;
  border-radius: 16px;
  font-size: 12px;
  color: #a6adc8;
  font-weight: 500;
}

.upload-tips {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #313244;
  border-radius: 8px;
  font-size: 14px;
  color: #a6adc8;
  text-align: left;
}

.tip-item i {
  color: #89b4fa;
  font-size: 16px;
}

/* 紧凑型卡片样式 */
.config-card.compact {
  margin-bottom: 16px;
}

.config-card.compact .card-header {
  padding: 16px 20px;
}

.config-card.compact .card-content {
  padding: 16px 20px;
}

.config-card.compact .card-content.compact {
  padding: 12px 16px;
}

/* 紧凑型工作流显示 */
.compact-workflow {
  margin-bottom: 0;
}

.workflow-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #313244;
  border-radius: 8px;
  border: 1px solid #45475a;
}

.workflow-summary .workflow-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.workflow-summary .workflow-info i {
  color: #89b4fa;
  font-size: 16px;
}

.workflow-summary .workflow-name {
  font-weight: 500;
  color: #cdd6f4;
  font-size: 14px;
}

.workflow-nodes {
  font-size: 12px;
  color: #a6adc8;
  background: #45475a;
  padding: 2px 8px;
  border-radius: 12px;
}

.workflow-summary .workflow-actions {
  display: flex;
  gap: 6px;
}

.compact-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: #45475a;
  color: #a6adc8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 12px;
}

.compact-btn:hover {
  background: #585b70;
  color: #cdd6f4;
}

.compact-btn.danger:hover {
  background: #f38ba8;
  color: #11111b;
}

/* 紧凑型上传区域 */
.compact-upload {
  margin-bottom: 0;
}

.upload-zone {
  border: 2px dashed #45475a;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #1e1e2e;
  text-align: center;
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: #89b4fa;
  background: #313244;
}

.upload-zone.uploading {
  border-color: #f9e2af;
  background: rgba(249, 226, 175, 0.05);
}

.upload-zone .upload-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.upload-zone .upload-icon {
  font-size: 20px;
  color: #89b4fa;
}

.upload-zone .upload-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: left;
}

.upload-zone .upload-title {
  font-size: 14px;
  font-weight: 500;
  color: #cdd6f4;
}

.upload-zone .upload-hint {
  font-size: 12px;
  color: #a6adc8;
}

/* 紧凑型节点映射 */
.compact-node-mapping {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.node-group {
  background: #313244;
  border-radius: 8px;
  border: 1px solid #45475a;
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #45475a;
  font-size: 13px;
  font-weight: 500;
  color: #cdd6f4;
}

.group-header i {
  font-size: 14px;
}

.node-group.required .group-header {
  background: rgba(137, 180, 250, 0.1);
  color: #89b4fa;
}

.node-group.optional .group-header {
  background: rgba(166, 173, 200, 0.1);
  color: #a6adc8;
}

.node-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  padding: 16px;
}

.node-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.node-label {
  font-size: 12px;
  font-weight: 500;
  color: #a6adc8;
}

.node-label .required {
  color: #f38ba8;
}

.compact-select {
  padding: 8px 12px;
  background: #1e1e2e;
  border: 1px solid #45475a;
  border-radius: 6px;
  color: #cdd6f4;
  font-size: 13px;
  transition: all 0.3s ease;
}

.compact-select:focus {
  outline: none;
  border-color: #89b4fa;
  box-shadow: 0 0 0 2px rgba(137, 180, 250, 0.1);
}

/* 配置状态 */
.mapping-status {
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
}

.status-success {
  background: rgba(166, 227, 161, 0.15);
  color: #a6e3a1;
  border: 1px solid rgba(166, 227, 161, 0.3);
}

.status-warning {
  background: rgba(249, 226, 175, 0.15);
  color: #f9e2af;
  border: 1px solid rgba(249, 226, 175, 0.3);
}

/* 生成设置 */
.generation-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compact-input {
  width: 100%;
  padding: 8px 12px;
  background: rgba(30, 30, 46, 0.8);
  border: 1px solid rgba(116, 199, 236, 0.3);
  border-radius: 6px;
  color: #cdd6f4;
  font-size: 14px;
  transition: all 0.3s ease;
}

.compact-input:focus {
  outline: none;
  border-color: #74c7ec;
  background: rgba(30, 30, 46, 1);
}

.compact-input::placeholder {
  color: rgba(205, 214, 244, 0.5);
}

.setting-hint {
  font-size: 12px;
  color: rgba(205, 214, 244, 0.6);
  margin-top: 4px;
  font-style: italic;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.setting-label {
  font-size: 12px;
  font-weight: 500;
  color: #a6adc8;
}

/* 🔥 新增：分辨率设置样式 */
.resolution-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.resolution-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.resolution-label {
  font-size: 12px;
  color: #a6adc8;
  font-weight: 500;
}

.resolution-input {
  width: 100%;
  max-width: 120px;
}

.resolution-separator {
  font-size: 16px;
  font-weight: bold;
  color: #cdd6f4;
  margin-top: 20px;
}

.resolution-unit {
  font-size: 12px;
  color: #6c7086;
  margin-left: 4px;
}

.resolution-presets {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.preset-button {
  padding: 6px 12px;
  background: #313244;
  border: 1px solid #45475a;
  border-radius: 6px;
  color: #cdd6f4;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-button:hover {
  background: #45475a;
  border-color: #585b70;
}

.preset-button.active {
  background: #89b4fa;
  border-color: #89b4fa;
  color: #1e1e2e;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compact-header {
    padding: 12px 16px;
    margin-bottom: 16px;
  }

  .compact-header .header-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }

  .compact-header .panel-title {
    font-size: 16px;
  }

  .compact-header .panel-subtitle {
    font-size: 12px;
  }

  .compact-progress {
    flex-direction: column;
    gap: 8px;
    padding: 12px 16px;
  }

  .progress-item {
    justify-content: center;
    padding: 6px 10px;
  }

  .progress-arrow {
    transform: rotate(90deg);
    font-size: 14px;
  }

  .config-card.compact .card-content {
    padding: 12px 16px;
  }

  .input-with-action {
    flex-direction: column;
    gap: 8px;
  }

  .quick-actions {
    flex-direction: column;
    gap: 8px;
  }

  .workflow-summary {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .node-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 12px;
  }

  .upload-content {
    flex-direction: column;
    gap: 8px;
  }

  .upload-text {
    text-align: center;
  }

  .model-grid {
    gap: 12px;
  }

  .lora-strength {
    flex-direction: column;
    gap: 8px;
  }

  .strength-item {
    justify-content: space-between;
  }
}

/* 🆕 模型选择样式 */
.model-selection-info {
  background: rgba(137, 180, 250, 0.1);
  border: 1px solid rgba(137, 180, 250, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.model-selection-info .info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #89b4fa;
  font-size: 13px;
  margin-bottom: 8px;
}

.model-selection-info .info-item:last-child {
  margin-bottom: 0;
}

.model-selection-info .info-item i {
  font-size: 14px;
}

.model-selection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.empty-models-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  background: rgba(40, 42, 54, 0.3);
  border: 2px dashed rgba(68, 71, 90, 0.5);
  border-radius: 12px;
}

.empty-models-state .empty-icon {
  font-size: 48px;
  color: #7f849c;
  margin-bottom: 16px;
}

.empty-models-state .empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #cdd6f4;
  margin-bottom: 12px;
}

.empty-models-state .empty-description {
  font-size: 14px;
  color: #a6adc8;
  line-height: 1.5;
  margin-bottom: 24px;
  max-width: 400px;
}

.model-group {
  background: rgba(40, 42, 54, 0.5);
  border: 1px solid rgba(68, 71, 90, 0.5);
  border-radius: 12px;
  padding: 20px;
}

.model-group .group-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  color: #f9e2af;
  font-weight: 600;
  font-size: 14px;
}

.model-group .group-header i {
  font-size: 16px;
}

.model-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.model-item {
  background: rgba(35, 35, 56, 0.8);
  border: 1px solid rgba(68, 71, 90, 0.3);
  border-radius: 8px;
  padding: 16px;
}

.model-item .model-label {
  display: block;
  color: #cdd6f4;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 13px;
}

.model-item .compact-select {
  width: 100%;
  background: rgba(35, 35, 56, 1);
  border: 1px solid rgba(68, 71, 90, 0.5);
  border-radius: 6px;
  color: #cdd6f4;
  padding: 8px 12px;
  font-size: 13px;
  margin-bottom: 8px;
}

.model-item .compact-select:focus {
  border-color: #89b4fa;
  outline: none;
  box-shadow: 0 0 0 2px rgba(137, 180, 250, 0.2);
}

.model-item .compact-select .disable-option {
  color: #f38ba8;
  font-style: italic;
}

.model-item .current-model {
  color: #7f849c;
  font-size: 12px;
  font-style: italic;
}

.model-item .current-model .disabled-status {
  color: #f38ba8;
  font-weight: 600;
}

.lora-item .lora-strength {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.lora-item .strength-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.lora-item .strength-item label {
  color: #a6adc8;
  font-size: 12px;
  white-space: nowrap;
  margin: 0;
}

.lora-item .strength-input {
  flex: 1;
  background: rgba(35, 35, 56, 1);
  border: 1px solid rgba(68, 71, 90, 0.5);
  border-radius: 4px;
  color: #cdd6f4;
  padding: 4px 8px;
  font-size: 12px;
  width: 60px;
}

.lora-item .strength-input:focus {
  border-color: #89b4fa;
  outline: none;
}

.lora-item .disabled-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(243, 139, 168, 0.1);
  border: 1px solid rgba(243, 139, 168, 0.3);
  border-radius: 6px;
  color: #f38ba8;
  font-size: 12px;
}

.lora-item .disabled-notice i {
  font-size: 14px;
}

.model-actions {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.refresh-btn {
  background: rgba(137, 180, 250, 0.1);
  border: 1px solid rgba(137, 180, 250, 0.3);
  color: #89b4fa;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(137, 180, 250, 0.2);
  border-color: rgba(137, 180, 250, 0.5);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-btn .spinning {
  animation: spin 1s linear infinite;
}
</style>