<template>
  <div class="file-section">
    <h2>{{ title }}</h2>
    <div 
      class="file-upload-area" 
      @click="triggerFileSelect"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave" 
      @drop.prevent="handleFileDrop"
      :class="{'dragging': isDragging}"
    >
      <div class="file-icon">
        <i :class="icon" />
      </div>
      <div class="file-text">
        <p class="file-main-text">
          {{ mainText }}
        </p>
        <p class="file-sub-text">
          {{ subText }}
        </p>
      </div>
      <input 
        type="file" 
        :id="inputId" 
        :accept="acceptTypes" 
        style="display: none" 
        @change="handleFileSelected"
      >
    </div>
    
    <!-- 显示上传状态 -->
    <div
      class="upload-status"
      v-if="uploadStatus"
      :class="{'upload-success-status': uploadStatus === '上传成功'}"
    >
      <div class="status-text">
        {{ fileName }} - {{ uploadStatus }}
      </div>
      <div class="progress-bar">
        <div
          class="progress"
          :style="{width: `${uploadProgress}%`}"
        />
      </div>
    </div>
    
    <!-- 已上传文件信息 -->
    <div
      class="uploaded-file-info"
      v-if="filePath && (!uploadStatus || uploadStatus === '上传成功')"
    >
      <div class="file-info">
        <i :class="fileIconClass" />
        <span class="file-name">{{ extractFileName(filePath) }}</span>
        <span class="upload-success">已上传</span>
      </div>
      <button
        class="delete-file-btn"
        @click="deleteFile"
      >
        <i class="ri-delete-bin-line" />
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileUploader',
  props: {
    title: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: 'ri-file-text-line'
    },
    mainText: {
      type: String,
      default: '导入本地文件'
    },
    subText: {
      type: String,
      default: '单击或拖动文件到此区域'
    },
    acceptTypes: {
      type: String,
      default: '*/*'
    },
    inputId: {
      type: String,
      required: true
    },
    fileIconClass: {
      type: String,
      default: 'ri-file-text-line file-icon-small'
    },
    uploadStatus: String,
    uploadProgress: {
      type: Number,
      default: 0
    },
    fileName: String,
    filePath: String,
    projectTitle: String,
    chapterTitle: String
  },
  emits: ['file-selected', 'delete-file'],
  data() {
    return {
      isDragging: false
    }
  },
  methods: {
    triggerFileSelect() {
      document.getElementById(this.inputId).click();
    },
    
    handleDragOver() {
      this.isDragging = true;
    },
    
    handleDragLeave() {
      this.isDragging = false;
    },
    
    handleFileDrop(event) {
      this.isDragging = false;
      
      const file = event.dataTransfer.files[0];
      if (!file) return;
      
      this.$emit('file-selected', file);
    },
    
    handleFileSelected(event) {
      const selectedFile = event.target?.files?.[0];
      if (!selectedFile) {
        console.log('未选择文件');
        return;
      }
      
      console.log(`文件选择: ${selectedFile.name}, 大小: ${selectedFile.size}B, 类型: ${selectedFile.type}`);
      
      // 手动创建一个FormData对象，测试是否能正确添加
      const testFormData = new FormData();
      testFormData.append('file', selectedFile);
      testFormData.append('projectTitle', this.projectTitle || '');
      testFormData.append('chapterTitle', this.chapterTitle || '');
      
      console.log('测试FormData创建:');
      for (const entry of testFormData.entries()) {
        const [key, value] = entry;
        console.log(`${key}: ${key === 'file' ? value.name : value}`);
      }
      
      // 重置file input，确保同一文件可以重复选择
      if (event.target) {
        event.target.value = '';
      }
      
      // 通知父组件处理文件上传
      this.$emit('file-selected', selectedFile);
    },
    
    deleteFile() {
      this.$emit('delete-file');
    },
    
    extractFileName(path) {
      if (!path) return '';
      const parts = path.split(/[/\\]/);
      return parts[parts.length - 1];
    }
  }
}
</script>

<style scoped>
.file-section {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.file-section h2 {
  font-size: 1.2rem;
  color: #f5c2e7;
  text-align: center;
  margin-bottom: 1rem;
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  background-color: #1d1d2c;
  border: 2px dashed #444466;
  border-radius: 8px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-upload-area:hover {
  border-color: #2dc0f0;
  background-color: #202035;
}

.file-icon {
  font-size: 2.5rem;
  color: #f5c2e7;
}

.file-text {
  text-align: center;
}

.file-main-text {
  font-size: 1.2rem;
  color: #cdd6f4;
  margin-bottom: 0.5rem;
}

.file-sub-text {
  font-size: 0.9rem;
  color: #7f849c;
}

.file-upload-area.dragging {
  background-color: #202040;
  border-color: #2dc0f0;
}

.upload-status {
  margin-top: 1rem;
  padding: 0.8rem;
  background-color: #1d1d2c;
  border-radius: 4px;
  border-left: 3px solid #f5c2e7;
}

.status-text {
  font-size: 0.9rem;
  color: #cdd6f4;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 6px;
  background-color: #333344;
  border-radius: 3px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #f5c2e7;
  transition: width 0.3s ease;
}

.upload-success-status {
  border-left-color: #2dc0f0;
}

.uploaded-file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem;
  margin-top: 1rem;
  background-color: #1d1d2c;
  border-radius: 4px;
  border-left: 3px solid #2dc0f0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-icon-small {
  font-size: 1.2rem;
  color: #f5c2e7;
}

.file-name {
  font-size: 0.9rem;
  color: #cdd6f4;
}

.upload-success {
  font-size: 0.8rem;
  color: #2dc0f0;
  margin-left: 0.5rem;
}

.delete-file-btn {
  background: none;
  border: none;
  color: #f38ba8;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.delete-file-btn:hover {
  color: #f5c2e7;
  transform: scale(1.1);
}
</style> 