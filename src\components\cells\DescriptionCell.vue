<template>
  <div class="grid-cell description-cell">
    <div class="description-wrapper">
      <div class="readonly-text">
        {{ description }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DescriptionCell',
  props: {
    description: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.description-cell {
  /* 🔧 改为内容自适应宽度 */
  width: fit-content;
  min-width: 150px;
  max-width: 300px;
}

.description-wrapper {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow: auto;
  background-color: #252525;
  text-align: left;
}

.readonly-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.4;
  color: #e0e0e0;
  font-size: 0.9rem;
}
</style>



 




 
