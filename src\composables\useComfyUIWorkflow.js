/**
 * ComfyUI工作流解析和管理
 * 负责解析工作流JSON文件，识别节点类型，管理节点映射
 */

import { ref, computed } from 'vue';

export function useComfyUIWorkflow() {
  // 状态管理
  const currentWorkflow = ref(null);
  const workflowNodes = ref([]);
  const nodeMapping = ref({
    positivePromptNode: '',
    negativePromptNode: '',
    imageSizeNode: '',
    saveImageNode: '',
    samplerNode: '',
    batchSizeNode: '' // 批次大小节点
  });
  // 🆕 新增：模型和LoRA检测结果
  const detectedModels = ref({
    checkpoints: [], // 检测到的Checkpoint节点
    loras: []       // 检测到的LoRA节点
  });
  const availableModels = ref({
    checkpoints: [], // 可用的Checkpoint模型列表
    loras: []       // 可用的LoRA模型列表
  });
  const isLoading = ref(false);
  const error = ref(null);

  // 计算属性：按类型分组的节点
  const nodesByType = computed(() => {
    const grouped = {};
    workflowNodes.value.forEach(node => {
      if (!grouped[node.class_type]) {
        grouped[node.class_type] = [];
      }
      grouped[node.class_type].push(node);
    });
    return grouped;
  });

  // 计算属性：获取特定类型的节点选项
  const getNodeOptions = computed(() => (nodeType) => {
    const nodes = nodesByType.value[nodeType] || [];
    return nodes.map(node => ({
      value: node.id,
      label: `${node.id} - ${node.title || node.class_type}`,
      node: node
    }));
  });

  // 计算属性：验证节点映射是否完整
  const isMappingValid = computed(() => {
    const requiredMappings = ['positivePromptNode', 'saveImageNode'];
    return requiredMappings.every(key => nodeMapping.value[key]);
  });

  /**
   * 解析工作流JSON文件
   * @param {Object} workflowData - 工作流JSON数据
   * @param {string} fileName - 文件名
   * @returns {Promise<Object>} 解析结果
   */
  async function parseWorkflow(workflowData, fileName = '未命名工作流') {
    isLoading.value = true;
    error.value = null;

    try {
      // 验证JSON格式
      if (!workflowData || typeof workflowData !== 'object') {
        throw new Error('无效的工作流格式：必须是有效的JSON对象');
      }

      // 解析节点
      const nodes = [];
      for (const [nodeId, nodeData] of Object.entries(workflowData)) {
        if (nodeData && nodeData.class_type) {
          nodes.push({
            id: nodeId,
            class_type: nodeData.class_type,
            title: nodeData._meta?.title || nodeData.class_type,
            inputs: nodeData.inputs || {},
            outputs: nodeData.outputs || [],
            meta: nodeData._meta || {}
          });
        }
      }

      if (nodes.length === 0) {
        throw new Error('工作流中没有找到有效的节点');
      }

      // 更新状态
      currentWorkflow.value = workflowData;
      workflowNodes.value = nodes;

      // 自动检测关键节点
      autoDetectNodes();

      // 🆕 检测模型和LoRA节点
      detectModelNodes();

      console.log(`[useComfyUIWorkflow] 成功解析工作流: ${fileName}`);
      console.log(`[useComfyUIWorkflow] 节点数量: ${nodes.length}`);
      console.log(`[useComfyUIWorkflow] 节点类型:`, Object.keys(nodesByType.value));
      console.log(`[useComfyUIWorkflow] 检测到的模型:`, detectedModels.value);

      return {
        success: true,
        fileName,
        nodeCount: nodes.length,
        nodeTypes: Object.keys(nodesByType.value),
        autoDetected: getAutoDetectedNodes(),
        detectedModels: detectedModels.value // 🆕 返回检测到的模型信息
      };

    } catch (err) {
      error.value = err.message;
      console.error('[useComfyUIWorkflow] 解析工作流失败:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * 自动检测关键节点
   */
  function autoDetectNodes() {
    const newMapping = { ...nodeMapping.value };

    // 检测CLIPTextEncode节点（提示词节点）
    const clipNodes = nodesByType.value['CLIPTextEncode'] || [];
    clipNodes.forEach(node => {
      const title = node.title.toLowerCase();
      if (title.includes('positive') || title.includes('正向')) {
        newMapping.positivePromptNode = node.id;
      } else if (title.includes('negative') || title.includes('负向')) {
        newMapping.negativePromptNode = node.id;
      }
    });

    // 如果没有通过标题识别，使用第一个和第二个CLIPTextEncode节点
    if (!newMapping.positivePromptNode && clipNodes.length > 0) {
      newMapping.positivePromptNode = clipNodes[0].id;
    }
    if (!newMapping.negativePromptNode && clipNodes.length > 1) {
      newMapping.negativePromptNode = clipNodes[1].id;
    }

    // 检测EmptyLatentImage节点（图像尺寸节点和批次大小节点）
    const latentNodes = nodesByType.value['EmptyLatentImage'] || [];
    if (latentNodes.length > 0) {
      newMapping.imageSizeNode = latentNodes[0].id;
      // 同时也是批次大小节点（因为EmptyLatentImage包含batch_size参数）
      newMapping.batchSizeNode = latentNodes[0].id;
    }

    // 检测SaveImage节点（保存节点）
    const saveNodes = nodesByType.value['SaveImage'] || [];
    if (saveNodes.length > 0) {
      newMapping.saveImageNode = saveNodes[0].id;
    }

    // 检测KSampler节点（采样器节点）
    const samplerNodes = nodesByType.value['KSampler'] || [];
    if (samplerNodes.length > 0) {
      newMapping.samplerNode = samplerNodes[0].id;
    }

    nodeMapping.value = newMapping;
  }

  /**
   * 🆕 检测模型和LoRA节点
   */
  function detectModelNodes() {
    const checkpoints = [];
    const loras = [];

    // 检测Checkpoint节点
    const checkpointNodes = nodesByType.value['CheckpointLoaderSimple'] || [];
    checkpointNodes.forEach(node => {
      const modelName = node.inputs?.ckpt_name;
      if (modelName) {
        checkpoints.push({
          nodeId: node.id,
          nodeName: node.title || `Checkpoint ${node.id}`,
          modelName: modelName,
          modelPath: modelName // 保存完整路径
        });
      }
    });

    // 检测LoRA节点
    const loraNodes = nodesByType.value['LoraLoader'] || [];
    loraNodes.forEach(node => {
      const loraName = node.inputs?.lora_name;
      if (loraName) {
        loras.push({
          nodeId: node.id,
          nodeName: node.title || `LoRA ${node.id}`,
          loraName: loraName,
          loraPath: loraName, // 保存完整路径
          strengthModel: node.inputs?.strength_model || 1.0,
          strengthClip: node.inputs?.strength_clip || 1.0
        });
      }
    });

    // 更新检测结果
    detectedModels.value = { checkpoints, loras };

    console.log('[useComfyUIWorkflow] 检测到的Checkpoint节点:', checkpoints);
    console.log('[useComfyUIWorkflow] 检测到的LoRA节点:', loras);
  }

  /**
   * 🆕 获取ComfyUI可用的模型列表
   */
  async function fetchAvailableModels() {
    try {
      console.log('[useComfyUIWorkflow] 获取可用模型列表...');

      // 通过API获取模型列表
      const response = await fetch('/api/local/comfyui/models', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        availableModels.value = {
          checkpoints: result.data.checkpoints || [],
          loras: result.data.loras || []
        };

        console.log('[useComfyUIWorkflow] 可用模型列表获取成功:', availableModels.value);
        return availableModels.value;
      } else {
        throw new Error(result.error || '获取模型列表失败');
      }
    } catch (error) {
      console.error('[useComfyUIWorkflow] 获取模型列表失败:', error);
      // 返回空列表，不阻塞工作流程
      availableModels.value = { checkpoints: [], loras: [] };
      return availableModels.value;
    }
  }

  /**
   * 获取自动检测到的节点信息
   */
  function getAutoDetectedNodes() {
    const detected = {};
    Object.entries(nodeMapping.value).forEach(([key, nodeId]) => {
      if (nodeId) {
        const node = workflowNodes.value.find(n => n.id === nodeId);
        if (node) {
          detected[key] = {
            id: nodeId,
            title: node.title,
            type: node.class_type
          };
        }
      }
    });
    return detected;
  }

  /**
   * 更新节点映射
   * @param {string} mappingKey - 映射键名
   * @param {string} nodeId - 节点ID
   */
  function updateNodeMapping(mappingKey, nodeId) {
    nodeMapping.value[mappingKey] = nodeId;
    console.log(`[useComfyUIWorkflow] 更新节点映射: ${mappingKey} -> ${nodeId}`);
  }

  /**
   * 验证工作流完整性
   * @returns {Object} 验证结果
   */
  function validateWorkflow() {
    const issues = [];
    const warnings = [];

    // 检查必需的节点
    if (!nodeMapping.value.positivePromptNode) {
      issues.push('缺少正向提示词节点 (CLIPTextEncode)');
    }
    if (!nodeMapping.value.saveImageNode) {
      issues.push('缺少图像保存节点 (SaveImage)');
    }

    // 检查可选但推荐的节点
    if (!nodeMapping.value.negativePromptNode) {
      warnings.push('建议设置负向提示词节点以获得更好的生成效果');
    }
    if (!nodeMapping.value.imageSizeNode) {
      warnings.push('建议设置图像尺寸节点以控制输出尺寸');
    }
    if (!nodeMapping.value.samplerNode) {
      warnings.push('建议设置采样器节点以控制生成参数');
    }

    return {
      isValid: issues.length === 0,
      issues,
      warnings,
      mappingComplete: isMappingValid.value
    };
  }

  /**
   * 重置工作流状态
   */
  function resetWorkflow() {
    currentWorkflow.value = null;
    workflowNodes.value = [];
    nodeMapping.value = {
      positivePromptNode: '',
      negativePromptNode: '',
      imageSizeNode: '',
      saveImageNode: '',
      samplerNode: '',
      batchSizeNode: ''
    };
    error.value = null;
  }

  /**
   * 导出工作流配置
   * @returns {Object} 工作流配置
   */
  function exportWorkflowConfig() {
    return {
      workflow: currentWorkflow.value,
      nodeMapping: { ...nodeMapping.value },
      metadata: {
        nodeCount: workflowNodes.value.length,
        nodeTypes: Object.keys(nodesByType.value),
        exportTime: new Date().toISOString()
      }
    };
  }

  return {
    // 状态
    currentWorkflow,
    workflowNodes,
    nodeMapping,
    detectedModels, // 🆕 检测到的模型信息
    availableModels, // 🆕 可用的模型列表
    isLoading,
    error,

    // 计算属性
    nodesByType,
    getNodeOptions,
    isMappingValid,

    // 方法
    parseWorkflow,
    autoDetectNodes,
    detectModelNodes, // 🆕 检测模型节点
    fetchAvailableModels, // 🆕 获取可用模型列表
    updateNodeMapping,
    validateWorkflow,
    resetWorkflow,
    exportWorkflowConfig,
    getAutoDetectedNodes
  };
}
