/**
 * Toast通知系统 Composable
 * 从 ContentCreationStudio.vue 中提取的Toast通知功能
 * 
 * 功能：
 * - 统一的Toast消息管理
 * - 支持多种消息类型（success, error, info, warning）
 * - 兼容新旧调用方式
 * - Vue 3响应式系统支持
 */

import { ref, nextTick } from 'vue'

export function useToastNotification() {
  // 响应式状态
  const toastType = ref('')
  const toastTitle = ref('')
  const toastMessage = ref('')
  const toastUpdateFlag = ref(0)
  
  // Toast组件引用（需要在组件中设置）
  let toastRef = null

  /**
   * 设置Toast组件引用
   * @param {Object} ref - Vue组件引用
   */
  const setToastRef = (ref) => {
    toastRef = ref
  }

  /**
   * 显示成功消息
   * @param {string} title - 标题
   * @param {string} message - 消息内容
   */
  const showSuccessMessage = (title, message) => {
    // 确保title和message都有值
    if (!title || !message) {
      console.warn('Toast消息缺少必要参数:', { title, message })
      return
    }

    toastType.value = 'success'
    toastTitle.value = title
    toastMessage.value = message
    
    nextTick(() => {
      if (toastRef) {
        toastRef.show()
      }
    })
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {string} title - 标题（可选，默认为"错误"）
   */
  const showErrorMessage = (message, title = '错误') => {
    // 确保message有值
    if (!message) {
      console.warn('Toast错误消息缺少message参数:', { title, message })
      return
    }

    toastType.value = 'error'
    toastTitle.value = title || '错误'
    toastMessage.value = message
    
    nextTick(() => {
      if (toastRef) {
        toastRef.show()
      }
    })
  }

  /**
   * 显示信息消息
   * @param {string} title - 标题
   * @param {string} message - 消息内容
   */
  const showInfoMessage = (title, message) => {
    // 确保title和message都有值
    if (!title || !message) {
      console.warn('Toast信息消息缺少必要参数:', { title, message })
      return
    }

    toastType.value = 'info'
    toastTitle.value = title
    toastMessage.value = message
    
    nextTick(() => {
      if (toastRef) {
        toastRef.show()
      }
    })
  }

  /**
   * 显示警告消息
   * @param {string} title - 标题
   * @param {string} message - 消息内容
   */
  const showWarningMessage = (title, message) => {
    console.log('🔔 [Toast调试] showWarningMessage被调用:', {
      title,
      message,
      currentToastMessage: toastMessage.value
    })

    toastType.value = 'warning'
    toastTitle.value = title || '警告'
    toastMessage.value = message || ''

    // 强制触发响应式更新
    toastUpdateFlag.value++

    console.log('🔔 [Toast调试] Toast数据已设置:', {
      toastType: toastType.value,
      toastTitle: toastTitle.value,
      toastMessage: toastMessage.value,
      toastUpdateFlag: toastUpdateFlag.value
    })

    nextTick(() => {
      if (toastRef) {
        console.log('🔔 [Toast调试] 调用Toast组件的show方法')
        console.log('🔔 [Toast调试] Toast组件当前props:', {
          title: toastRef.$props?.title,
          message: toastRef.$props?.message,
          type: toastRef.$props?.type
        })
        toastRef.show()
      } else {
        console.error('🔔 [Toast调试] Toast组件引用不存在')
      }
    })
  }

  /**
   * 通用Toast显示方法 - 兼容新旧调用方式
   * @param {string|Object} typeOrOptions - 消息类型或选项对象
   * @param {string} title - 标题（当第一个参数为字符串时）
   * @param {string} message - 消息（当第一个参数为字符串时）
   */
  const showToast = (typeOrOptions, title, message) => {
    console.log('🔔 [Toast调试] showToast被调用，参数:', {
      typeOrOptions,
      title,
      message,
      argumentsLength: arguments.length
    })

    let finalType, finalTitle, finalMessage

    // 检查第一个参数是否为对象（新的调用方式）
    if (typeof typeOrOptions === 'object' && typeOrOptions !== null) {
      const { type = 'info', title: objTitle = '', message: objMessage = '' } = typeOrOptions
      finalType = type
      finalTitle = objTitle
      finalMessage = objMessage
      console.log('🔔 [Toast调试] 使用对象参数方式')
    } else {
      // 旧的调用方式：showToast(type, title, message)
      finalType = typeOrOptions || 'info'
      finalTitle = title || ''
      finalMessage = message || ''
      console.log('🔔 [Toast调试] 使用传统参数方式')
    }

    console.log('🔔 [Toast调试] 最终参数:', {
      finalType,
      finalTitle,
      finalMessage
    })

    // 尝试直接使用Toast组件的showMessage方法
    if (toastRef && toastRef.showMessage) {
      console.log('🔔 [Toast调试] 使用Toast组件的showMessage方法')
      toastRef.showMessage({
        type: finalType,
        title: finalTitle,
        message: finalMessage
      })
      return
    }

    // 根据类型调用相应的消息方法
    switch (finalType) {
      case 'success':
        console.log('🔔 [Toast调试] 调用showSuccessMessage')
        showSuccessMessage(finalTitle, finalMessage)
        break
      case 'error':
        console.log('🔔 [Toast调试] 调用showErrorMessage')
        showErrorMessage(finalMessage, finalTitle)
        break
      case 'warning':
        console.log('🔔 [Toast调试] 调用showWarningMessage')
        showWarningMessage(finalTitle, finalMessage)
        break
      case 'info':
      default:
        console.log('🔔 [Toast调试] 调用showInfoMessage')
        showInfoMessage(finalTitle, finalMessage)
        break
    }
  }

  /**
   * 设置全局Toast通知系统
   * 将showToast方法绑定到window对象，供其他模块使用
   */
  const setupGlobalToastSystem = () => {
    // 将showToast方法绑定到window对象，供其他模块使用
    window.showToast = (options) => {
      console.log('🔔 [Toast] 全局toast被调用:', options)

      // 详细验证参数
      if (!options) {
        console.warn('🔔 [Toast] options参数为空，跳过显示')
        return
      }

      if (!options.title && !options.message) {
        console.warn('🔔 [Toast] title和message都为空，跳过显示:', {
          title: options.title,
          message: options.message,
          hasTitle: !!options.title,
          hasMessage: !!options.message
        })
        return
      }

      // 确保至少有一个有效的显示内容
      const finalTitle = options.title || '通知'
      const finalMessage = options.message || '无消息内容'

      console.log('🔔 [Toast] 调用本地showToast方法:', {
        type: options.type || 'info',
        title: finalTitle,
        message: finalMessage
      })

      showToast(
        options.type || 'info',
        finalTitle,
        finalMessage
      )
    }

    console.log('🔔 [Toast] 全局toast通知系统已设置')
  }

  return {
    // 响应式状态
    toastType,
    toastTitle,
    toastMessage,
    toastUpdateFlag,
    
    // 方法
    setToastRef,
    showSuccessMessage,
    showErrorMessage,
    showInfoMessage,
    showWarningMessage,
    showToast,
    setupGlobalToastSystem
  }
}
