/**
 * 行操作工具 Composable
 * 从 ContentCreationStudio.vue 中提取的行操作相关功能
 *
 * 功能：
 * - 行选择操作（单选、全选、清空选择）
 * - 行锁定/解锁操作
 * - 行合并和分拆操作
 * - 行状态管理
 * - 行数据操作
 * - 批量生图操作
 */

import { ref } from 'vue';
import { useBatchImageGeneration } from './useBatchImageGeneration.js';

export function useRowOperations() {
  // 响应式状态
  const isProcessing = ref(false);

  // 🎯 集成批量生图功能
  const batchImageGeneration = useBatchImageGeneration();

  /**
   * 切换行选择状态
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const toggleRowSelection = (context, index) => {
    if (index < 0 || index >= context.rows.length) {
      console.warn('无效的行索引:', index);
      return;
    }

    const row = context.rows[index];

    // 检查行是否被锁定
    if (row.isLocked) {
      context.showLockedRowMessage();
      return;
    }

    // 切换选中状态 - Vue 3 中直接赋值即可
    row.isSelected = !row.isSelected;

    // 保存项目数据
    context.debouncedSaveProject();
  };

  /**
   * 全选所有行（跳过锁定行）
   * @param {Object} context - 组件上下文
   */
  const selectAllRows = (context) => {
    let selectedCount = 0;
    let lockedCount = 0;

    context.rows.forEach(row => {
      if (row.isLocked) {
        lockedCount++;
      } else {
        row.isSelected = true;
        selectedCount++;
      }
    });

    console.log(`🔍 [全选完成] ${selectedCount} 行已选择，${lockedCount} 行已锁定`);

    // 保存项目数据
    context.debouncedSaveProject();

    // 显示提示消息
    if (lockedCount > 0) {
      context.showInfoMessage(
        '全选完成',
        `已选中 ${selectedCount} 行，跳过 ${lockedCount} 个锁定行`
      );
    } else {
      context.showSuccessMessage(
        '全选完成',
        `已选中所有 ${selectedCount} 行`
      );
    }
  };

  /**
   * 清空所有选择
   * @param {Object} context - 组件上下文
   */
  const clearAllSelections = (context) => {
    let clearedCount = 0;

    context.rows.forEach(row => {
      if (row.isSelected) {
        row.isSelected = false;
        clearedCount++;
      }
    });

    console.log(`🔍 [清空完成] ${clearedCount} 行选择已清空`);

    // 保存项目数据
    context.debouncedSaveProject();

    // 显示提示消息
    context.showInfoMessage('清空选择', `已取消 ${clearedCount} 行的选中状态`);
  };

  /**
   * 切换行锁定状态
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   * @returns {Promise<void>}
   */
  const toggleRowLock = async (context, rowIndex) => {
    if (rowIndex < 0 || rowIndex >= context.rows.length) {
      console.warn('无效的行索引:', rowIndex);
      return;
    }

    const row = context.rows[rowIndex];
    const wasLocked = row.isLocked || false;

    // 切换锁定状态 - Vue 3 中直接赋值即可
    row.isLocked = !wasLocked;

    // 如果锁定，清除选中状态（因为锁定的行不应该被选中）
    if (!wasLocked) {
      row.isSelected = false;
    }

    // 🔧 修复：更新项目数据中的行数据，使用深拷贝避免引用问题
    if (context.projectData && context.projectData.data && context.projectData.data.rows) {
      context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));
    }

    // 立即保存项目数据，确保状态同步
    try {
      await context.saveProjectDataImmediately();
      console.log(`🔒 行${rowIndex + 1} 锁定状态已保存: ${row.isLocked}`);
    } catch (error) {
      console.error('保存锁定状态失败:', error);
      context.showErrorMessage('保存失败', '锁定状态保存失败，请重试');
      return;
    }

    // 显示提示消息
    const message = wasLocked ? '行已解锁' : '行已锁定';
    const detail = wasLocked ?
      '该行现在可以进行编辑操作' :
      '该行已被锁定，禁止所有编辑操作';

    context.showInfoMessage(message, detail);
  };

  /**
   * 显示锁定行提示消息
   * @param {Object} context - 组件上下文
   */
  const showLockedRowMessage = (context) => {
    context.showErrorMessage(
      '该行已锁定，无法选择。请先解锁该行再进行操作。',
      '操作被阻止'
    );
  };

  /**
   * 向上合并行
   * @param {Object} context - 组件上下文
   * @param {number} index - 当前行索引
   */
  const mergeUpRow = (context, index) => {
    if (index <= 0) {
      return;
    }

    // 检查当前行和目标行是否被锁定
    const currentRow = context.rows[index];
    const targetRow = context.rows[index - 1];

    if (currentRow.isLocked || targetRow.isLocked) {
      context.showErrorMessage(
        '无法合并：存在锁定的行。请先解锁相关行再进行合并操作。',
        '合并操作被阻止'
      );
      return;
    }

    console.log('[Performance] 开始合并操作');

    // 使用mergeUp方法实现向上合并逻辑
    const result = context.mergeUp(index, context.rows);
    if (result.success) {
      // 更新行数据
      context.rows = result.rows;

      // 🔧 修复：更新项目数据中的行，使用深拷贝避免引用问题
      if (context.projectData.data) {
        context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));

        // 有合并操作时，记录到本地存储以便后续加载时保留状态
        const hasMerged = context.rows.some(row => row.isMerged);
        if (hasMerged) {
          const storageKey = `project_${context.localProjectTitle}_${context.localChapterTitle}_hasMerged`;
          localStorage.setItem(storageKey, 'true');
          console.log('[Performance] 记录合并状态:', storageKey, '=', 'true');
        }

        // 使用防抖保存而非立即保存
        context.debouncedSaveProject();

        // 显示成功消息
        context.showSuccessMessage('合并成功', `第${index + 1}行已与上一行合并`);
      }
    } else {
      // 合并失败，显示错误消息
      context.showErrorMessage('合并失败', '合并操作未能完成');
    }
  };

  /**
   * 向下分拆行
   * @param {Object} context - 组件上下文
   * @param {number} index - 当前行索引
   */
  const splitDownRow = (context, index) => {
    // 检查当前行是否被锁定
    const currentRow = context.rows[index];

    if (currentRow.isLocked) {
      context.showErrorMessage(
        '无法分拆：该行已被锁定。请先解锁该行再进行分拆操作。',
        '分拆操作被阻止'
      );
      return;
    }

    console.log('[Performance] 开始分拆操作');

    // 实现向下分拆逻辑，使用splitDown方法
    const result = context.splitDown(index, context.rows);
    if (result.success) {
      console.log('[Performance] 分拆操作成功，更新UI数据');

      // Vue 3 中直接赋值即可触发响应式更新
      context.rows = result.rows;

      // 强制触发响应式更新
      context.$nextTick(() => {
        context.$forceUpdate();
        console.log('[Performance] 强制更新UI完成，当前行数:', context.rows.length);
      });

      // 🔧 修复：更新项目数据中的行，使用深拷贝避免引用问题
      if (context.projectData.data) {
        context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));

        // 即使没有合并行也保留合并标志，以保持数据连续性
        const storageKey = `project_${context.localProjectTitle}_${context.localChapterTitle}_hasMerged`;
        localStorage.setItem(storageKey, 'true');

        // 使用防抖保存而非立即保存
        context.debouncedSaveProject();
      }

      // 显示成功消息
      context.showSuccessMessage('分拆成功', `第${index + 1}行已分拆为独立行`);

      console.log('[Performance] 分拆操作完成，UI已更新');
    } else {
      console.error('[Performance] 分拆操作失败');
      // 显示失败消息
      context.showErrorMessage('分拆失败', '分拆操作未能完成');
    }
  };

  // 🎯 批量生图功能函数

  /**
   * 处理批量生图操作
   * @param {Object} context - 组件上下文
   * @returns {Promise<void>}
   */
  const handleBatchGeneration = async (context) => {
    // 获取选中的行索引
    const selectedRowIndices = context.rows
      .map((row, index) => ({ row, index }))
      .filter(({ row }) => row.isSelected && !row.isLocked && row.keywords?.trim())
      .map(({ index }) => index);

    if (selectedRowIndices.length === 0) {
      context.showErrorMessage('没有可生成的行', '请选择至少一行有效的内容进行批量生图');
      return;
    }

    console.log('🎨 [批量生图] 开始批量生图操作:', {
      selectedCount: selectedRowIndices.length,
      indices: selectedRowIndices
    });

    // 🔍 添加详细的状态调试
    console.log('🔍 [调试] 批量生图前状态检查:', {
      currentBatchState: batchImageGeneration.batchGenerationState.value,
      isBatchGenerating: batchImageGeneration.isBatchGenerating.value,
      selectedRowIndices
    });

    try {
      // 检查当前是否正在进行批量生图
      if (batchImageGeneration.isBatchGenerating.value) {
        // 如果正在生图，则执行取消操作
        console.log('🚫 [调试] 执行取消批量生图');
        await batchImageGeneration.cancelBatchGeneration(context);
      } else {
        // 如果没有在生图，则开始批量生图
        console.log('🎨 [调试] 执行开始批量生图');
        await batchImageGeneration.startBatchGeneration(context, selectedRowIndices);

        // 🔧 立即检查状态是否正确设置
        console.log('🔍 [调试] 批量生图启动后状态检查:', {
          batchState: batchImageGeneration.batchGenerationState.value,
          isBatchGenerating: batchImageGeneration.isBatchGenerating.value
        });
      }
    } catch (error) {
      console.error('❌ [批量生图] 批量生图操作失败:', error);
      context.showErrorMessage('批量生图失败', error.message || '批量生图操作遇到错误');
    }
  };

  /**
   * 获取选中行的统计信息
   * @param {Object} context - 组件上下文
   * @returns {Object} 统计信息
   */
  const getSelectedRowsStats = (context) => {
    const stats = {
      total: 0,
      valid: 0,
      locked: 0,
      empty: 0
    };

    context.rows.forEach(row => {
      if (row.isSelected) {
        stats.total++;
        if (row.isLocked) {
          stats.locked++;
        } else if (!row.keywords?.trim()) {
          stats.empty++;
        } else {
          stats.valid++;
        }
      }
    });

    return stats;
  };

  return {
    // 响应式状态
    isProcessing,

    // 行选择操作
    toggleRowSelection,
    selectAllRows,
    clearAllSelections,

    // 行锁定操作
    toggleRowLock,
    showLockedRowMessage,

    // 行合并分拆操作
    mergeUpRow,
    splitDownRow,

    // 🎯 批量生图功能
    handleBatchGeneration,
    getSelectedRowsStats,

    // 批量生图状态和计算属性
    isBatchGenerating: batchImageGeneration.isBatchGenerating,
    batchProgress: batchImageGeneration.batchProgress,
    batchButtonText: batchImageGeneration.batchButtonText,
    batchButtonClass: batchImageGeneration.batchButtonClass,
    activeBatchTaskCount: batchImageGeneration.activeBatchTaskCount,

    // 🔧 新增：基于context的计算函数
    calculateBatchProgress: batchImageGeneration.calculateBatchProgress,
    calculateActiveBatchTaskCount: batchImageGeneration.calculateActiveBatchTaskCount,

    // 批量生图核心功能
    batchGenerationState: batchImageGeneration.batchGenerationState,
    getBatchStats: batchImageGeneration.getBatchStats,
    handleSingleRowOperationImpact: batchImageGeneration.handleSingleRowOperationImpact
  };
}
