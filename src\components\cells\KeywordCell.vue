<template>
  <div class="grid-cell keyword-cell">
    <div class="keyword-wrapper" :class="{ 'locked-row': isLocked }">
      <div class="keyword-input-container">
        <!-- 🔥 新增：推理状态覆盖层 -->
        <div
          v-if="isInferring"
          class="inferring-overlay"
        >
          <div class="inferring-content">
            <i class="ri-loader-4-line spinning" />
            <span class="inferring-text">推理中...</span>
          </div>
        </div>

        <textarea
          class="keyword-textarea"
          :class="{
            'inferring': isInferring,
            'disabled': isLocked
          }"
          placeholder="输入关键词描述..."
          v-model="localKeywords"
          :disabled="isInferring || isLocked"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          :title="isLocked ? '该行已锁定，无法编辑关键词' : '输入关键词描述...'"
        />
        <button
          v-if="localKeywords && localKeywords.trim() && !isInferring && !isLocked"
          class="clear-single-button"
          @click="clearSingleKeyword"
          title="清除此行提示词"
        >
          <i class="ri-close-line" />
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KeywordCell',
  emits: {
    'keywords-updated': (data) => typeof data === 'object' && data !== null,
    'clear-tags': (data) => typeof data === 'object' && data !== null,
    'show-locked-message': null
  },
  props: {
    keywords: {
      type: String,
      default: ''
    },
    rowIndex: {
      type: Number,
      required: true
    },
    // 🔥 新增：推理状态
    isInferring: {
      type: Boolean,
      default: false
    },
    isLocked: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localKeywords: this.keywords,
      isUserEditing: false,
      lastUserInput: null
    }
  },
  watch: {
    keywords(newVal) {
      // 只有在用户没有正在编辑时才更新本地值
      if (!this.isUserEditing) {
        this.localKeywords = newVal;
      }
    }
  },
  methods: {
    handleInput() {
      // 检查是否锁定
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }

      // 标记用户正在手动编辑
      this.isUserEditing = true;
      this.lastUserInput = Date.now();

      // 发送手动输入事件到父组件
      this.$emit('keywords-updated', {
        rowIndex: this.rowIndex,
        keywords: this.localKeywords,
        isAutoGenerated: false  // 标记这是用户手动输入
      });

      // 延迟重置编辑状态
      setTimeout(() => {
        this.isUserEditing = false;
      }, 1000);
    },

    handleFocus() {
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }
      this.isUserEditing = true;
    },

    handleBlur() {
      // 延迟重置，确保输入事件能正确处理
      setTimeout(() => {
        this.isUserEditing = false;
      }, 200);
    },

    clearSingleKeyword() {
      // 检查是否锁定
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }

      // 清除当前行的关键词
      this.localKeywords = '';
      this.isUserEditing = true;

      // 发送清除事件到父组件
      this.$emit('keywords-updated', {
        rowIndex: this.rowIndex,
        keywords: '',
        isAutoGenerated: false  // 标记这是用户手动清除
      });

      // 🔥 新增：同时清除相关的Tag标签
      this.$emit('clear-tags', {
        rowIndex: this.rowIndex
      });

      // 延迟重置编辑状态
      setTimeout(() => {
        this.isUserEditing = false;
      }, 500);
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.keyword-cell {
  /* 🔧 关键词列获得最多空间 */
  width: 40%;
  min-width: 200px;
}

.keyword-wrapper {
  width: 100%;
  height: 100%;
  background-color: #252525;
  padding: 5px;
}

.keyword-input-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.keyword-textarea {
  width: 100%;
  height: 100%;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 0.9rem;
  padding: 8px;
  padding-right: 25px; /* 恢复正常的右边距，因为不再有滚动条冲突 */
  resize: none;
  outline: none;
  box-sizing: border-box;

  /* 隐藏滚动条但保持滚动功能 */
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.keyword-textarea::-webkit-scrollbar {
  display: none;
}

/* 清除单行按钮样式 - 优化位置，无滚动条冲突 */
.clear-single-button {
  position: absolute;
  top: 4px;
  right: 4px; /* 恢复到边缘位置，因为没有滚动条冲突 */
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  z-index: 10;
}

.clear-single-button:hover {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.clear-single-button i {
  font-size: 10px;
}

/* 为长内容添加视觉提示 - 鼠标悬停时显示底部渐变，提示可滚动 */
.keyword-input-container::after {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 1px;
  right: 1px;
  height: 6px;
  background: linear-gradient(transparent, rgba(26, 26, 26, 0.6));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 0 0 3px 3px;
}

/* 鼠标悬停时显示滚动提示 */
.keyword-input-container:hover::after {
  opacity: 1;
}

/* 🔥 新增：推理状态样式 */
.inferring-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 26, 26, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border-radius: 4px;
}

.inferring-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #89b4fa;
  font-size: 0.85rem;
}

.inferring-text {
  font-weight: 500;
}

.keyword-textarea.inferring {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* 锁定状态样式 */
.keyword-wrapper.locked-row {
  border: 2px solid #dc2626;
  border-radius: 4px;
  background-color: rgba(220, 38, 38, 0.1);
  position: relative;
}

.keyword-wrapper.locked-row::before {
  content: '🔒';
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  background-color: #dc2626;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.keyword-textarea.disabled {
  background-color: #333 !important;
  color: #666 !important;
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}
</style>