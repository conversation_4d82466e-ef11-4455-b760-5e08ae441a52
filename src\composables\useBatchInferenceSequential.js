/**
 * 批量推理功能模块 - 顺序处理版本
 * 基于现有的单行推理功能，实现顺序批量推理逻辑
 */

import { ref, computed } from 'vue';

export function useBatchInferenceSequential() {
  // 状态管理
  const isProcessing = ref(false);
  const currentRowIndex = ref(-1);
  const totalRows = ref(0);
  const processedRows = ref(0);
  const failedRows = ref([]);
  const processingMessage = ref('');
  const processingError = ref('');
  const isCancelled = ref(false);

  // 队列管理
  const processingQueue = ref([]);
  const currentTask = ref(null);

  // 计算属性
  const progress = computed(() => {
    if (totalRows.value === 0) return 0;
    return Math.round((processedRows.value / totalRows.value) * 100);
  });

  const isCompleted = computed(() => {
    return processedRows.value === totalRows.value && totalRows.value > 0;
  });

  const hasErrors = computed(() => {
    return failedRows.value.length > 0;
  });

  const canCancel = computed(() => {
    return isProcessing.value && !isCancelled.value;
  });

  /**
   * 重置状态
   */
  function resetState() {
    isProcessing.value = false;
    currentRowIndex.value = -1;
    totalRows.value = 0;
    processedRows.value = 0;
    failedRows.value = [];
    processingMessage.value = '';
    processingError.value = '';
    isCancelled.value = false;
    processingQueue.value = [];
    currentTask.value = null;
  }

  /**
   * 开始批量推理
   * @param {Array} selectedRows - 选中的行数据
   * @param {Function} inferenceFunction - 推理函数（通常是 handleInferPrompt）
   * @param {Function} onProgress - 进度回调
   * @param {Function} onRowComplete - 单行完成回调
   */
  async function startBatchInference(selectedRows, inferenceFunction, onProgress, onRowComplete) {
    if (!selectedRows || selectedRows.length === 0) {
      throw new Error('没有选中的行数据');
    }

    if (typeof inferenceFunction !== 'function') {
      throw new Error('推理函数无效');
    }

    console.log('🧠 [顺序批量推理] 开始处理:', {
      totalRows: selectedRows.length,
      selectedRowsData: selectedRows.map((row, index) => ({ 
        index, 
        description: row.description?.substring(0, 50) 
      }))
    });

    // 初始化状态
    resetState();
    isProcessing.value = true;
    totalRows.value = selectedRows.length;
    processingMessage.value = '准备开始批量推理...';

    // 创建处理队列
    processingQueue.value = selectedRows.map((row, index) => ({
      row,
      index,
      status: 'waiting'
    }));

    try {
      // 顺序处理每一行
      for (let i = 0; i < processingQueue.value.length; i++) {
        // 检查是否被取消
        if (isCancelled.value) {
          console.log('🧠 [顺序批量推理] 检测到取消信号，停止处理');
          break;
        }

        const task = processingQueue.value[i];
        currentTask.value = task;
        currentRowIndex.value = i;
        task.status = 'processing';

        processingMessage.value = `正在处理第 ${i + 1}/${totalRows.value} 行...`;

        console.log(`🧠 [顺序批量推理] 开始处理第 ${i + 1} 行:`, task.row.description?.substring(0, 100));

        try {
          // 调用推理函数
          const result = await inferenceFunction(task.row, i);

          console.log(`🧠 [顺序批量推理] 第 ${i + 1} 行推理完成:`, result);

          // 更新进度
          processedRows.value++;
          task.status = 'completed';
          task.result = result;

          // 调用单行完成回调
          if (onRowComplete) {
            console.log(`🧠 [顺序批量推理] 调用单行完成回调，第 ${i + 1} 行`);
            onRowComplete(task.row, i, result, null);
          }

          // 调用进度回调
          if (onProgress) {
            console.log(`🧠 [顺序批量推理] 调用进度回调，第 ${i + 1} 行`);
            onProgress({
              current: processedRows.value,
              total: totalRows.value,
              progress: progress.value,
              currentRow: task.row,
              result: result
            });
          }

          console.log(`✅ [顺序批量推理] 第 ${i + 1} 行处理完成`);

        } catch (error) {
          console.error(`❌ [顺序批量推理] 第 ${i + 1} 行处理失败:`, error);

          // 记录失败
          failedRows.value.push({
            row: task.row,
            index: i,
            error: error.message || '推理失败'
          });

          task.status = 'failed';
          task.error = error;

          // 调用单行完成回调（错误情况）
          if (onRowComplete) {
            onRowComplete(task.row, i, null, error);
          }

          // 继续处理下一行，不中断整个批量处理
        }
      }

      // 处理完成
      currentTask.value = null;
      currentRowIndex.value = -1;

      const successCount = processedRows.value - failedRows.value.length;
      processingMessage.value = isCancelled.value 
        ? `批量推理已取消，已处理 ${processedRows.value}/${totalRows.value} 行`
        : `批量推理完成，成功 ${successCount}/${totalRows.value} 行`;

      console.log('🧠 [顺序批量推理] 处理完成:', {
        total: totalRows.value,
        processed: processedRows.value,
        failed: failedRows.value.length,
        success: successCount,
        cancelled: isCancelled.value
      });

      return {
        success: true,
        total: totalRows.value,
        processed: processedRows.value,
        failed: failedRows.value.length,
        failedRows: failedRows.value,
        cancelled: isCancelled.value
      };

    } catch (error) {
      console.error('❌ [顺序批量推理] 批量处理失败:', error);
      processingError.value = error.message || '批量推理失败';
      throw error;
    } finally {
      isProcessing.value = false;
    }
  }

  /**
   * 取消批量推理
   * 允许当前正在处理的行完成，取消等待中的行
   */
  function cancelBatchInference() {
    console.log('🛑 [顺序批量推理] 用户取消批量处理');
    isCancelled.value = true;
    processingMessage.value = '正在取消批量推理...';

    // 取消等待中的任务
    const waitingTasks = processingQueue.value.filter(task => task.status === 'waiting');
    waitingTasks.forEach(task => {
      task.status = 'cancelled';
    });

    console.log(`🛑 [顺序批量推理] 已标记 ${waitingTasks.length} 个等待任务为取消状态`);
  }

  /**
   * 获取当前状态
   */
  function getStatus() {
    return {
      isProcessing: isProcessing.value,
      currentRowIndex: currentRowIndex.value,
      totalRows: totalRows.value,
      processedRows: processedRows.value,
      failedRows: failedRows.value,
      progress: progress.value,
      processingMessage: processingMessage.value,
      processingError: processingError.value,
      isCancelled: isCancelled.value,
      canCancel: canCancel.value,
      currentTask: currentTask.value,
      processingQueue: processingQueue.value
    };
  }

  return {
    // 状态
    isProcessing,
    currentRowIndex,
    totalRows,
    processedRows,
    failedRows,
    processingMessage,
    processingError,
    isCancelled,
    processingQueue,
    currentTask,

    // 计算属性
    progress,
    isCompleted,
    hasErrors,
    canCancel,

    // 方法
    startBatchInference,
    cancelBatchInference,
    resetState,
    getStatus
  };
}
