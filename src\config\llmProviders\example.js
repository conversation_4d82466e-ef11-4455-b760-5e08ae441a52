/**
 * LLM提供商配置使用示例
 * 展示如何使用新的模块化配置系统
 */

import { 
  getProviderConfig, 
  getProviderList, 
  validateProviderConfig,
  getApiKeyForProvider,
  buildApiRequest,
  parseApiResponse,
  handleApiError
} from './index.js';

/**
 * 示例1: 获取和显示所有提供商信息
 */
function example1_listProviders() {
  console.log('📋 示例1: 获取提供商列表\n');
  
  // 获取所有提供商
  const allProviders = getProviderList();
  console.log('所有提供商:');
  allProviders.forEach(provider => {
    console.log(`  - ${provider.name} (${provider.id}): ${provider.description}`);
  });
  
  // 获取热门提供商
  const popularProviders = getProviderList(true);
  console.log('\n热门提供商:');
  popularProviders.forEach(provider => {
    console.log(`  - ${provider.name} (${provider.id})`);
  });
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

/**
 * 示例2: 获取特定提供商的详细配置
 */
function example2_providerDetails() {
  console.log('🔍 示例2: 获取提供商详细配置\n');
  
  const providerId = 'openai';
  const config = getProviderConfig(providerId);
  
  if (config) {
    console.log(`提供商: ${config.name}`);
    console.log(`描述: ${config.description}`);
    console.log(`API基础URL: ${config.api.baseUrl}`);
    console.log(`默认模型: ${config.defaults.model}`);
    console.log(`支持的模型数量: ${config.models.length}`);
    
    console.log('\n支持的模型:');
    config.models.forEach(model => {
      console.log(`  - ${model.name} (${model.id})`);
      console.log(`    最大tokens: ${model.maxTokens}`);
      console.log(`    价格: 输入$${model.pricing.input}/1K, 输出$${model.pricing.output}/1K`);
    });
  } else {
    console.log(`❌ 未找到提供商: ${providerId}`);
  }
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

/**
 * 示例3: 验证配置
 */
function example3_validateConfig() {
  console.log('✅ 示例3: 配置验证\n');
  
  const providerId = 'openai';
  
  // 测试有效配置
  const validConfig = {
    apiKey: 'sk-test123456789',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 4096
  };
  
  const validErrors = validateProviderConfig(providerId, validConfig);
  console.log('有效配置验证结果:');
  if (validErrors.length === 0) {
    console.log('  ✅ 配置有效');
  } else {
    console.log('  ❌ 配置错误:', validErrors);
  }
  
  // 测试无效配置
  const invalidConfig = {
    // 缺少apiKey
    model: 'gpt-4',
    temperature: 3.0, // 超出范围
    maxTokens: 200000 // 超出限制
  };
  
  const invalidErrors = validateProviderConfig(providerId, invalidConfig);
  console.log('\n无效配置验证结果:');
  if (invalidErrors.length === 0) {
    console.log('  ✅ 配置有效');
  } else {
    console.log('  ❌ 配置错误:');
    invalidErrors.forEach(error => console.log(`    - ${error}`));
  }
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

/**
 * 示例4: 构建API请求
 */
function example4_buildRequest() {
  console.log('🔧 示例4: 构建API请求\n');
  
  const providerId = 'openai';
  const prompt = '请帮我写一首关于春天的诗';
  const settings = {
    apiKey: 'sk-test123456789',
    model: 'gpt-4',
    temperature: 0.7,
    maxOutputTokens: 1000
  };
  
  try {
    const requestConfig = buildApiRequest(providerId, prompt, settings);
    
    console.log('构建的API请求:');
    console.log(`  URL: ${requestConfig.url}`);
    console.log(`  方法: ${requestConfig.method}`);
    console.log('  请求头:');
    Object.entries(requestConfig.headers).forEach(([key, value]) => {
      // 隐藏敏感信息
      const displayValue = key.toLowerCase().includes('authorization') 
        ? value.substring(0, 20) + '...' 
        : value;
      console.log(`    ${key}: ${displayValue}`);
    });
    
    console.log('  请求体 (前200字符):');
    const bodyPreview = requestConfig.body.substring(0, 200);
    console.log(`    ${bodyPreview}...`);
    
  } catch (error) {
    console.log(`❌ 构建请求失败: ${error.message}`);
  }
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

/**
 * 示例5: 解析API响应
 */
function example5_parseResponse() {
  console.log('📥 示例5: 解析API响应\n');
  
  // 模拟不同提供商的响应格式
  const responses = {
    openai: {
      choices: [
        {
          message: {
            content: '春风吹绿江南岸，明月何时照我还。'
          }
        }
      ],
      usage: { prompt_tokens: 10, completion_tokens: 15, total_tokens: 25 },
      model: 'gpt-4'
    },
    
    google: {
      candidates: [
        {
          content: {
            parts: [
              { text: '春暖花开满园香，蝶舞蜂飞忙采蜜。' }
            ]
          }
        }
      ],
      usageMetadata: { promptTokenCount: 8, candidatesTokenCount: 12 }
    },
    
    anthropic: {
      content: [
        { text: '春雨绵绵润万物，柳絮飞舞舞春风。' }
      ],
      usage: { input_tokens: 9, output_tokens: 14 },
      model: 'claude-3-haiku'
    }
  };
  
  // 解析每个提供商的响应
  Object.entries(responses).forEach(([providerId, responseData]) => {
    try {
      const parsed = parseApiResponse(providerId, responseData);
      console.log(`${providerId.toUpperCase()} 响应解析结果:`);
      console.log(`  内容: ${parsed.content}`);
      console.log(`  模型: ${parsed.model || '未知'}`);
      console.log(`  使用情况: ${JSON.stringify(parsed.usage || {})}`);
      console.log();
    } catch (error) {
      console.log(`❌ ${providerId} 响应解析失败: ${error.message}\n`);
    }
  });
  
  console.log('─'.repeat(60) + '\n');
}

/**
 * 示例6: 错误处理
 */
function example6_errorHandling() {
  console.log('🚨 示例6: 错误处理\n');
  
  const providerId = 'openai';
  
  // 模拟不同类型的错误
  const errors = [
    { status: 401, message: 'Invalid API key' },
    { status: 429, message: 'Rate limit exceeded' },
    { status: 402, message: 'Insufficient credits' },
    { status: 500, message: 'Internal server error' }
  ];
  
  errors.forEach(({ status, message }) => {
    const mockResponse = { status };
    const error = new Error(message);
    
    try {
      const handledError = handleApiError(providerId, error, mockResponse);
      console.log(`HTTP ${status}: ${handledError.message}`);
    } catch (e) {
      console.log(`❌ 错误处理失败: ${e.message}`);
    }
  });
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

/**
 * 示例7: 完整的API调用流程
 */
async function example7_completeFlow() {
  console.log('🔄 示例7: 完整的API调用流程\n');
  
  const providerId = 'openai';
  const userSettings = {
    openaiApiKey: 'sk-test123456789', // 实际使用时请使用真实的API密钥
    defaultModel: 'gpt-4',
    temperature: 0.7,
    maxOutputTokens: 1000
  };
  
  try {
    // 1. 获取API密钥
    const apiKey = getApiKeyForProvider(userSettings, providerId);
    console.log(`1. 获取API密钥: ${apiKey ? '✅ 成功' : '❌ 失败'}`);
    
    if (!apiKey) {
      console.log('   请在userSettings中配置API密钥');
      return;
    }
    
    // 2. 准备设置
    const settings = {
      apiKey,
      model: userSettings.defaultModel,
      temperature: userSettings.temperature,
      maxOutputTokens: userSettings.maxOutputTokens
    };
    
    // 3. 验证配置
    const validationErrors = validateProviderConfig(providerId, settings);
    console.log(`2. 配置验证: ${validationErrors.length === 0 ? '✅ 通过' : '❌ 失败'}`);
    
    if (validationErrors.length > 0) {
      console.log('   验证错误:', validationErrors);
      return;
    }
    
    // 4. 构建请求
    const prompt = '请简单介绍一下人工智能';
    const requestConfig = buildApiRequest(providerId, prompt, settings);
    console.log(`3. 构建请求: ✅ 成功`);
    console.log(`   URL: ${requestConfig.url}`);
    
    // 5. 模拟发送请求（实际使用时取消注释）
    console.log(`4. 发送请求: ⏸️ 跳过（演示模式）`);
    /*
    const response = await fetch(requestConfig.url, {
      method: requestConfig.method,
      headers: requestConfig.headers,
      body: requestConfig.body
    });
    
    const responseData = await response.json();
    
    if (!response.ok) {
      const error = handleApiError(providerId, new Error(responseData.error?.message), response);
      throw error;
    }
    
    // 6. 解析响应
    const parsed = parseApiResponse(providerId, responseData);
    console.log(`5. 解析响应: ✅ 成功`);
    console.log(`   内容: ${parsed.content}`);
    */
    
  } catch (error) {
    console.log(`❌ 流程失败: ${error.message}`);
  }
  
  console.log('\n' + '─'.repeat(60) + '\n');
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  console.log('🎯 LLM提供商配置使用示例\n');
  console.log('='.repeat(60) + '\n');
  
  example1_listProviders();
  example2_providerDetails();
  example3_validateConfig();
  example4_buildRequest();
  example5_parseResponse();
  example6_errorHandling();
  await example7_completeFlow();
  
  console.log('🎉 所有示例运行完成！');
}

// 如果直接运行此文件，执行所有示例
if (import.meta.url === `file://${process.argv[1]}`) {
  await runAllExamples();
}

export {
  example1_listProviders,
  example2_providerDetails,
  example3_validateConfig,
  example4_buildRequest,
  example5_parseResponse,
  example6_errorHandling,
  example7_completeFlow,
  runAllExamples
};
