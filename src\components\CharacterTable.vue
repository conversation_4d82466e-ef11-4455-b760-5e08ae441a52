<template>
  <div class="character-table-container">
    <table class="preset-table auto-width-table">
      <thead>
        <tr>
          <th class="column-select">
            <div class="select-header">
              <span>选择</span>
              <div class="select-dropdown">
                <button
                  class="select-dropdown-btn"
                  @click="toggleSelectDropdown"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    width="12"
                    height="12"
                  >
                    <path d="M7 10L12 15L17 10H7Z" />
                  </svg>
                </button>
                <div
                  v-if="showSelectDropdown"
                  class="select-dropdown-menu"
                >
                  <button
                    @click="selectAll"
                    class="dropdown-item"
                  >
                    全选
                  </button>
                  <button
                    @click="clearAll"
                    class="dropdown-item"
                  >
                    清空
                  </button>
                </div>
              </div>
            </div>
          </th>
          <th class="column-type">
            类型
          </th>
          <th class="column-name">
            名称
          </th>
          <th class="column-description">
            视觉特征
          </th>
          <th
            v-if="showActions"
            class="column-actions"
          >
            操作
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="items.length === 0">
          <td
            :colspan="showActions ? 5 : 4"
            class="no-data-cell"
          >
            暂无数据
          </td>
        </tr>
        <tr
          v-for="(item, index) in items"
          :key="item.id || item.name || index"
          class="preset-table-row"
        >
          <td class="cell-select">
            <input
              type="checkbox"
              :checked="selectedItems.includes(item.id || item.name || index)"
              @change="toggleItemSelection(item, index)"
              class="item-checkbox"
            >
          </td>
          <td class="cell-type">
            [{{ item.type === 'character' ? '角色' : '特殊' }}]
          </td>
          <td class="cell-name">
            <div class="name-container">
              <div class="main-name">
                {{ item.name }}
              </div>
              <div
                v-if="item.alias"
                class="alias-tags"
              >
                <span
                  v-for="alias in getAliasArray(item.alias)"
                  :key="alias"
                  class="alias-tag"
                >
                  {{ alias }}
                  <button
                    class="alias-remove-btn"
                    @click="removeAlias(item, alias)"
                    title="删除别名"
                  >
                    ×
                  </button>
                </span>
              </div>
            </div>
          </td>
          <td class="cell-description">
            {{ item.description }}
          </td>
          <td
            v-if="showActions"
            class="cell-actions"
          >
            <div class="action-buttons-container">
              <div class="button-row">
                <button
                  @click="$emit('edit', item)"
                  class="icon-button edit-preset-button"
                  title="编辑"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    width="16"
                    height="16"
                  ><path d="M12.962 2.19l7.846 7.846a2.25 2.25 0 010 3.182l-1.407 1.407-9.253-9.253 1.407-1.407a2.25 2.25 0 011.407-.675zm-1.407 1.407L3.948 11.204a2.252 2.252 0 00-.675 1.407V18.75c0 .414.336.75.75.75h6.139a2.252 2.252 0 001.407-.675L19.176 10.22l-9.028-9.028-1.591 1.591zM4.5 19.5V13.06l5.432 5.432H4.5zM12 19.5H6.568l5.432-5.432L12 14.068V19.5z" /></svg>
                </button>
                <button
                  @click="$emit('delete', item)"
                  class="icon-button delete-preset-button"
                  title="删除"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    width="16"
                    height="16"
                  ><path d="M7 4V2H17V4H22V6H20V21C20 22.1046 19.1046 23 18 23H6C4.89543 23 4 22.1046 4 21V6H2V4H7ZM6 6V21H18V6H6ZM9 9H11V19H9V9ZM13 9H15V19H13V9Z" /></svg>
                </button>
              </div>
              <!-- 预设相关按钮 -->
              <div
                v-if="showPresetActions"
                class="button-row"
              >
                <button
                  @click="$emit('replace-with-preset', item)"
                  class="icon-button replace-preset-button"
                  :class="{ 'highlight-available': hasPresetMatch(item.name) }"
                  title="替换为预设"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    width="16"
                    height="16"
                  ><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.4 5C10.2 5 7.26 6.22 5.27 8.21C7.1 8.79 8.74 9.85 10 11.31V9C10 7.9 10.9 7 12 7S14 7.9 14 9V11C14 12.1 13.1 13 12 13S10 12.1 10 11V9.69C8.74 8.23 7.1 7.17 5.27 6.59C3.28 8.58 2.06 11.52 2.06 14.72C2.06 15.64 2.12 16.56 2.23 17.47L7 22L9 20V18C9 16.9 9.9 16 11 16H13C14.1 16 15 16.9 15 18V20L17 22L21.77 17.23C21.88 16.32 21.94 15.4 21.94 14.48C21.94 12.77 21.59 11.14 21 9.6V9Z" /></svg>
                </button>
                <button
                  @click="$emit('add-to-preset', item)"
                  class="icon-button add-preset-button"
                  title="加入预设"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    width="16"
                    height="16"
                  ><path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" /></svg>
                </button>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 底部应用按钮 -->
    <div
      v-if="items.length > 0"
      class="apply-button-container"
    >
      <button
        @click="applyToSelected"
        class="apply-button"
        :disabled="selectedItems.length === 0"
      >
        应用选中角色 ({{ selectedItems.length }}/{{ items.length }})
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';

const props = defineProps({
  items: { type: Array, required: true, default: () => [] },
  showActions: { type: Boolean, default: true },
  presetCharacters: { type: Array, default: () => [] }, // 预设角色列表
  showPresetActions: { type: Boolean, default: false }, // 是否显示预设相关按钮
  currentSelectedCharacters: { type: Array, default: () => [] } // 当前选中的角色
});

const emit = defineEmits(['edit', 'delete', 'replace-with-preset', 'add-to-preset', 'update-alias', 'apply-to-selected']);

// 选择功能相关状态
const selectedItems = ref([]);
const showSelectDropdown = ref(false);

// 同步选择状态 - 根据当前选中的角色来设置复选框状态
const syncSelectionState = () => {
  if (!props.currentSelectedCharacters || props.currentSelectedCharacters.length === 0) {
    selectedItems.value = [];
    return;
  }

  // 根据当前选中的角色名称来设置选择状态
  const selectedNames = props.currentSelectedCharacters.map(char =>
    typeof char === 'string' ? char : char.name
  );

  selectedItems.value = props.items
    .map((item, index) => item.id || item.name || index)
    .filter((itemId, index) => {
      const item = props.items[index];
      return selectedNames.includes(item.name);
    });

  console.log('CharacterTable - 同步选择状态:', {
    currentSelectedCharacters: props.currentSelectedCharacters,
    selectedNames,
    selectedItems: selectedItems.value
  });
};

// 监听当前选中角色的变化
watch(() => props.currentSelectedCharacters, () => {
  syncSelectionState();
}, { deep: true, immediate: true });

// 监听items变化，重新同步状态
watch(() => props.items, () => {
  syncSelectionState();
}, { deep: true });

// 组件挂载时同步状态
onMounted(() => {
  syncSelectionState();
});



// 切换下拉菜单显示
const toggleSelectDropdown = () => {
  showSelectDropdown.value = !showSelectDropdown.value;
};

// 全选
const selectAll = () => {
  selectedItems.value = props.items.map((item, index) => item.id || item.name || index);
  showSelectDropdown.value = false;
};

// 清空选择
const clearAll = () => {
  selectedItems.value = [];
  showSelectDropdown.value = false;
};

// 切换单个项目的选择状态
const toggleItemSelection = (item, index) => {
  const itemId = item.id || item.name || index;
  const selectedIndex = selectedItems.value.indexOf(itemId);

  if (selectedIndex > -1) {
    selectedItems.value.splice(selectedIndex, 1);
  } else {
    selectedItems.value.push(itemId);
  }
};

// 应用选中的角色
const applyToSelected = () => {
  // 根据selectedItems状态过滤出用户实际选中的角色
  const selectedCharacters = props.items.filter((item, index) => {
    const itemId = item.id || item.name || index;
    return selectedItems.value.includes(itemId);
  });

  console.log('CharacterTable - 应用选中角色:', {
    selectedItems: selectedItems.value,
    selectedCharacters
  });

  // 发送选中的角色对象数组（保持完整的角色信息）
  emit('apply-to-selected', selectedCharacters);
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  if (!event.target.closest('.select-dropdown')) {
    showSelectDropdown.value = false;
  }
};

// 监听点击外部事件
if (typeof window !== 'undefined') {
  document.addEventListener('click', handleClickOutside);
}

// 获取别名数组 - 支持逗号或空格分隔
const getAliasArray = (alias) => {
  if (!alias) return [];
  // 先尝试逗号分隔，如果没有逗号则用空格分隔
  const separator = alias.includes(',') ? ',' : ' ';
  return alias.split(separator).map(a => a.trim()).filter(a => a);
};

// 删除别名
const removeAlias = (item, aliasToRemove) => {
  const aliases = getAliasArray(item.alias);
  const updatedAliases = aliases.filter(alias => alias !== aliasToRemove);

  // 保持原来的分隔符格式
  const originalSeparator = item.alias && item.alias.includes(',') ? ', ' : ' ';
  const newAliasString = updatedAliases.join(originalSeparator);

  // 发送更新事件
  emit('update-alias', {
    item: item,
    newAlias: newAliasString
  });
};

// 检查当前角色是否在预设中存在同名或同别名角色
const hasPresetMatch = (characterName) => {
  return props.presetCharacters.some(preset => {
    // 检查主名称匹配
    if (preset.name === characterName) return true;

    // 检查别名匹配
    if (preset.alias) {
      const presetAliases = getAliasArray(preset.alias);
      if (presetAliases.includes(characterName)) return true;
    }

    return false;
  });
};
</script>

<style scoped>
.character-table-container { width: 100%; }
.preset-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
  background-color: transparent;
  color: #e0def4;
  table-layout: auto;
}
.preset-table th,
.preset-table td {
  border: 1px solid #44415a;
  padding: 10px 12px;
  text-align: left;
  vertical-align: middle;
  background-color: transparent;
}
.preset-table th {
  background-color: #232136;
  font-weight: 600;
  font-size: 0.9em;
  color: #b8b5d6;
}
/* 选择列样式 - 恢复fit-content宽度 */
.column-select {
  width: fit-content;
  min-width: fit-content;
  white-space: nowrap;
  text-align: center;
  position: relative;
}

.select-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.select-dropdown {
  position: relative;
  display: inline-block;
}

.select-dropdown-btn {
  background: none;
  border: none;
  color: #b8b5d6;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: background 0.2s, color 0.2s;
}

.select-dropdown-btn:hover {
  background: #44415a;
  color: #e0def4;
}

.select-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #232136;
  border: 1px solid #44415a;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 80px;
  overflow: hidden;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #e0def4;
  cursor: pointer;
  font-size: 0.9em;
  text-align: left;
  transition: background 0.2s;
}

.dropdown-item:hover {
  background: #44415a;
}

.cell-select {
  text-align: center;
  vertical-align: middle;
}

.item-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #3e8fb0;
}

/* 🔧 优化列宽度设置 - 使用fit-content和min-content */
.column-type {
  width: fit-content;
  min-width: fit-content;
  white-space: nowrap;
}
.column-name {
  width: fit-content;
  min-width: fit-content;
  white-space: nowrap;
}
.column-description {
  width: auto; /* 描述列使用剩余空间 */
  white-space: normal;
}
.column-actions {
  width: fit-content;
  min-width: fit-content;
  text-align: center;
  white-space: nowrap;
}
.cell-type { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

.cell-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.name-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.main-name {
  font-weight: 500;
  color: #e0def4;
}

.alias-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.alias-tag {
  display: inline-flex;
  align-items: center;
  background: #44415a;
  color: #e0def4;
  font-size: 0.8em;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #6e6a86;
  transition: background 0.2s, border-color 0.2s;
}

.alias-tag:hover {
  background: #524f67;
  border-color: #908caa;
}

.alias-remove-btn {
  background: none;
  border: none;
  color: #908caa;
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  margin-left: 4px;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: color 0.2s, background 0.2s;
}

.alias-remove-btn:hover {
  color: #eb6f92;
  background: rgba(235, 111, 146, 0.1);
}
.cell-description {
  color: #b8b5d6;
  line-height: 1.6;
  white-space: pre-line;
  word-break: break-word;
  font-size: 0.98em;
  max-width: 100%;
  overflow: hidden;
}
.cell-actions {
  text-align: center;
  vertical-align: middle;
  padding: 8px 6px;
}

.action-buttons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.button-row {
  display: flex;
  gap: 4px;
  align-items: center;
}

.edit-preset-button, .delete-preset-button, .replace-preset-button, .add-preset-button {
  background: none;
  border: none;
  padding: 4px;
  margin: 0;
  cursor: pointer;
  color: #3e8fb0;
  transition: color 0.18s, background 0.18s, box-shadow 0.18s;
  border-radius: 4px;
  font-size: 1em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}
.edit-preset-button:hover, .delete-preset-button:hover, .replace-preset-button:hover, .add-preset-button:hover {
  color: #4ea8c6;
  background: #232f3e;
}

/* 替换为预设按钮的闪耀效果 */
.replace-preset-button.highlight-available {
  color: #f6c177;
  animation: pulse-glow 2s infinite;
  box-shadow: 0 0 8px rgba(246, 193, 119, 0.4);
}

.replace-preset-button.highlight-available:hover {
  color: #f6c177;
  background: rgba(246, 193, 119, 0.1);
  box-shadow: 0 0 12px rgba(246, 193, 119, 0.6);
}

/* 加入预设按钮特殊颜色 */
.add-preset-button {
  color: #9ccfd8;
}

.add-preset-button:hover {
  color: #9ccfd8;
  background: rgba(156, 207, 216, 0.1);
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(246, 193, 119, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 16px rgba(246, 193, 119, 0.8);
    transform: scale(1.05);
  }
}
.no-data-cell {
  text-align: center;
  color: #908caa;
  font-style: italic;
  background: #232136;
}

/* 底部应用按钮样式 */
.apply-button-container {
  margin-top: 15px;
  text-align: center;
}

.apply-button {
  background-color: #3e8fb0;
  color: #e0def4;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9em;
  cursor: pointer;
  transition: background-color 0.2s;
}

.apply-button:hover:not(:disabled) {
  background-color: #2c677d;
}

.apply-button:disabled {
  background-color: #44415a;
  color: #908caa;
  cursor: not-allowed;
}
</style>