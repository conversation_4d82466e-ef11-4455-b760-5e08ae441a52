/**
 * 图片删除服务
 * 处理图片的数据删除和文件删除
 */

class ImageDeleteService {
  /**
   * 删除主图
   * @param {Object} row - 行数据
   * @param {Function} saveCallback - 保存回调函数
   */
  async deleteMainImage(row, saveCallback) {
    try {
      const imageUrl = row.imageSrc;

      console.log('🗑️ [图片删除] 开始删除主图:', {
        imageUrl: imageUrl ? imageUrl.substring(0, 100) + '...' : null,
        imageAlt: row.imageAlt
      });

      // 🔧 修复：完全清除主图数据
      row.imageSrc = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
      row.imageAlt = '';

      // 🔧 修复：清除可能存在的其他图片相关字段
      if (row.isImageLocked !== undefined) {
        row.isImageLocked = false;
      }

      console.log('🗑️ [图片删除] 主图数据已清除');

      // 2. 删除文件（如果不是base64图片）
      await this.deleteImageFile(imageUrl);

      // 3. 保存数据
      if (saveCallback) {
        saveCallback();
      }

      console.log('🗑️ [图片删除] 主图删除成功');
      return { success: true, message: '主图删除成功' };

    } catch (error) {
      console.error('🗑️ [图片删除] 主图删除失败:', error);
      return { success: false, message: '主图删除失败: ' + error.message };
    }
  }
  
  /**
   * 删除缩略图
   * @param {Object} row - 行数据
   * @param {number} thumbnailIndex - 缩略图索引
   * @param {Function} saveCallback - 保存回调函数
   */
  async deleteThumbnail(row, thumbnailIndex, saveCallback) {
    try {
      if (!row.thumbnails || thumbnailIndex < 0 || thumbnailIndex >= row.thumbnails.length) {
        throw new Error('无效的缩略图索引');
      }

      const thumbnail = row.thumbnails[thumbnailIndex];
      const imageUrl = thumbnail.src;

      console.log('🗑️ [图片删除] 开始删除缩略图:', {
        index: thumbnailIndex,
        imageUrl: imageUrl ? imageUrl.substring(0, 100) + '...' : null,
        alt: thumbnail.alt,
        thumbnailsLength: row.thumbnails.length
      });

      // 🔧 修复：完全从数组中移除缩略图
      row.thumbnails.splice(thumbnailIndex, 1);

      console.log('🗑️ [图片删除] 缩略图数据已移除，剩余数量:', row.thumbnails.length);

      // 2. 删除文件（如果不是base64图片）
      await this.deleteImageFile(imageUrl);

      // 3. 保存数据
      if (saveCallback) {
        saveCallback();
      }

      console.log('🗑️ [图片删除] 缩略图删除成功:', thumbnailIndex);
      return { success: true, message: '缩略图删除成功' };

    } catch (error) {
      console.error('🗑️ [图片删除] 缩略图删除失败:', error);
      return { success: false, message: '缩略图删除失败: ' + error.message };
    }
  }
  
  /**
   * 删除图片文件
   * @param {string} imageUrl - 图片URL
   */
  async deleteImageFile(imageUrl) {
    try {
      // 🔧 修复：跳过默认图片和base64图片
      if (!imageUrl ||
          imageUrl.includes('data:image/gif;base64') ||
          imageUrl.startsWith('data:image/')) {
        console.log('🗑️ [图片删除] 跳过默认图片或base64图片');
        return; // 跳过默认图片和base64图片
      }

      // 提取文件路径
      const urlParams = new URLSearchParams(imageUrl.split('?')[1]);
      const filePath = urlParams.get('path');

      if (!filePath) {
        console.log('🗑️ [图片删除] 无法从URL中提取文件路径，可能是base64图片');
        return; // 对于base64图片，不需要删除文件
      }

      // 调用后端API删除文件
      const response = await fetch('/api/local/delete-image', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ filePath })
      });

      if (!response.ok) {
        throw new Error(`删除文件失败: ${response.status} ${response.statusText}`);
      }

      console.log('🗑️ [图片删除] 文件删除成功:', filePath);

    } catch (error) {
      console.error('🗑️ [图片删除] 文件删除失败:', error);
      // 🔧 修复：对于base64图片删除失败不应该抛出错误
      if (!imageUrl.startsWith('data:image/')) {
        throw error;
      }
    }
  }
  
  /**
   * 批量删除图片
   * @param {Array} imageUrls - 图片URL数组
   */
  async deleteMultipleImages(imageUrls) {
    const results = [];
    
    for (const imageUrl of imageUrls) {
      try {
        await this.deleteImageFile(imageUrl);
        results.push({ url: imageUrl, success: true });
      } catch (error) {
        results.push({ url: imageUrl, success: false, error: error.message });
      }
    }
    
    return results;
  }
  
  /**
   * 获取图片文件信息
   * @param {string} imageUrl - 图片URL
   */
  getImageFileInfo(imageUrl) {
    try {
      if (!imageUrl || imageUrl.includes('data:image/gif;base64')) {
        return null;
      }
      
      const urlParams = new URLSearchParams(imageUrl.split('?')[1]);
      const filePath = urlParams.get('path');
      
      if (!filePath) {
        return null;
      }
      
      const fileName = filePath.split('/').pop();
      const fileExtension = fileName.split('.').pop();
      
      return {
        filePath,
        fileName,
        fileExtension
      };
    } catch (error) {
      console.error('🗑️ [图片删除] 获取文件信息失败:', error);
      return null;
    }
  }
}

// 创建单例实例
const imageDeleteService = new ImageDeleteService();

export default imageDeleteService;
