# 代码库结构

## 目录结构
```
txt2video/
├── public/              # 静态资源
│   ├── index.html       # HTML模板
│   ├── favicon.ico      # 网站图标
│   └── styles.css       # 全局样式
├── src/                 # 源代码
│   ├── components/      # Vue组件
│   ├── composables/     # Vue 3 组合式API函数
│   ├── config/          # 配置文件（LLM提供商等）
│   ├── router/          # Vue Router配置
│   ├── server/          # 后端服务器代码
│   ├── services/        # 业务服务层
│   ├── types/           # 类型定义
│   ├── utils/           # 工具函数
│   ├── views/           # 页面组件
│   ├── App.vue          # 主应用组件
│   ├── main.js          # Vue应用入口
│   ├── background.js    # Electron主进程
│   ├── preload.js       # Electron预加载脚本
│   └── server.js        # Express后端服务器
├── scripts/             # 构建和维护脚本
├── data/                # 数据存储目录
├── uploads/             # 上传文件存储
├── userdata/            # 用户数据和设置
├── temp/                # 临时文件
├── draft/               # 草稿文件
└── docs/                # 文档
```

## 关键组件
- **HomePage.vue**: 主页组件
- **CreationPage.vue**: 创作页面
- **ContentCreationStudio.vue**: 内容创作工作室
- **composables/**: 包含大量可复用的组合式API函数
- **services/**: ComfyUI集成、LLM服务、图像处理等业务逻辑
- **server/**: Express API路由和中间件