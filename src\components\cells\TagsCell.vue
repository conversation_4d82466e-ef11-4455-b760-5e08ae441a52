<template>
  <div class="grid-cell tags-cell">
    <div class="tags-wrapper" :class="{ 'locked-row': isLocked }">
      <!-- 显示可选的角色标签 -->
      <div
        v-if="availableCharacters && availableCharacters.length > 0"
        class="available-tags"
      >
        <button
          v-for="character in availableCharacters"
          :key="character.name"
          class="tag-button character-tag"
          :class="{
            'tag-selected': selectedTags.includes(character.name),
            'tag-unselected': !selectedTags.includes(character.name),
            'tag-synced': isTagSynced(character.name),
            'disabled': isLocked
          }"
          :disabled="isLocked"
          @click="toggleTagSelection(character.name)"
          :title="isLocked ? '该行已锁定，无法修改标签' : `点击选择/取消选择 ${character.name}`"
        >
          {{ character.name }}
        </button>
      </div>

      <!-- 如果没有可选角色，显示空状态 -->
      <div
        v-else
        class="empty-tags"
      >
        <span class="empty-text">无可选角色</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TagsCell',
  emits: {
    'tag-selection-changed': (data) => typeof data === 'object' && data !== null,
    'keywords-updated': (data) => typeof data === 'object' && data !== null,
    'show-locked-message': null
  },
  props: {
    selectedCharacters: {
      type: Array,
      default: () => []
    },
    rowIndex: {
      type: Number,
      required: true
    },
    persistedSelectedTags: {
      type: Array,
      default: () => []
    },
    isLocked: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 获取可选的角色列表（来自全局选择的角色）
    availableCharacters() {
      return this.selectedCharacters || [];
    }
  },
  data() {
    return {
      selectedTags: [...this.persistedSelectedTags], // 从持久化数据初始化
      lastKeywordsUpdate: '', // 记录上次更新的关键词，避免重复更新
      isUpdatingKeywords: false // 防止重复更新标志
    }
  },
  methods: {
    toggleTagSelection(tagName) {
      // 检查是否锁定
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }

      const index = this.selectedTags.indexOf(tagName);
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedTags.splice(index, 1);
      } else {
        // 如果未选中，则添加到选择列表
        this.selectedTags.push(tagName);
      }

      if (process.env.NODE_ENV === 'development') {
        console.log(`TagsCell[${this.rowIndex}] - 标签选择状态变化:`, {
          tagName,
          selectedTags: this.selectedTags.length
        });
      }

      // 发送事件给父组件，包含行索引信息（性能优化：避免深拷贝）
      this.$emit('tag-selection-changed', {
        rowIndex: this.rowIndex,
        tagName,
        isSelected: this.selectedTags.includes(tagName),
        selectedTags: this.selectedTags // 直接传递引用，由父组件处理拷贝
      });

      // 性能优化：延迟关键词更新，避免与标签选择保存冲突
      this.$nextTick(() => {
        this.updateKeywords();
      });
    },

    // 根据选中的标签更新关键词
    updateKeywords() {
      // 防止重复更新
      if (this.isUpdatingKeywords) {
        return;
      }

      const selectedDescriptions = [];

      // 遍历选中的标签，找到对应的角色描述
      this.selectedTags.forEach(tagName => {
        const character = this.availableCharacters.find(char =>
          (typeof char === 'string' ? char : char.name) === tagName
        );

        if (character && character.description) {
          selectedDescriptions.push(character.description);
        }
      });

      // 将所有描述用逗号连接
      const keywordsText = selectedDescriptions.join(', ');

      // 检查是否与上次更新的内容相同，避免重复更新
      if (keywordsText === this.lastKeywordsUpdate) {
        return;
      }

      // 设置更新标志
      this.isUpdatingKeywords = true;
      this.lastKeywordsUpdate = keywordsText;

      // 发送关键词更新事件（标记为自动生成）
      this.$emit('keywords-updated', {
        rowIndex: this.rowIndex,
        keywords: keywordsText,
        isAutoGenerated: true  // 标记这是自动生成的内容
      });

      // 重置更新标志
      this.$nextTick(() => {
        this.isUpdatingKeywords = false;
      });
    },

    // 检查标签是否与全局推理抽屉同步
    isTagSynced(tagName) {
      return this.selectedCharacters.some(char =>
        (typeof char === 'string' ? char : char.name) === tagName
      );
    }
  },
  watch: {
    selectedCharacters: {
      handler(newVal, oldVal) {
        // 大幅减少日志输出 - 只在第一行且有显著变化时输出
        if (process.env.NODE_ENV === 'development' && this.rowIndex === 0 && newVal?.length !== oldVal?.length) {
          console.log('TagsCell - selectedCharacters changed for all rows:', newVal?.length || 0, 'items');
        }

        // 当可选角色变化时，不自动触发关键词更新，避免无限循环
        // 关键词更新应该只在用户主动选择标签时触发
      },
      immediate: true
    },
    persistedSelectedTags: {
      handler(newVal, oldVal) {
        // 避免不必要的更新和日志
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) {
          return;
        }

        // 只在开发模式下且有实际变化时输出日志，并且只对前几行输出
        if (process.env.NODE_ENV === 'development' && this.rowIndex < 3) {
          const hasContent = newVal && newVal.length > 0;
          if (hasContent || (oldVal && oldVal.length > 0)) {
            console.log(`TagsCell[${this.rowIndex}] - persistedSelectedTags changed:`, newVal?.length || 0, 'tags');
          }
        }

        // 当持久化数据变化时，更新本地状态（性能优化：避免深拷贝）
        this.selectedTags.length = 0;
        if (newVal && newVal.length > 0) {
          this.selectedTags.push(...newVal);
        }

        // 修复：当标签状态发生变化时，总是更新关键词
        // 这解决了AI分组后标签选择Description不显示的问题
        const hasTagsNow = newVal && newVal.length > 0;
        const hadTagsBefore = oldVal && oldVal.length > 0;

        if (hasTagsNow || hadTagsBefore) {
          this.$nextTick(() => {
            this.updateKeywords();
          });
        }
      },
      immediate: true
    },
    availableCharacters: {
      handler(newVal, oldVal) {
        // 大幅减少日志输出 - 只在第一行且有显著变化时输出
        if (process.env.NODE_ENV === 'development' && this.rowIndex === 0 && newVal?.length !== oldVal?.length) {
          console.log('TagsCell - availableCharacters changed for all rows:', newVal?.length || 0, 'characters');
        }

        // 当可选角色变化时，不自动更新关键词，避免无限循环
        // 只有用户主动点击标签时才应该更新关键词
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.tags-cell {
  /* 🔧 改为内容自适应宽度 */
  width: fit-content;
  min-width: fit-content;
  max-width: 200px;
}

.tags-wrapper {
  width: 100%;
  height: 100%;
  padding: 4px;
  overflow: auto;
  background-color: #252525;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

/* 可选标签样式 */
.available-tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 4px;
  width: 100%;
  justify-content: flex-start;
  align-content: flex-start;
}

.tag-button {
  background: #6b7280;
  color: #d1d5db;
  border: none;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75em;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: calc(50% - 2px);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tag-button:hover {
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tag-button.character-tag {
  /* 基础角色标签样式 */
}

/* 选中状态 - 绿色亮起 */
.tag-button.tag-selected {
  background: #22c55e !important;
  color: white !important;
  opacity: 1;
  box-shadow: 0 0 12px rgba(34, 197, 94, 0.6);
  border: 1px solid #16a34a;
  transform: translateY(-1px);
}

.tag-button.tag-selected:hover {
  background: #16a34a !important;
  box-shadow: 0 0 16px rgba(34, 197, 94, 0.8);
}

/* 未选中状态 - 淡色 */
.tag-button.tag-unselected {
  background: #6b7280;
  color: #d1d5db;
  opacity: 0.6;
}

.tag-button.tag-unselected:hover {
  background: #9ca3af;
  color: #f3f4f6;
  opacity: 0.8;
}

/* 同步状态 - 与全局推理抽屉同步的标签 */
.tag-button.tag-synced {
  border: 2px solid #3b82f6;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
}

.tag-button.tag-synced.tag-selected {
  border: 2px solid #16a34a;
  box-shadow: 0 0 12px rgba(34, 197, 94, 0.6);
}

/* 锁定状态样式 */
.tags-wrapper.locked-row {
  border: 2px solid #dc2626;
  border-radius: 4px;
  background-color: rgba(220, 38, 38, 0.1);
  position: relative;
}

.tags-wrapper.locked-row::before {
  content: '🔒';
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  background-color: #dc2626;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.tag-button.disabled {
  background-color: #333 !important;
  color: #666 !important;
  cursor: not-allowed !important;
  opacity: 0.5 !important;
  box-shadow: none !important;
  transform: none !important;
}

.tag-button.disabled:hover {
  background-color: #333 !important;
  color: #666 !important;
  transform: none !important;
}

.tag-button.tag-synced.tag-unselected {
  border: 2px solid #3b82f6;
  background: #dbeafe;
  color: #1e40af;
  opacity: 0.8;
}

/* 空状态样式 */
.empty-tags {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 40px;
}

.empty-text {
  color: #666;
  font-size: 0.7em;
  font-style: italic;
}
</style>









