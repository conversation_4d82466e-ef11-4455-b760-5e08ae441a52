/**
 * 图像生成组合式API
 * 提供ComfyUI图像生成功能的Vue 3组合式API
 */

import { ref, reactive, computed, readonly, onMounted } from 'vue';
import comfyuiImageGeneration from '../services/comfyuiImageGeneration.js';
import { useComfyUIErrorRecovery } from './useComfyUIErrorRecovery.js';

export function useImageGeneration() {
  // 🆕 集成错误恢复机制
  const errorRecovery = useComfyUIErrorRecovery();

  // 响应式状态
  const isGenerating = ref(false);
  const isCancelling = ref(false); // 🆕 取消中状态，防止竞态条件
  const generationProgress = ref(0);
  const generationStage = ref('');
  const generationMessage = ref('');
  const generationError = ref('');
  const currentTaskId = ref(null);

  // 创建一个包装函数来追踪 isGenerating 的变化
  const setIsGenerating = (value, location = 'unknown') => {
    const oldValue = isGenerating.value;

    // 只在值真正发生变化时更新和记录
    if (oldValue !== value) {
      isGenerating.value = value;
      console.log(`[状态] isGenerating: ${oldValue} -> ${value} (${location})`);
    }
  };

  // 🆕 任务队列管理
  const taskQueue = reactive([]);
  const currentTask = ref(null);
  const queueStats = reactive({
    total: 0,
    waiting: 0,
    processing: 0,
    completed: 0,
    cancelled: 0
  });

  // 生成历史记录
  const generationHistory = reactive(new Map());

  // 响应式的服务可用性状态
  const isServiceAvailable = ref(false);

  // 更新服务状态的方法
  const updateServiceStatus = () => {
    const available = comfyuiImageGeneration.isAvailable();
    if (isServiceAvailable.value !== available) {
      isServiceAvailable.value = available;
      console.log('🎨 [useImageGeneration] 服务状态更新:', available);
    }
  };

  // 🆕 计算属性 - 始终允许点击，将检查逻辑移到点击处理中
  const canGenerate = computed(() => {
    // 始终返回true，让按钮保持可点击状态
    // 具体的服务可用性检查将在点击时进行，并通过Toast通知用户
    return true;
  });

  const serviceStatus = computed(() => {
    return comfyuiImageGeneration.getStatus();
  });

  // 🆕 自动初始化服务
  onMounted(async () => {
    console.log('🎨 [图像生成] 组件挂载，开始自动初始化服务...');
    try {
      await initializeService();
      console.log('✅ [图像生成] 服务自动初始化成功');
    } catch (error) {
      console.warn('⚠️ [图像生成] 服务自动初始化失败，将在首次使用时重试:', error.message);
    }
  });

  /**
   * 初始化服务
   */
  const initializeService = async () => {
    try {
      const success = await comfyuiImageGeneration.initialize();
      if (!success) {
        throw new Error('ComfyUI服务初始化失败');
      }
      console.log('🎨 [图像生成] 服务初始化成功');

      // 初始化服务状态
      updateServiceStatus();

      return true;
    } catch (error) {
      console.error('🎨 [图像生成] 服务初始化失败:', error);
      generationError.value = error.message;
      isServiceAvailable.value = false;
      return false;
    }
  };

  /**
   * 为指定行生成图像 - 支持实时回调
   * @param {Object} params 生成参数
   * @param {string} params.prompt 正向提示词
   * @param {string} params.negativePrompt 负向提示词
   * @param {string} params.projectTitle 项目标题
   * @param {string} params.chapterTitle 章节标题
   * @param {number} params.rowIndex 行索引
   * @param {Function} params.onImageGenerated 单张图片生成完成回调
   * @param {Function} params.onProgress 进度更新回调
   */
  const generateImageForRow = async (params) => {
    const {
      prompt,
      negativePrompt = '',
      projectTitle,
      chapterTitle,
      rowIndex,
      onImageGenerated,
      onProgress
    } = params;

    if (!prompt || !projectTitle || !chapterTitle || rowIndex === undefined) {
      throw new Error('缺少必要的生成参数');
    }

    console.log('🎨 [图像生成] 请求为行生成图像:', {
      rowIndex,
      promptLength: prompt.length,
      projectTitle,
      chapterTitle
    });

    console.log('🔍 [DEBUG] generateImageForRow调用时的状态:', {
      isGenerating: isGenerating.value,
      currentTask: currentTask.value,
      taskQueueLength: taskQueue.length,
      timestamp: new Date().toISOString()
    });

    // 🆕 在生成前检查服务可用性
    const serviceStatus = comfyuiImageGeneration.getStatus();
    if (!serviceStatus.isAvailable) {
      const errorMsg = 'ComfyUI服务不可用，请检查设置中的服务器地址和工作流配置';

      console.log('🔔 [Toast] 服务不可用，准备显示Toast通知');

      // 使用全局Toast通知
      if (typeof window !== 'undefined' && window.showToast) {
        console.log('🔔 [Toast] 调用全局showToast，参数:', {
          type: 'error',
          title: '服务不可用',
          message: errorMsg
        });

        window.showToast({
          type: 'error',
          title: '服务不可用',
          message: errorMsg
        });
      } else {
        console.warn('🔔 [Toast] 全局showToast方法不可用');
      }

      throw new Error(errorMsg);
    }

    // 🔧 修复：检查队列状态时，确保不受已取消任务的影响
    // 只有当前确实有正在处理的任务时才加入队列
    const hasActiveTask = currentTask.value &&
                         currentTask.value.status === 'processing';

    // 🔧 修复：检查前后端状态一致性
    const backendStatus = comfyuiImageGeneration.getStatus();
    const frontendIsGenerating = isGenerating.value;
    const backendIsGenerating = backendStatus?.isGenerating || false;

    console.log('🔍 [队列检查] 当前状态:', {
      前端isGenerating: frontendIsGenerating,
      后端isGenerating: backendIsGenerating,
      currentTask: currentTask.value?.status,
      hasActiveTask,
      状态一致: frontendIsGenerating === backendIsGenerating,
      rowIndex: rowIndex
    });

    // 🔧 关键修复：基于实际状态决定是否排队
    const shouldQueue = frontendIsGenerating || backendIsGenerating || hasActiveTask;

    console.log('🔍 [队列决策] 是否需要排队:', {
      shouldQueue,
      frontendIsGenerating,
      backendIsGenerating,
      hasActiveTask,
      isCancelling: isCancelling.value,
      rowIndex
    });

    // 🔧 关键修复：如果需要排队，立即处理
    if (shouldQueue && !isCancelling.value) {
      console.log('📋 [队列管理] 有任务正在执行，将新任务加入队列');

      const queueTask = addToQueue({
        prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        onImageGenerated,
        onProgress
      });

      // 🔧 关键修复：通知UI任务已进入队列
      if (onProgress) {
        onProgress({
          stage: 'queued',
          progress: 0,
          message: `排队中 (${queueTask.queuePosition})`,
          isQueued: true,
          taskId: queueTask.id,
          queuePosition: queueTask.queuePosition,
          isProcessing: false,
          isCompleted: false,
          isError: false,
          isCancelled: false
        });
      }

      // 返回队列任务信息而不是直接执行
      return {
        isQueued: true,
        taskId: queueTask.id,
        queuePosition: queueTask.queuePosition
      };
    }

    // 🔧 关键修复：如果没有任务在执行，立即设置状态并执行
    console.log('🔧 [并发保护] 立即设置isGenerating状态，防止并发任务');
    setIsGenerating(true, 'generateImageForRow-开始执行');

    // 🎨 直接执行图像生成
    console.log('🎨 [图像生成] 直接执行图像生成');

    try {
      return await executeImageGeneration({
        prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        onImageGenerated,
        onProgress
      });
    } catch (error) {
      // 如果执行失败，重置状态
      setIsGenerating(false, 'generateImageForRow-执行失败');
      throw error;
    }
  };

  /**
   * 批量生成图像
   * @param {Array} rows 行数据数组
   * @param {string} projectTitle 项目标题
   * @param {string} chapterTitle 章节标题
   */
  const generateImagesForRows = async (rows, projectTitle, chapterTitle) => {
    if (!rows || rows.length === 0) {
      throw new Error('没有要生成的行数据');
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      
      try {
        // 检查行是否有关键词
        if (!row.keywords || row.keywords.trim() === '') {
          // console.warn(`🎨 [图像生成] 跳过第${i}行，没有关键词`);
          continue;
        }

        // console.log(`🎨 [图像生成] 批量生成 ${i + 1}/${rows.length}`);

        const result = await generateImageForRow({
          prompt: row.keywords,
          negativePrompt: '', // 可以从设置中获取默认负向提示词
          projectTitle,
          chapterTitle,
          rowIndex: i
        });

        results.push({
          rowIndex: i,
          success: true,
          images: result
        });

        // 批量生成时添加延迟，避免服务器压力过大
        if (i < rows.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        // console.error(`🎨 [图像生成] 第${i}行生成失败:`, error);
        errors.push({
          rowIndex: i,
          error: error.message
        });
        
        results.push({
          rowIndex: i,
          success: false,
          error: error.message
        });
      }
    }

    return {
      results,
      errors,
      successCount: results.filter(r => r.success).length,
      totalCount: rows.length
    };
  };

  /**
   * 🆕 健壮的同步取消生成 - 确保ComfyUI真正停止
   */
  const cancelGeneration = async () => {
    try {
      console.log('🎨 [图像生成] 开始取消生成...');

      // 🆕 步骤1：设置取消中状态，阻止新任务启动
      isCancelling.value = true;
      generationStage.value = 'cancelling';
      generationMessage.value = '正在取消任务...';

      // 🆕 如果有当前任务在处理，标记为取消
      if (currentTask.value && currentTask.value.status === 'processing') {
        currentTask.value.status = 'cancelled';
        currentTask.value.completedAt = new Date().toISOString();
        currentTask.value = null;
        updateQueueStats();
      }

      // 🆕 步骤2：调用后端同步取消
      const cancelResult = await comfyuiImageGeneration.cancelGeneration();

      console.log('🎨 [图像生成] 取消结果:', cancelResult);

      // 🆕 步骤3：等待ComfyUI确认取消完成
      await waitForCancellationComplete();

      // 🆕 步骤4：根据取消结果更新UI状态
      if (cancelResult.success) {
        currentTaskId.value = null;
        generationProgress.value = 0;

        // 检查是否有错误信息（兼容不同的返回格式）
        const errors = cancelResult.errors || [];
        if (errors.length > 0) {
          // 有错误但本地状态已重置
          generationStage.value = 'cancelled';
          generationMessage.value = `任务已取消 (${errors.length}个警告)`;
          generationError.value = `取消过程中的警告: ${errors.join(', ')}`;

          console.warn('⚠️ [图像生成] 取消过程中有警告:', errors);
        } else {
          // 完全成功
          generationStage.value = 'cancelled';
          generationMessage.value = '任务已成功取消';
          generationError.value = '';
        }
      } else {
        // 取消失败，强制重置
        console.error('❌ [图像生成] 取消失败，执行强制重置');
        forceResetState();
        generationError.value = '取消失败，已强制重置状态';
      }

      // 🆕 步骤4：验证状态一致性
      const validation = comfyuiImageGeneration.validateStateConsistency();
      if (!validation.isConsistent) {
        console.warn('⚠️ [图像生成] 检测到状态不一致:', validation.issues);
        // 强制重置状态
        comfyuiImageGeneration.forceStateReset();
      }

      // 🆕 步骤5：更新服务状态
      updateServiceStatus();

      // 🆕 步骤6：重置取消中状态，允许新任务启动
      setIsGenerating(false, 'cancelGeneration-完成');
      isCancelling.value = false;

      // 🔧 修复：取消操作后，短暂延迟触发队列处理
      console.log('🔧 [取消后队列] 短暂延迟检查队列');
      setTimeout(() => {
        checkAndTriggerQueue();
      }, 500); // 🔧 减少延迟时间，快速响应

      // 🆕 步骤6：显示取消结果摘要
      const successRate = [
        cancelResult.interruptSent,
        cancelResult.queueCleared,
        cancelResult.verificationPassed
      ].filter(Boolean).length;

      console.log(`🎨 [图像生成] 取消操作完成 (${successRate}/3 步骤成功)`);

      return {
        success: cancelResult.success,
        details: cancelResult,
        hasWarnings: (cancelResult.errors || []).length > 0
      };

    } catch (error) {
      console.error('🎨 [图像生成] 取消生成时出错:', error);

      // 🆕 出错时强制重置状态
      forceResetState();
      handleError(error, '取消生成');

      return {
        success: false,
        error: error.message,
        hasWarnings: true
      };
    }
  };

  /**
   * 获取行的生成历史
   */
  const getRowGenerationHistory = (projectTitle, chapterTitle, rowIndex) => {
    const historyKey = `${projectTitle}/${chapterTitle}/${rowIndex}`;
    return generationHistory.get(historyKey) || null;
  };

  /**
   * 清除生成历史
   */
  const clearGenerationHistory = () => {
    generationHistory.clear();
  };

  /**
   * 处理生成进度
   */
  const handleProgress = (progressData) => {
    generationProgress.value = progressData.progress || 0;
    generationStage.value = progressData.stage || '';
    generationMessage.value = progressData.message || '';
    
    console.log('🎨 [图像生成] 进度更新:', progressData);
  };

  /**
   * 🆕 简化的取消确认 - 快速完成
   */
  const waitForCancellationComplete = async () => {
    console.log('⏳ [取消确认] 简单等待取消完成...');
    // 只等待很短时间，确保interrupt信号发送完成
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log('✅ [取消确认] 取消确认完成');
  };

  /**
   * 🆕 增强的错误处理 - 使用toast通知
   */
  const handleError = (error, context = '', userInputData = null) => {
    const errorMessage = error.message || error.toString();
    generationError.value = errorMessage;
    console.error('🎨 [图像生成] 错误:', error);

    // 🆕 检查是否是用户取消操作，如果是则不显示错误toast
    if (errorMessage.includes('任务被取消') ||
        errorMessage.includes('cancelled') ||
        errorMessage.includes('CANCELLED') ||
        errorMessage.includes('interrupted')) {
      console.log('ℹ️ [图像生成] 用户取消操作，不显示错误toast');
      return;
    }

    // 🆕 使用错误恢复机制处理错误（现在使用toast通知）
    errorRecovery.handleError(error, context, userInputData);

    // 🆕 更新服务状态
    updateServiceStatusFromError(error);

    // 🆕 确保错误信息通过toast显示（移除重复调用）
    // errorRecovery.handleError已经处理了Toast通知，这里不需要重复调用
  };

  /**
   * 🆕 根据错误更新服务状态
   */
  const updateServiceStatusFromError = (error) => {
    const errorMessage = error.message || error.toString();

    if (errorMessage.includes('连接失败') || errorMessage.includes('Failed to fetch')) {
      errorRecovery.updateServiceState('disconnected', {
        isAvailable: false,
        connectionQuality: 'bad'
      });
      isServiceAvailable.value = false;
    } else if (errorMessage.includes('CUDA out of memory') || errorMessage.includes('OutOfMemoryError')) {
      errorRecovery.updateServiceState('connected', {
        isAvailable: true,
        connectionQuality: 'poor'
      });
    } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
      errorRecovery.updateServiceState('error', {
        isAvailable: false,
        connectionQuality: 'poor'
      });
    }
  };

  /**
   * 重置生成状态
   */
  const resetGenerationState = () => {
    generationProgress.value = 0;
    generationStage.value = '';
    generationMessage.value = '';
    generationError.value = '';
    currentTaskId.value = null;

    console.log('🎨 [图像生成] 状态已重置');
  };

  // 🆕 任务队列管理功能
  /**
   * 创建新的队列任务
   */
  const createQueueTask = (taskData) => {
    const task = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'waiting', // waiting, processing, completed, cancelled, failed
      createdAt: new Date().toISOString(),
      startedAt: null,
      completedAt: null,
      ...taskData
    };
    return task;
  };

  /**
   * 添加任务到队列
   */
  const addToQueue = (taskData) => {
    const task = createQueueTask(taskData);

    // 🔧 设置队列位置
    task.queuePosition = taskQueue.filter(t => t.status === 'waiting').length + 1;

    taskQueue.push(task);
    updateQueueStats();

    console.log('📋 [队列管理] 任务已加入队列:', {
      taskId: task.id,
      rowIndex: task.rowIndex,
      queuePosition: task.queuePosition,
      queueLength: taskQueue.length
    });

    // 显示队列状态通知
    if (typeof window !== 'undefined' && window.showToast) {
      window.showToast({
        type: 'info',
        title: '任务已加入队列',
        message: `第${task.rowIndex + 1}行图像生成任务已排队，当前队列中有${taskQueue.filter(t => t.status === 'waiting').length}个任务等待`
      });
    }

    // 如果当前没有任务在执行，立即开始处理队列
    if (!isGenerating.value) {
      processQueue();
    }

    return task;
  };

  /**
   * 从队列中移除任务
   */
  const removeFromQueue = (taskId) => {
    const taskIndex = taskQueue.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
      const task = taskQueue[taskIndex];

      // 只能移除等待中的任务
      if (task.status === 'waiting') {
        task.status = 'cancelled';
        task.completedAt = new Date().toISOString();
        taskQueue.splice(taskIndex, 1);
        updateQueueStats();

        console.log('📋 [队列管理] 任务已从队列移除:', {
          taskId: task.id,
          rowIndex: task.rowIndex
        });

        // 显示取消通知
        if (typeof window !== 'undefined' && window.showToast) {
          window.showToast({
            type: 'success',
            title: '已取消排队',
            message: `第${task.rowIndex + 1}行的图像生成任务已从队列中移除`
          });
        }

        return true;
      } else {
        console.warn('📋 [队列管理] 无法移除非等待状态的任务:', task.status);
        return false;
      }
    }
    return false;
  };

  /**
   * 更新队列统计信息
   */
  const updateQueueStats = () => {
    queueStats.total = taskQueue.length;
    queueStats.waiting = taskQueue.filter(t => t.status === 'waiting').length;
    queueStats.processing = taskQueue.filter(t => t.status === 'processing').length;
    queueStats.completed = taskQueue.filter(t => t.status === 'completed').length;
    queueStats.cancelled = taskQueue.filter(t => t.status === 'cancelled').length;
  };

  /**
   * 🆕 统一的队列触发检查函数
   */
  const checkAndTriggerQueue = () => {
    // 延迟检查，确保状态已完全更新
    setTimeout(() => {
      try {
        const hasWaitingTasks = taskQueue.some(task => task.status === 'waiting');
        const isCurrentlyGenerating = isGenerating.value;
        const hasCurrentTask = currentTask.value && currentTask.value.status === 'processing';
        const isCurrentlyCancelling = isCancelling.value; // 🆕 检查取消中状态

        // 🆕 如果正在取消中，不启动新任务
        if (isCurrentlyCancelling) {
          console.log('🔧 [队列检查] 正在取消中，跳过队列处理');
          return;
        }

        // 🆕 状态一致性验证
        if (isCurrentlyGenerating && !hasCurrentTask && !hasWaitingTasks) {
          console.warn('🔧 [状态修复] 检测到虚假的isGenerating状态，自动修复');
          setIsGenerating(false, 'checkAndTriggerQueue-修复虚假状态');
          return;
        }

        if (hasWaitingTasks && !isCurrentlyGenerating && !hasCurrentTask) {
          console.log('🔧 [队列检查] 发现等待任务，触发队列处理');
          processQueue();
        }
      } catch (error) {
        console.error('🔧 [队列检查] 检查过程出错:', error);
        // 出错时强制重置状态
        setIsGenerating(false, 'checkAndTriggerQueue-错误重置');
        currentTask.value = null;
        isCancelling.value = false; // 🆕 重置取消状态
      }
    }, 100);
  };

  /**
   * 获取指定行的队列状态
   */
  const getRowQueueStatus = (rowIndex) => {
    const task = taskQueue.find(t => t.rowIndex === rowIndex);
    if (!task) {
      return {
        isQueued: false,
        status: null,
        taskId: null,
        position: -1
      };
    }

    const waitingTasks = taskQueue.filter(t => t.status === 'waiting');
    const position = waitingTasks.findIndex(t => t.id === task.id);

    return {
      isQueued: true,
      status: task.status,
      taskId: task.id,
      position: position >= 0 ? position + 1 : -1, // 1-based position
      canCancel: task.status === 'waiting'
    };
  };

  /**
   * 根据行索引取消排队
   */
  const cancelQueueByRowIndex = (rowIndex) => {
    const task = taskQueue.find(t => t.rowIndex === rowIndex && t.status === 'waiting');
    if (task) {
      return removeFromQueue(task.id);
    }
    return false;
  };

  /**
   * 清空队列 - 🔧 增强版：同时取消正在处理的任务
   */
  const clearQueue = async () => {
    console.log('📋 [队列管理] 开始清空队列...');

    const waitingTasks = taskQueue.filter(t => t.status === 'waiting');
    const processingTasks = taskQueue.filter(t => t.status === 'processing');

    // 🔧 首先取消正在处理的任务
    if (processingTasks.length > 0) {
      console.log(`📋 [队列管理] 取消${processingTasks.length}个正在处理的任务`);

      try {
        // 调用取消生成
        await cancelGeneration();
      } catch (error) {
        console.error('📋 [队列管理] 取消正在处理的任务失败:', error);
      }
    }

    // 🔧 标记所有等待中的任务为已取消
    waitingTasks.forEach(task => {
      task.status = 'cancelled';
      task.completedAt = new Date().toISOString();

      // 🔧 通知UI：任务被取消
      if (task.onProgress) {
        task.onProgress({
          stage: 'cancelled',
          progress: 0,
          message: '任务已取消',
          isCompleted: true,
          isCancelled: true,
          rowIndex: task.rowIndex,
          taskId: task.id
        });
      }
    });

    // 移除所有已取消的任务
    for (let i = taskQueue.length - 1; i >= 0; i--) {
      if (taskQueue[i].status === 'cancelled') {
        taskQueue.splice(i, 1);
      }
    }

    updateQueueStats();

    const totalCancelled = waitingTasks.length + processingTasks.length;
    console.log(`📋 [队列管理] 队列已清空，取消了${totalCancelled}个任务 (等待中: ${waitingTasks.length}, 处理中: ${processingTasks.length})`);

    // 显示清空通知
    if (typeof window !== 'undefined' && window.showToast && totalCancelled > 0) {
      window.showToast({
        type: 'info',
        title: '队列已清空',
        message: `已取消${totalCancelled}个任务 (等待中: ${waitingTasks.length}, 处理中: ${processingTasks.length})`
      });
    }

    return {
      cancelled: totalCancelled,
      waiting: waitingTasks.length,
      processing: processingTasks.length
    };
  };

  /**
   * 处理队列 - FIFO调度
   */
  const processQueue = async () => {
    console.log('🔧 [队列处理] 开始处理队列，当前状态:', {
      isGenerating: isGenerating.value,
      isCancelling: isCancelling.value,
      queueLength: taskQueue.length,
      waitingTasks: taskQueue.filter(t => t.status === 'waiting').length
    });

    // 如果正在生成、正在取消或队列为空，则不处理
    if (isGenerating.value || isCancelling.value || taskQueue.length === 0) {
      console.log('🔧 [队列处理] 跳过处理：', {
        isGenerating: isGenerating.value,
        isCancelling: isCancelling.value,
        queueEmpty: taskQueue.length === 0
      });
      return;
    }

    // 查找下一个等待中的任务
    const nextTask = taskQueue.find(task => task.status === 'waiting');
    if (!nextTask) {
      console.log('🔧 [队列处理] 没有等待中的任务');
      return;
    }

    console.log(`🔧 [队列处理] 准备处理任务 - 行${nextTask.rowIndex + 1}`);

    // 🔧 修复：确保底层服务状态干净，避免受到之前取消状态影响
    console.log('🔧 [队列处理] 重置底层服务状态');
    if (comfyuiImageGeneration.forceStateReset) {
      comfyuiImageGeneration.forceStateReset();
    }

    // 设置全局生成状态，防止重复处理
    setIsGenerating(true, 'processQueue-开始');

    // 开始处理任务
    currentTask.value = nextTask;
    nextTask.status = 'processing';
    nextTask.startedAt = new Date().toISOString();

    console.log(`🔧 [队列处理] 开始处理任务 - 行${nextTask.rowIndex + 1}, 任务ID: ${nextTask.id}`);

    // 🔧 立即更新任务状态为processing，确保队列统计正确
    nextTask.status = 'processing';
    nextTask.startedAt = new Date().toISOString();

    // 🔧 立即更新队列统计，确保队列面板显示正确
    updateQueueStats();

    console.log('📊 [队列统计] 任务开始处理，更新统计:', {
      total: queueStats.total,
      waiting: queueStats.waiting,
      processing: queueStats.processing,
      taskId: nextTask.id,
      rowIndex: nextTask.rowIndex
    });

    // 🔧 立即通知UI：任务开始执行，从"排队中"切换到"生成中"
    if (nextTask.onProgress) {
      nextTask.onProgress({
        stage: 'starting',
        progress: 0,
        message: '开始生成图像...',
        isProcessing: true, // 🔧 关键：标记任务正在处理
        rowIndex: nextTask.rowIndex, // 🔧 确保行索引被传递
        taskId: nextTask.id // 🔧 传递任务ID
      });
    }

    try {
      // 调用专门的队列图像生成函数
      const result = await executeImageGenerationForQueue(nextTask);

      // 任务完成
      nextTask.status = 'completed';
      nextTask.completedAt = new Date().toISOString();
      nextTask.result = result;

      console.log(`✅ [队列处理] 任务完成 - 行${nextTask.rowIndex + 1}, 生成了${result?.length || 0}张图像`);

      // 🔧 立即更新队列统计，确保队列面板显示正确
      updateQueueStats();

      console.log('📊 [队列统计] 任务完成，更新统计:', {
        total: queueStats.total,
        waiting: queueStats.waiting,
        processing: queueStats.processing,
        completed: queueStats.completed,
        taskId: nextTask.id
      });

      // 🔧 通知UI：任务完成，清理生成状态
      if (nextTask.onProgress) {
        nextTask.onProgress({
          stage: 'completed',
          progress: 100,
          message: `生成完成，共${result?.length || 0}张图像`,
          isCompleted: true, // 🔧 关键：标记任务完成
          rowIndex: nextTask.rowIndex,
          taskId: nextTask.id
        });
      }

    } catch (error) {
      // 任务失败
      nextTask.status = 'failed';
      nextTask.completedAt = new Date().toISOString();
      nextTask.error = error.message;

      console.error(`❌ [队列处理] 任务失败 - 行${nextTask.rowIndex + 1}:`, error.message);

      // 🔧 立即更新队列统计，确保队列面板显示正确
      updateQueueStats();

      console.log('📊 [队列统计] 任务失败，更新统计:', {
        total: queueStats.total,
        waiting: queueStats.waiting,
        processing: queueStats.processing,
        completed: queueStats.completed,
        taskId: nextTask.id
      });

      // 🔧 通知UI：任务失败，清理生成状态
      if (nextTask.onProgress) {
        nextTask.onProgress({
          stage: 'failed',
          progress: 0,
          message: `生成失败: ${error.message}`,
          isCompleted: true, // 🔧 关键：标记任务完成（失败也是完成）
          isError: true, // 🔧 标记为错误状态
          rowIndex: nextTask.rowIndex,
          taskId: nextTask.id
        });
      }

      // 集成错误恢复机制
      handleError(error, `队列任务执行失败 (行${nextTask.rowIndex + 1})`, {
        taskId: nextTask.id,
        rowIndex: nextTask.rowIndex,
        prompt: nextTask.prompt
      });
    } finally {
      // 🔧 清理生成状态
      generationProgress.value = 0;
      generationStage.value = '';
      generationMessage.value = '';
      currentTaskId.value = null;

      // 清理当前任务状态和生成状态
      currentTask.value = null;
      setIsGenerating(false, 'processQueue-完成');
      updateQueueStats();

      console.log(`🔧 [队列处理] 任务状态已清理 - 行${nextTask.rowIndex + 1}`);

      // 🔧 只有在还有等待任务时才继续处理
      const hasWaitingTasks = taskQueue.some(task => task.status === 'waiting');
      if (hasWaitingTasks) {
        console.log(`🔧 [队列处理] 发现${taskQueue.filter(t => t.status === 'waiting').length}个等待任务，继续处理`);
        setTimeout(() => {
          processQueue();
        }, 500);
      } else {
        console.log('✅ [队列处理] 没有等待任务，队列处理完成');
      }
    }
  };

  /**
   * 专门用于队列的图像生成函数（不管理 isGenerating 状态）
   */
  const executeImageGenerationForQueue = async (task) => {
    const {
      prompt,
      negativePrompt,
      projectTitle,
      chapterTitle,
      rowIndex,
      onImageGenerated,
      onProgress
    } = task;

    console.log(`🔧 [队列生成] 开始处理队列任务 - 行${rowIndex + 1}`);

    try {
      // 🔧 修复：确保底层服务状态正确，避免受到之前取消状态的影响
      console.log('🔧 [队列生成] 重置底层服务状态，清除取消状态');
      if (comfyuiImageGeneration.forceStateReset) {
        comfyuiImageGeneration.forceStateReset();
      }

      // 🔧 重置生成状态（但不设置isGenerating，由processQueue管理）
      generationProgress.value = 0;
      generationStage.value = 'preparing';
      generationMessage.value = '准备生成图像...';
      generationError.value = '';
      currentTaskId.value = task.id;

      // 创建组合的进度处理函数
      const combinedProgressHandler = (progressData) => {
        // 更新内部状态
        handleProgress(progressData);

        // 🔧 确保队列任务的进度更新被正确传递
        console.log(`🔧 [队列进度] 行${rowIndex + 1}:`, progressData);

        // 调用外部回调
        if (onProgress) {
          onProgress(progressData);
        }
      };

      // 🔧 修复：生成图像，标识为队列任务
      const result = await comfyuiImageGeneration.generateImage({
        positivePrompt: prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        isQueueTask: true, // 🔧 关键：标识这是队列任务，允许强制执行
        onProgress: combinedProgressHandler,
        onError: handleError,
        onImageGenerated: onImageGenerated
      });

      if (result && result.length > 0) {
        // 保存生成历史
        const historyKey = `${projectTitle}/${chapterTitle}/${rowIndex}`;
        generationHistory.set(historyKey, {
          rowIndex,
          prompt,
          negativePrompt,
          images: result,
          timestamp: new Date().toISOString(),
          projectTitle,
          chapterTitle
        });

        return result;
      } else {
        throw new Error('图像生成失败，未返回有效结果');
      }

    } catch (error) {
      generationError.value = error.message;
      updateServiceStatus();
      throw error;
    }
    // 注意：这里不设置 isGenerating = false，由 processQueue 管理
  };

  /**
   * 执行实际的图像生成（直接调用，非队列）
   */
  const executeImageGeneration = async (task) => {
    const {
      prompt,
      negativePrompt,
      projectTitle,
      chapterTitle,
      rowIndex,
      onImageGenerated,
      onProgress
    } = task;

    // 重置状态
    resetGenerationState();
    // 🔧 注意：isGenerating已经在generateImageForRow中设置，这里不重复设置

    try {
      // 创建组合的进度处理函数
      const combinedProgressHandler = (progressData) => {
        // 更新内部状态
        handleProgress(progressData);
        // 调用外部回调
        if (onProgress) {
          onProgress(progressData);
        }
      };

      // 🔧 修复：生成图像，标识为队列任务
      const result = await comfyuiImageGeneration.generateImage({
        positivePrompt: prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        isQueueTask: true, // 🔧 关键：标识这是队列任务，允许强制执行
        onProgress: combinedProgressHandler,
        onError: handleError,
        onImageGenerated: onImageGenerated
      });

      if (result && result.length > 0) {
        // 保存生成历史
        const historyKey = `${projectTitle}/${chapterTitle}/${rowIndex}`;
        generationHistory.set(historyKey, {
          rowIndex,
          prompt,
          negativePrompt,
          images: result,
          timestamp: new Date().toISOString(),
          projectTitle,
          chapterTitle
        });

        console.log('🎨 [图像生成] 生成成功:', result.length, '张图像');
        return result;
      } else {
        // 🔧 检查是否是取消操作导致的空结果
        if (generationStage.value === 'cancelled' || generationStage.value === 'cancelling') {
          console.log('ℹ️ [图像生成] 任务已取消，不抛出错误');
          return []; // 返回空数组而不是抛出错误
        }
        throw new Error('图像生成失败，未返回有效结果');
      }

    } catch (error) {
      console.error('🎨 [图像生成] 生成失败:', error);
      generationError.value = error.message;

      // 更新服务状态
      updateServiceStatus();

      throw error;
    } finally {
      setIsGenerating(false, 'executeImageGeneration-finally');
      currentTaskId.value = null;

      // 生成完成后更新服务状态
      updateServiceStatus();

      // 🆕 检查并处理队列中的等待任务
      checkAndTriggerQueue();
    }
  };

  /**
   * 🆕 强化的状态同步验证
   */
  const validateAndFixStateSync = () => {
    try {
      const frontendState = {
        isGenerating: isGenerating.value,
        currentTask: currentTask.value,
        queueLength: taskQueue.length,
        waitingTasks: taskQueue.filter(t => t.status === 'waiting').length,
        processingTasks: taskQueue.filter(t => t.status === 'processing').length
      };

      // 检测状态不一致的情况
      const inconsistencies = [];

      // 1. 检查isGenerating与实际任务状态的一致性
      if (frontendState.isGenerating && frontendState.processingTasks === 0 && !frontendState.currentTask) {
        inconsistencies.push('isGenerating为true但没有处理中的任务');
      }

      // 2. 检查currentTask与队列状态的一致性
      if (frontendState.currentTask && frontendState.processingTasks === 0) {
        inconsistencies.push('存在currentTask但队列中没有处理中的任务');
      }

      // 3. 检查队列统计的一致性
      if (frontendState.waitingTasks > 0 && !frontendState.isGenerating && !frontendState.currentTask) {
        inconsistencies.push('有等待任务但没有触发处理');
      }

      if (inconsistencies.length > 0) {
        console.warn('🔧 [状态验证] 检测到状态不一致:', inconsistencies);
        console.log('🔧 [状态验证] 当前状态:', frontendState);

        // 自动修复
        if (frontendState.isGenerating && frontendState.processingTasks === 0 && !frontendState.currentTask) {
          setIsGenerating(false, 'validateAndFixStateSync-修复虚假isGenerating');
        }

        if (frontendState.currentTask && frontendState.processingTasks === 0) {
          currentTask.value = null;
          console.log('🔧 [状态验证] 清除无效的currentTask');
        }

        return false;
      }

      return true;
    } catch (error) {
      console.error('🔧 [状态验证] 验证过程出错:', error);
      return false;
    }
  };

  /**
   * 🆕 强制重置状态 - 解决状态同步问题
   */
  const forceResetState = () => {
    console.log('🔧 [图像生成] 执行强制状态重置...');

    // 重置前端状态
    setIsGenerating(false, 'forceResetState');
    currentTaskId.value = null;
    generationProgress.value = 0;
    generationStage.value = '';
    generationMessage.value = '';
    generationError.value = '';

    // 🆕 重置队列状态
    currentTask.value = null;
    // 将所有处理中的任务标记为失败
    taskQueue.forEach(task => {
      if (task.status === 'processing') {
        task.status = 'failed';
        task.completedAt = new Date().toISOString();
      }
    });
    updateQueueStats();

    // 重置后端状态
    const resetResult = comfyuiImageGeneration.forceStateReset();

    // 更新服务状态
    updateServiceStatus();

    // 🆕 验证重置后的状态
    setTimeout(() => {
      const isStateValid = validateAndFixStateSync();
      if (!isStateValid) {
        console.warn('🔧 [图像生成] 重置后状态仍有问题，进行二次修复');
      }
    }, 200);

    console.log('🔧 [图像生成] 强制状态重置完成:', {
      resetResult,
      isGenerating: isGenerating.value,
      currentTask: currentTask.value,
      queueStats: queueStats
    });

    return resetResult;
  };

  /**
   * 🆕 验证状态一致性
   */
  const validateStateConsistency = () => {
    const backendValidation = comfyuiImageGeneration.validateStateConsistency();
    const frontendState = {
      isGenerating: isGenerating.value,
      currentTaskId: currentTaskId.value,
      hasProgress: generationProgress.value > 0
    };

    const backendState = comfyuiImageGeneration.getStatus();

    const validation = {
      ...backendValidation,
      frontendState,
      backendState,
      frontendBackendSync: {
        isGeneratingMatch: frontendState.isGenerating === backendState.isGenerating,
        taskIdMatch: frontendState.currentTaskId === backendState.currentTaskId
      }
    };

    // 检查前后端状态同步
    if (!validation.frontendBackendSync.isGeneratingMatch) {
      validation.isConsistent = false;
      validation.issues.push('前后端isGenerating状态不同步');
      validation.recommendations.push('调用forceResetState()同步状态');
    }

    return validation;
  };

  /**
   * 🆕 测试多种WebSocket端点配置
   */
  const testWebSocketEndpoints = async () => {
    console.log('🔍 [图像生成] 测试多种WebSocket端点配置...');

    const clientId = 'test_' + Date.now();
    const testUrls = [
      `ws://localhost:8188/ws?clientId=${clientId}`,
      `ws://localhost:8188/ws`,
      `ws://localhost:8188/websocket?clientId=${clientId}`,
      `ws://localhost:8188/websocket`,
      `ws://localhost:8188/api/ws?clientId=${clientId}`,
      `ws://localhost:8188/socket.io/?EIO=4&transport=websocket`,
      `ws://localhost:8188/`
    ];

    const results = [];

    for (const url of testUrls) {
      console.log(`🔍 [图像生成] 测试端点: ${url}`);
      const result = await testSingleWebSocketEndpoint(url);
      results.push({
        url,
        ...result
      });

      // 如果找到工作的端点，立即返回
      if (result.success) {
        console.log(`✅ [图像生成] 找到工作的WebSocket端点: ${url}`);
        break;
      }

      // 在测试之间稍作延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return {
      clientId,
      results,
      workingEndpoint: results.find(r => r.success)?.url || null,
      summary: {
        totalTested: results.length,
        successCount: results.filter(r => r.success).length,
        commonErrors: getCommonErrors(results)
      }
    };
  };

  /**
   * 🆕 测试单个WebSocket端点
   */
  const testSingleWebSocketEndpoint = (wsUrl) => {
    return new Promise((resolve) => {
      const result = {
        success: false,
        error: null,
        events: [],
        responseTime: null,
        closeCode: null,
        closeReason: ''
      };

      const startTime = Date.now();

      try {
        const ws = new WebSocket(wsUrl);

        const timeout = setTimeout(() => {
          result.error = 'Connection timeout (5s)';
          result.events.push('timeout');
          result.responseTime = Date.now() - startTime;
          ws.close();
          resolve(result);
        }, 5000);

        ws.onopen = (event) => {
          console.log(`✅ [图像生成] WebSocket连接成功: ${wsUrl}`, event);
          result.success = true;
          result.events.push('open');
          result.responseTime = Date.now() - startTime;
          clearTimeout(timeout);
          ws.close();
          resolve(result);
        };

        ws.onclose = (event) => {
          console.log(`🔍 [图像生成] WebSocket连接关闭: ${wsUrl} - ${event.code} ${event.reason}`);
          result.events.push(`close:${event.code}`);
          result.closeCode = event.code;
          result.closeReason = event.reason;
          result.responseTime = Date.now() - startTime;

          if (!result.success && !result.error) {
            result.error = `Connection closed: ${event.code} ${event.reason || 'No reason'}`;
          }
          clearTimeout(timeout);
          resolve(result);
        };

        ws.onerror = (error) => {
          console.error(`❌ [图像生成] WebSocket连接错误: ${wsUrl}`, error);
          result.events.push('error');
          result.error = 'WebSocket connection error';
          result.responseTime = Date.now() - startTime;
          clearTimeout(timeout);
          resolve(result);
        };

        ws.onmessage = (event) => {
          console.log(`📨 [图像生成] WebSocket消息: ${wsUrl}`, event.data);
          result.events.push('message');
        };

      } catch (error) {
        result.error = `Failed to create WebSocket: ${error.message}`;
        result.events.push('creation_error');
        result.responseTime = Date.now() - startTime;
        resolve(result);
      }
    });
  };

  /**
   * 🆕 分析常见错误模式
   */
  const getCommonErrors = (results) => {
    const errorCounts = {};
    const closeCodes = {};

    results.forEach(result => {
      if (result.error) {
        errorCounts[result.error] = (errorCounts[result.error] || 0) + 1;
      }
      if (result.closeCode) {
        closeCodes[result.closeCode] = (closeCodes[result.closeCode] || 0) + 1;
      }
    });

    return {
      errors: errorCounts,
      closeCodes: closeCodes,
      mostCommonError: Object.keys(errorCounts).reduce((a, b) =>
        errorCounts[a] > errorCounts[b] ? a : b, null),
      mostCommonCloseCode: Object.keys(closeCodes).reduce((a, b) =>
        closeCodes[a] > closeCodes[b] ? a : b, null)
    };
  };

  /**
   * 🆕 手动测试WebSocket连接 (使用代理)
   */
  const testWebSocketDirectly = () => {
    const clientId = 'test_' + Date.now();
    // 🆕 使用Agent代理端点而不是直接连接ComfyUI
    const wsUrl = `ws://localhost:8091/api/local/comfyui-ws?clientId=${clientId}`;
    return testSingleWebSocketEndpoint(wsUrl).then(result => ({
      ...result,
      url: wsUrl,
      clientId: clientId
    }));
  };

  /**
   * 🆕 检查ComfyUI配置和WebSocket支持
   */
  const diagnoseComfyUIWebSocketIssue = async () => {
    console.log('🔍 [图像生成] 开始ComfyUI WebSocket问题诊断...');

    const diagnosis = {
      timestamp: new Date().toISOString(),
      comfyuiVersion: null,
      serverInfo: null,
      webSocketTests: null,
      configurationIssues: [],
      recommendations: []
    };

    try {
      // 1. 获取ComfyUI详细信息
      console.log('🔍 [图像生成] 步骤1: 获取ComfyUI服务器信息...');
      const systemStatsResult = await testProxyEndpoint();

      if (systemStatsResult.success) {
        diagnosis.serverInfo = systemStatsResult.responseData;
        diagnosis.comfyuiVersion = systemStatsResult.responseData?.system?.comfyui_version;
        console.log(`🔍 [图像生成] ComfyUI版本: ${diagnosis.comfyuiVersion}`);
      } else {
        diagnosis.configurationIssues.push('无法获取ComfyUI服务器信息');
      }

      // 2. 测试多种WebSocket端点
      console.log('🔍 [图像生成] 步骤2: 测试WebSocket端点...');
      diagnosis.webSocketTests = await testWebSocketEndpoints();

      // 3. 分析ComfyUI版本特定的WebSocket配置
      console.log('🔍 [图像生成] 步骤3: 分析版本特定配置...');
      const versionAnalysis = analyzeComfyUIVersionWebSocketSupport(diagnosis.comfyuiVersion);
      diagnosis.configurationIssues.push(...versionAnalysis.issues);
      diagnosis.recommendations.push(...versionAnalysis.recommendations);

      // 4. 检查常见的WebSocket配置问题
      console.log('🔍 [图像生成] 步骤4: 检查配置问题...');
      const configIssues = analyzeWebSocketConfigurationIssues(diagnosis.webSocketTests);
      diagnosis.configurationIssues.push(...configIssues.issues);
      diagnosis.recommendations.push(...configIssues.recommendations);

      // 5. 生成最终诊断报告
      const finalReport = generateWebSocketDiagnosisReport(diagnosis);

      console.log('🔍 [图像生成] WebSocket诊断完成:', finalReport);
      return finalReport;

    } catch (error) {
      console.error('❌ [图像生成] WebSocket诊断失败:', error);
      return {
        ...diagnosis,
        error: error.message,
        recommendations: [
          '检查ComfyUI是否正在运行',
          '确认localhost:8188端口可访问',
          '重启ComfyUI服务器'
        ]
      };
    }
  };

  /**
   * 🆕 分析ComfyUI版本的WebSocket支持
   */
  const analyzeComfyUIVersionWebSocketSupport = (version) => {
    const issues = [];
    const recommendations = [];

    if (!version) {
      issues.push('无法确定ComfyUI版本');
      recommendations.push('检查ComfyUI是否正确安装和运行');
      return { issues, recommendations };
    }

    console.log(`🔍 [图像生成] 分析ComfyUI版本 ${version} 的WebSocket支持...`);

    // 解析版本号
    const versionMatch = version.match(/(\d+)\.(\d+)\.(\d+)/);
    if (!versionMatch) {
      issues.push(`无法解析版本号: ${version}`);
      return { issues, recommendations };
    }

    const [, major, minor, patch] = versionMatch.map(Number);
    const versionNumber = major * 10000 + minor * 100 + patch;

    // ComfyUI WebSocket支持历史分析
    if (versionNumber >= 3038) { // 0.3.38+
      console.log('✅ [图像生成] ComfyUI版本支持WebSocket');
      recommendations.push('ComfyUI版本支持WebSocket，检查启动配置');

      // 检查0.3.38特定的WebSocket配置
      recommendations.push('确认ComfyUI启动时使用了正确的参数');
      recommendations.push('检查是否需要 --enable-cors-header 参数');
      recommendations.push('验证WebSocket端点路径是否为 /ws');

    } else if (versionNumber >= 3000) { // 0.3.0+
      issues.push('ComfyUI版本可能不完全支持WebSocket');
      recommendations.push('建议升级到ComfyUI 0.3.38或更高版本');
      recommendations.push('或者检查是否需要特殊配置启用WebSocket');

    } else {
      issues.push('ComfyUI版本过旧，不支持WebSocket');
      recommendations.push('必须升级到ComfyUI 0.3.0或更高版本以支持WebSocket');
    }

    return { issues, recommendations };
  };

  /**
   * 🆕 分析WebSocket配置问题
   */
  const analyzeWebSocketConfigurationIssues = (webSocketTests) => {
    const issues = [];
    const recommendations = [];

    if (!webSocketTests || !webSocketTests.results) {
      issues.push('WebSocket测试失败');
      return { issues, recommendations };
    }

    const { summary } = webSocketTests;

    // 如果找到工作的端点
    if (webSocketTests.workingEndpoint) {
      recommendations.push(`找到工作的WebSocket端点: ${webSocketTests.workingEndpoint}`);
      recommendations.push('更新应用配置以使用正确的端点');
      return { issues, recommendations };
    }

    // 分析失败模式
    const { mostCommonCloseCode, mostCommonError } = summary.commonErrors;

    if (mostCommonCloseCode === '1006') {
      issues.push('WebSocket连接被异常关闭 (错误1006)');
      recommendations.push('这通常表示ComfyUI的WebSocket服务未正确启动');
      recommendations.push('检查ComfyUI启动日志中是否有WebSocket相关错误');
      recommendations.push('确认ComfyUI启动参数包含WebSocket支持');
      recommendations.push('尝试重启ComfyUI服务器');
    }

    if (mostCommonError && mostCommonError.includes('timeout')) {
      issues.push('WebSocket连接超时');
      recommendations.push('ComfyUI可能响应缓慢或WebSocket服务未启动');
      recommendations.push('检查系统资源使用情况');
    }

    // 检查是否所有端点都失败
    if (summary.successCount === 0) {
      issues.push('所有WebSocket端点测试都失败');
      recommendations.push('ComfyUI的WebSocket功能可能未启用或配置错误');
      recommendations.push('检查ComfyUI启动命令和配置文件');
      recommendations.push('确认防火墙没有阻止WebSocket连接');
    }

    return { issues, recommendations };
  };

  /**
   * 🆕 生成WebSocket诊断报告
   */
  const generateWebSocketDiagnosisReport = (diagnosis) => {
    const report = {
      ...diagnosis,
      severity: 'unknown',
      rootCause: 'unknown',
      fixSteps: []
    };

    // 确定问题严重程度和根本原因
    if (diagnosis.webSocketTests?.workingEndpoint) {
      report.severity = 'low';
      report.rootCause = 'incorrect_endpoint';
      report.fixSteps = [
        `更新WebSocket连接URL为: ${diagnosis.webSocketTests.workingEndpoint}`,
        '重新启动应用以应用新配置'
      ];
    } else if (diagnosis.comfyuiVersion && diagnosis.serverInfo) {
      report.severity = 'medium';
      report.rootCause = 'websocket_not_enabled';
      report.fixSteps = [
        '检查ComfyUI启动命令',
        '确认包含WebSocket支持参数',
        '重启ComfyUI服务器',
        '验证WebSocket端点可访问性'
      ];
    } else {
      report.severity = 'high';
      report.rootCause = 'comfyui_not_running';
      report.fixSteps = [
        '确认ComfyUI正在运行',
        '检查localhost:8188端口状态',
        '查看ComfyUI启动日志',
        '重新安装或配置ComfyUI'
      ];
    }

    return report;
  };

  /**
   * 🆕 测试代理端点连接
   */
  const testProxyEndpoint = async () => {
    try {
      console.log('🔍 [图像生成] 测试代理端点...');

      generationStage.value = 'testing';
      generationMessage.value = '正在测试代理端点连接...';

      // 测试代理端点是否可访问
      console.log('🔍 [图像生成] 发送请求到: /api/local/comfyui-proxy/system_stats');

      const response = await fetch('/api/local/comfyui-proxy/system_stats', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      console.log('🔍 [图像生成] 代理响应状态:', response.status, response.statusText);
      console.log('🔍 [图像生成] 代理响应头:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const data = await response.json();
        console.log('🔍 [图像生成] 代理响应数据:', data);

        generationStage.value = '';
        generationMessage.value = '代理端点测试成功';

        return {
          success: true,
          proxyWorking: true,
          responseData: data,
          message: '代理端点连接正常'
        };
      } else {
        const errorText = await response.text();
        console.error('❌ [图像生成] 代理端点测试失败:', response.status, errorText);

        generationStage.value = '';
        generationMessage.value = '代理端点测试失败';

        return {
          success: false,
          proxyWorking: false,
          error: `HTTP ${response.status}: ${errorText}`,
          message: '代理端点连接失败'
        };
      }
    } catch (error) {
      console.error('❌ [图像生成] 代理端点测试出错:', error);

      generationStage.value = '';
      generationMessage.value = '代理端点测试出错';
      generationError.value = `代理测试失败: ${error.message}`;

      return {
        success: false,
        proxyWorking: false,
        error: error.message,
        message: '代理端点测试过程中出现错误'
      };
    }
  };

  /**
   * 🆕 检查ComfyUI WebSocket配置
   */
  const checkComfyUIWebSocketConfig = async () => {
    try {
      console.log('🔍 [图像生成] 检查ComfyUI WebSocket配置...');

      generationStage.value = 'testing';
      generationMessage.value = '正在检查ComfyUI WebSocket配置...';

      // 1. 检查ComfyUI Web界面是否可访问
      let webInterfaceResult;
      try {
        const webResponse = await fetch('http://localhost:8188/', {
          method: 'GET',
          mode: 'no-cors' // 避免CORS问题
        });
        webInterfaceResult = {
          accessible: webResponse.type === 'opaque', // no-cors模式下成功返回opaque
          responseType: webResponse.type
        };
        console.log('🔍 [图像生成] ComfyUI Web界面状态:', webResponse.type);
      } catch (error) {
        webInterfaceResult = {
          accessible: false,
          error: error.message
        };
      }

      // 2. 检查WebSocket端点
      console.log('🔍 [图像生成] 测试WebSocket连接...');
      const wsTestResult = await testWebSocketDirectly();

      // 3. 检查系统状态
      console.log('🔍 [图像生成] 检查系统状态...');
      const proxyTestResult = await testProxyEndpoint();

      // 4. 生成诊断建议
      const recommendations = generateWebSocketRecommendations(wsTestResult, webInterfaceResult, proxyTestResult);

      generationStage.value = '';
      generationMessage.value = 'WebSocket配置检查完成';

      const result = {
        webInterface: webInterfaceResult,
        websocket: wsTestResult,
        systemStats: proxyTestResult,
        recommendations: recommendations,
        summary: {
          webAccessible: webInterfaceResult.accessible,
          websocketWorking: wsTestResult.success,
          systemStatsWorking: proxyTestResult.success,
          overallStatus: webInterfaceResult.accessible && proxyTestResult.success ?
            (wsTestResult.success ? 'healthy' : 'websocket_issue') : 'server_issue'
        }
      };

      console.log('🔍 [图像生成] WebSocket配置检查结果:', result);
      return result;

    } catch (error) {
      console.error('❌ [图像生成] ComfyUI WebSocket配置检查失败:', error);
      generationStage.value = '';
      generationMessage.value = 'WebSocket配置检查失败';

      return {
        error: error.message,
        webInterface: { accessible: false, error: error.message },
        websocket: { success: false, error: error.message },
        systemStats: { success: false, error: error.message },
        recommendations: ['检查ComfyUI服务器是否正在运行', '确认localhost:8188端口可访问']
      };
    }
  };

  /**
   * 🆕 生成WebSocket问题解决建议
   */
  const generateWebSocketRecommendations = (wsTestResult, webInterfaceResult, systemStatsResult) => {
    const recommendations = [];

    // 基于WebSocket测试结果
    if (!wsTestResult.success) {
      if (wsTestResult.events.includes('close:1006')) {
        recommendations.push('🔧 WebSocket连接被异常关闭 (错误1006)');
        recommendations.push('   - 这通常表示ComfyUI的WebSocket功能未正确启用');
        recommendations.push('   - 检查ComfyUI版本是否支持WebSocket (建议使用最新版本)');
        recommendations.push('   - 确认ComfyUI启动时没有禁用WebSocket功能');
      }

      if (wsTestResult.events.includes('error')) {
        recommendations.push('🔧 WebSocket连接错误');
        recommendations.push('   - 检查防火墙是否阻止了WebSocket连接');
        recommendations.push('   - 确认localhost:8188端口可访问');
      }

      if (wsTestResult.events.includes('timeout')) {
        recommendations.push('🔧 WebSocket连接超时');
        recommendations.push('   - ComfyUI可能响应缓慢或未响应');
        recommendations.push('   - 尝试重启ComfyUI服务器');
      }
    }

    // 基于Web界面测试结果
    if (!webInterfaceResult.accessible) {
      recommendations.push('🔧 ComfyUI Web界面不可访问');
      recommendations.push('   - 确认ComfyUI正在localhost:8188端口运行');
      recommendations.push('   - 检查ComfyUI启动日志是否有错误');
    }

    // 基于系统状态测试结果
    if (!systemStatsResult.success) {
      recommendations.push('🔧 系统状态API不可访问');
      recommendations.push('   - ComfyUI API可能未正确启动');
      recommendations.push('   - 检查ComfyUI版本和配置');
    }

    // 综合建议
    if (systemStatsResult.success && !wsTestResult.success) {
      recommendations.push('💡 建议解决方案:');
      recommendations.push('   1. 更新ComfyUI到最新版本');
      recommendations.push('   2. 检查ComfyUI启动参数，确保启用WebSocket');
      recommendations.push('   3. 在ComfyUI Web界面中测试WebSocket功能');
      recommendations.push('   4. 如果问题持续，可以使用HTTP轮询模式 (性能较低但更稳定)');
    }

    if (!systemStatsResult.success) {
      recommendations.push('💡 基础问题解决:');
      recommendations.push('   1. 确认ComfyUI正在运行');
      recommendations.push('   2. 检查端口8188是否被占用');
      recommendations.push('   3. 重启ComfyUI服务器');
    }

    return recommendations;
  };

  /**
   * 🆕 验证CORS修复是否生效
   */
  const verifyCORSFix = async () => {
    try {
      console.log('🔍 [图像生成] 验证CORS修复...');

      generationStage.value = 'testing';
      generationMessage.value = '正在验证CORS修复...';

      // 测试代理端点是否可访问
      const response = await fetch('/api/local/comfyui-proxy/system_stats', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        generationStage.value = '';

        if (data.success && data.available) {
          generationMessage.value = 'CORS修复验证成功，ComfyUI服务器可访问';
          console.log('✅ [图像生成] CORS修复验证成功');
          console.log('🔍 [图像生成] ComfyUI信息:', data.data);

          return {
            success: true,
            corsFixed: true,
            serverAvailable: true,
            serverInfo: data.data,
            message: 'CORS问题已解决，ComfyUI服务器正常运行'
          };
        } else {
          generationMessage.value = 'CORS已修复但ComfyUI服务器不可用';
          console.warn('⚠️ [图像生成] CORS已修复但服务器不可用:', data);

          return {
            success: false,
            corsFixed: true,
            serverAvailable: false,
            error: data.error || data.message,
            message: 'CORS问题已解决，但ComfyUI服务器不可用'
          };
        }
      } else {
        generationStage.value = '';
        generationMessage.value = 'CORS修复验证失败';

        const errorText = await response.text();
        console.error('❌ [图像生成] CORS修复验证失败:', response.status, errorText);

        return {
          success: false,
          corsFixed: false,
          serverAvailable: false,
          error: `HTTP ${response.status}: ${errorText}`,
          message: 'CORS问题可能未完全解决'
        };
      }
    } catch (error) {
      generationStage.value = '';
      generationMessage.value = 'CORS修复验证出错';
      generationError.value = `CORS验证失败: ${error.message}`;

      console.error('❌ [图像生成] CORS修复验证出错:', error);

      return {
        success: false,
        corsFixed: false,
        serverAvailable: false,
        error: error.message,
        message: 'CORS修复验证过程中出现错误'
      };
    }
  };

  /**
   * 🆕 获取WebSocket连接状态
   */
  const getWebSocketStatus = () => {
    try {
      return comfyuiImageGeneration.getWebSocketStatus();
    } catch (error) {
      console.error('❌ [图像生成] 获取WebSocket状态失败:', error);
      return null;
    }
  };

  /**
   * 🆕 测试WebSocket连接
   */
  const testWebSocketConnection = async () => {
    try {
      console.log('🔍 [图像生成] 测试WebSocket连接...');

      generationStage.value = 'testing';
      generationMessage.value = '正在测试WebSocket连接...';

      const result = await comfyuiImageGeneration.testWebSocketConnection();

      generationStage.value = '';
      generationMessage.value = result.serverAvailable ?
        'WebSocket连接测试完成' :
        'ComfyUI服务器不可用';

      console.log('🔍 [图像生成] WebSocket测试结果:', result);
      return result;

    } catch (error) {
      console.error('❌ [图像生成] WebSocket连接测试失败:', error);
      generationError.value = `WebSocket测试失败: ${error.message}`;
      return null;
    }
  };

  /**
   * 🆕 重置WebSocket连接
   */
  const resetWebSocketConnection = async () => {
    try {
      console.log('🔄 [图像生成] 重置WebSocket连接...');

      // 更新状态
      generationStage.value = 'connecting';
      generationMessage.value = '正在重置WebSocket连接...';

      // 调用后端重置
      await comfyuiImageGeneration.resetWebSocketConnection();

      // 更新状态
      generationStage.value = '';
      generationMessage.value = 'WebSocket连接已重置';

      // 更新服务状态
      updateServiceStatus();

      console.log('✅ [图像生成] WebSocket连接重置完成');
      return true;

    } catch (error) {
      console.error('❌ [图像生成] WebSocket连接重置失败:', error);
      generationError.value = `WebSocket重置失败: ${error.message}`;
      return false;
    }
  };

  /**
   * 🆕 强制停止ComfyUI所有任务
   */
  const forceStopComfyUITasks = async () => {
    try {
      console.log('🚨 [图像生成] 强制停止ComfyUI所有任务...');

      // 立即重置前端状态
      setIsGenerating(false, 'forceStopComfyUITasks');
      currentTaskId.value = null;
      generationProgress.value = 0;
      generationStage.value = 'stopping';
      generationMessage.value = '正在强制停止ComfyUI任务...';

      // 调用后端强制停止
      const stopResult = await comfyuiImageGeneration.forceStopComfyUITasks();

      console.log('🚨 [图像生成] 强制停止结果:', stopResult);

      // 更新状态
      if (stopResult.verified) {
        generationStage.value = 'stopped';
        generationMessage.value = 'ComfyUI任务已强制停止';
        generationError.value = '';
      } else {
        generationStage.value = 'stopped';
        generationMessage.value = 'ComfyUI任务已停止 (部分操作失败)';
        generationError.value = stopResult.errors.join(', ');
      }

      // 强制重置所有状态
      forceResetState();

      return stopResult;

    } catch (error) {
      console.error('🚨 [图像生成] 强制停止ComfyUI任务失败:', error);

      // 即使失败也要重置前端状态
      forceResetState();
      generationError.value = `强制停止失败: ${error.message}`;

      return {
        success: false,
        error: error.message
      };
    }
  };

  /**
   * 🆕 增强的服务可用性检查 - 集成错误恢复
   */
  const checkServiceAvailability = async () => {
    try {
      errorRecovery.updateServiceState('connecting');

      await initializeService();
      const available = comfyuiImageGeneration.isAvailable();

      if (available) {
        errorRecovery.updateServiceState('connected', {
          isAvailable: true,
          connectionQuality: 'good'
        });
      } else {
        errorRecovery.updateServiceState('disconnected', {
          isAvailable: false
        });
      }

      return available;
    } catch (error) {
      console.error('🎨 [图像生成] 服务检查失败:', error);
      handleError(error, '服务可用性检查');
      return false;
    }
  };

  /**
   * 🆕 重试生成任务
   */
  const retryGeneration = async (originalParams) => {
    if (!errorRecovery.canRetryNow.value) {
      throw new Error('当前无法重试，请稍后再试');
    }

    return await errorRecovery.retry(async () => {
      // 恢复用户输入数据
      const restoredData = errorRecovery.restoreUserInput();
      const params = restoredData || originalParams;

      console.log('🔄 [图像生成] 开始重试生成任务');
      return await generateImageForRow(params);
    });
  };

  /**
   * 🆕 优化设置并重试
   */
  const optimizeAndRetry = async (originalParams) => {
    try {
      console.log('🧠 [图像生成] 应用内存优化并重试');

      // 触发ComfyUI服务的内存优化
      if (comfyuiImageGeneration.optimizeMemoryUsage) {
        comfyuiImageGeneration.optimizeMemoryUsage();
      }

      // 清除错误状态
      errorRecovery.clearError();

      // 重试生成
      return await generateImageForRow(originalParams);
    } catch (error) {
      handleError(error, '优化重试');
      throw error;
    }
  };

  /**
   * 🆕 重置连接
   */
  const resetConnection = async () => {
    try {
      console.log('🔄 [图像生成] 重置连接');

      // 清除错误状态
      errorRecovery.clearError();

      // 重新初始化服务
      await initializeService();

      // 检查连接状态
      return await checkServiceAvailability();
    } catch (error) {
      handleError(error, '重置连接');
      return false;
    }
  };

  /**
   * 获取生成统计信息
   */
  const getGenerationStats = () => {
    const totalGenerations = generationHistory.size;
    const recentGenerations = Array.from(generationHistory.values())
      .filter(item => {
        const itemTime = new Date(item.timestamp);
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return itemTime > oneDayAgo;
      }).length;

    return {
      totalGenerations,
      recentGenerations,
      isGenerating: isGenerating.value,
      serviceAvailable: comfyuiImageGeneration.isAvailable()
    };
  };

  return {
    // 状态
    isGenerating: readonly(isGenerating),
    isCancelling: readonly(isCancelling),
    generationProgress: readonly(generationProgress),
    generationStage: readonly(generationStage),
    generationMessage: readonly(generationMessage),
    generationError: readonly(generationError),
    currentTaskId: readonly(currentTaskId),

    // 🆕 队列状态
    taskQueue: readonly(taskQueue),
    currentTask: readonly(currentTask),
    queueStats: readonly(queueStats),

    // 计算属性
    canGenerate,
    serviceStatus,

    // 🆕 错误恢复状态和方法
    errorRecovery,

    // 方法
    initializeService,
    generateImageForRow,
    generateImagesForRows,
    cancelGeneration,
    getRowGenerationHistory,
    clearGenerationHistory,
    checkServiceAvailability,
    getGenerationStats,
    resetGenerationState,
    getStatus: () => comfyuiImageGeneration.getStatus(),

    // 🆕 队列管理方法
    addToQueue,
    removeFromQueue,
    processQueue,
    updateQueueStats,
    getRowQueueStatus,
    cancelQueueByRowIndex,
    clearQueue,
    checkAndTriggerQueue,

    // 🆕 错误恢复和重试方法
    retryGeneration,
    optimizeAndRetry,
    resetConnection,

    // 🆕 状态管理和验证方法
    forceResetState,
    validateStateConsistency,
    validateAndFixStateSync,
    forceStopComfyUITasks,
    resetWebSocketConnection,
    getWebSocketStatus,
    testWebSocketConnection,
    testWebSocketDirectly,
    testWebSocketEndpoints,
    diagnoseComfyUIWebSocketIssue,
    testProxyEndpoint,
    checkComfyUIWebSocketConfig,
    verifyCORSFix,

    // 暴露ComfyUI服务实例供调试使用
    comfyuiImageGeneration
  };
}
