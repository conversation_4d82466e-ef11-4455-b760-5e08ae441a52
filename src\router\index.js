import { createRouter, createWebHistory } from 'vue-router';
import HomePage from '@/components/HomePage.vue';
import CreationPage from '@/components/CreationPage.vue';
import ContentCreationStudio from '@/components/ContentCreationStudio.vue';

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage
  },
  {
    path: '/creation/:projectTitle/:chapterTitle',
    name: 'creation',
    component: CreationPage,
    props: true
  },
  {
    path: '/studio',
    name: 'studio',
    component: ContentCreationStudio,
    props: route => ({
      project: null,
      projectTitle: route.query.project,
      chapterTitle: route.query.chapter
    })
  }
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
});

export default router;