/**
 * 图片管理 Composable
 * 从 ContentCreationStudio.vue 中提取的图片管理相关功能
 * 
 * 功能：
 * - 图片上传处理
 * - 图片删除操作
 * - 缩略图管理
 * - 图片数据结构处理
 * - 图片文件操作
 */

import { ref } from 'vue';
import imageDeleteService from '../services/imageDeleteService.js';

export function useImageManagement() {
  // 响应式状态
  const showImageManagementModal = ref(false);
  const currentManageImageRowIndex = ref(-1);

  /**
   * 计算当前管理的图片数据
   * @param {Array} rows - 行数据数组
   * @returns {Object} 当前管理的图片数据
   */
  const getCurrentManageImageData = (rows) => {
    if (currentManageImageRowIndex.value >= 0 && 
        currentManageImageRowIndex.value < rows.length) {
      const row = rows[currentManageImageRowIndex.value];
      return {
        mainImage: {
          src: row.imageSrc,
          alt: row.imageAlt
        },
        optionalImages: row.thumbnails || [],
        isLocked: row.isLocked || false
      };
    }
    return {
      mainImage: { src: '', alt: '' },
      optionalImages: [],
      isLocked: false
    };
  };

  /**
   * 将文件保存到项目目录并返回访问路径
   * @param {File} file - 要保存的文件
   * @param {Object} projectData - 项目数据
   * @param {string} localChapterTitle - 章节标题
   * @returns {Promise<string>} 图片访问URL
   */
  const saveImageToProject = async (file, projectData, localChapterTitle) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('projectTitle', projectData.title || '1');
      formData.append('chapterTitle', localChapterTitle || '法力无边');
      formData.append('subfolder', 'UserImages'); // 用户上传的图片单独存放

      const response = await fetch('/api/local/save-generated-image', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`保存图像失败: ${response.status}`);
      }

      const result = await response.json();

      // 返回可访问的URL路径，而不是本地文件路径
      const imageUrl = `/api/local/get-image?path=${encodeURIComponent(result.path)}`;
      console.log('🎨 [图像保存] 图像已保存，访问路径:', imageUrl);

      return imageUrl;
    } catch (error) {
      console.error('🎨 [图像保存] 保存失败:', error);
      throw error;
    }
  };

  /**
   * 将文件转换为base64（备用方法）
   * @param {File} file - 要转换的文件
   * @returns {Promise<string>} base64字符串
   */
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
      reader.readAsDataURL(file);
    });
  };

  /**
   * 删除主图
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   * @returns {Promise<void>}
   */
  const deleteMainImage = async (context, index) => {
    if (index >= 0 && index < context.rows.length) {
      const row = context.rows[index];

      // 检查行是否被锁定
      if (row.isLocked) {
        context.showErrorMessage(
          '该行已锁定，无法删除图片。请先解锁该行再进行操作。',
          '删除操作被阻止'
        );
        return;
      }

      try {
        const result = await imageDeleteService.deleteMainImage(row, () => {
          // 更新项目数据
          if (context.projectData && context.projectData.data) {
            context.projectData.data.rows = context.rows;
          }
          // 保存数据
          context.debouncedSaveProject();
        });

        if (result.success) {
          context.showSuccessMessage('删除成功', result.message || '主图删除成功');
        } else {
          context.showErrorMessage('删除失败', result.message || '删除主图时发生未知错误');
        }
      } catch (error) {
        console.error('🗑️ [主图删除] 删除失败:', error);
        context.showErrorMessage('删除失败', '删除主图时发生错误: ' + (error.message || '未知错误'));
      }
    }
  };

  /**
   * 删除缩略图
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   * @param {number} thumbnailIndex - 缩略图索引
   * @returns {Promise<void>}
   */
  const deleteThumbnail = async (context, rowIndex, thumbnailIndex) => {
    if (rowIndex >= 0 && rowIndex < context.rows.length) {
      const row = context.rows[rowIndex];

      try {
        const result = await imageDeleteService.deleteThumbnail(row, thumbnailIndex, () => {
          // 更新项目数据
          if (context.projectData && context.projectData.data) {
            context.projectData.data.rows = context.rows;
          }
          // 保存数据
          context.debouncedSaveProject();
        });

        if (result.success) {
          context.showSuccessMessage('删除成功', result.message || '缩略图删除成功');
        } else {
          context.showErrorMessage('删除失败', result.message || '删除缩略图时发生未知错误');
        }
      } catch (error) {
        console.error('🗑️ [缩略图删除] 删除失败:', error);
        context.showErrorMessage('删除失败', '删除缩略图时发生错误: ' + (error.message || '未知错误'));
      }
    }
  };

  /**
   * 选择缩略图（与主图交换）
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   * @param {number} thumbnailIndex - 缩略图索引
   */
  const selectThumbnail = (context, rowIndex, thumbnailIndex) => {
    console.log('🎨 [缩略图选择] 选择缩略图:', { rowIndex, thumbnailIndex });

    if (rowIndex >= 0 && rowIndex < context.rows.length) {
      const row = context.rows[rowIndex];
      if (row.thumbnails && row.thumbnails[thumbnailIndex]) {
        // 获取当前主图信息，统一数据结构
        const currentMainImage = {
          src: row.imageSrc,
          alt: row.imageAlt,
          ismain: false  // 使用统一的 ismain 字段名
        };

        // 获取选中的缩略图
        const selectedThumbnail = { ...row.thumbnails[thumbnailIndex] };

        console.log('🎨 [缩略图选择] 交换前状态:', {
          currentMainImage: currentMainImage.src,
          selectedThumbnail: selectedThumbnail.src,
          thumbnailIndex
        });

        // 将选中的缩略图设置为新的主图
        row.imageSrc = selectedThumbnail.src;
        row.imageAlt = selectedThumbnail.alt;

        // 将原来的主图放入被选中的缩略图位置
        if (currentMainImage.src &&
            currentMainImage.src !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7') {
          // 创建新的缩略图数组，避免直接修改
          const newThumbnails = [...row.thumbnails];
          newThumbnails[thumbnailIndex] = currentMainImage;
          row.thumbnails = newThumbnails;
          console.log('🎨 [缩略图选择] 原主图已放入缩略图位置', thumbnailIndex);
        } else {
          // 如果原主图是默认图片，创建新数组移除选中项
          const newThumbnails = row.thumbnails.filter((_, index) => index !== thumbnailIndex);
          row.thumbnails = newThumbnails;
          console.log('🎨 [缩略图选择] 原主图为默认图片，移除选中缩略图');
        }

        console.log('🎨 [缩略图选择] 交换后状态:', {
          newMainImage: row.imageSrc,
          thumbnailsCount: row.thumbnails.length,
          thumbnails: row.thumbnails.map(t => t.src)
        });

        context.showSuccessMessage('图片选择', `图片交换成功`);

        // 🔧 修复：更新项目数据中的行数据，使用深拷贝避免引用问题
        if (context.projectData && context.projectData.data) {
          context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));
          console.log('🎨 [缩略图选择] 已更新项目数据中的行数据');
        }

        // 保存数据
        context.debouncedSaveProject();

        // 调用简化的验证方法
        if (context.simpleImageDataValidation) {
          context.simpleImageDataValidation();
        }
      } else {
        context.showErrorMessage('选择失败', '缩略图不存在');
      }
    }
  };

  /**
   * 打开图片管理窗口
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const openImageManagement = (context, index) => {
    // 检查行索引有效性
    if (index < 0 || index >= context.rows.length) {
      context.showErrorMessage('无效的行索引', '操作失败');
      return;
    }

    // 设置当前管理的行索引
    currentManageImageRowIndex.value = index;

    // 显示图片管理窗口
    showImageManagementModal.value = true;
  };

  /**
   * 关闭图片管理窗口
   */
  const closeImageManagement = () => {
    showImageManagementModal.value = false;
    currentManageImageRowIndex.value = -1;
  };

  /**
   * 从模态窗口上传图片
   * @param {Object} context - 组件上下文
   * @param {FileList} files - 上传的文件列表
   * @returns {Promise<void>}
   */
  const uploadImagesFromModal = async (context, files) => {
    if (currentManageImageRowIndex.value >= 0 && files && files.length > 0) {
      try {
        console.log('🎨 [ContentCreationStudio] 处理图片上传，文件数量:', files.length);

        const currentRow = context.rows[currentManageImageRowIndex.value];
        const currentThumbnails = currentRow.thumbnails || [];

        // 显示上传进度提示
        context.showInfoMessage('正在上传', `正在处理 ${files.length} 张图片...`);

        // 将文件保存到项目目录并添加到可选图列表
        const newThumbnails = [];
        for (const file of files) {
          try {
            const imageUrl = await saveImageToProject(file, context.projectData, context.localChapterTitle);
            // 使用文件路径而不是base64，统一图片数据结构
            newThumbnails.push({
              src: imageUrl,
              alt: file.name,
              ismain: false  // 添加缺失的 ismain 字段，可选图都不是主图
              // 移除多余的 name, size, type 字段
            });
            console.log('🎨 [ContentCreationStudio] 文件保存成功:', file.name, '访问路径:', imageUrl);
          } catch (error) {
            console.error('🎨 [ContentCreationStudio] 文件保存失败:', file.name, error);
          }
        }

        if (newThumbnails.length > 0) {
          // 添加到当前行的可选图列表
          const updatedThumbnails = [...currentThumbnails, ...newThumbnails];
          context.rows[currentManageImageRowIndex.value].thumbnails = updatedThumbnails;

          // 立即保存数据
          console.log('🎨 [ContentCreationStudio] 保存图片数据到项目文件');
          context.saveProjectDataImmediately();

          context.showSuccessMessage('上传成功', `成功上传 ${newThumbnails.length} 张图片到第${currentManageImageRowIndex.value + 1}行`);
        } else {
          context.showErrorMessage('上传失败', '没有图片被成功处理');
        }

      } catch (error) {
        console.error('🎨 [ContentCreationStudio] 图片上传失败:', error);
        context.showErrorMessage('图片上传失败', error.message || '未知错误');
      }
    }
  };

  /**
   * 从模态窗口替换主图
   * @param {Object} context - 组件上下文
   * @param {File} file - 新的图片文件
   * @returns {Promise<void>}
   */
  const replaceMainImageFromModal = async (context, file) => {
    if (currentManageImageRowIndex.value >= 0 && file) {
      try {
        console.log('🎨 [ContentCreationStudio] 开始替换主图，文件:', file.name, '行索引:', currentManageImageRowIndex.value);

        const imageUrl = await saveImageToProject(file, context.projectData, context.localChapterTitle);
        const currentRow = context.rows[currentManageImageRowIndex.value];

        console.log('🎨 [ContentCreationStudio] 文件保存完成，访问路径:', imageUrl);
        console.log('🎨 [ContentCreationStudio] 更新前的主图数据:', {
          imageSrc: currentRow.imageSrc,
          imageAlt: currentRow.imageAlt
        });

        // 更新主图数据，使用文件路径而不是base64
        currentRow.imageSrc = imageUrl;
        currentRow.imageAlt = file.name;

        console.log('🎨 [ContentCreationStudio] 更新后的主图数据:', {
          imageSrc: currentRow.imageSrc,
          imageAlt: currentRow.imageAlt
        });

        // 立即保存数据
        console.log('🎨 [ContentCreationStudio] 调用 saveProjectDataImmediately 保存主图数据');
        await context.saveProjectDataImmediately();
        console.log('🎨 [ContentCreationStudio] 主图数据保存完成');

        context.showSuccessMessage('主图替换成功', `已将 ${file.name} 设为第${currentManageImageRowIndex.value + 1}行的主图`);

      } catch (error) {
        console.error('🎨 [ContentCreationStudio] 主图替换失败:', error);
        context.showErrorMessage('主图替换失败', error.message || '未知错误');
      }
    } else {
      console.warn('🎨 [ContentCreationStudio] 主图替换参数无效:', {
        currentManageImageRowIndex: currentManageImageRowIndex.value,
        file: file ? file.name : null
      });
    }
  };

  /**
   * 从模态窗口替换可选图
   * @param {Object} context - 组件上下文
   * @param {Object} params - 参数对象 {file, index}
   * @returns {Promise<void>}
   */
  const replaceOptionalImageFromModal = async (context, { file, index }) => {
    if (currentManageImageRowIndex.value >= 0 && file && typeof index === 'number') {
      try {
        console.log('🎨 [ContentCreationStudio] 开始替换可选图，文件:', file.name, '位置:', index, '行索引:', currentManageImageRowIndex.value);

        const imageUrl = await saveImageToProject(file, context.projectData, context.localChapterTitle);
        const currentRow = context.rows[currentManageImageRowIndex.value];

        console.log('🎨 [ContentCreationStudio] 文件保存完成，访问路径:', imageUrl);
        console.log('🎨 [ContentCreationStudio] 更新前的可选图数组长度:', currentRow.thumbnails ? currentRow.thumbnails.length : 0);

        // 确保thumbnails数组存在
        if (!currentRow.thumbnails) {
          currentRow.thumbnails = [];
          console.log('🎨 [ContentCreationStudio] 创建新的thumbnails数组');
        }

        // 扩展数组到指定索引
        while (currentRow.thumbnails.length <= index) {
          currentRow.thumbnails.push(null);
          console.log('🎨 [ContentCreationStudio] 扩展数组到索引:', currentRow.thumbnails.length - 1);
        }

        // 替换或添加图片，使用文件路径而不是base64，统一数据结构
        const newImageData = {
          src: imageUrl,
          alt: file.name,
          ismain: false  // 添加缺失的 ismain 字段，可选图都不是主图
          // 移除多余的 name, size, type 字段
        };

        console.log('🎨 [ContentCreationStudio] 在位置', index, '添加图片数据（统一结构）:', {
          alt: newImageData.alt,
          ismain: newImageData.ismain,
          src: newImageData.src
        });

        currentRow.thumbnails[index] = newImageData;

        // 移除数组末尾的null值
        while (currentRow.thumbnails.length > 0 && !currentRow.thumbnails[currentRow.thumbnails.length - 1]) {
          currentRow.thumbnails.pop();
        }

        console.log('🎨 [ContentCreationStudio] 更新后的可选图数组长度:', currentRow.thumbnails.length);
        console.log('🎨 [ContentCreationStudio] 更新后的可选图数组:', currentRow.thumbnails.map((img, i) => ({
          index: i,
          name: img ? img.name : 'null',
          hasData: !!img
        })));

        // 立即保存数据
        console.log('🎨 [ContentCreationStudio] 调用 saveProjectDataImmediately 保存可选图数据');
        await context.saveProjectDataImmediately();
        console.log('🎨 [ContentCreationStudio] 可选图数据保存完成');

        context.showSuccessMessage('可选图更新成功', `已将 ${file.name} 添加到第${currentManageImageRowIndex.value + 1}行的位置${index + 1}`);

      } catch (error) {
        console.error('🎨 [ContentCreationStudio] 可选图替换失败:', error);
        context.showErrorMessage('可选图替换失败', error.message || '未知错误');
      }
    } else {
      console.warn('🎨 [ContentCreationStudio] 可选图替换参数无效:', {
        currentManageImageRowIndex: currentManageImageRowIndex.value,
        file: file ? file.name : null,
        index: index
      });
    }
  };

  /**
   * 处理上传错误
   * @param {Object} context - 组件上下文
   * @param {string} errorMessage - 错误消息
   */
  const handleUploadError = (context, errorMessage) => {
    context.showErrorMessage('上传错误', errorMessage);
  };

  return {
    // 响应式状态
    showImageManagementModal,
    currentManageImageRowIndex,

    // 计算属性
    getCurrentManageImageData,

    // 工具方法
    saveImageToProject,
    fileToBase64,

    // 删除操作
    deleteMainImage,
    deleteThumbnail,

    // 缩略图操作
    selectThumbnail,

    // 模态窗口管理
    openImageManagement,
    closeImageManagement,

    // 上传操作
    uploadImagesFromModal,

    // 替换操作
    replaceMainImageFromModal,
    replaceOptionalImageFromModal,

    // 错误处理
    handleUploadError
  };
}
