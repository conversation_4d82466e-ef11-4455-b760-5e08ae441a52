<template>
  <!-- 测试窗口遮罩 -->
  <div
    v-if="showProp"
    class="modal-overlay"
    @click.self="closeModal"
  >
    <div class="test-modal">
      <!-- 现代化头部 -->
      <header class="modal-header">
        <div class="header-content">
          <div class="header-title">
            <div class="title-icon">
              <i class="ri-brain-line" />
            </div>
            <div class="title-text">
              <h2>推理提示词预览</h2>
              <p class="subtitle">
                查看即将发送给LLM的提示词内容
              </p>
            </div>
          </div>
          <div class="header-actions">
            <button
              @click="closeModal"
              class="action-btn close-btn"
              title="关闭窗口"
            >
              <i class="ri-close-line" />
              <span>关闭</span>
            </button>
          </div>
        </div>
      </header>

      <!-- 主要内容区域 -->
      <main class="modal-body">
        <!-- 目标信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <i class="ri-target-line" />
            <span class="card-title">目标信息</span>
          </div>
          <div class="card-body">
            <div class="info-item">
              <span class="info-label">目标行:</span>
              <span class="info-value">第 {{ (promptData?.targetIndex || 0) + 1 }} 行</span>
            </div>
            <div class="info-item">
              <span class="info-label">目标内容:</span>
              <span class="info-value">{{ promptData?.targetText || '无内容' }}</span>
            </div>
          </div>
        </div>

        <!-- 完整提示词预览 -->
        <div class="prompt-card">
          <div class="card-header">
            <i class="ri-file-text-line" />
            <span class="card-title">完整提示词内容</span>
            <div class="card-actions">
              <button
                @click="copyPrompt"
                class="copy-btn"
                title="复制提示词"
              >
                <i class="ri-file-copy-line" />
                复制
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="prompt-preview">
              <pre>{{ fullPrompt }}</pre>
            </div>
          </div>
        </div>

        <!-- 数据统计信息 -->
        <div class="stats-card">
          <div class="card-header">
            <i class="ri-bar-chart-line" />
            <span class="card-title">数据统计</span>
          </div>
          <div class="card-body">
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">提示词长度:</span>
                <span class="stat-value">{{ fullPrompt.length }} 字符</span>
              </div>

              <div class="stat-item">
                <span class="stat-label">角色数量:</span>
                <span class="stat-value">{{ promptData?.currentData?.characters?.length || 0 }} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">场景数量:</span>
                <span class="stat-value">{{ promptData?.currentData?.scenes?.length || 0 }} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">大纲数量:</span>
                <span class="stat-value">{{ promptData?.currentData?.outlines?.length || 0 }} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">上下文行数:</span>
                <span class="stat-value">{{ getContextLinesFromPrompt }} 行</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 角色识别结果卡片 -->
        <div
          v-if="hasRecognizedCharacters"
          class="recognition-card"
        >
          <div class="card-header">
            <i class="ri-user-search-line" />
            <span class="card-title">角色识别结果</span>
          </div>
          <div class="card-body">
            <div class="recognition-content">
              <div class="recognition-summary">
                <span class="summary-text">
                  识别到 {{ recognitionResult.recognizedCharacters.length }} 个角色，
                  将自动选中 {{ recognitionResult.autoSelectedTags.length }} 个标签
                </span>
              </div>
              <div class="recognized-characters">
                <div
                  v-for="(char, index) in recognitionResult.recognizedCharacters"
                  :key="index"
                  class="character-item"
                >
                  <span class="original-text">{{ char.originalText }}</span>
                  <i class="ri-arrow-right-line" />
                  <span class="character-name">{{ char.characterName }}</span>
                  <span
                    class="match-type"
                    :class="`match-${char.matchType}`"
                  >
                    {{ char.matchType === 'exact' ? '精确匹配' : '占位符替换' }}
                  </span>
                </div>
              </div>
              <div
                v-if="recognitionResult.hasPlaceholders"
                class="placeholder-info"
              >
                <i class="ri-information-line" />
                <span>检测到占位符，将在发送时自动替换为角色名称</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- 底部操作按钮 -->
      <footer class="modal-footer">
        <div class="footer-actions">
          <button
            @click="copyPrompt"
            class="action-button secondary"
          >
            <i class="ri-file-copy-line" />
            复制提示词
          </button>
          <button
            @click="closeModal"
            class="action-button primary"
          >
            <i class="ri-check-line" />
            知道了
          </button>
        </div>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import { useCharacterRecognition } from '../composables/useCharacterRecognition.js';

// Props
const props = defineProps({
  show: Boolean,
  promptData: Object,
  fullPrompt: String,
  isLoading: {
    type: Boolean,
    default: false
  },
  availableCharacters: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:show', 'close', 'apply-character-tags']);

// 角色识别功能
const {
  recognitionResult,
  hasRecognizedCharacters,
  recognizeCharactersInPrompt,
  resetRecognitionState
} = useCharacterRecognition();

// 本地状态
const showProp = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// 监听用户输入的提示词变化，只在包含 {character} 占位符时进行角色识别
watch(() => props.promptData?.prompt, (newPrompt) => {
  console.log('[ImagePromptTestModal] 提示词变化监听触发:', {
    newPrompt: newPrompt ? newPrompt.substring(0, 100) + '...' : null,
    promptLength: newPrompt ? newPrompt.length : 0,
    availableCharacters: props.availableCharacters,
    availableCharactersCount: props.availableCharacters ? props.availableCharacters.length : 0
  });

  // 只有当提示词包含 {character} 或 {Character} 占位符时才进行角色识别
  const hasCharacterPlaceholder = newPrompt && (newPrompt.includes('{character}') || newPrompt.includes('{Character}'));
  if (hasCharacterPlaceholder && props.availableCharacters && props.availableCharacters.length > 0) {
    console.log('[ImagePromptTestModal] 开始角色识别:', {
      promptLength: newPrompt.length,
      availableCharactersCount: props.availableCharacters.length
    });

    // 执行角色识别
    const result = recognizeCharactersInPrompt(newPrompt, props.availableCharacters);

    // 如果识别到角色，立即应用标签选择
    if (result && result.autoSelectedTags && result.autoSelectedTags.length > 0) {
      console.log('[ImagePromptTestModal] 立即应用角色标签:', result.autoSelectedTags);

      // 发送标签应用事件到父组件
      emit('apply-character-tags', {
        rowIndex: props.promptData?.targetIndex,
        autoSelectedTags: result.autoSelectedTags,
        replacedPrompt: result.replacedPrompt
      });
    }
  } else {
    console.log('[ImagePromptTestModal] 跳过角色识别:', {
      hasPrompt: !!newPrompt,
      hasCharacterPlaceholder: hasCharacterPlaceholder,
      hasAvailableCharacters: !!(props.availableCharacters && props.availableCharacters.length > 0)
    });
    // 重置识别状态
    resetRecognitionState();
  }
}, { immediate: true });

// 监听模态框显示状态，重置识别状态
watch(() => props.show, (isShowing) => {
  if (!isShowing) {
    resetRecognitionState();
  }
});

// 关闭窗口
function closeModal() {
  resetRecognitionState();
  emit('close');
}

// 复制提示词
async function copyPrompt() {
  try {
    await navigator.clipboard.writeText(props.fullPrompt);
    console.log('提示词已复制到剪贴板');
    // 这里可以添加一个简单的提示
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案：选择文本
    const textArea = document.createElement('textarea');
    textArea.value = props.fullPrompt;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }
}

// 从提示词中提取上下文行数
const getContextLinesFromPrompt = computed(() => {
  try {
    if (!props || !props.fullPrompt || !props.promptData) {
      return '未知';
    }

    // 从 promptData 中获取上下文信息
    const context = props.promptData.context || '';
    if (!context) {
      return '0';
    }

    // 计算上下文中的行数（通过换行符分割）
    const lines = context.split('\n').filter(line => line.trim() !== '');
    return lines.length.toString();
  } catch (error) {
    console.error('计算上下文行数时出错:', error);
    return '未知';
  }
});
</script>

<style scoped>
/* 遮罩层 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

/* 主窗口 */
.test-modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 16px;
  box-shadow: 0 24px 48px rgba(0, 0, 0, 0.4);
  width: 90vw;
  max-width: 900px;
  max-height: 90vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #333;
}

/* 头部样式 */
.modal-header {
  background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  border-bottom: 1px solid #333;
  padding: 20px 24px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-actions {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 12px;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3e8fb0 0%, #2a6b7a 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.title-text h2 {
  margin: 0;
  color: #e0e0e0;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 4px 0 0 0;
  color: #999;
  font-size: 14px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #444;
  border-radius: 8px;
  background: #2a2a2a;
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.action-btn:hover {
  background: #3a3a3a;
  border-color: #555;
}

.close-btn:hover {
  background: #dc3545;
  border-color: #dc3545;
}



/* 主体内容 */
.modal-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 卡片样式 */
.info-card,
.prompt-card,
.stats-card,
.recognition-card {
  background: #222;
  border: 1px solid #333;
  border-radius: 12px;
  overflow: visible;
  flex-shrink: 0;
}

/* 确保统计卡片有足够空间 */
.stats-card {
  min-height: 280px;
  overflow: visible;
}

.card-header {
  background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
  padding: 18px 24px;
  border-bottom: 1px solid #333;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
}

/* 目标信息卡片头部居中样式 */
.info-card .card-header {
  justify-content: center;
  position: relative;
}

.info-card .card-header i {
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

/* 统计卡片头部居中样式 */
.stats-card .card-header {
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  border-bottom: 1px solid #3e8fb0;
  justify-content: center;
  position: relative;
}

.stats-card .card-header i {
  color: #3e8fb0;
  font-size: 20px;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.stats-card .card-title {
  color: #e0e0e0;
  font-weight: 700;
  font-size: 17px;
}

.card-title {
  color: #e0e0e0;
  font-weight: 600;
  font-size: 16px;
}

.card-header i {
  color: #3e8fb0;
  font-size: 18px;
}

.card-body {
  padding: 24px;
}

/* 统计卡片特殊样式 */
.stats-card .card-body {
  padding: 20px;
  background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%);
  overflow: visible;
  min-height: 200px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #999;
  min-width: 80px;
  font-weight: 500;
}

.info-value {
  color: #e0e0e0;
  flex: 1;
  word-break: break-word;
}

/* 提示词预览 */
.prompt-preview {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.prompt-preview pre {
  margin: 0;
  color: #e0e0e0;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 复制按钮 */
.copy-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #444;
  border-radius: 6px;
  background: #333;
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.copy-btn:hover {
  background: #3e8fb0;
  border-color: #3e8fb0;
}

/* 统计网格 - 简化布局，确保所有项目可见 */
.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  width: 100%;
}

/* 响应式布局调整 - 优先保证可见性 */
@media (min-width: 768px) {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
  }
}

@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
  border: 1px solid #333;
  border-radius: 8px;
  transition: all 0.2s ease;
  min-height: 48px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: visible;
  flex-shrink: 0;
}

.stat-item:hover {
  background: linear-gradient(135deg, #252525 0%, #2a2a2a 100%);
  border-color: #3e8fb0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(62, 143, 176, 0.15);
}

.stat-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #3e8fb0 0%, #2a6b7a 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.stat-item:hover::before {
  opacity: 1;
}

.stat-label {
  color: #b0b0b0;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.3;
  margin-right: 10px;
  flex-shrink: 0;
  white-space: nowrap;
}

.stat-value {
  color: #3e8fb0;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  text-align: right;
  flex-shrink: 0;
  min-width: fit-content;
}

/* 角色识别卡片样式 */
.recognition-card {
  border-color: #4a9eff;
  background: linear-gradient(135deg, #1a1f2e 0%, #252a3a 100%);
}

.recognition-card .card-header {
  background: linear-gradient(135deg, #2a3441 0%, #1e2530 100%);
  border-bottom: 1px solid #4a9eff;
  justify-content: center;
  position: relative;
}

.recognition-card .card-header i {
  color: #4a9eff;
  font-size: 20px;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.recognition-card .card-title {
  color: #e0e0e0;
  font-weight: 700;
  font-size: 17px;
}

.recognition-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recognition-summary {
  padding: 12px 16px;
  background: linear-gradient(135deg, #1e2530 0%, #2a3441 100%);
  border: 1px solid #4a9eff;
  border-radius: 8px;
}

.summary-text {
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
}

.recognized-characters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 14px;
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
  border: 1px solid #333;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.character-item:hover {
  border-color: #4a9eff;
  background: linear-gradient(135deg, #252525 0%, #2a2a2a 100%);
}

.original-text {
  color: #999;
  font-size: 13px;
  font-family: monospace;
  background: #1a1a1a;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #333;
}

.character-item i {
  color: #4a9eff;
  font-size: 14px;
}

.character-name {
  color: #4a9eff;
  font-weight: 600;
  font-size: 14px;
}

.match-type {
  margin-left: auto;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.match-exact {
  background: #28a745;
  color: white;
}

.match-placeholder {
  background: #ffc107;
  color: #000;
}

.placeholder-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  background: linear-gradient(135deg, #2a2a1a 0%, #3a3a2a 100%);
  border: 1px solid #ffc107;
  border-radius: 6px;
  color: #ffc107;
  font-size: 13px;
}

.placeholder-info i {
  font-size: 16px;
}

/* 底部操作区 */
.modal-footer {
  background: #1e1e1e;
  border-top: 1px solid #333;
  padding: 20px 24px;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-button.secondary {
  background: #444;
  color: #e0e0e0;
}

.action-button.secondary:hover:not(:disabled) {
  background: #555;
}

.action-button.primary {
  background: linear-gradient(135deg, #3e8fb0 0%, #2a6b7a 100%);
  color: white;
}

.action-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #4a9bc0 0%, #356b7a 100%);
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar,
.prompt-preview::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track,
.prompt-preview::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb,
.prompt-preview::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.prompt-preview::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
