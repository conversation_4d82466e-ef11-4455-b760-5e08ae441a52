<template>
  <BaseDrawer
    :show="show"
    @update:show="val => $emit('update:show', val)"
    :drawer-width="'50vw'"
  >
    <div class="adjust-shots-drawer">
      <div class="drawer-header-row">
        <h2 class="drawer-title">
          调整合并字幕
        </h2>
        <div class="drawer-description">
          点击字幕左键合并，右键分拆。完成后点击"确认合并"。
        </div>
        <div class="action-buttons">
          <button
            class="action-button"
            @click="importGroups"
            title="导入外部修改好的分组文本"
          >
            <i class="ri-file-text-line" /> 导入分组
          </button>
          <button
            class="action-button"
            @click="exportText"
            title="导出当前所有文本到剪贴板"
          >
            <i class="ri-clipboard-line" /> 导出文本
          </button>
          <button
            class="action-button"
            @click="cancelAllMergedGroups"
            title="取消所有合并分组"
          >
            <i class="ri-delete-bin-line" /> 取消分组
          </button>
          <button
            class="action-button ai-button"
            @click="showAIGrouping = true"
            title="使用AI智能分组字幕"
          >
            <i class="ri-robot-line" /> AI分组
          </button>
        </div>
        <div class="status-bar">
          <div
            class="status-text"
            v-if="mergedGroups.length > 0"
          >
            已合并 {{ mergedGroups.length }} 组，当前显示 {{ subtitleItems.length }} 行
          </div>
          <div
            class="status-text"
            v-else
          >
            尚未进行任何合并
          </div>
        </div>
      </div>
      <div class="subtitle-list">
        <div class="subtitle-list-header">
          <div class="column-header">
            字幕
          </div>
        </div>
        <div class="subtitle-items-container">
          <div class="subtitle-items">
            <div
              v-for="(item, index) in subtitleItems"
              :key="item.id"
              :class="['subtitle-item', { 'merged': item.isMerged }]"
              @click="handleClick(index)"
              @contextmenu.prevent="handleRightClick(index)"
            >
              <div class="item-serial">
                {{ item.id }}
              </div>
              <div
                class="item-content"
                :title="item.content"
              >
                {{ item.content }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="drawer-footer">
        <div class="operation-buttons">
          <button
            class="operation-button cancel"
            @click="$emit('update:show', false)"
          >
            取消
          </button>
          <button
            class="operation-button confirm"
            @click="onConfirmMerge"
          >
            确认合并
          </button>
        </div>
      </div>
    </div>
  </BaseDrawer>

  <!-- AI分组组件，放在BaseDrawer外部渲染 -->
  <Teleport to="body">
    <AIGroupingModal
      v-if="showAIGrouping"
      :show="showAIGrouping"
      :subtitles="subtitleItems"
      @update:show="showAIGrouping = $event"
      @apply-grouping="applyAIGrouping"
    />
  </Teleport>

  <!-- 验证错误弹窗 -->
  <Teleport to="body">
    <ValidationErrorModal
      v-if="showValidationError"
      :show="showValidationError"
      :error-type="validationErrorType"
      :error-title="validationErrorTitle"
      :error-message="validationErrorMessage"
      :validation-details="validationErrorDetails"
      @update:show="showValidationError = $event"
    />
  </Teleport>

  <!-- 成功消息动画 -->
  <Teleport to="body">
    <div
      v-if="showSuccessMessage"
      class="success-message-overlay"
    >
      <div class="success-message">
        <i class="ri-check-line success-icon" />
        <span>{{ successMessageText }}</span>
      </div>
    </div>
  </Teleport>
</template>

<script>
import BaseDrawer from './BaseDrawer.vue';
import AIGroupingModal from './AIGroupingModal.vue';
import ValidationErrorModal from './ValidationErrorModal.vue';
import { ref, watch, onMounted } from 'vue';
import { useGroupingOperations } from '../composables/useGroupingOperations';
import { useStudioActions } from '../composables/useStudioActions';

export default {
  name: 'AdjustShotsDrawer',

  components: {
    BaseDrawer,
    AIGroupingModal,
    ValidationErrorModal
  },

  props: {
    show: {
      type: Boolean,
      default: false
    },
    rows: {
      type: Array,
      default: () => []
    }
  },

  emits: ['update:show', 'update-rows'],

  setup(props, { emit }) {
    // 状态
    const subtitleItems = ref([]);
    const mergedGroups = ref([]);
    const rawSrtRows = ref([]);
    const showAIGrouping = ref(false);

    // 验证错误弹窗状态
    const showValidationError = ref(false);
    const validationErrorType = ref('general');
    const validationErrorTitle = ref('');
    const validationErrorMessage = ref('');
    const validationErrorDetails = ref(null);

    // 成功消息状态
    const showSuccessMessage = ref(false);
    const successMessageText = ref('');

    // 使用分组操作
    const groupingOperations = useGroupingOperations();

    // 使用统一的 Studio Actions（与 ContentCreationStudio 相同的合并/分拆逻辑）
    const studioActions = useStudioActions();

    // 当前的行数据（与 ContentCreationStudio 格式一致）
    const currentRows = ref([]);

    /**
     * 显示验证错误弹窗
     */
    function showValidationErrorModal(type, title, message, details = null) {
      validationErrorType.value = type;
      validationErrorTitle.value = title;
      validationErrorMessage.value = message;
      validationErrorDetails.value = details;
      showValidationError.value = true;
    }

    /**
     * 显示成功消息动画
     */
    function showSuccessMessageAnimation(message) {
      successMessageText.value = message;
      showSuccessMessage.value = true;

      // 3秒后自动隐藏
      setTimeout(() => {
        showSuccessMessage.value = false;
      }, 3000);
    }

    // 监听抽屉显示变化
    watch(() => props.show, (newVal) => {
      if (newVal) {
        initializeItems();
      }
    });

    // 初始化时检查是否显示
    onMounted(() => {
      if (props.show) {
        initializeItems();
      }
    });

    /**
     * 初始化字幕项 - 使用统一的行格式
     */
    async function initializeItems() {
      if (!props.rows || props.rows.length === 0) {
        currentRows.value = [];
        subtitleItems.value = [];
        mergedGroups.value = [];
        rawSrtRows.value = [];
        return;
      }

      // 直接使用 ContentCreationStudio 的行格式，深拷贝以避免引用问题
      currentRows.value = JSON.parse(JSON.stringify(props.rows));

      // 保存原始未合并数据到 localStorage（如果还没有的话）
      const hasOriginalData = loadFromLocalStorage();
      if (!hasOriginalData || hasOriginalData.length === 0) {
        const originalData = extractUnmergedRows(currentRows.value);
        if (originalData && originalData.length > 0) {
          saveToLocalStorage(originalData);
          rawSrtRows.value = originalData;
        }
      } else {
        rawSrtRows.value = hasOriginalData;
      }

      // 为了兼容现有的 UI，将行数据转换为 subtitleItems 格式用于显示
      subtitleItems.value = currentRows.value.map((row, index) => ({
        id: index + 1,
        content: row.description || '',
        isMerged: row.isMerged || false,
        originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
        rowIndex: row.index !== undefined ? row.index : index + 1,
        originalRow: JSON.parse(JSON.stringify(row)), // 深拷贝，避免引用问题
        startTime: row.startTime,
        endTime: row.endTime,
        duration: row.duration
      }));

      // 收集合并组信息
      collectMergedGroups();
    }

    /**
     * 保存原始SRT数据到localStorage
     */
    function saveToLocalStorage(data) {
      try {
        // 获取项目和章节信息
        const urlParams = new URLSearchParams(window.location.search);
        const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
        const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

        if (!projectTitle || !chapterTitle || !data || data.length === 0) {
          return false;
        }

        // 保存原始SRT数据
        const key = `original_srt_data_${projectTitle}_${chapterTitle}`;
        localStorage.setItem(key, JSON.stringify(data));
        return true;
      } catch (error) {
        console.error('保存原始SRT数据到localStorage失败:', error);
        return false;
      }
    }

    /**
     * 从可能包含合并项的数据中提取所有未合并状态的行
     * 完全重写版本，更加可靠地处理各种情况
     */
    function extractUnmergedRows(rows) {
      // 如果没有输入行，返回空数组
      if (!rows || rows.length === 0) return [];

      // 创建一个映射来存储所有行，按originalIndex索引
      const allRowsMap = new Map();

      // 第一步：收集所有行，包括合并行中的子行
      function collectAllRows(row, isTopLevel = false) {
        // 跳过无效行
        if (!row) return;

        // 🔥 修复：确保行有正确的 originalIndex
        if (row.originalIndex === undefined) {
          // 如果没有 originalIndex，使用 index（如果存在）或者设为 0
          row.originalIndex = row.index !== undefined ? row.index : 0;
        }

        // 🔥 修复：创建基本行对象（始终设置为未合并状态），确保图像字段正确处理
        const selectedTags = Array.isArray(row.selectedTags) ? [...row.selectedTags] : [];
        const thumbnails = Array.isArray(row.thumbnails) ? [...row.thumbnails] : [];
        const generatedImages = Array.isArray(row.generatedImages) ? [...row.generatedImages] : [];

        // 🔥 统一数据结构：创建与 ContentCreationStudio.vue 完全一致的行对象
        const baseRow = {
          description: row.description || '',
          originalIndex: row.originalIndex,
          index: row.originalIndex, // 🔥 修复：暂时使用 originalIndex，稍后会重新编号
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration,
          isMerged: false, // 重要：始终设置为未合并状态
          isSelected: false,
          // 🔥 统一数据结构：包含所有 ContentCreationStudio.vue 使用的字段
          selectedTags,
          keywords: row.keywords || '',
          keywordsMetadata: { ...(row.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
          // 🔥 修复：确保图像相关字段被正确复制
          imageSrc: row.imageSrc || '',
          imageAlt: row.imageAlt || '',
          isImageLocked: row.isImageLocked || false,
          thumbnails,
          generatedImages,
          tags: row.tags || []
        };

        // 🔥 统一数据结构：确保没有多余字段
        delete baseRow.id;
        delete baseRow.rowIndex;
        delete baseRow.originalState;

        // 如果是顶级行或尚未收集此originalIndex，则添加到映射
        if (isTopLevel || !allRowsMap.has(baseRow.originalIndex)) {
          allRowsMap.set(baseRow.originalIndex, baseRow);
        }

        // 🔥 统一数据结构：如果是合并行，处理其子行
        if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
          // 处理所有子行
          row.mergedRows.forEach(subRow => {
            collectAllRows(subRow);
          });
        } else if (!row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
          // 🔥 统一数据结构：对于未合并行，mergedRows[0] 就是行本身的数据
          // 不需要额外处理，因为行本身已经被处理了
        }
      }

      // 处理所有顶级行
      rows.forEach(row => {
        collectAllRows(row, true);
      });

      // 第二步：从映射中提取所有行并排序
      const result = Array.from(allRowsMap.values());

      // 按startTime排序，确保顺序与原始SRT文件一致
      result.sort((a, b) => {
        // 首先按时间排序（如果有）
        if (a.startTime !== undefined && b.startTime !== undefined) {
          return a.startTime - b.startTime;
        }
        // 然后按originalIndex排序
        return a.originalIndex - b.originalIndex;
      });

      // 重新编号
      result.forEach((row, index) => {
        row.index = index + 1;
      });

      return result;
    }

    /**
     * 收集合并组信息
     */
    function collectMergedGroups() {
      mergedGroups.value = [];

      subtitleItems.value.forEach((item) => {
        if (item.isMerged && item.originalRow &&
            Array.isArray(item.originalRow.mergedRows) &&
            item.originalRow.mergedRows.length > 0) {

          // 收集此行及其合并的行的originalIndex
          const group = [item.originalIndex].concat(
            item.originalRow.mergedRows.map(subRow => subRow.originalIndex)
          );

          mergedGroups.value.push(group);
        }
      });
    }



    /**
     * 处理点击事件（合并）- 使用统一的合并逻辑
     */
    function handleClick(index) {
      // 检查是否是第一行（第一行没有上一行可合并）
      if (index === 0) {
        return;
      }

      console.log('[HandleClick] 开始合并操作，索引:', index);

      // 使用统一的 mergeUp 方法（与 ContentCreationStudio 相同）
      const result = studioActions.mergeUp(index, currentRows.value);

      if (result.success) {
        // 更新当前行数据
        currentRows.value = result.rows;

        // 🔥 修复：确保所有行的索引正确重新编号
        currentRows.value.forEach((row, idx) => {
          row.index = idx + 1;
        });

        console.log('[HandleClick] 合并成功，重新编号后的行数据:', currentRows.value);

        // 同步更新 subtitleItems 用于显示
        syncSubtitleItemsFromRows();

        // 重新收集合并组信息
        collectMergedGroups();
      } else {
        console.error('AdjustShotsDrawer - 合并失败:', result.error);
      }
    }

    /**
     * 处理右键点击事件（分拆）- 使用统一的分拆逻辑
     */
    function handleRightClick(index) {
      // 检查是否是合并项
      if (!currentRows.value[index] || !currentRows.value[index].isMerged) {
        return;
      }

      console.log('[HandleRightClick] 开始分拆操作，索引:', index);

      // 使用统一的 splitDown 方法（与 ContentCreationStudio 相同）
      const result = studioActions.splitDown(index, currentRows.value);

      if (result.success) {
        // 更新当前行数据
        currentRows.value = result.rows;

        // 🔥 修复：确保所有行的索引正确重新编号
        currentRows.value.forEach((row, idx) => {
          row.index = idx + 1;
        });

        console.log('[HandleRightClick] 分拆成功，重新编号后的行数据:', currentRows.value);

        // 同步更新 subtitleItems 用于显示
        syncSubtitleItemsFromRows();

        // 重新收集合并组信息
        collectMergedGroups();
      } else {
        console.error('AdjustShotsDrawer - 分拆失败:', result.error);
      }
    }

    /**
     * 从 currentRows 同步更新 subtitleItems（用于显示）
     * 🔥 统一数据结构：确保与 ContentCreationStudio.vue 格式完全一致
     */
    function syncSubtitleItemsFromRows() {
      subtitleItems.value = currentRows.value.map((row, index) => {
        // 🔥 统一数据结构：构建与 ContentCreationStudio.vue 完全一致的 originalRow 结构
        const originalRow = {
          description: row.description || '',
          isMerged: row.isMerged || false,
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration,
          originalIndex: row.originalIndex,
          index: row.index,
          // 🔥 统一数据结构：包含所有 ContentCreationStudio.vue 使用的字段
          selectedTags: [...(row.selectedTags || [])],
          keywords: row.keywords || '',
          keywordsMetadata: { ...(row.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
          imageSrc: row.imageSrc || '',
          imageAlt: row.imageAlt || '',
          isImageLocked: row.isImageLocked || false,
          thumbnails: Array.isArray(row.thumbnails) ? [...row.thumbnails] : [],
          generatedImages: Array.isArray(row.generatedImages) ? [...row.generatedImages] : [],
          tags: row.tags || [],
          isSelected: row.isSelected || false
        };

        // 🔥 统一数据结构：添加 mergedRows（所有行都有）
        if (row.mergedRows && Array.isArray(row.mergedRows)) {
          originalRow.mergedRows = row.mergedRows.map(mergedRow => {
            // 🔥 修复：确保图像字段被正确复制到 mergedRows 中
            const cleanMergedRow = {
              index: mergedRow.index,
              originalIndex: mergedRow.originalIndex,
              description: mergedRow.description || '',
              startTime: mergedRow.startTime,
              endTime: mergedRow.endTime,
              duration: mergedRow.duration,
              selectedTags: [...(mergedRow.selectedTags || [])],
              keywords: mergedRow.keywords || '',
              keywordsMetadata: { ...(mergedRow.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
              // 🔥 修复：确保图像相关字段被正确复制
              imageSrc: mergedRow.imageSrc || '',
              imageAlt: mergedRow.imageAlt || '',
              isImageLocked: mergedRow.isImageLocked || false,
              thumbnails: Array.isArray(mergedRow.thumbnails) ? [...mergedRow.thumbnails] : [],
              generatedImages: Array.isArray(mergedRow.generatedImages) ? [...mergedRow.generatedImages] : [],
              tags: mergedRow.tags || [],
              isSelected: mergedRow.isSelected || false
            };

            // 🔥 统一数据结构：确保没有多余字段（如 id, rowIndex, originalState 等）
            delete cleanMergedRow.id;
            delete cleanMergedRow.rowIndex;
            delete cleanMergedRow.originalState;

            return cleanMergedRow;
          });
        } else {
          // 🔥 统一数据结构：如果没有 mergedRows，创建一个包含行本身数据的数组
          originalRow.mergedRows = [{
            index: row.index,
            originalIndex: row.originalIndex,
            description: row.description || '',
            startTime: row.startTime,
            endTime: row.endTime,
            duration: row.duration,
            selectedTags: [...(row.selectedTags || [])],
            keywords: row.keywords || '',
            keywordsMetadata: { ...(row.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
            // 🔥 修复：确保图像相关字段被正确复制
            imageSrc: row.imageSrc || '',
            imageAlt: row.imageAlt || '',
            isImageLocked: row.isImageLocked || false,
            thumbnails: Array.isArray(row.thumbnails) ? [...row.thumbnails] : [],
            generatedImages: Array.isArray(row.generatedImages) ? [...row.generatedImages] : [],
            tags: row.tags || [],
            isSelected: row.isSelected || false
          }];
        }

        // 🔥 修复：返回包含正确 id 字段的格式，用于界面显示序号
        return {
          id: index + 1, // 🔥 修复：确保 id 字段存在，用于界面显示序号
          content: row.description || '',
          isMerged: row.isMerged || false,
          originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
          originalRow: originalRow,
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration
        };
      });
    }

    /**
     * 取消所有合并分组 - 使用 extractUnmergedRows 正确恢复原始单行格式
     */
    async function cancelAllMergedGroups() {
      // 从 localStorage 获取原始数据
      const originalData = loadFromLocalStorage();
      if (!originalData || originalData.length === 0) {
        console.error('无法获取原始数据');
        alert('无法取消分组：找不到原始未合并数据。请尝试重新打开抽屉或刷新页面。');
        return;
      }

      console.log('[CancelAllMergedGroups] 开始取消分组，原始数据:', originalData);

      // 🔥 关键修复：使用 extractUnmergedRows 函数正确提取所有未合并的行
      // 这会将所有合并项完全拆分为独立的单行，并正确设置索引
      const unmergedRows = extractUnmergedRows(originalData);

      console.log('[CancelAllMergedGroups] 提取的未合并行:', unmergedRows);

      // 🔥 统一数据结构：确保每行都有正确的 mergedRows（包含自身数据）
      currentRows.value = unmergedRows.map((row, index) => {
        // 确保索引正确
        row.index = index + 1;
        row.isMerged = false;
        row.isSelected = false;

        // 🔥 统一数据结构：为每个未合并行创建包含自身数据的 mergedRows
        row.mergedRows = [{
          index: index + 1,
          originalIndex: row.originalIndex,
          description: row.description || '',
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration,
          selectedTags: [...(row.selectedTags || [])],
          keywords: row.keywords || '',
          keywordsMetadata: { ...(row.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
          imageSrc: row.imageSrc || '',
          imageAlt: row.imageAlt || '',
          isImageLocked: row.isImageLocked || false,
          thumbnails: Array.isArray(row.thumbnails) ? [...row.thumbnails] : [],
          generatedImages: Array.isArray(row.generatedImages) ? [...row.generatedImages] : [],
          tags: row.tags || [],
          isSelected: false
        }];

        return row;
      });

      console.log('[CancelAllMergedGroups] 最终恢复的行数据:', currentRows.value);

      // 同步更新 subtitleItems
      syncSubtitleItemsFromRows();

      // 重新收集合并组信息（应该为空，因为所有行都已取消合并）
      collectMergedGroups();

      console.log('[CancelAllMergedGroups] 取消分组完成，当前行数:', currentRows.value.length);
    }

    /**
     * 从localStorage加载原始SRT数据
     * @returns {Array|null} 原始SRT数据或null
     */
    function loadFromLocalStorage() {
      try {
        // 获取项目和章节信息
        const urlParams = new URLSearchParams(window.location.search);
        const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
        const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

        if (!projectTitle || !chapterTitle) {
          console.error('无法获取项目或章节信息');
          return null;
        }

        // 构建localStorage键
        const key = `original_srt_data_${projectTitle}_${chapterTitle}`;

        // 从localStorage获取数据
        const dataStr = localStorage.getItem(key);
        if (!dataStr) {
          console.warn('localStorage中没有找到原始SRT数据');
          return null;
        }

        // 解析数据
        const data = JSON.parse(dataStr);
        if (!Array.isArray(data) || data.length === 0) {
          console.warn('localStorage中的原始SRT数据无效');
          return null;
        }

        // 🔥 统一数据结构：清理加载的数据中的所有不一致字段
        const cleanedData = data.map(row => {
          const cleanRow = { ...row };

          // 🔥 统一数据结构：移除所有可能存在的多余字段
          delete cleanRow.id;
          delete cleanRow.rowIndex;
          delete cleanRow.originalState;

          // 移除 mergedRows 中的多余字段
          if (cleanRow.mergedRows && Array.isArray(cleanRow.mergedRows)) {
            cleanRow.mergedRows = cleanRow.mergedRows.map(subRow => {
              const cleanSubRow = { ...subRow };

              // 移除多余字段
              delete cleanSubRow.id;
              delete cleanSubRow.rowIndex;
              delete cleanSubRow.originalState;

              return cleanSubRow;
            });
          }

          // 🔥 统一数据结构：确保包含所有必要字段
          cleanRow.isSelected = cleanRow.isSelected || false;
          cleanRow.selectedTags = cleanRow.selectedTags || [];
          cleanRow.keywordsMetadata = cleanRow.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 };
          cleanRow.imageSrc = cleanRow.imageSrc || '';
          cleanRow.imageAlt = cleanRow.imageAlt || '';
          cleanRow.isImageLocked = cleanRow.isImageLocked || false;
          cleanRow.thumbnails = Array.isArray(cleanRow.thumbnails) ? cleanRow.thumbnails : [];
          cleanRow.generatedImages = Array.isArray(cleanRow.generatedImages) ? cleanRow.generatedImages : [];
          cleanRow.tags = cleanRow.tags || [];

          return cleanRow;
        });

        return cleanedData;
      } catch (error) {
        console.error('从localStorage加载原始SRT数据失败:', error);
        return null;
      }
    }

    /**
     * 导出文本
     */
    async function exportText() {
      if (!subtitleItems.value || subtitleItems.value.length === 0) {
        alert('没有可用的字幕内容可导出');
        return;
      }

      try {
        const result = await groupingOperations.exportSubtitleText(subtitleItems.value);

        if (result.success) {
          alert(result.message);
        } else {
          console.error('导出文本失败:', result.error);
          alert(`导出文本失败: ${result.error}`);
        }
      } catch (error) {
        console.error('导出文本过程中出错:', error);
        alert(`导出文本过程中出错: ${error.message || '未知错误'}`);
      }
    }

    /**
     * 导入分组
     */
    function importGroups() {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.txt';
      input.onchange = handleFileSelection;
      input.click();
    }

    /**
     * 处理文件选择 - 使用统一的数据格式
     */
    function handleFileSelection(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;

        // 使用原始数据进行导入分组
        const result = groupingOperations.importGroupingByOrder(content, rawSrtRows.value);

        if (result.success) {
          // 将导入结果转换为 currentRows 格式
          currentRows.value = convertImportResultToRows(result.rows);

          // 🔥 修复：确保所有行的索引正确重新编号
          currentRows.value.forEach((row, idx) => {
            row.index = idx + 1;
          });

          console.log('[HandleFileSelection] 导入成功，重新编号后的行数据:', currentRows.value);

          // 同步更新 subtitleItems 用于显示
          syncSubtitleItemsFromRows();

          // 重新收集合并组信息
          collectMergedGroups();

          showSuccessMessageAnimation(result.message || '导入分组成功！');
        } else {
          console.error('AdjustShotsDrawer - 导入分组失败:', result.error);
          showValidationErrorModal(
            'import',
            '导入分组失败',
            result.error || '未知错误',
            result.validationDetails // 传递详细校验信息
          );
        }
      };
      reader.readAsText(file, 'utf-8');
    }

    /**
     * 将导入分组结果转换为统一的行格式
     */
    function convertImportResultToRows(importedRows) {
      if (!importedRows || !Array.isArray(importedRows)) {
        console.error('导入结果格式无效');
        return [];
      }

      return importedRows.map((item, index) => {
        // 🔥 统一数据结构：清理导入数据中的 originalState
        const cleanItem = { ...item };
        if (cleanItem.originalState) {
          console.log(`[ConvertImportResult] 清理导入数据中的originalState，行[${cleanItem.originalIndex || index}]`);
          delete cleanItem.originalState;
        }

        // 🔥 统一数据结构：创建与 ContentCreationStudio.vue 完全一致的基本行对象
        const row = {
          index: index + 1,
          originalIndex: cleanItem.originalIndex !== undefined ? cleanItem.originalIndex : index,
          description: cleanItem.content || cleanItem.description || '',
          startTime: cleanItem.startTime,
          endTime: cleanItem.endTime,
          duration: cleanItem.duration,
          isMerged: cleanItem.isMerged || false,
          isSelected: false,
          // 🔥 统一数据结构：包含所有 ContentCreationStudio.vue 使用的字段
          selectedTags: [...(cleanItem.selectedTags || [])],
          keywords: cleanItem.keywords || '',
          keywordsMetadata: { ...(cleanItem.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
          imageSrc: cleanItem.imageSrc || 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
          imageAlt: cleanItem.imageAlt || '',
          isImageLocked: cleanItem.isImageLocked || false,
          thumbnails: Array.isArray(cleanItem.thumbnails) ? [...cleanItem.thumbnails] : [],
          generatedImages: Array.isArray(cleanItem.generatedImages) ? [...cleanItem.generatedImages] : [],
          tags: cleanItem.tags || [],
          // 🔥 统一数据结构：所有行都有 mergedRows
          mergedRows: []
        };

        // 🔥 统一数据结构：确保没有多余字段
        delete row.id;
        delete row.rowIndex;
        delete row.originalState;

        // 🔥 统一数据结构：构建 mergedRows
        if (cleanItem.isMerged && cleanItem.mergedRows && Array.isArray(cleanItem.mergedRows)) {
          // 如果是合并项，使用提供的 mergedRows（清理 originalState）
          row.mergedRows = cleanItem.mergedRows.map(mergedRow => {
            const cleanMergedRow = { ...mergedRow };
            if (cleanMergedRow.originalState) {
              console.log(`[ConvertImportResult] 清理mergedRows中的originalState，子行[${cleanMergedRow.originalIndex || cleanMergedRow.index}]`);
              delete cleanMergedRow.originalState;
            }
            return {
            index: cleanMergedRow.index,
            originalIndex: cleanMergedRow.originalIndex,
            description: cleanMergedRow.description || cleanMergedRow.content || '',
            startTime: cleanMergedRow.startTime,
            endTime: cleanMergedRow.endTime,
            duration: cleanMergedRow.duration,
            selectedTags: [...(cleanMergedRow.selectedTags || [])],
            keywords: cleanMergedRow.keywords || '',
            keywordsMetadata: { ...(cleanMergedRow.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
            imageSrc: cleanMergedRow.imageSrc || '',
            imageAlt: cleanMergedRow.imageAlt || '',
            isImageLocked: cleanMergedRow.isImageLocked || false,
            thumbnails: Array.isArray(cleanMergedRow.thumbnails) ? [...cleanMergedRow.thumbnails] : [],
            generatedImages: Array.isArray(cleanMergedRow.generatedImages) ? [...cleanMergedRow.generatedImages] : [],
            tags: cleanMergedRow.tags || [],
            isSelected: cleanMergedRow.isSelected || false
          };
          });
        } else {
          // 🔥 统一数据结构：对于未合并项，mergedRows 包含行本身的数据
          row.mergedRows = [{
            index: row.index,
            originalIndex: row.originalIndex,
            description: row.description || '',
            startTime: row.startTime,
            endTime: row.endTime,
            duration: row.duration,
            selectedTags: [...(row.selectedTags || [])],
            keywords: row.keywords || '',
            keywordsMetadata: { ...(row.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
            imageSrc: row.imageSrc || '',
            imageAlt: row.imageAlt || '',
            isImageLocked: row.isImageLocked || false,
            thumbnails: Array.isArray(row.thumbnails) ? [...row.thumbnails] : [],
            generatedImages: Array.isArray(row.generatedImages) ? [...row.generatedImages] : [],
            tags: row.tags || [],
            isSelected: row.isSelected || false
          }];

          // 如果标记为合并但没有 mergedRows 信息，将其标记为非合并项
          if (cleanItem.isMerged) {
            row.isMerged = false;
          }
        }

        return row;
      });
    }

    /**
     * 应用AI分组结果 - 使用统一的数据格式
     */
    function applyAIGrouping(groupingResult) {
      let result;

      // 检查是否是包含rawText的对象（从AIGroupingModal传来的原始文本格式）
      if (groupingResult && groupingResult.rawText) {
        // 使用parseImportedContent处理原始文本
        result = groupingOperations.parseImportedContent(groupingResult.rawText, subtitleItems.value);
      } else {
        // 处理数组格式的分组结果
        result = groupingOperations.applyAIGroupingResult(subtitleItems.value, groupingResult);
      }

      if (result.success) {
        // 将AI分组结果转换为 currentRows 格式
        currentRows.value = convertImportResultToRows(result.subtitleItems || result.rows);

        // 🔥 修复：确保所有行的索引正确重新编号
        currentRows.value.forEach((row, idx) => {
          row.index = idx + 1;
        });

        console.log('[ApplyAIGrouping] AI分组成功，重新编号后的行数据:', currentRows.value);

        // 同步更新 subtitleItems 用于显示
        syncSubtitleItemsFromRows();

        // 重新收集合并组信息
        collectMergedGroups();
      } else {
        console.error('AdjustShotsDrawer - AI分组失败:', result.error);
        showValidationErrorModal(
          'ai',
          'AI分组失败',
          result.error,
          result.validationDetails
        );
      }

      showAIGrouping.value = false;
    }

    /**
     * 确认合并 - 返回与 ContentCreationStudio.vue 完全一致的数据格式
     */
    function onConfirmMerge() {
      // 🔥 统一数据结构：清理并标准化数据格式
      const finalRows = currentRows.value.map((row, index) => {
        const cleanRow = { ...row };

        // 🔥 统一数据结构：移除可能存在的多余字段
        delete cleanRow.id;
        delete cleanRow.rowIndex;
        delete cleanRow.originalState;

        // 🔥 统一数据结构：确保索引正确
        cleanRow.index = index + 1;

        // 🔥 统一数据结构：清理 mergedRows 中的多余字段
        if (cleanRow.mergedRows && Array.isArray(cleanRow.mergedRows)) {
          cleanRow.mergedRows = cleanRow.mergedRows.map(mergedRow => {
            const cleanMergedRow = { ...mergedRow };

            // 移除多余字段
            delete cleanMergedRow.id;
            delete cleanMergedRow.rowIndex;
            delete cleanMergedRow.originalState;

            return cleanMergedRow;
          });
        }

        return cleanRow;
      });

      console.log('[AdjustShotsDrawer] 发送给 ContentCreationStudio 的数据格式:', finalRows);

      // 发送清理后的数据给父组件
      emit('update-rows', finalRows);
      emit('update:show', false);
    }

    return {
      // 状态
      subtitleItems,
      mergedGroups,
      showAIGrouping,

      // 验证错误弹窗状态
      showValidationError,
      validationErrorType,
      validationErrorTitle,
      validationErrorMessage,
      validationErrorDetails,

      // 成功消息状态
      showSuccessMessage,
      successMessageText,

      // 方法
      handleClick,
      handleRightClick,
      cancelAllMergedGroups,
      exportText,
      importGroups,
      applyAIGrouping,
      onConfirmMerge
    };
  }
}
</script>

<style scoped>
.adjust-shots-drawer {
  background: #232136;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem 1.2rem 0.5rem 1.2rem; /* 减少顶部和底部padding */
}

.drawer-header-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.drawer-title {
  color: #e0def4;
  font-size: 1.25rem; /* 稍微小一点 */
  font-weight: 600;
  margin-bottom: 0.1rem;
}

.drawer-description {
  color: #908caa;
  font-size: 0.98rem;
  margin-bottom: 0.2rem;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 0.7rem;
  margin-bottom: 0.2rem;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.8rem;
  background: #2a273f;
  border: 1px solid #44415a;
  color: #e0def4;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s;
}

.action-button:hover {
  background: #393552;
}

.action-button i {
  font-size: 1.1rem;
}

.ai-button {
  background: #3e8fb0;
  border-color: #3e8fb0;
}

.ai-button:hover {
  background: #2c6a85;
}

.status-bar {
  margin-bottom: 0.2rem;
  font-size: 0.92rem;
}

.status-text {
  color: #908caa;
}

.subtitle-list {
  flex: 1 1 0;
  min-height: 0;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  border: 1px solid #44415a;
  border-radius: 5px;
  overflow: hidden;
}

.subtitle-list-header {
  background: #2a273f;
  border-bottom: 1px solid #44415a;
  padding: 0.7rem 1rem;
}

.column-header {
  color: #c4a7e7;
  font-weight: 600;
  font-size: 0.95rem;
}

.subtitle-items-container {
  flex: 1;
  overflow-y: auto;
  background: #1f1d2e;
}

.subtitle-items {
  display: flex;
  flex-direction: column;
}

.subtitle-item {
  display: flex;
  align-items: flex-start;
  padding: 0.7rem 1rem;
  border-bottom: 1px solid #2a273f;
  cursor: pointer;
  transition: background 0.2s;
}

.subtitle-item:hover {
  background: #2a273f;
}

.subtitle-item.merged {
  background: #3e8fb0;
  color: #e0def4;
}

.subtitle-item.merged:hover {
  background: #2c6a85;
}

/* 合并行的序号使用更明显的颜色 */
.subtitle-item.merged .item-serial {
  color: #ffffff;
  font-weight: 600;
}

.item-serial {
  color: #908caa;
  font-size: 0.8rem;
  width: 3rem;
  flex-shrink: 0;
  padding-top: 0.1rem;
}

.item-content {
  flex: 1;
  color: #e0def4;
  line-height: 1.4;
  word-break: break-word;
  white-space: pre-line;
}

.drawer-footer {
  padding-top: 0.7rem;
  border-top: 1px solid #44415a;
  margin-top: 0.5rem;
}

.operation-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.operation-button {
  padding: 0.6rem 2rem;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.operation-button.cancel {
  background: transparent;
  border: 1px solid #908caa;
  color: #908caa;
}

.operation-button.cancel:hover {
  background: #2a273f;
}

.operation-button.confirm {
  background: #3e8fb0;
  border: none;
  color: #e0def4;
}

.operation-button.confirm:hover {
  background: #2c6a85;
}

/* 成功消息动画样式 */
.success-message-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 100px;
}

.success-message {
  background: linear-gradient(135deg, #31c48d, #22c55e);
  color: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(49, 196, 141, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  font-weight: 500;
  animation: successSlideIn 0.4s ease-out forwards, successSlideOut 0.4s ease-in 2.6s forwards;
  transform: translateY(-50px);
  opacity: 0;
}

.success-icon {
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes successSlideIn {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes successSlideOut {
  to {
    transform: translateY(-50px);
    opacity: 0;
  }
}
</style>