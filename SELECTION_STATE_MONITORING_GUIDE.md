# 选择状态监控指南（全面版）

## 🎯 监控目标

为了彻底诊断选择状态被意外清除的问题，我已经添加了全面的监控系统，包括属性拦截器和详细的调用栈追踪。

## 📊 已添加的监控系统（全面版）

### 1. **属性拦截器** (`src/utils/selectionStateInterceptor.js`)
- ✅ 拦截所有 `isSelected`, `selected`, `chosen`, `picked` 属性的修改
- ✅ 记录每次修改的调用栈信息
- ✅ 自动检测选择状态被清除的操作
- ✅ 提供详细的时间戳和来源信息

### 2. **行对象结构验证**
- ✅ 验证选择属性的确切名称（`isSelected` vs `selected`）
- ✅ 记录所有选择相关的属性
- ✅ 显示完整的行对象结构

### 3. **任务生命周期监控**
- ✅ 任务开始前的状态快照
- ✅ 任务进行中的状态变化
- ✅ 任务完成前后的状态对比
- ✅ 所有行的选择状态快照

### 4. **批量操作全程追踪**
- ✅ 批量操作开始前的选择状态
- ✅ 每个任务完成时的状态变化
- ✅ 批量操作完成后的最终状态

## 🔍 需要您从IDE调试控制台复制的关键信息

### **运行批量生图时，请复制以下日志**：

#### **1. 任务开始状态**
```
🔍 [任务开始状态] 第X行任务开始前状态: {
  isSelected: true/false,
  isGenerating: true/false,
  isQueued: true/false,
  queueTaskId: "...",
  batchUIProcessing: true/false,
  timestamp: "..."
}
```

#### **2. 选择状态变化追踪**
```
🔍 [选择状态追踪] 第X行状态变化: {
  from: true,
  to: false,
  source: "...",
  reason: "...",
  timestamp: "..."
}

⚠️ [选择状态清除] 第X行选择状态被清除！ {
  source: "...",
  reason: "...",
  stackTrace: [...]
}
```

#### **3. 批量操作追踪**
```
🚀 [批量操作追踪] 批量图像生成操作开始: {
  operationType: "批量图像生成",
  selectedRowCount: X,
  selectedRowIndexes: [...],
  timestamp: "..."
}

📊 [批量操作追踪] 批量图像生成进度: X/Y

✅ [批量操作追踪] 批量图像生成操作完成: {
  operationType: "批量图像生成",
  result: {...},
  timestamp: "..."
}
```

#### **4. 批量完成前状态**
```
🔍 [批量完成前状态] 所有行的选择状态: [
  {rowIndex: 1, isSelected: true/false, isGenerating: true/false, isQueued: true/false},
  {rowIndex: 2, isSelected: true/false, isGenerating: true/false, isQueued: true/false},
  ...
]
```

#### **5. 状态管理日志**
```
🔧 [状态管理] 行X 任务完成，保持选择状态 (isSelected: true/false)
```

## 📋 测试步骤

### **请按以下步骤操作并复制日志**：

1. **清除控制台历史**
   ```javascript
   // 在浏览器控制台执行
   console.clear();
   ```

2. **选择多行进行批量生图**
   - 选择2-3行
   - 确保它们都有关键词
   - 点击"2. 批量生图"按钮

3. **观察并复制关键时间点的日志**：
   - **批量操作开始时** - 复制所有 `🚀 [批量操作追踪]` 日志
   - **第一个任务完成时** - 复制所有 `🔍 [选择状态...]` 日志
   - **批量操作完成时** - 复制所有 `✅ [批量操作追踪]` 和 `🔍 [批量完成前状态]` 日志

4. **特别关注**：
   - 任何 `⚠️ [选择状态清除]` 警告
   - 任何 `🔍 [选择状态变化]` 记录
   - 调用栈信息（stackTrace）

## 🎯 关键监控参数

### **请特别关注这些参数的变化**：

1. **`isSelected` 状态变化时机**
   - 从 `true` 变为 `false` 的确切时间点
   - 变化的来源（source）和原因（reason）

2. **批量操作状态**
   - `batchUIProcessing` 的值
   - 批量操作是否被标记为取消（`cancelled: true`）

3. **调用栈信息**
   - 当选择状态被清除时，是哪个函数调用的
   - 调用链路的前5层

4. **时间序列**
   - 任务开始、进行、完成的时间戳
   - 选择状态变化的时间戳

## 🔧 调试工具

### **浏览器控制台命令**：
```javascript
// 查看选择状态变化历史
window.selectionStateTracker.getHistory()

// 清除历史记录
window.selectionStateTracker.clearHistory()

// 启用/禁用追踪
window.selectionStateTracker.setTracking(true/false)
```

## 📝 需要您提供的信息

请运行批量生图测试，然后提供：

1. **完整的IDE调试控制台输出**（从批量操作开始到完成）
2. **特别是包含以下标记的日志**：
   - `🔍 [任务开始状态]`
   - `🔍 [选择状态追踪]`
   - `⚠️ [选择状态清除]`
   - `🚀 [批量操作追踪]`
   - `🔍 [批量完成前状态]`
   - `🔧 [状态管理]`

3. **如果有选择状态被清除，请特别复制**：
   - 完整的 `stackTrace` 信息
   - 变化的 `source` 和 `reason`
   - 变化发生的确切时间点

通过这些详细的监控日志，我们就能准确定位选择状态被清除的根本原因和具体位置！
