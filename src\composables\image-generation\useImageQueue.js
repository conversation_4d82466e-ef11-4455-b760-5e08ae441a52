/**
 * 图像生成队列管理
 * 负责任务队列的增删改查、状态管理和调度
 */

import { ref, reactive } from 'vue';

export function useImageQueue() {
  // 🆕 任务队列管理
  const taskQueue = reactive([]);
  const currentTask = ref(null);
  const queueStats = reactive({
    total: 0,
    waiting: 0,
    processing: 0,
    completed: 0,
    cancelled: 0
  });

  /**
   * 创建新的队列任务
   */
  const createQueueTask = (taskData) => {
    const task = {
      id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      status: 'waiting', // waiting, processing, completed, cancelled, failed
      createdAt: new Date().toISOString(),
      startedAt: null,
      completedAt: null,
      queuePosition: taskQueue.length + 1,
      ...taskData
    };

    // console.log('📋 [队列管理] 创建新任务:', { // Original log, can be too verbose
    //   taskId: task.id,
    //   rowIndex: task.rowIndex,
    //   queuePosition: task.queuePosition
    // });

    return task;
  };

  /**
   * 添加任务到队列
   */
  const addToQueue = (taskData) => {
    const task = createQueueTask(taskData);

    // 检查是否已有相同行的任务在队列中
    const existingTaskIndex = taskQueue.findIndex(
      t => t.rowIndex === task.rowIndex && t.status === 'waiting'
    );

    if (existingTaskIndex !== -1) {
      console.log(`[ImageQueue] 替换第${task.rowIndex + 1}行的现有任务: ${taskQueue[existingTaskIndex].id} -> ${task.id}`);
      taskQueue[existingTaskIndex] = task;
    } else {
      taskQueue.push(task);
      console.log(`[ImageQueue] 添加任务: ${task.id} (第${task.rowIndex + 1}行)，队列长度: ${taskQueue.length}`);
    }

    updateQueueStats();
    return task;
  };

  /**
   * 从队列中移除任务
   */
  const removeFromQueue = (taskId) => {
    const taskIndex = taskQueue.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
      const removedTask = taskQueue.splice(taskIndex, 1)[0];
      console.log(`[ImageQueue] removeFromQueue: Task ${removedTask.id} (Row ${removedTask.rowIndex}, Status: ${removedTask.status}) removed. Queue length: ${taskQueue.length}`);

      // 更新剩余任务的队列位置
      taskQueue.forEach((task, index) => {
        if (task.status === 'waiting') {
          task.queuePosition = index + 1;
        }
      });

      updateQueueStats();
      return removedTask;
    }
    return null;
  };

  /**
   * 更新任务状态
   */
  const updateTaskStatus = (taskId, status, additionalData = {}) => {
    const task = taskQueue.find(t => t.id === taskId);
    if (task) {
      const oldStatus = task.status;
      task.status = status;

      // 更新时间戳
      if (status === 'processing' && !task.startedAt) {
        task.startedAt = new Date().toISOString();
      } else if (['completed', 'cancelled', 'failed'].includes(status) && !task.completedAt) {
        task.completedAt = new Date().toISOString();
      }

      // 合并额外数据
      Object.assign(task, additionalData);

      // 🔧 优化：只记录重要的状态变化
      if (['processing', 'completed', 'cancelled', 'failed'].includes(status)) {
        console.log(`[ImageQueue] 任务状态更新: ${taskId} (第${task.rowIndex + 1}行) ${oldStatus} -> ${status}`);
      }

      updateQueueStats();
      return task;
    }
    return null;
  };

  /**
   * 更新队列统计信息
   */
  const updateQueueStats = () => {
    queueStats.total = taskQueue.length;
    queueStats.waiting = taskQueue.filter(t => t.status === 'waiting').length;
    const oldWaiting = queueStats.waiting;
    const oldProcessing = queueStats.processing;

    queueStats.total = taskQueue.length;
    queueStats.waiting = taskQueue.filter(t => t.status === 'waiting').length;
    queueStats.processing = taskQueue.filter(t => t.status === 'processing').length;
    queueStats.completed = taskQueue.filter(t => t.status === 'completed').length;
    queueStats.cancelled = taskQueue.filter(t => t.status === 'cancelled').length;

    if (oldWaiting !== queueStats.waiting || oldProcessing !== queueStats.processing) {
      console.log(`[ImageQueue] updateQueueStats: Waiting: ${queueStats.waiting} (was ${oldWaiting}), Processing: ${queueStats.processing} (was ${oldProcessing}), Total: ${queueStats.total}`);
    }
  };

  /**
   * 获取下一个待处理任务
   */
  const getNextTask = () => {
    return taskQueue.find(task => task.status === 'waiting');
  };

  /**
   * 获取指定行的队列状态
   */
  const getRowQueueStatus = (rowIndex) => {
    const task = taskQueue.find(t => t.rowIndex === rowIndex);
    if (!task) {
      return {
        isQueued: false,
        queuePosition: 0,
        status: null,
        taskId: null
      };
    }

    return {
      isQueued: true,
      queuePosition: task.queuePosition,
      status: task.status,
      taskId: task.id,
      createdAt: task.createdAt,
      startedAt: task.startedAt,
      completedAt: task.completedAt
    };
  };

  /**
   * 根据行索引取消排队
   */
  const cancelQueueByRowIndex = (rowIndex) => {
    const task = taskQueue.find(t => t.rowIndex === rowIndex && t.status === 'waiting');
    if (task) {
      console.log('📋 [队列管理] 取消排队任务:', { taskId: task.id, rowIndex });
      // 直接移除任务，不需要先改状态
      const removedTask = removeFromQueue(task.id);
      console.log('📋 [队列管理] 任务已移除，当前队列长度:', taskQueue.length);
      return removedTask;
    }
    console.log('📋 [队列管理] 未找到待取消的任务:', { rowIndex });
    return null;
  };

  /**
   * 清空队列
   */
  const clearQueue = async () => {
    console.log('📋 [队列管理] 开始清空队列...');

    // 取消所有等待中的任务，并通知UI更新状态
    const waitingTasks = taskQueue.filter(t => t.status === 'waiting');
    waitingTasks.forEach(task => {
      updateTaskStatus(task.id, 'cancelled');

      // 🔧 关键修复：通知UI更新行状态，清除isQueued状态
      if (task.onProgress) {
        console.log(`📋 [队列管理] 通知UI清除第${task.rowIndex + 1}行的队列状态`);
        task.onProgress({
          stage: 'cancelled',
          progress: 0,
          message: '队列已清空',
          taskId: task.id,
          rowIndex: task.rowIndex,
          isQueued: false,        // 🔧 关键：清除队列状态
          isProcessing: false,
          isCompleted: false,
          isError: false,
          isCancelled: true
        });
      }
    });

    // 清空队列数组
    taskQueue.splice(0);
    currentTask.value = null;

    updateQueueStats();
    console.log('📋 [队列管理] 队列已清空');
  };

  /**
   * 获取队列摘要信息
   */
  const getQueueSummary = () => {
    return {
      stats: { ...queueStats },
      currentTask: currentTask.value,
      nextTask: getNextTask(),
      totalTasks: taskQueue.length,
      isEmpty: taskQueue.length === 0
    };
  };

  /**
   * 查找任务
   */
  const findTask = (taskId) => {
    return taskQueue.find(t => t.id === taskId);
  };

  /**
   * 获取所有任务（只读）
   */
  const getAllTasks = () => {
    return [...taskQueue];
  };

  return {
    // 状态
    taskQueue,
    currentTask,
    queueStats,

    // 任务管理
    createQueueTask,
    addToQueue,
    removeFromQueue,
    updateTaskStatus,
    findTask,
    getAllTasks,

    // 队列操作
    getNextTask,
    clearQueue,
    getQueueSummary,

    // 行相关操作
    getRowQueueStatus,
    cancelQueueByRowIndex,

    // 统计
    updateQueueStats
  };
}
