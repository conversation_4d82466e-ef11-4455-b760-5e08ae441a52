<template>
  <div
    v-if="errorState.hasError"
    class="error-recovery-modal"
    @click="closeModal"
  >
    <div
      class="modal-content"
      @click.stop
    >
      <!-- 错误标题和图标 -->
      <div class="error-header">
        <div
          class="error-icon"
          :class="errorIconClass"
        >
          <i :class="errorIconName" />
        </div>
        <h3 class="error-title">
          {{ currentErrorInfo?.title || '发生错误' }}
        </h3>
        <button
          class="close-button"
          @click="closeModal"
        >
          <i class="fas fa-times" />
        </button>
      </div>

      <!-- 错误描述 -->
      <div class="error-body">
        <p class="error-message">
          {{ currentErrorInfo?.message || errorState.errorMessage }}
        </p>
        
        <!-- 详细错误信息（可折叠） -->
        <div
          class="error-details"
          v-if="showDetails"
        >
          <h4>详细信息：</h4>
          <pre class="error-stack">{{ errorState.originalError?.stack || errorState.errorMessage }}</pre>
        </div>
        
        <button
          class="toggle-details"
          @click="showDetails = !showDetails"
        >
          {{ showDetails ? '隐藏' : '显示' }}详细信息
          <i :class="showDetails ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" />
        </button>

        <!-- 建议解决方案 -->
        <div
          class="suggestions"
          v-if="currentErrorInfo?.suggestions"
        >
          <h4>建议解决方案：</h4>
          <ul>
            <li
              v-for="(suggestion, index) in currentErrorInfo.suggestions"
              :key="index"
            >
              {{ suggestion }}
            </li>
          </ul>
        </div>

        <!-- 自动修复信息 -->
        <div
          class="auto-fix-info"
          v-if="currentErrorInfo?.autoFix"
        >
          <div class="auto-fix-badge">
            <i class="fas fa-magic" />
            自动修复
          </div>
          <p>{{ currentErrorInfo.autoFix }}</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <button 
          class="btn btn-secondary" 
          @click="closeModal"
        >
          取消
        </button>
        
        <button 
          v-if="canRetryNow" 
          class="btn btn-primary" 
          @click="handleRetry"
          :disabled="errorState.isRetrying"
        >
          <i
            v-if="errorState.isRetrying"
            class="fas fa-spinner fa-spin"
          />
          <i
            v-else
            class="fas fa-redo"
          />
          {{ errorState.isRetrying ? '重试中...' : `重试 (${errorState.retryCount}/${errorState.maxRetries})` }}
        </button>
        
        <button 
          v-if="errorState.errorType === 'memory'" 
          class="btn btn-warning" 
          @click="handleOptimizeAndRetry"
          :disabled="errorState.isRetrying"
        >
          <i class="fas fa-cog" />
          优化设置并重试
        </button>
        
        <button 
          v-if="errorState.errorType === 'connection'" 
          class="btn btn-info" 
          @click="handleCheckConnection"
        >
          <i class="fas fa-wifi" />
          检查连接
        </button>
      </div>

      <!-- 重试进度 -->
      <div
        v-if="errorState.isRetrying"
        class="retry-progress"
      >
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: retryProgress + '%' }"
          />
        </div>
        <p class="progress-text">
          正在重试... {{ Math.round(retryProgress) }}%
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'ComfyUIErrorRecoveryModal',
  props: {
    errorState: {
      type: Object,
      required: true
    },
    serviceState: {
      type: Object,
      required: true
    },
    currentErrorInfo: {
      type: Object,
      default: null
    },
    canRetryNow: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'retry', 'optimize-and-retry', 'check-connection'],
  setup(props, { emit }) {
    const showDetails = ref(false);
    const retryProgress = ref(0);

    // 错误图标和样式
    const errorIconClass = computed(() => {
      switch (props.errorState.errorType) {
        case 'memory':
          return 'error-icon-memory';
        case 'connection':
          return 'error-icon-connection';
        case 'timeout':
          return 'error-icon-timeout';
        default:
          return 'error-icon-unknown';
      }
    });

    const errorIconName = computed(() => {
      switch (props.errorState.errorType) {
        case 'memory':
          return 'fas fa-memory';
        case 'connection':
          return 'fas fa-wifi-slash';
        case 'timeout':
          return 'fas fa-clock';
        default:
          return 'fas fa-exclamation-triangle';
      }
    });

    // 重试进度模拟
    watch(() => props.errorState.isRetrying, (isRetrying) => {
      if (isRetrying) {
        retryProgress.value = 0;
        const interval = setInterval(() => {
          retryProgress.value += Math.random() * 10;
          if (retryProgress.value >= 90) {
            retryProgress.value = 90;
            clearInterval(interval);
          }
        }, 200);
        
        // 清理定时器
        setTimeout(() => {
          clearInterval(interval);
          if (props.errorState.isRetrying) {
            retryProgress.value = 100;
          }
        }, 5000);
      } else {
        retryProgress.value = 0;
      }
    });

    // 事件处理
    const closeModal = () => {
      emit('close');
    };

    const handleRetry = () => {
      emit('retry');
    };

    const handleOptimizeAndRetry = () => {
      emit('optimize-and-retry');
    };

    const handleCheckConnection = () => {
      emit('check-connection');
    };

    return {
      showDetails,
      retryProgress,
      errorIconClass,
      errorIconName,
      closeModal,
      handleRetry,
      handleOptimizeAndRetry,
      handleCheckConnection
    };
  }
};
</script>

<style scoped>
.error-recovery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: #1e1e2e;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid #313244;
}

.error-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #313244;
  position: relative;
}

.error-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.error-icon-memory {
  background: #fab387;
  color: #1e1e2e;
}

.error-icon-connection {
  background: #f38ba8;
  color: #1e1e2e;
}

.error-icon-timeout {
  background: #f9e2af;
  color: #1e1e2e;
}

.error-icon-unknown {
  background: #cba6f7;
  color: #1e1e2e;
}

.error-title {
  flex: 1;
  margin: 0;
  color: #cdd6f4;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #6c7086;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #313244;
  color: #cdd6f4;
}

.error-body {
  padding: 20px;
}

.error-message {
  color: #cdd6f4;
  margin-bottom: 16px;
  line-height: 1.5;
}

.toggle-details {
  background: none;
  border: none;
  color: #89b4fa;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-details {
  background: #181825;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #313244;
}

.error-details h4 {
  color: #cdd6f4;
  margin: 0 0 12px 0;
  font-size: 14px;
}

.error-stack {
  color: #f38ba8;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.suggestions {
  margin-bottom: 16px;
}

.suggestions h4 {
  color: #cdd6f4;
  margin: 0 0 12px 0;
  font-size: 14px;
}

.suggestions ul {
  margin: 0;
  padding-left: 20px;
  color: #a6adc8;
}

.suggestions li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.auto-fix-info {
  background: linear-gradient(135deg, #a6e3a1, #94e2d5);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  color: #1e1e2e;
}

.auto-fix-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 8px;
}

.error-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #313244;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #89b4fa;
  color: #1e1e2e;
}

.btn-primary:hover:not(:disabled) {
  background: #74c7ec;
}

.btn-secondary {
  background: #6c7086;
  color: #cdd6f4;
}

.btn-secondary:hover {
  background: #7f849c;
}

.btn-warning {
  background: #fab387;
  color: #1e1e2e;
}

.btn-warning:hover:not(:disabled) {
  background: #f9e2af;
}

.btn-info {
  background: #74c7ec;
  color: #1e1e2e;
}

.btn-info:hover {
  background: #89dceb;
}

.retry-progress {
  padding: 0 20px 20px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #313244;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #89b4fa, #74c7ec);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #a6adc8;
  font-size: 14px;
  margin: 0;
}
</style>
