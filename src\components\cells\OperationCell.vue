<template>
  <div class="grid-cell operation-cell">
    <div class="cell-content">
      <div class="operation-container">
        <!-- 操作按钮区域 -->
        <div class="operation-buttons">
          <!-- 行锁定按钮 -->
          <button
            class="operation-button lock-button"
            :class="{
              'locked': isLocked,
              'unlocked': !isLocked
            }"
            @click="toggleLock"
            :title="isLocked ? '点击解锁该行' : '点击锁定该行'"
          >
            <div class="button-content">
              <i :class="isLocked ? 'ri-lock-line' : 'ri-lock-unlock-line'" />
              <span class="button-text">{{ isLocked ? '已锁定' : '上锁' }}</span>
            </div>
          </button>

          <!-- 生成图像按钮 - 支持生成/取消切换 -->
          <button
            class="operation-button generate"
            :class="{
              'generating': isGenerating,
              'cancelling': isGenerating,
              'disabled': isLocked
            }"
            :disabled="isLocked"
            @click="handleGenerateClick"
            :title="generateButtonTitle"
          >
            <div class="button-content">
              <div
                v-if="isGenerating"
                class="generating-indicator"
              >
                <div class="progress-circle">
                  <svg
                    class="progress-ring"
                    width="20"
                    height="20"
                  >
                    <circle
                      class="progress-ring-circle"
                      stroke="#4ade80"
                      stroke-width="2"
                      fill="transparent"
                      r="8"
                      cx="10"
                      cy="10"
                      :stroke-dasharray="circumference"
                      :stroke-dashoffset="progressOffset"
                    />
                  </svg>
                  <i class="ri-image-add-line progress-icon" />
                </div>
              </div>
              <i
                v-else
                class="ri-image-add-line"
              />
              <span class="button-text">{{ generateButtonText }}</span>
            </div>
          </button>
          <!-- 🆕 取消排队按钮（原重绘按钮） -->
          <button
            v-if="isQueued"
            class="operation-button cancel-queue"
            :class="{ 'disabled': isLocked }"
            :disabled="isLocked"
            @click="cancelQueue"
            :title="isLocked ? '该行已锁定，无法取消排队' : `取消排队（当前位置：${queuePosition}）`"
          >
            <div class="button-content">
              <i class="ri-close-line" />
              <span class="button-text">取消排队</span>
            </div>
          </button>
          <div class="prompt-button-group">
            <button
              class="operation-button highlight main-button"
              :class="{
                'loading': isInferring,
                'disabled': isLocked
              }"
              :disabled="isInferring || isLocked"
              @click="inferPrompt"
              :title="getInferButtonTitle()"
            >
              <div class="button-content">
                <div
                  v-if="isInferring"
                  class="loading-indicator"
                >
                  <div class="pulse-ring" />
                  <i class="ri-brain-line pulse-icon" />
                </div>
                <i
                  v-else
                  class="ri-brain-line"
                />
                <span class="button-text">{{ isInferring ? '推理中...' : '推理提示词' }}</span>
              </div>
            </button>
            <button
              class="operation-button preview-button"
              :disabled="isInferring || isLocked"
              @click="previewPrompt"
              :title="isLocked ? '该行已锁定，无法预览' : '预览即将发送的提示词内容'"
            >
              <i class="ri-eye-line" />
            </button>
          </div>
        </div>

        <!-- 🔧 简洁模式：移除生成进度指示器，保持UI简洁 -->

        <!-- 🆕 队列状态指示器 -->
        <div
          v-if="isQueued && !isGenerating"
          class="queue-status"
        >
          <div class="queue-indicator">
            <i class="ri-time-line queue-icon" />
            <span class="queue-text">排队中</span>
          </div>
          <div class="queue-position">
            第 {{ queuePosition }} 位
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OperationCell',
  emits: {
    'redraw-image': null,
    'infer-prompt': null,
    'preview-prompt': null,
    'generate-image': null,
    'cancel-generation': null,
    'cancel-queue': null,
    'toggle-lock': null
  },
  props: {
    isGenerating: {
      type: Boolean,
      default: false
    },
    generationProgress: {
      type: Number,
      default: 0
    },
    generationMessage: {
      type: String,
      default: ''
    },
    canGenerate: {
      type: Boolean,
      default: true
    },
    hasKeywords: {
      type: Boolean,
      default: false
    },
    // 🔥 新增：推理加载状态
    isInferring: {
      type: Boolean,
      default: false
    },
    // 🔥 新增：行锁定状态
    isLocked: {
      type: Boolean,
      default: false
    },
    // 🆕 队列相关状态
    isQueued: {
      type: Boolean,
      default: false
    },
    queuePosition: {
      type: Number,
      default: 0
    },
    queueTaskId: {
      type: String,
      default: ''
    }
  },
  computed: {
    generateButtonTitle() {
      if (this.isLocked) {
        return '该行已锁定，无法生成图像';
      }
      if (this.isQueued) {
        return `任务已排队（第${this.queuePosition}位），点击可取消排队`;
      }
      if (!this.hasKeywords && !this.isGenerating) {
        return '请先输入关键词';
      }
      if (this.isGenerating) {
        return '点击取消生成任务';
      }
      return '使用ComfyUI生成图像';
    },
    // 🔥 新增：进度圆环计算
    circumference() {
      return 2 * Math.PI * 8; // r=8
    },
    progressOffset() {
      const progress = Math.max(0, Math.min(100, this.generationProgress));
      return this.circumference - (progress / 100) * this.circumference;
    },
    // 🔥 新增：生成按钮文本（包含百分比和取消功能）
    generateButtonText() {
      if (this.isQueued) {
        return `排队中 (${this.queuePosition})`;
      }

      if (!this.isGenerating) {
        return '生成图像';
      }

      const progress = Math.max(0, Math.min(100, this.generationProgress));

      if (progress === 0) {
        return '取消';
      } else if (progress >= 100) {
        return '完成';
      } else {
        return `取消 ${Math.round(progress)}%`;
      }
    }
  },
  mounted() {
    // 🔧 简洁模式：移除组件挂载日志

    // 🔧 设置组件已挂载标志和状态跟踪
    this._isMounted = true;
    this._lastIsGenerating = this.isGenerating;
    this._lastIsQueued = this.isQueued;
  },
  updated() {
    // 🔧 简洁模式：移除状态更新日志
    if (this._isMounted && this._lastIsGenerating !== this.isGenerating) {
      this._lastIsGenerating = this.isGenerating;
    }

    // 🔧 添加 isQueued 状态跟踪
    if (this._isMounted && this._lastIsQueued !== this.isQueued) {
      this._lastIsQueued = this.isQueued;
    }
  },
  beforeUnmount() {
    // 🔧 清理标志，防止卸载后的操作
    this._isMounted = false;
  },
  // 🔧 移除有问题的watchers，改用更安全的方法
  // watch: {
  //   // 注释掉有问题的watchers，避免runtime错误
  // },
  methods: {
    redrawImage() {
      this.$emit('redraw-image');
    },
    inferPrompt() {
      this.$emit('infer-prompt');
    },
    previewPrompt() {
      this.$emit('preview-prompt');
    },
    // 🆕 处理生成/取消按钮点击
    handleGenerateClick() {
      if (this.isLocked) {
        return; // 锁定状态下不允许操作
      }

      if (this.isQueued) {
        // 如果在队列中，则取消排队
        this.cancelQueue();
      } else if (this.isGenerating) {
        // 如果正在生成，则取消
        this.cancelGeneration();
      } else if (this.hasKeywords) {
        // 如果未生成且有关键词，则开始生成（移除canGenerate检查）
        this.generateImage();
      }
    },
    generateImage() {
      this.$emit('generate-image');
    },
    // 🆕 取消生成
    cancelGeneration() {
      this.$emit('cancel-generation');
    },
    // 🆕 取消排队
    cancelQueue() {
      this.$emit('cancel-queue');
    },
    // 🆕 切换锁定状态
    toggleLock() {
      this.$emit('toggle-lock');
    },
    // 🆕 获取推理按钮标题
    getInferButtonTitle() {
      if (this.isLocked) {
        return '该行已锁定，无法推理';
      }
      if (this.isInferring) {
        return '正在推理中...';
      }
      return '直接发送推理请求给LLM';
    },

    // 🔧 调试按钮状态
    debugButtonState() {
      try {
        const state = {
          isGenerating: this.isGenerating,
          isQueued: this.isQueued,
          generationProgress: this.generationProgress,
          buttonText: this.generateButtonText || 'N/A',
          hasKeywords: this.hasKeywords,
          isLocked: this.isLocked,
          isMounted: this._isMounted,
          hasElement: !!this.$el
        };
        // 🔧 简洁模式：移除调试状态日志
        return state;
      } catch (error) {
        // 🔧 简洁模式：移除调试错误日志
        return { error: error.message };
      }
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.operation-cell {
  /* 🔧 操作列保持内容自适应 */
  width: fit-content;
  min-width: fit-content;
  white-space: nowrap;
  border-right: none;
}

.cell-content {
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
}

/* 操作列样式 - 动态宽度优化布局 */
.operation-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 6px 8px; /* 调整内边距，左右稍多以适应动态宽度 */
  min-width: fit-content; /* 确保容器适应内容 */
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 恢复合适的按钮间距 */
  min-width: fit-content; /* 确保按钮容器适应内容 */
}

.operation-button {
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
  padding: 6px 12px; /* 增加水平内边距，提供更好的视觉效果 */
  border-radius: 3px;
  font-size: 12px; /* 恢复合适的字体大小 */
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1.3; /* 调整行高 */
  text-align: center;
  white-space: nowrap; /* 防止文字换行 */
  min-width: fit-content; /* 按钮宽度适应文字内容 */
  width: auto; /* 允许按钮根据内容调整宽度 */
}

.operation-button:hover {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}

.operation-button.highlight {
  background-color: #2a2a2a;
  color: #3e8fb0;
  border: 1px solid #3e8fb0;
  font-weight: normal;
  /* 🔧 修复溢出问题 */
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.operation-button.highlight:hover {
  background-color: #3e8fb0;
  color: #fff;
  /* 🔧 确保hover状态下也不溢出 */
  box-sizing: border-box;
}



/* 🔥 新增：推理提示词按钮组样式 */
.prompt-button-group {
  display: flex;
  align-items: center;
  gap: 2px;
  width: 100%;
  /* 🔧 修复溢出问题 */
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.prompt-button-group .main-button {
  flex: 1;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  /* 🔧 确保按钮不溢出 */
  min-width: 0;
  max-width: calc(100% - 34px); /* 减去预览按钮的宽度 */
  box-sizing: border-box;
}

.prompt-button-group .preview-button {
  background-color: #2a2a2a;
  color: #89b4fa;
  border: 1px solid #3e8fb0;
  border-left: none;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding: 6px 8px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prompt-button-group .preview-button:hover {
  background-color: #89b4fa;
  color: #1e1e2e;
}

.prompt-button-group .preview-button i {
  font-size: 14px;
}

/* 🔥 新增：按钮内容布局 */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: 100%;
  /* 🔧 修复内容溢出 */
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.button-text {
  font-size: inherit;
  line-height: 1;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  /* 🔧 防止文本溢出 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 🔥 新增：推理加载状态样式 */
.prompt-button-group .main-button.loading {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-color: #8b5cf6;
  color: #ffffff;
  cursor: not-allowed;
  position: relative;
  overflow: hidden;
}

.prompt-button-group .main-button.loading:hover {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-color: #8b5cf6;
}

.prompt-button-group .main-button.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

/* 推理加载指示器 */
.loading-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.pulse-ring {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  animation: pulse-ring 1.5s ease-out infinite;
}

.pulse-icon {
  position: relative;
  z-index: 1;
  font-size: 12px;
  color: #ffffff;
  animation: pulse-icon 1.5s ease-in-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes pulse-icon {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.prompt-button-group .preview-button:disabled {
  background-color: #1a1a1a;
  color: #4b5563;
  cursor: not-allowed;
  opacity: 0.5;
}

.prompt-button-group .preview-button:disabled:hover {
  background-color: #1a1a1a;
  color: #4b5563;
}

/* 🆕 队列状态样式 */
.queue-status {
  margin-top: 8px;
  padding: 6px 8px;
  background-color: #2a2a2a;
  border: 1px solid #f59e0b;
  border-radius: 4px;
  color: #fbbf24;
  font-size: 11px;
  text-align: center;
}

.queue-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 2px;
}

.queue-icon {
  font-size: 12px;
  animation: queue-pulse 2s ease-in-out infinite;
}

.queue-text {
  font-weight: 500;
}

.queue-position {
  font-size: 10px;
  color: #d97706;
  font-weight: 600;
}

@keyframes queue-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 🆕 取消排队按钮样式 */
.operation-button.cancel-queue {
  background-color: #dc2626;
  color: #ffffff;
  border: 1px solid #dc2626;
}

.operation-button.cancel-queue:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.operation-button.cancel-queue .button-content {
  gap: 4px;
}

.operation-button.cancel-queue i {
  font-size: 14px;
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* 锁定按钮样式 */
.operation-button.lock-button {
  background-color: #2a2a2a;
  border: 1px solid #666;
  color: #999;
  font-weight: normal;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.operation-button.lock-button.unlocked {
  border-color: #f59e0b;
  color: #f59e0b;
}

.operation-button.lock-button.unlocked:hover {
  background-color: #f59e0b;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.operation-button.lock-button.locked {
  background-color: #dc2626;
  border-color: #dc2626;
  color: white;
  box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.operation-button.lock-button.locked:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

/* 生成图像按钮样式 */
.operation-button.generate {
  background-color: #2a2a2a;
  color: #4ade80;
  border: 1px solid #4ade80;
  font-weight: normal;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.operation-button.generate:hover:not(.disabled) {
  background-color: #4ade80;
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
}

.operation-button.generate i {
  font-size: 14px;
}

/* 生成中状态样式 - 改为取消按钮样式 */
.operation-button.generate.generating {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  border-color: #dc2626;
  color: #ffffff;
  animation: cancel-pulse 2s ease-in-out infinite;
}

.operation-button.generate.generating:hover {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-1px);
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.4);
}

/* 🔥 新增：生成中状态的文本样式 */
.operation-button.generate.generating .button-text {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.3px;
}

/* 生成进度指示器 */
.generating-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.progress-ring {
  position: absolute;
  transform: rotate(-90deg);
  width: 20px;
  height: 20px;
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.3s ease;
  stroke-linecap: round;
  filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.5));
}

.progress-icon {
  position: relative;
  z-index: 1;
  font-size: 10px;
  color: #ffffff;
  animation: rotate-icon 3s linear infinite;
}

@keyframes cancel-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(220, 38, 38, 0);
  }
}

@keyframes rotate-icon {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 旋转动画 - 保留原有的 */

/* 禁用状态样式 */
.operation-button.disabled {
  background-color: #1a1a1a;
  color: #555;
  border-color: #2a2a2a;
  cursor: not-allowed;
  opacity: 0.6;
}

.operation-button.disabled:hover {
  background-color: #1a1a1a;
  color: #555;
  border-color: #2a2a2a;
}

/* 生成进度指示器 */
.generation-progress {
  margin-top: 8px;
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background-color: #4ade80;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.progress-text {
  font-size: 10px;
  color: #aaa;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 - 小屏幕设备优化 */
@media (max-width: 768px) {
  .operation-button {
    font-size: 11px; /* 在小屏幕上适当减小字体 */
    padding: 4px 8px; /* 保持合适的内边距 */
    min-height: 28px; /* 确保最小可点击高度 */
  }

  .operation-container {
    padding: 4px 6px; /* 在小屏幕上减少容器内边距 */
  }

  .operation-buttons {
    gap: 6px; /* 减少按钮间距 */
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .operation-button {
    min-height: 36px; /* 触摸设备上确保足够的点击区域 */
    padding: 8px 12px; /* 增加触摸区域 */
    font-size: 13px; /* 触摸设备上使用稍大的字体 */
  }

  .operation-container {
    padding: 8px 10px; /* 触摸设备上增加容器内边距 */
  }
}
</style> 