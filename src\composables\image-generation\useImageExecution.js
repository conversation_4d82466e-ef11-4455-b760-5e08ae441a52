/**
 * 图像生成执行模块
 * 负责实际的图像生成执行逻辑
 */

import comfyuiImageGeneration from '../../services/comfyuiImageGeneration.js';

export function useImageExecution() {

  /**
   * 执行实际的图像生成（直接调用，非队列）
   */
  const executeImageGeneration = async (task) => {
    const {
      prompt,
      negativePrompt = '',
      projectTitle,
      chapterTitle,
      rowIndex,
      onImageGenerated,
      onProgress
    } = task;

    console.log('🎨 [图像执行] 开始执行图像生成:', {
      rowIndex,
      promptLength: prompt.length,
      projectTitle,
      chapterTitle
    });

    try {
      // 🎯 关键修复：动态读取用户配置的batchSize，在应用层面调用多次API
      let batchSize = comfyuiImageGeneration.comfyuiConfig?.batchSize;

      // 🔧 配置验证：确保batchSize是有效值
      if (!batchSize || typeof batchSize !== 'number' || batchSize < 1 || batchSize > 10) {
        console.warn(`⚠️ [图像执行] 无效的batchSize配置: ${batchSize}，使用默认值1`);
        batchSize = 1;
      }

      console.log(`🔢 [图像执行] 应用层面批量生成: ${batchSize} 次API调用 (来自用户配置)`);

      const allResults = [];
      let totalProgress = 0;

      // 应用层面的批量调用
      for (let i = 0; i < batchSize; i++) {
        console.log(`🎨 [图像执行] 第 ${i + 1}/${batchSize} 次API调用`);

        // 调用ComfyUI服务生成单张图像
        const result = await comfyuiImageGeneration.generateImage({
          positivePrompt: prompt,
          negativePrompt,
          onProgress: (progressData) => {
            // 计算总体进度：当前批次进度 + 已完成批次的进度
            const currentBatchProgress = progressData.progress || 0;
            const completedBatchesProgress = (i / batchSize) * 100;
            const currentBatchWeight = (1 / batchSize) * 100;
            totalProgress = completedBatchesProgress + (currentBatchProgress / 100) * currentBatchWeight;

            // 🔧 关键日志：批量任务完成信息
            if (i === batchSize - 1 && currentBatchProgress === 100) {
              console.log(`✅ [批量生成] 完成 ${batchSize} 张图像 - 行索引: ${rowIndex}`);
              console.log(`📊 [批量生成] 任务详情:`, {
                rowIndex,
                batchSize,
                totalProgress: totalProgress.toFixed(1),
                currentBatchProgress,
                taskCompleted: true
              });
            }

            if (onProgress) {
              onProgress({
                ...progressData,
                progress: Math.round(totalProgress),
                message: ``, // 🔧 简洁模式：移除进度消息
                rowIndex,
                isProcessing: true,
                isCompleted: false,
                isError: false,
                isCancelled: false
              });
            }
          },
          onError: (error) => {
            console.error(`❌ [图像执行] 第${i + 1}/${batchSize}次生成错误:`, error);
            if (onProgress) {
              onProgress({
                stage: 'error',
                progress: 0,
                message: error.message || '生成失败',
                rowIndex,
                isProcessing: false,
                isCompleted: false,
                isError: true,
                isCancelled: false
              });
            }
            throw error;
          },
          onImageGenerated: (imageData) => {
            console.log(`✅ [图像执行] 第${i + 1}/${batchSize}张图像生成完成:`, {
              rowIndex,
              imageUrl: imageData.imageUrl,
              promptId: imageData.promptId
            });

            if (onImageGenerated) {
              onImageGenerated({
                ...imageData,
                rowIndex,
                projectTitle,
                chapterTitle
              });
            }
          },
          // 🔧 添加完成回调监听
          onPromptComplete: (promptId, finalOutputs) => {
            console.log(`🎉 [图像执行] 第${i + 1}/${batchSize}次任务完成:`, {
              rowIndex,
              promptId,
              finalOutputs
            });
          },
          isQueueTask: true // 标识这是队列任务，允许强制执行
        });

        allResults.push(result);
        console.log(`✅ [图像执行] 第${i + 1}/${batchSize}次API调用完成`);

        // 添加小延迟避免过快调用
        if (i < batchSize - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 🔧 最终完成回调
      if (onProgress) {
        console.log('🔧 [图像执行] 所有批量任务完成，标记最终完成状态');
        onProgress({
          stage: 'completed',
          progress: 100,
          message: `生成完成 (${batchSize}张图片)`,
          rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: false,
          isCancelled: false
        });
      }

      console.log('✅ [图像执行] 所有批量图像生成任务完成:', {
        rowIndex,
        totalImages: allResults.length,
        batchSize
      });

      return {
        success: true,
        results: allResults,
        totalImages: allResults.length
      };

    } catch (error) {
      console.error('❌ [图像执行] 图像生成失败:', {
        rowIndex,
        error: error.message,
        stack: error.stack
      });

      // 通知进度回调错误状态
      if (onProgress) {
        onProgress({
          stage: 'error',
          progress: 0,
          message: error.message || '生成失败',
          rowIndex,
          isProcessing: false,
          isCompleted: false,
          isError: true,
          isCancelled: false
        });
      }

      throw error;
    }
  };

  /**
   * 专门用于队列的图像生成函数（不管理 isGenerating 状态）
   */
  const executeImageGenerationForQueue = async (task) => {
    const {
      prompt,
      negativePrompt = '',
      projectTitle,
      chapterTitle,
      rowIndex,
      onImageGenerated,
      onProgress
    } = task;

    console.log('🔄 [队列执行] 开始队列任务:', {
      taskId: task.id,
      rowIndex,
      promptLength: prompt.length
    });

    try {
      // 通知开始处理
      if (onProgress) {
        onProgress({
          stage: 'processing',
          progress: 0,
          message: '开始生成图像...',
          rowIndex,
          taskId: task.id,
          isProcessing: true,
          isCompleted: false,
          isError: false,
          isCancelled: false,
          isQueued: false
        });
      }

      // 执行图像生成
      const result = await executeImageGeneration({
        prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        onImageGenerated: (imageData) => {
          console.log('✅ [队列执行] 队列任务图像生成完成:', {
            taskId: task.id,
            rowIndex,
            imageUrl: imageData.url,
            filename: imageData.filename
          });

          // 🔧 关键修复：确保图像数据正确传递
          if (onImageGenerated) {
            console.log('🔧 [队列执行] 调用onImageGenerated回调');
            onImageGenerated(imageData);
          } else {
            console.warn('⚠️ [队列执行] onImageGenerated回调未定义');
          }

          // 🔧 注意：不在这里调用完成状态，让onPromptComplete处理
          console.log('🔧 [队列执行] 图像已生成，等待onPromptComplete触发完成状态');
        },
        onProgress: (progressData) => {
          if (onProgress) {
            onProgress({
              ...progressData,
              rowIndex,
              taskId: task.id,
              isProcessing: true,
              isCompleted: false,
              isError: false,
              isCancelled: false,
              isQueued: false
            });
          }
        }
      });

      console.log('✅ [队列执行] 队列任务执行完成:', {
        taskId: task.id,
        rowIndex,
        success: result.success
      });

      return result;

    } catch (error) {
      console.error('❌ [队列执行] 队列任务执行失败:', {
        taskId: task.id,
        rowIndex,
        error: error.message
      });

      // 通知错误状态
      if (onProgress) {
        onProgress({
          stage: 'error',
          progress: 0,
          message: error.message || '生成失败',
          rowIndex,
          taskId: task.id,
          isProcessing: false,
          isCompleted: false,
          isError: true,
          isCancelled: false,
          isQueued: false
        });
      }

      throw error;
    }
  };

  // 批量生成图像功能已被移除，将由新的 BatchGenerationManager 替代

  /**
   * 取消生成
   */
  const cancelGeneration = async () => {
    try {
      console.log('🎨 [图像执行] 开始取消生成...');

      // 调用ComfyUI服务取消
      const result = await comfyuiImageGeneration.cancelGeneration();
      
      console.log('✅ [图像执行] 取消生成完成:', result);
      return result;

    } catch (error) {
      console.error('❌ [图像执行] 取消生成失败:', error);
      throw error;
    }
  };

  /**
   * 强制停止ComfyUI所有任务
   */
  const forceStopComfyUITasks = async () => {
    try {
      console.log('🚨 [图像执行] 强制停止ComfyUI所有任务...');

      // 调用ComfyUI服务强制停止
      const result = await comfyuiImageGeneration.forceStopAllTasks();
      
      console.log('✅ [图像执行] 强制停止完成:', result);
      return result;

    } catch (error) {
      console.error('❌ [图像执行] 强制停止失败:', error);
      throw error;
    }
  };

  return {
    // 核心执行方法
    executeImageGeneration,
    executeImageGenerationForQueue,

    // 控制方法
    cancelGeneration,
    forceStopComfyUITasks
  };
}
