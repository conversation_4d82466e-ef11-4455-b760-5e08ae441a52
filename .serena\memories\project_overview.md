# 项目概览

## 项目名称
txt2video - 有声漫画生成器

## 项目目的
这是一个基于Vue.js的现代化Web应用程序，可以创建有声漫画，支持网页端和桌面端（通过Electron）使用。主要功能包括：
- 创建和管理有声漫画项目
- 上传音频文件和SRT字幕文件
- 自动生成漫画风格的图像，配合音频创建有声漫画
- 支持导出最终视频作品
- 跨平台支持：Web端和桌面端（Windows、macOS、Linux）

## 技术栈
- **前端**: Vue.js 3 + Vue Router
- **后端**: Express.js + Node.js
- **桌面端**: Electron
- **样式**: 自定义CSS（支持暗色主题）
- **图标**: Remix Icon
- **构建工具**: Vue CLI
- **包管理**: npm
- **AI集成**: ComfyUI（图像生成）、多种LLM提供商（文本处理）

## 系统要求
- Node.js 16.x 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge等）
- 用于桌面版本：Windows 10+, macOS 10.14+, 或 Linux