/**
 * III. 核心模块: MessageParser
 * 
 * 职责：
 * - 接收来自 WebSocketManager 的原始JSON字符串消息
 * - 安全地解析JSON
 * - 根据消息的 type 字段，将其转换为结构化的内部事件对象或直接调用相应的处理器
 */

export class MessageParser {
  constructor() {
    console.log('📨 [MessageParser] 初始化完成');
  }

  /**
   * 解析并分发消息
   * @param {string|Blob} rawMessage - 原始消息字符串或Blob
   * @param {Object} handlers - 处理器对象
   */
  async parseAndDispatch(rawMessage, handlers = {}) {
    try {
      let messageText;

      // 处理不同类型的消息
      if (rawMessage instanceof Blob) {
        console.log('📨 [MessageParser] 处理Blob消息，大小:', rawMessage.size);

        // 将Blob转换为文本
        messageText = await this.blobToText(rawMessage);

        // 如果Blob是空的或者不是文本数据，跳过处理
        if (!messageText || messageText.trim() === '') {
          console.log('📨 [MessageParser] Blob消息为空，跳过处理');
          return;
        }
      } else if (typeof rawMessage === 'string') {
        messageText = rawMessage;
      } else {
        console.warn('⚠️ [MessageParser] 未知的消息类型:', typeof rawMessage);
        return;
      }

      // 解析JSON消息
      const message = JSON.parse(messageText);

      if (!message.type) {
        console.warn('⚠️ [MessageParser] 消息缺少type字段:', message);
        return;
      }

      // 🔧 简洁日志：只显示关键消息类型，忽略进度消息
      if (['executed', 'execution_error', 'execution_interrupted'].includes(message.type)) {
        console.log(`📨 [MessageParser] 处理关键消息: ${message.type}`);
      }
      // 进度和状态消息不显示日志，保持控制台安静

      // 根据消息类型分发
      switch (message.type) {
        case 'status':
          this.handleStatusMessage(message, handlers);
          break;
        case 'execution_start':
          this.handleExecutionStart(message, handlers);
          break;
        case 'executing':
          this.handleExecuting(message, handlers);
          break;
        case 'progress':
          this.handleProgress(message, handlers);
          break;
        case 'executed':
          console.log('🎯 [MessageParser] 节点执行完成:', message.data?.prompt_id);
          this.handleExecuted(message, handlers);
          break;
        case 'execution_cached':
          this.handleExecutionCached(message, handlers);
          break;
        case 'execution_error':
          this.handleExecutionError(message, handlers);
          break;
        case 'execution_interrupted':
          this.handleExecutionInterrupted(message, handlers);
          break;
        case 'preview':
          this.handlePreview(message, handlers);
          break;
        // 🆕 新增错误状态处理
        case 'server_shutdown':
          this.handleServerShutdown(message, handlers);
          break;
        case 'out_of_memory':
          this.handleOutOfMemory(message, handlers);
          break;
        case 'user_cancelled':
          this.handleUserCancelled(message, handlers);
          break;
        case 'connection_lost':
          this.handleConnectionLost(message, handlers);
          break;
        case 'queue_full':
          this.handleQueueFull(message, handlers);
          break;
        default:
          // 🆕 增强的未知消息处理
          this.handleUnknownMessage(message, handlers);
      }

    } catch (error) {
      console.error('❌ [MessageParser] 消息解析失败:', error);
      console.error('❌ [MessageParser] 原始消息类型:', typeof rawMessage);
      if (typeof rawMessage === 'string') {
        console.error('❌ [MessageParser] 原始消息内容:', rawMessage.substring(0, 500));
      }
    }
  }

  /**
   * 将Blob转换为文本
   * @param {Blob} blob - Blob对象
   * @returns {Promise<string>} 文本内容
   */
  async blobToText(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        resolve(reader.result);
      };

      reader.onerror = () => {
        reject(new Error('读取Blob失败'));
      };

      reader.readAsText(blob);
    });
  }

  /**
   * 处理状态消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleStatusMessage(message, handlers) {
    if (handlers.onStatusUpdate) {
      handlers.onStatusUpdate(message.data);
    }
  }

  /**
   * 处理执行开始消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleExecutionStart(message, handlers) {
    if (handlers.onExecutionStart && message.data) {
      handlers.onExecutionStart(message.data);
    }
  }

  /**
   * 处理节点执行消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleExecuting(message, handlers) {
    if (handlers.onExecuting && message.data) {
      handlers.onExecuting(message.data);
    }
  }

  /**
   * 处理进度消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleProgress(message, handlers) {
    if (handlers.onProgress && message.data) {
      handlers.onProgress(message.data);
    }
  }

  /**
   * 处理节点执行完成消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleExecuted(message, handlers) {
    console.log('🎯 [MessageParser] 收到executed消息:', message);
    if (handlers.onExecuted && message.data) {
      console.log('🎯 [MessageParser] 调用onExecuted处理器:', message.data);
      handlers.onExecuted(message.data);
    } else {
      console.warn('⚠️ [MessageParser] onExecuted处理器未定义或消息数据为空');
    }
  }

  /**
   * 处理缓存执行消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleExecutionCached(message, handlers) {
    if (handlers.onExecutionCached && message.data) {
      handlers.onExecutionCached(message.data);
    }
  }

  /**
   * 处理执行错误消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleExecutionError(message, handlers) {
    if (handlers.onExecutionError && message.data) {
      handlers.onExecutionError(message.data);
    }
  }

  /**
   * 处理执行中断消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleExecutionInterrupted(message, handlers) {
    if (handlers.onExecutionInterrupted && message.data) {
      handlers.onExecutionInterrupted(message.data);
    }
  }

  /**
   * 处理预览消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handlePreview(message, handlers) {
    if (handlers.onPreview && message.data) {
      handlers.onPreview(message.data);
    }
  }

  /**
   * 🆕 处理服务器关闭消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleServerShutdown(message, handlers) {
    console.warn('🛑 [MessageParser] ComfyUI服务器正在关闭');
    if (handlers.onServerShutdown) {
      handlers.onServerShutdown(message.data);
    }
  }

  /**
   * 🆕 处理内存不足消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleOutOfMemory(message, handlers) {
    console.error('💾 [MessageParser] ComfyUI内存不足');
    if (handlers.onOutOfMemory) {
      handlers.onOutOfMemory(message.data);
    }
  }

  /**
   * 🆕 处理用户取消消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleUserCancelled(message, handlers) {
    console.log('🚫 [MessageParser] 用户取消了任务');
    if (handlers.onUserCancelled) {
      handlers.onUserCancelled(message.data);
    }
  }

  /**
   * 🆕 处理连接丢失消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleConnectionLost(message, handlers) {
    console.error('🔌 [MessageParser] 与ComfyUI的连接丢失');
    if (handlers.onConnectionLost) {
      handlers.onConnectionLost(message.data);
    }
  }

  /**
   * 🆕 处理队列满消息
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleQueueFull(message, handlers) {
    console.warn('📋 [MessageParser] ComfyUI队列已满');
    if (handlers.onQueueFull) {
      handlers.onQueueFull(message.data);
    }
  }

  /**
   * 🆕 处理未知消息类型
   * @param {Object} message - 消息对象
   * @param {Object} handlers - 处理器对象
   */
  handleUnknownMessage(message, handlers) {
    console.log(`📨 [MessageParser] 未处理的消息类型: ${message.type}`, message);

    // 🔧 特别检查是否是遗漏的executed消息
    if (message.type === 'executed' || (message.data && message.data.output)) {
      console.log('🚨🚨🚨 [MessageParser] 发现遗漏的EXECUTED消息在未知类型中！', message);
      this.handleExecuted(message, handlers);
      return;
    }

    // 🆕 智能错误检测 - 分析消息内容寻找错误模式
    const errorPatterns = this.detectErrorPatterns(message);
    if (errorPatterns.length > 0) {
      console.warn('🔍 [MessageParser] 检测到潜在错误模式:', errorPatterns);
      if (handlers.onPotentialError) {
        handlers.onPotentialError({
          message,
          detectedPatterns: errorPatterns
        });
      }
    }

    // 通用未知消息处理
    if (handlers.onUnknownMessage) {
      handlers.onUnknownMessage(message);
    }
  }

  /**
   * 🆕 检测消息中的错误模式
   * @param {Object} message - 消息对象
   * @returns {Array} 检测到的错误模式
   */
  detectErrorPatterns(message) {
    const patterns = [];
    const messageStr = JSON.stringify(message).toLowerCase();

    // 内存相关错误
    if (messageStr.includes('out of memory') ||
        messageStr.includes('cuda out of memory') ||
        messageStr.includes('memory error') ||
        messageStr.includes('allocation failed')) {
      patterns.push('memory_error');
    }

    // 模型加载错误
    if (messageStr.includes('model not found') ||
        messageStr.includes('checkpoint') ||
        messageStr.includes('failed to load')) {
      patterns.push('model_error');
    }

    // 连接错误
    if (messageStr.includes('connection') ||
        messageStr.includes('timeout') ||
        messageStr.includes('network')) {
      patterns.push('connection_error');
    }

    // 中断/取消
    if (messageStr.includes('interrupt') ||
        messageStr.includes('cancel') ||
        messageStr.includes('abort')) {
      patterns.push('interruption');
    }

    // 权限错误
    if (messageStr.includes('permission') ||
        messageStr.includes('access denied') ||
        messageStr.includes('unauthorized')) {
      patterns.push('permission_error');
    }

    return patterns;
  }

  /**
   * 验证消息格式
   * @param {Object} message - 消息对象
   * @returns {boolean} 是否有效
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || typeof message.type !== 'string') {
      return false;
    }

    return true;
  }

  /**
   * 提取消息中的prompt_id
   * @param {Object} message - 消息对象
   * @returns {string|null} prompt_id 或 null
   */
  extractPromptId(message) {
    if (message.data && message.data.prompt_id) {
      return message.data.prompt_id;
    }
    return null;
  }

  /**
   * 获取支持的消息类型列表
   * @returns {Array<string>} 支持的消息类型
   */
  getSupportedMessageTypes() {
    return [
      'status',
      'execution_start',
      'executing',
      'progress',
      'executed',
      'execution_cached',
      'execution_error',
      'execution_interrupted',
      'preview'
    ];
  }
}
