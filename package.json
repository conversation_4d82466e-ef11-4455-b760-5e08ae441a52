{"name": "txt2video", "version": "1.0.0", "private": true, "description": "有声漫画生成器", "author": "", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:build": "vue-cli-service electron:build", "electron:serve": "vue-cli-service electron:serve", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps", "stop": "npx kill-port 8080 8089 8091", "dev": "nodemon src/server.js", "start": "node src/server.js", "dev-full": "concurrently \"npm run start\" \"npm run serve\"", "clean": "npx kill-port 8080 8089 8091 && echo 所有端口已清理", "restart": "npm run clean && npm run dev-full"}, "main": "src/background.js", "dependencies": {"core-js": "^3.8.3", "cors": "^2.8.5", "express": "^4.21.2", "http-proxy-middleware": "^2.0.9", "multer": "^1.4.5-lts.1", "vue": "^3.2.13", "vue-router": "^4.5.1", "ws": "^8.18.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "concurrently": "^9.1.2", "electron": "^13.0.0", "electron-devtools-installer": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "iconv-lite": "^0.6.3", "kill-port": "^2.0.1", "nodemon": "^3.0.2", "vue-cli-plugin-electron-builder": "~2.1.1"}, "eslintConfig": {"root": true, "env": {"node": true, "browser": true, "vue/setup-compiler-macros": true}, "extends": ["plugin:vue/vue3-strongly-recommended", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false, "ecmaVersion": "latest", "sourceType": "module"}, "rules": {"vue/multi-word-component-names": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "_id": "txt2video@1.0.0", "keywords": ["有声漫画", "文本转视频", "AI"], "license": "MIT", "readme": "ERROR: No README data found!"}