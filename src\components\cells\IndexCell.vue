<template>
  <div class="grid-cell index-cell">
    <div class="cell-content">
      {{ rowIndex + 1 }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndexCell',
  props: {
    rowIndex: {
      type: Number,
      required: true
    }
  }
}
</script>

<style scoped>
.grid-cell {
  /* 🔧 强制保持表格单元格布局 */
  display: table-cell !important;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
  /* 🔧 防止布局干扰 */
  float: none !important;
  position: static !important;
  /* 🔧 移除强制宽度设置，让列宽度规则生效 */
  /* width: auto !important; 注释掉，让fit-content生效 */
}

.index-cell {
  /* 🔧 改为内容自适应宽度，节省空间 */
  width: fit-content;
  min-width: fit-content;
  max-width: 80px;
}

.cell-content {
  width: 100%;
  height: 100%;
  /* 🔧 修复：移除flex布局，使用简单的文本对齐 */
  background-color: #252525;
  font-size: 0.9rem;
  color: #999;
  font-weight: 500;
  padding-top: 8px;
  text-align: center;
  /* 🔧 确保内容不会影响表格单元格布局 */
  box-sizing: border-box;
}

/* 🔧 强制布局稳定性媒体查询 */
@media (max-width: 768px) {
  .grid-cell {
    display: table-cell !important;
    float: none !important;
    position: static !important;
  }

  .cell-content {
    text-align: center !important;
  }
}

@media (max-width: 480px) {
  .cell-content {
    font-size: 0.8rem;
    padding-top: 6px;
  }
}
</style>



 




 
