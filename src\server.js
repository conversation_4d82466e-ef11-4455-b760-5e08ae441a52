const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const http = require('http');
const WebSocket = require('ws');
const localCommunication = require('./server/localCommunication');
const comfyuiRoutes = require('./server/comfyuiRoutes');

const app = express();
const server = http.createServer(app);

// 启用跨域
app.use(cors());

// 解析 JSON 请求体，增加大小限制以支持大型项目数据
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// 添加简单的健康检查接口
app.get('/api/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 挂载本地通信路由
app.use('/api/local', localCommunication);

// 挂载ComfyUI路由
app.use('/api/local/comfyui', comfyuiRoutes);

// 全局错误处理
// eslint-disable-next-line no-unused-vars
app.use((err, req, res, next) => {
  console.error('全局错误:', err);
  res.status(500).json({ error: err.message || '服务器内部错误' });
});

// 使用8091端口，与前端配置匹配
const PORT = process.env.PORT || 8091;

// 🆕 简化的WebSocket代理 - 使用ws库的内置服务器
const wss = new WebSocket.Server({
  server: server,
  path: '/api/local/comfyui-ws'
});

wss.on('connection', (clientWs, request) => {
  console.log('🔌 [WebSocket代理] 客户端连接:', request.url);

  // 提取clientId参数
  const url = new URL(request.url, 'http://localhost');
  const clientId = url.searchParams.get('clientId');

  if (!clientId) {
    console.error('❌ [WebSocket代理] 缺少clientId参数');
    clientWs.close(1008, 'Missing clientId parameter');
    return;
  }

  // 创建到ComfyUI的WebSocket连接
  const comfyuiWsUrl = `ws://localhost:8188/ws?clientId=${clientId}`;
  console.log('🔌 [WebSocket代理] 连接到ComfyUI:', comfyuiWsUrl);

  const comfyuiWs = new WebSocket(comfyuiWsUrl);

  comfyuiWs.on('open', () => {
    console.log('✅ [WebSocket代理] ComfyUI连接成功');
  });

  comfyuiWs.on('message', (data) => {
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(data);
    }
  });

  comfyuiWs.on('close', (code, reason) => {
    console.log('🔌 [WebSocket代理] ComfyUI连接关闭:', code, reason.toString());
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.close(code, reason);
    }
  });

  comfyuiWs.on('error', (error) => {
    console.error('❌ [WebSocket代理] ComfyUI连接错误:', error.message);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.close(1011, 'ComfyUI connection error');
    }
  });

  // 客户端到ComfyUI的消息转发
  clientWs.on('message', (data) => {
    if (comfyuiWs.readyState === WebSocket.OPEN) {
      comfyuiWs.send(data);
    }
  });

  clientWs.on('close', (code, reason) => {
    console.log('🔌 [WebSocket代理] 客户端连接关闭:', code, reason);
    comfyuiWs.close();
  });

  clientWs.on('error', (error) => {
    console.error('❌ [WebSocket代理] 客户端连接错误:', error.message);
    comfyuiWs.close();
  });
});

// 启动服务器（支持WebSocket）
server.listen(PORT, () => {
  console.log(`===========================================`);
  console.log(`Server running at http://localhost:${PORT}`);
  console.log(`Backend API available at /api/local/*`);
  console.log(`WebSocket proxy available at ws://localhost:${PORT}/api/local/comfyui-ws`);
  console.log(`===========================================`);
});

// 优雅关闭处理
function gracefulShutdown(signal) {
  console.log(`\n收到 ${signal} 信号，开始优雅关闭...`);
  
  // 关闭WebSocket服务器
  wss.close(() => {
    console.log('WebSocket服务器已关闭');
  });
  
  // 关闭HTTP服务器
  server.close(() => {
    console.log('HTTP服务器已关闭');
    process.exit(0);
  });
  
  // 强制退出超时
  setTimeout(() => {
    console.error('强制退出：优雅关闭超时');
    process.exit(1);
  }, 10000);
}

// 监听退出信号
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
  gracefulShutdown('uncaughtException');
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, _promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  gracefulShutdown('unhandledRejection');
});