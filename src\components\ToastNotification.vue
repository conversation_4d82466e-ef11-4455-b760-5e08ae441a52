<template>
  <transition name="toast-fade">
    <div
      v-if="isVisible"
      class="toast-container"
      :class="displayType"
    >
      <div class="toast-icon">
        <i
          class="ri-error-warning-fill"
          v-if="displayType === 'error'"
        />
        <i
          class="ri-checkbox-circle-fill"
          v-else-if="displayType === 'success'"
        />
        <i
          class="ri-information-fill"
          v-else
        />
      </div>
      <div class="toast-content">
        <div class="toast-title">
          {{ displayTitle }}
        </div>
        <div class="toast-message">
          {{ displayMessage }}
        </div>
      </div>
      <div
        class="toast-close"
        @click="hide"
      >
        <i class="ri-close-line" />
      </div>
      <div class="toast-progress">
        <div
          class="progress-bar"
          :style="{width: progressWidth}"
        />
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['info', 'success', 'error', 'warning'].includes(value)
    },
    duration: {
      type: Number,
      default: 3000
    }
  },
  data() {
    return {
      isVisible: false,
      progressWidth: '100%',
      timer: null,
      progressTimer: null,
      // 🆕 用于直接显示消息的数据
      currentTitle: '',
      currentMessage: '',
      currentType: 'info'
    }
  },
  computed: {
    // 🆕 动态计算显示的内容
    displayTitle() {
      return this.currentTitle || this.title;
    },
    displayMessage() {
      return this.currentMessage || this.message;
    },
    displayType() {
      return this.currentType || this.type;
    }
  },
  methods: {
    show() {
      // 🔍 调试props值和计算属性值
      console.log('🔔 [ToastNotification] show()被调用，props和计算属性:', {
        propsTitle: this.title,
        propsMessage: this.message,
        propsType: this.type,
        displayTitle: this.displayTitle,
        displayMessage: this.displayMessage,
        displayType: this.displayType,
        hasDisplayTitle: !!this.displayTitle,
        hasDisplayMessage: !!this.displayMessage
      });

      // 🆕 使用计算属性检查是否有有效的消息内容
      if (!this.displayTitle && !this.displayMessage) {
        console.warn('ToastNotification: 尝试显示空消息，已忽略', {
          displayTitle: this.displayTitle,
          displayMessage: this.displayMessage,
          propsTitle: this.title,
          propsMessage: this.message
        });
        return;
      }

      console.log('🔔 [ToastNotification] 显示Toast通知');
      this.isVisible = true
      this.startTimer()
    },

    // 🆕 直接显示消息的方法，不依赖props
    showMessage(options) {
      console.log('🔔 [ToastNotification] showMessage()被调用:', options);

      if (!options || (!options.title && !options.message)) {
        console.warn('ToastNotification: showMessage缺少有效参数', options);
        return;
      }

      // 直接使用传入的参数，不依赖props
      this.currentTitle = options.title || '';
      this.currentMessage = options.message || '';
      this.currentType = options.type || 'info';

      console.log('🔔 [ToastNotification] 直接显示消息:', {
        title: this.currentTitle,
        message: this.currentMessage,
        type: this.currentType
      });

      this.isVisible = true;
      this.startTimer();
    },
    hide() {
      this.isVisible = false
      this.clearTimer()
    },
    startTimer() {
      this.clearTimer()
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          this.hide()
        }, this.duration)
        
        const startTime = Date.now()
        const endTime = startTime + this.duration
        const updateProgress = () => {
          const now = Date.now()
          if (now >= endTime) {
            this.progressWidth = '0%'
            return
          }
          const remaining = endTime - now
          this.progressWidth = `${(remaining / this.duration) * 100}%`
          this.progressTimer = requestAnimationFrame(updateProgress)
        }
        this.progressTimer = requestAnimationFrame(updateProgress)
      }
    },
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      if (this.progressTimer) {
        cancelAnimationFrame(this.progressTimer)
        this.progressTimer = null
      }
    }
  },
  beforeUnmount() {
    this.clearTimer()
  }
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 300px;
  max-width: 400px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: flex-start;
  z-index: 9999;
  transition: all 0.3s ease;
}

.toast-icon {
  margin-right: 12px;
  font-size: 20px;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.toast-message {
  font-size: 14px;
  color: #666;
}

.toast-close {
  margin-left: 12px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.toast-close:hover {
  opacity: 1;
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.progress-bar {
  height: 100%;
  background: #1890ff;
  transition: width 0.1s linear;
}

.success .toast-icon {
  color: #52c41a;
}

.error .toast-icon {
  color: #f5222d;
}

.warning .toast-icon {
  color: #faad14;
}

.info .toast-icon {
  color: #1890ff;
}

.toast-fade-enter-active,
.toast-fade-leave-active {
  transition: all 0.3s ease;
}

.toast-fade-enter-from,
.toast-fade-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
