# 建议的开发命令

## 🚀 启动和开发命令

### 开发环境
```bash
# 一键启动前端和后端服务（推荐）
npm run dev-full

# 或分开启动：
npm run start      # 启动后端API服务（端口8091）
npm run serve      # 启动前端开发服务器（端口8080）

# 仅启动后端开发模式（使用nodemon）
npm run dev
```

### 桌面应用（Electron）
```bash
# 开发模式运行桌面应用
npm run electron:serve

# 构建桌面应用
npm run electron:build
```

## 🔧 构建和部署
```bash
# 构建Web应用
npm run build

# 构建桌面应用
npm run electron:build
```

## 🧹 维护和清理
```bash
# 清理所有端口（8080, 8089, 8091）
npm run clean

# 停止特定端口
npm run stop

# 重启所有服务
npm run restart
```

## 📦 包管理
```bash
# 安装依赖
npm install

# 安装Electron应用依赖
npm run postinstall
```

## 🔍 代码质量
```bash
# 运行ESLint检查
npm run lint
```

## 🛠️ 系统工具命令（Windows）
```cmd
# 查看端口占用
netstat -ano | findstr :8080
netstat -ano | findstr :8091

# 杀死进程
taskkill /PID <进程ID> /F

# 查看目录
dir
cd <目录名>

# 查找文件
where <文件名>
findstr /s /i "搜索内容" *.js
```

## 🎯 常用开发流程
1. `npm run dev-full` - 启动完整开发环境
2. 在浏览器访问 http://localhost:8080
3. 修改代码后自动热重载
4. `npm run clean` - 清理端口（如遇到端口冲突）
5. `npm run lint` - 检查代码质量