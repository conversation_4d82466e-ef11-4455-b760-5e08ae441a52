<template>
  <div class="home-page">
    <section class="hero-section">
      <div class="gradient-bar">
        <button
          class="create-button"
          @click="navigateToCreation"
        >
          <i class="ri-add-line" />
          <span>开始创建</span>
        </button>
      </div>
    </section>

    <!-- 功能按钮卡片区域 -->
    <section class="feature-cards-section">
      <div class="feature-cards">
        <div
          class="feature-card"
          v-for="feature in features"
          :key="feature.title"
          @click="handleFeatureClick(feature)"
        >
          <i :class="feature.icon" />
          <div class="feature-title">
            {{ feature.title }}
          </div>
          <div class="feature-desc">
            {{ feature.desc }}
          </div>
        </div>
      </div>
    </section>

    <!-- 项目列表 -->
    <ProjectList
      :projects="projects"
      @select="handleProjectSelect"
      @delete="handleProjectDelete"
      @update="loadProjects"
    />

    <!-- 创建项目弹窗 -->
    <ProjectCreateDialog
      v-model="showDialog"
      @created="loadProjects"
    />

    <!-- 章节管理抽屉 -->
    <BaseDrawer
      v-model:show="showDrawer"
      :drawer-width="'50vw'"
    >
      <ChapterManager
        v-if="currentProject"
        :project="currentProject"
        @update:project="currentProject = $event"
        @navigate="handleNavigate"
      />
    </BaseDrawer>
  </div>
</template>

<script>
import { ref } from 'vue';
import BaseDrawer from './BaseDrawer.vue';
import ProjectList from './ProjectList.vue';
import ProjectCreateDialog from './ProjectCreateDialog.vue';
import ChapterManager from './ChapterManager.vue';
import { useLocalCommunication } from '@/utils/localCommunication';

export default {
  name: 'HomePage',

  emits: ['navigate'],

  components: {
    BaseDrawer,
    ProjectList,
    ProjectCreateDialog,
    ChapterManager
  },

  setup(props, { emit }) {
    const { listFolders, deleteFolder } = useLocalCommunication();

    const showDialog = ref(false);
    const showDrawer = ref(false);
    const projects = ref([]);
    const currentProject = ref(null);

    // 功能卡片数据
    const features = [
      {
        icon: 'ri-megaphone-line',
        title: '邀请返利',
        desc: '邀请好友使用软件，获取现金奖励'
      },
      {
        icon: 'ri-megaphone-line',
        title: '配音/充值',
        desc: '低门槛，超高返，秒级支付'
      },
      {
        icon: 'ri-megaphone-line',
        title: '角色预设',
        desc: '快速固定角色'
      },
      {
        icon: 'ri-megaphone-line',
        title: '下载视频',
        desc: '下载非官方无水印视频'
      },
      {
        icon: 'ri-megaphone-line',
        title: '日志/公告',
        desc: '软件的更新记录'
      }
    ];

    // 加载项目列表
    async function loadProjects() {
      try {
        const folders = await listFolders('draft');
        projects.value = folders
          .filter(name => name !== 'draft')
          .map(name => ({
            title: name,
            date: '',
            chapters: []
          }));
      } catch (e) {
        console.error('加载项目失败:', e);
        projects.value = [];
      }
    }

    // 选择项目
    function handleProjectSelect(project) {
      currentProject.value = project;
      showDrawer.value = true;
    }

    // 删除项目
    async function handleProjectDelete(title) {
      if (title === 'draft') {
        alert('不能删除顶层目录 draft');
        return;
      }
      if (!confirm(`确定要删除文件夹"${title}"吗？`)) return;

      try {
        await deleteFolder(title);
        await loadProjects();
      } catch (error) {
        alert('删除失败: ' + error.message);
      }
    }

    // 处理导航事件的方法
    function handleNavigate(view, data) {
      console.log('HomePage接收到导航事件', view, data?.title);

      // 关闭当前抽屉
      showDrawer.value = false;

      // 转发导航事件给父组件(App.vue)
      emit('navigate', view, data);
    }

    // 初始加载
    loadProjects();

    // 创建项目方法
    function navigateToCreation() {
      showDialog.value = true;
    }

    // 处理功能卡片点击
    async function handleFeatureClick(feature) {
      console.log('🔥 功能卡片被点击:', feature.title);
      // 功能卡片点击处理逻辑
    }

    return {
      showDialog,
      showDrawer,
      projects,
      currentProject,
      features,
      loadProjects,
      handleProjectSelect,
      handleProjectDelete,
      handleNavigate,
      navigateToCreation,
      handleFeatureClick
    };
  }
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: #181825;
}

.hero-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.2rem;
}

.gradient-bar {
  width: 100%;
  max-width: 85vw;
  margin: 0 auto;
  padding: 1.2rem 0;
  border-radius: 12px;
  background: linear-gradient(90deg, #29908b 0%, #a23a5a 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 24px 0 #00000022;
}

.create-button {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  background: #fff;
  color: #22223b;
  border: none;
  border-radius: 8px;
  padding: 0.7rem 2.2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 #22223b22;
  transition: all 0.18s;
  letter-spacing: 1px;
}

.create-button i {
  font-size: 1.3em;
}

.create-button:hover {
  background: #f5c2e7;
  color: #a23a5a;
  transform: scale(1.04);
  box-shadow: 0 8px 32px 0 #a23a5a33;
}

.create-button span {
  animation: blink-text 1s steps(2, start) infinite;
}

@keyframes blink-text {
  to {
    color: transparent;
  }
}

/* 功能卡片区 */
.feature-cards-section {
  padding: 1rem 2rem;
}

.feature-cards {
  max-width: 85vw;
  margin: 1rem auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: #232136;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 24px 0 #00000022;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 32px 0 #00000033;
}

.feature-card i {
  color: #9ccfd8;
  font-size: 2.2rem;
  margin-bottom: 1rem;
  pointer-events: none;
}

.feature-title {
  color: #e0def4;
  font-size: 1.08rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
  pointer-events: none;
}

.feature-desc {
  color: #7dc4e4;
  font-size: 0.95rem;
  font-weight: 400;
  pointer-events: none;
}
</style>

