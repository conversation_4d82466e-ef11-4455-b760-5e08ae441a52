<template>
  <div
    class="image-delete-button"
    v-if="showDelete"
  >
    <button
      class="delete-btn"
      @click.stop="handleDelete"
      :title="deleteTitle"
    >
      <svg
        class="delete-icon"
        viewBox="0 0 24 24"
      >
        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
      </svg>
    </button>
  </div>
</template>

<script>
export default {
  name: 'ImageDeleteButton',
  emits: ['delete'],
  props: {
    showDelete: {
      type: Boolean,
      default: true
    },
    deleteTitle: {
      type: String,
      default: '删除图片'
    }
  },
  methods: {
    handleDelete(event) {
      // 阻止事件冒泡，避免触发父容器的点击事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      this.$emit('delete');
    }
  }
}
</script>

<style scoped>
.image-delete-button {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
}

.delete-btn {
  width: 20px;
  height: 20px;
  background: rgba(255, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

.delete-icon {
  width: 12px;
  height: 12px;
  fill: white;
}
</style>
