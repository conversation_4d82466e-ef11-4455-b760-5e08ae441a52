<template>
  <div class="excel-grid-layout">
    <slot />
  </div>
</template>

<script>
export default {
  name: 'GridLayout'
}
</script>

<style scoped>
.excel-grid-layout {
  display: table;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  background-color: #1a1a1a;
  table-layout: fixed;
  /* 🔧 确保表格在所有屏幕尺寸下保持横向布局 */
  min-width: 800px; /* 设置最小宽度防止过度压缩 */
}

/* 🔧 防止在小屏幕下表格布局变为垂直排列 */
@media (max-width: 768px) {
  .excel-grid-layout {
    /* 保持表格布局，不改为flex */
    display: table;
    table-layout: fixed;
    /* 允许水平滚动而不是垂直堆叠 */
    overflow-x: auto;
    min-width: 800px;
  }
}
</style> 