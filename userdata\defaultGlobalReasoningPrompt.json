{"prompt": ["# AI绘画角色造型提取指令 (最终版)", "", "## 任务目标：", "1.  **首要任务：理解文本的时代背景与题材。** AI需首先尝试根据文本的用词、描述、情节元素、环境氛围等，判断故事发生的时代背景（如：古代中国、中世纪欧洲、现代都市、近未来、远未来星际等）和主要题材（如：武侠、仙侠、玄幻、奇幻、科幻、言情、悬疑等）。", "2.  **角色信息提取与造型推断：** 从提供的小说文本片段中，识别并提取主要角色的人物信息。根据文本描述及推断出的时代背景与题材，推断或总结其视觉造型特征。", "3.  **完整性与明确性要求：**", "    *   当文本信息不足时，AI必须基于上下文、推断的时代背景、常见题材设定或合理想象，为所有必需的造型栏目（性别、年龄、发型、服装）提供一个具体、可绘画的描述。", "    *   **输出的每一个造型特征（性别、年龄、发型、服装等）都必须是单一且明确的。严禁使用'或'、'可能'、'也许'、'例如'、'比如'等词语给出多个选项或不确定的描述。AI必须在多种可能性中做出唯一选择。**", "    *   **绝不允许使用'未知'、'未提及'、'不详'等表示信息缺失的词语。**", "", "## 文本背景感知与应用规则：", "*   **分析判断**：AI需在处理角色前，先对文本片段进行整体分析，明确或尽可能推断出其时代背景和题材。", "*   **造型约束**：在推断角色的发型、服装时，**必须严格遵守推断出的时代背景和题材的常见设定与视觉风格。**", "*   **避免时代错乱**：**核心原则：除非文本明确提及穿越、特殊设定或有强烈的反差暗示，否则严禁为非现代背景角色设计典型的现代服装，或为东方古典背景角色设计纯西方特有的古典服饰（反之亦然，除非有明确的文化融合设定）。**", "*   **若背景模糊**：如果文本片段极短，难以明确判断背景，AI应选择一个基于文本中有限线索最有可能的背景进行推断，并在此基础上设计造型。", "", "## 输出格式、语言及最终呈现要求：", "**最终输出应仅为角色列表，不包含任何其他文字、标题或解释。**", "每个角色信息占一行，格式如下：", "[角色名字 (原文)] - [Gender (English)], [Age/Age Range (English)], [<PERSON>style (English)], [<PERSON><PERSON><PERSON> (English)]", "", "*   **角色名字 (Character Name)**：保持文本中给出的原始语言（例如，如果原文是中文名，则输出中文名；如果原文是英文名，则输出英文名）。", "*   **所有描述词 (Gender, Age, Hairstyle, Clothing)**：**必须全部使用英文表述，确保单一明确。**", "    *   Gender: e.g., Male, Female", "    *   Age/Age Range: e.g., approx. 20 years old, young adult, late teens, elderly", "    *   Hairstyle: e.g., black long straight hair, golden shoulder-length wavy hair, dark brown high ponytail", "    *   Clothing: e.g., dark blue long robe, white hanfu with light pink outer robe, black tight-fitting combat suit", "", "## 提取与推断规则：", "1.  **角色识别**：识别文本中明确提及的角色名字。", "2.  **性别判断 (Gender - English)**：优先根据名字、称谓或描述判断。**若无法明确判断，必须基于名字的常见性别联想或上下文氛围进行合理推测并指定一个英文性别描述。**", "3.  **年龄推断 (Age - English)**：优先使用文本明确信息。**若无明确信息，必须根据角色行为、称呼、对话风格或与其他角色的关系，推断一个符合其定位的具体英文年龄描述。**", "4.  **发型与发色 (Hairstyle - English)**：优先提取文本明确描述。**若信息不完整或缺失，必须结合推断的时代背景、题材、角色性别、年龄及可能的身份/职业，设定一个具体、视觉化、单一明确且使用英文描述的发型与发色组合。**", "5.  **服装与颜色 (Clothing - English)**：优先提取文本明确描述。**若信息不完整或缺失，必须结合推断的时代背景、题材、角色性别、年龄、可能的身份/职业及所处环境，设定一套具体、符合逻辑、单一明确且使用英文描述的服装与颜色。**", "6.  **核心原则：提供完整且可绘画的细节**：所有输出的造型描述都应该是具体、完整且可以直接用于AI绘画提示的。", "7.  **专注视觉特征（再次强调）**：**仅关注**可直接用于绘画的静态视觉描述。**严格排除**非直接视觉造型元素。", "8.  **一致性与区分度**：如果同时提取多个角色，在推断信息时，尽量使不同角色的造型具有一定的区分度（除非是制服等）。", "9.  **简洁性**：在保证信息完整和准确的前提下，尽量使描述简洁明了。", "", "## 示例：", "**输入文本片段1 (玄幻/仙侠背景暗示，中文名):**", "“紫云峰顶，一位白发老者凭虚御风，身旁跟随着两名弟子。左边的弟子名唤林轩，神情坚毅；右边的少女名为苏瑶，灵动可爱。”", "", "白发老者 - Male, approx. 75 years old, snow-white long hair and beard styled into a Daoist bun, grey wide-sleeved Daoist robe", "林轩 - Male, approx. 20 years old, black long hair tied into a high ponytail with a cyan ribbon, cyan sect-uniform style long-sleeved shirt with fitted sleeves", "苏瑶 - Female, approx. 16 years old, jet-black long hair styled into double hanging buns adorned with a light-colored hairband, light pink cross-collared Hanfu dress", "", "**输入文本片段2 (近未来科幻背景暗示，英文代号):**", "“Neon-lit streets hummed as 'Night Owl', a lone operative, moved through the shadows. His target: the cyborg 'Iron Fist', who was striding into an underground bar.”", "", "Night Owl - Male, approx. 30 years old, black short choppy hair concealed by a dark grey hood, black high-tech material tactical trench coat over a dark tight-fitting undersuit", "Iron Fist - Male, approx. 35 years old (appearance might be altered by cybernetics), silver-grey short crew-cut hair with a visible metallic implant on the right temple, upper body largely exposed showing dark cyan mechanical prosthetics and metallic endoskeleton, wearing worn-out dark khaki cargo pants", "", "---", "", "请根据以上规则，处理我接下来提供的文本。务必先分析文本的时代背景和题材，并在此基础上进行角色的造型推断与补全。确保角色名字使用原文，所有造型描述（性别、年龄、发型、服装）都使用英文，并且是具体、可绘画、单一明确且符合背景的描述。", "**最终输出时，只给出角色列表，每行一个角色及其描述，不添加任何额外文字或解释。**", "请根据以上规则，处理我接下来提供的文本：\n[TEXT_PLACEHOLDER]"]}