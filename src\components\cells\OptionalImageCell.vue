<template>
  <div class="grid-cell optional-image-cell">
    <div class="optional-wrapper" :class="{ 'locked-row': isLocked }">
      <div class="thumbnails-container">
        <div class="thumbnails-grid">
          <!-- 显示实际的缩略图 -->
          <div
            class="image-item"
            v-for="(thumbnail, index) in displayThumbnails"
            :key="index"
            :class="{ 'disabled': isLocked }"
            @click="selectThumbnail(index, $event)"
            @contextmenu.prevent="showPreview(thumbnail)"
            :title="isLocked ? '该行已锁定，无法操作图片' : '点击选择缩略图'"
          >
            <div
              v-if="thumbnail && thumbnail.src"
              class="thumbnail-container"
            >
              <img
                :src="thumbnail.src"
                :alt="thumbnail.alt || `缩略图${index + 1}`"
                class="thumbnail-image"
              >
              <!-- 删除按钮 -->
              <ImageDeleteButton
                :show-delete="!isLocked"
                @delete="handleDeleteThumbnail(index)"
              />
            </div>
            <div
              v-else
              class="placeholder-text"
            >
              空位{{ index + 1 }}
            </div>
            <div class="sequence-badge">
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="vertical-manage-btn"
        :class="{ 'disabled': isLocked }"
        @click="manageImages"
        :title="isLocked ? '该行已锁定，无法管理图片' : '管理图片'"
      >
        <div class="vertical-text">
          管理图片
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog
      :visible="showDeleteConfirm"
      :message="deleteMessage"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />

    <!-- 图片预览模态框 -->
    <ImagePreviewModal
      :visible="showPreviewModal"
      :image-src="previewImageSrc"
      :image-alt="previewImageAlt"
      @close="closePreview"
    />
  </div>
</template>

<script>
import ImageDeleteButton from '../common/ImageDeleteButton.vue';
import DeleteConfirmDialog from '../common/DeleteConfirmDialog.vue';
import ImagePreviewModal from '../common/ImagePreviewModal.vue';

export default {
  name: 'OptionalImageCell',
  components: {
    ImageDeleteButton,
    DeleteConfirmDialog,
    ImagePreviewModal
  },
  emits: {
    'select-thumbnail': (index) => typeof index === 'number',
    'manage-images': null,
    'delete-thumbnail': (index) => typeof index === 'number',
    'show-locked-message': null
  },
  props: {
    thumbnails: {
      type: Array,
      default: () => []
    },
    isLocked: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDeleteConfirm: false,
      showPreviewModal: false,
      deleteIndex: -1,
      previewImageSrc: '',
      previewImageAlt: ''
    };
  },
  computed: {
    displayThumbnails() {
      // 确保至少显示4个位置，如果缩略图少于4个，用null填充
      const minSlots = 4;
      const result = [...(this.thumbnails || [])];

      // 填充空位到最少4个
      while (result.length < minSlots) {
        result.push(null);
      }



      return result;
    },

    deleteMessage() {
      return `确定要删除第${this.deleteIndex + 1}张缩略图吗？`;
    }
  },

  methods: {
    selectThumbnail(index, event) {
      // 检查是否锁定
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }

      // 检查是否是删除按钮触发的事件，如果是则忽略
      if (event && event.target) {
        // 检查点击的是否是删除按钮或其子元素
        const deleteButton = event.target.closest('.image-delete-button');
        if (deleteButton) {
          console.log('🎨 [OptionalImageCell] 忽略删除按钮触发的选择事件');
          return;
        }
      }

      // 只有当缩略图存在时才触发选择
      if (this.thumbnails && this.thumbnails[index]) {
        console.log('🎨 [OptionalImageCell] selectThumbnail:', index);
        this.$emit('select-thumbnail', index);
      }
    },

    manageImages() {
      // 检查是否锁定
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }
      this.$emit('manage-images');
    },

    handleDeleteThumbnail(index) {
      // 检查是否锁定
      if (this.isLocked) {
        this.$emit('show-locked-message');
        return;
      }
      console.log('🗑️ [OptionalImageCell] 删除缩略图请求:', { index, thumbnailsLength: this.thumbnails?.length });

      // 严格验证索引和缩略图存在性
      if (this.thumbnails &&
          typeof index === 'number' &&
          index >= 0 &&
          index < this.thumbnails.length &&
          this.thumbnails[index] &&
          this.thumbnails[index].src) {

        console.log('🗑️ [OptionalImageCell] 确认删除缩略图:', {
          index,
          thumbnail: this.thumbnails[index]
        });

        this.deleteIndex = index;
        this.showDeleteConfirm = true;
      } else {
        console.warn('⚠️ [OptionalImageCell] 无效的删除请求:', {
          index,
          thumbnailsLength: this.thumbnails?.length,
          thumbnail: this.thumbnails?.[index]
        });
      }
    },

    confirmDelete() {
      this.showDeleteConfirm = false;
      if (this.deleteIndex >= 0) {
        this.$emit('delete-thumbnail', this.deleteIndex);
        this.deleteIndex = -1;
      }
    },

    cancelDelete() {
      this.showDeleteConfirm = false;
      this.deleteIndex = -1;
    },

    showPreview(thumbnail) {
      // 预览功能不受锁定限制，用户可以查看但不能修改
      if (thumbnail && thumbnail.src) {
        this.previewImageSrc = thumbnail.src;
        this.previewImageAlt = thumbnail.alt || '缩略图预览';
        this.showPreviewModal = true;
      }
    },

    closePreview() {
      this.showPreviewModal = false;
      this.previewImageSrc = '';
      this.previewImageAlt = '';
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.optional-image-cell {
  /* 🔧 可选图片列需要足够空间 */
  width: 25%;
  min-width: 180px;
}

.optional-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #252525;
  gap: 4px;
}

.thumbnails-container {
  flex: 1;
  overflow-y: auto;
  background-color: #1e1e1e;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #444 #222;
}

.thumbnails-container::-webkit-scrollbar {
  width: 6px;
}

.thumbnails-container::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.thumbnails-container::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 3px;
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: minmax(80px, 1fr);
  gap: 4px;
  padding: 4px;
  min-height: 200px;
}

.image-item {
  background-color: #111;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 80px;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #333;
}

.thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: #111;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.3);
  font-size: 11px;
  text-align: center;
  user-select: none;
}

.sequence-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #ff3d7f;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.vertical-manage-btn {
  width: 30px;
  background-color: #1e1e1e;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.vertical-manage-btn:hover {
  background-color: #2d2d2d;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  color: #ccc;
  font-size: 12px;
  letter-spacing: 2px;
  user-select: none;
}

/* 锁定状态样式 */
.optional-wrapper.locked-row {
  border: 2px solid #dc2626;
  border-radius: 4px;
  background-color: rgba(220, 38, 38, 0.1);
  position: relative;
}

.optional-wrapper.locked-row::before {
  content: '🔒';
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  background-color: #dc2626;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.image-item.disabled {
  cursor: not-allowed !important;
  opacity: 0.5;
}

.image-item.disabled:hover {
  background-color: #111 !important;
}

.vertical-manage-btn.disabled {
  cursor: not-allowed !important;
  opacity: 0.5;
  background-color: #1a1a1a !important;
}

.vertical-manage-btn.disabled:hover {
  background-color: #1a1a1a !important;
}

.vertical-manage-btn.disabled .vertical-text {
  color: #666 !important;
}
</style>