/**
 * 图像提示词推理功能
 * 整合场景数据、上下文信息和LLM通信，为单行内容生成图像提示词
 */

import { ref, computed } from 'vue';
import { useLLMService } from './useLLMService.js';
import { useUserImageSettings } from './useUserImageSettings.js';

/**
 * 图像提示词推理 composable
 * @returns {Object} 推理功能相关的方法和状态
 */
export function useImagePromptReasoning() {
  // LLM 服务
  const {
    isLoading,
    error,
    sendTextToLLMIndependent
  } = useLLMService();

  // 用户设置服务
  const userImageSettings = useUserImageSettings();

  // 内部状态
  const currentPromptData = ref(null);
  const showTestModal = ref(false);
  const fullPromptPreview = ref('');

  /**
   * 获取项目和章节信息
   * @returns {Object} 项目信息
   */
  function getProjectInfo() {
    const urlParams = new URLSearchParams(window.location.search);
    const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
    const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');
    
    return { projectTitle, chapterTitle };
  }

  /**
   * 加载 Current.json 数据
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @returns {Promise<Object>} Current.json 数据
   */
  async function loadCurrentData(projectTitle, chapterTitle) {
    try {
      const currentPath = `draft/${projectTitle}/${chapterTitle}/Current.json`;
      console.log(`[useImagePromptReasoning] 正在加载 Current.json: ${currentPath}`);

      const res = await fetch(`/api/local/read-file?path=${encodeURIComponent(currentPath)}&t=${Date.now()}`);
      if (!res.ok) {
        console.warn('[useImagePromptReasoning] Current.json 文件不存在或无法访问');
        return getDefaultCurrentData();
      }

      const response = await res.json();
      let data;
      
      if (response.success && response.content) {
        data = JSON.parse(response.content);
      } else if (response.eraBackground !== undefined || response.characters !== undefined) {
        data = response;
      } else {
        console.warn('[useImagePromptReasoning] 无效的API响应格式');
        return getDefaultCurrentData();
      }

      return {
        eraBackground: data.eraBackground || '',
        characters: data.characters || [],
        scenes: data.scenes || [],
        outlines: data.outlines || [],
        specialItems: data.specialItems || [],
        techniques: data.techniques || []
      };
    } catch (error) {
      console.error('[useImagePromptReasoning] 加载 Current.json 失败:', error);
      return getDefaultCurrentData();
    }
  }

  /**
   * 获取默认的 Current.json 数据结构
   * @returns {Object} 默认数据
   */
  function getDefaultCurrentData() {
    return {
      eraBackground: '',
      characters: [],
      scenes: [],
      outlines: [],
      specialItems: [],
      techniques: []
    };
  }

  /**
   * 获取图像提示词的 prompt 模板
   * @returns {Promise<string>} prompt 模板
   */
  async function getImagePromptTemplate() {
    try {
      const response = await fetch('/userdata/prompts.json');
      if (!response.ok) {
        throw new Error('无法加载 prompts.json');
      }
      
      const promptsData = await response.json();
      
      // 优先使用用户自定义的 prompt，否则使用默认的
      const imagePromptData = promptsData.imagePrompt || {};
      const userPrompt = imagePromptData.user;
      const defaultPrompt = imagePromptData.default;
      
      let promptLines = userPrompt || defaultPrompt || [];
      
      if (Array.isArray(promptLines)) {
        return promptLines.join('\n');
      }
      
      return promptLines || '';
    } catch (error) {
      console.error('[useImagePromptReasoning] 获取 prompt 模板失败:', error);
      // 返回一个基本的默认 prompt
      return `请根据以下信息生成图像提示词：

【总纲】：{synopsis}

【角色列表】：{characters}

【本组场景与时间】：{sceneAndTime}

【上下文】：{context}

【目标文本】：{targetText}

请生成一行优化过的、逗号分隔的英文ComfyUI关键词列表。`;
    }
  }

  /**
   * 获取指定行的上下文内容
   * @param {Array} rows - 所有行数据
   * @param {number} targetIndex - 目标行索引
   * @param {number} contextLines - 上下文行数，默认10行
   * @returns {string} 上下文内容
   */
  function getContextForRow(rows, targetIndex, contextLines = 10) {
    // 确保不超过总行数
    const maxContextLines = Math.min(contextLines, rows.length);

    // 计算理想的开始和结束位置
    const halfContext = Math.floor(maxContextLines / 2);
    let startIndex = targetIndex - halfContext;
    let endIndex = targetIndex + halfContext;

    // 如果是偶数行，目标行后面多一行
    if (maxContextLines % 2 === 0) {
      endIndex = targetIndex + halfContext - 1;
    }

    // 调整边界，确保获取足够的行数
    if (startIndex < 0) {
      // 如果开始位置小于0，向后扩展
      const deficit = -startIndex;
      startIndex = 0;
      endIndex = Math.min(rows.length - 1, endIndex + deficit);
    }

    if (endIndex >= rows.length) {
      // 如果结束位置超出范围，向前扩展
      const deficit = endIndex - (rows.length - 1);
      endIndex = rows.length - 1;
      startIndex = Math.max(0, startIndex - deficit);
    }

    // 最终确保获取的行数不超过设定值
    const actualLines = endIndex - startIndex + 1;
    if (actualLines > maxContextLines) {
      // 如果获取的行数过多，优先保持目标行居中
      const excess = actualLines - maxContextLines;
      const frontCut = Math.floor(excess / 2);
      const backCut = excess - frontCut;
      startIndex += frontCut;
      endIndex -= backCut;
    }

    const contextRows = rows.slice(startIndex, endIndex + 1);

    console.log(`[getContextForRow] 目标行: ${targetIndex + 1}, 设定行数: ${contextLines}, 实际行数: ${contextRows.length}, 范围: ${startIndex + 1}-${endIndex + 1}`);

    return contextRows.map((row, index) => {
      const actualIndex = startIndex + index;
      const marker = actualIndex === targetIndex ? '**' : '';
      return `${actualIndex + 1}. ${marker}${row.description || ''}${marker}`;
    }).join('\n');
  }

  /**
   * 查找与目标行相关的场景信息
   * @param {Array} scenes - 场景数据
   * @param {Array} rows - 分组后的行数据
   * @param {number} targetIndex - 目标行索引（从0开始，分组后的索引）
   * @returns {Object|null} 匹配的场景信息
   */
  function findSceneForRow(scenes, rows, targetIndex) {
    if (!scenes || scenes.length === 0) return null;
    if (!rows || !rows[targetIndex]) return null;

    const targetRow = rows[targetIndex];

    // 获取原始行号范围
    let originalLineNumbers = [];

    if (targetRow.isMerged && targetRow.mergedRows && targetRow.mergedRows.length > 0) {
      // 如果是合并行，获取所有合并行的原始索引
      originalLineNumbers = targetRow.mergedRows.map(mergedRow => mergedRow.originalIndex);
      console.log(`[findSceneForRow] 合并行 ${targetIndex + 1}，包含原始行号:`, originalLineNumbers);
    } else {
      // 如果是单行，使用其原始索引
      originalLineNumbers = [targetRow.originalIndex || targetIndex + 1];
      console.log(`[findSceneForRow] 单行 ${targetIndex + 1}，原始行号:`, originalLineNumbers);
    }

    // 查找匹配的场景
    for (const scene of scenes) {
      if (scene.range) {
        // 解析范围，如 "1-75" 或 "76-129"
        const rangeParts = scene.range.split('-');
        if (rangeParts.length === 2) {
          const startLine = parseInt(rangeParts[0]);
          const endLine = parseInt(rangeParts[1]);

          // 检查是否有任何原始行号在场景范围内
          const hasMatch = originalLineNumbers.some(lineNumber =>
            lineNumber >= startLine && lineNumber <= endLine
          );

          if (hasMatch) {
            console.log(`[findSceneForRow] 找到匹配场景: ${scene.location || '未知'} (${scene.time || '未知'})，范围: ${startLine}-${endLine}`);
            return scene;
          }
        }
      }
    }

    console.log(`[findSceneForRow] 未找到匹配的场景，原始行号:`, originalLineNumbers);
    return null;
  }

  /**
   * 格式化角色列表为字符串
   * @param {Array} characters - 角色数据
   * @returns {string} 格式化的角色列表
   */
  function formatCharacters(characters) {
    if (!characters || characters.length === 0) {
      return '暂无角色信息';
    }

    return characters.map(char => {
      let result = `${char.name}: ${char.description || ''}`;
      if (char.alias) {
        result += ` (别名: ${char.alias})`;
      }
      return result;
    }).join('\n');
  }

  /**
   * 格式化故事总纲
   * @param {Array} outlines - 大纲数据
   * @returns {string} 格式化的故事总纲
   */
  function formatSynopsis(outlines) {
    if (!outlines || outlines.length === 0) {
      return '暂无故事总纲';
    }

    // 使用最新的大纲
    const latestOutline = outlines[outlines.length - 1];
    return latestOutline.content || '暂无故事总纲';
  }

  /**
   * 格式化场景与时间信息
   * @param {Object|null} scene - 场景数据
   * @returns {string} 格式化的场景与时间
   */
  function formatSceneAndTime(scene) {
    if (!scene) {
      return '暂无场景信息';
    }

    return `地点: ${scene.location || '未知'}\n时间: ${scene.time || '未知'}`;
  }

  /**
   * 格式化特名列表为字符串
   * @param {Array} specialItems - 特名数据
   * @returns {string} 格式化的特名列表
   */
  function formatSpecialItems(specialItems) {
    if (!specialItems || specialItems.length === 0) {
      return '暂无特名信息';
    }

    return specialItems.map(item => {
      return `${item.name}: ${item.description || ''}`;
    }).join('\n');
  }

  /**
   * 准备推理数据
   * @param {Array} rows - 所有行数据
   * @param {number} targetIndex - 目标行索引
   * @param {number} contextLines - 上下文行数（可选，如果不提供则从用户设置获取）
   * @returns {Promise<Object>} 推理数据
   */
  async function prepareReasoningData(rows, targetIndex, contextLines = null) {
    try {
      console.log(`[useImagePromptReasoning] 开始准备推理数据，目标行: ${targetIndex}`);

      // 获取项目信息
      const { projectTitle, chapterTitle } = getProjectInfo();
      if (!projectTitle || !chapterTitle) {
        throw new Error('无法获取项目或章节信息');
      }

      // 获取上下文行数设置
      if (contextLines === null) {
        contextLines = await userImageSettings.getContextLines(projectTitle, chapterTitle);
        console.log(`[useImagePromptReasoning] 从用户设置获取上下文行数: ${contextLines}`);
      } else {
        console.log(`[useImagePromptReasoning] 使用传入的上下文行数: ${contextLines}`);
      }

      // 加载 Current.json 数据
      const currentData = await loadCurrentData(projectTitle, chapterTitle);

      // 获取目标行内容
      const targetRow = rows[targetIndex];
      if (!targetRow) {
        throw new Error(`无法找到索引为 ${targetIndex} 的行`);
      }

      // 获取上下文
      const context = getContextForRow(rows, targetIndex, contextLines);

      // 查找相关场景（传递 rows 数据以正确映射原始行号）
      const scene = findSceneForRow(currentData.scenes, rows, targetIndex);

      // 格式化数据
      const synopsis = formatSynopsis(currentData.outlines);
      const characters = formatCharacters(currentData.characters);
      const specialItems = formatSpecialItems(currentData.specialItems);
      const sceneAndTime = formatSceneAndTime(scene);
      const targetText = targetRow.description || '';

      // 获取 prompt 模板
      const promptTemplate = await getImagePromptTemplate();

      // 构建用户输入部分
      const userInput = `
**总纲：** ${synopsis}

**角色：** ${characters}

**特名：** ${specialItems}

**本分组的场景与时间：** ${sceneAndTime}

**上下文：** ${context}

**需要给出画面的文本：** ${targetText}`;

      // 替换用户输入格式部分
      let fullPrompt = promptTemplate;

      // 查找并替换"用户输入格式"部分
      const userInputFormatRegex = /\*\*用户输入格式：\*\*[\s\S]*?(?=\*\*最终输出：\*\*)/;
      if (userInputFormatRegex.test(fullPrompt)) {
        fullPrompt = fullPrompt.replace(userInputFormatRegex, userInput + '\n\n');
      } else {
        // 如果没有找到用户输入格式部分，直接在末尾添加
        fullPrompt = fullPrompt + '\n\n' + userInput;
      }

      const reasoningData = {
        projectTitle,
        chapterTitle,
        targetIndex,
        targetText,
        synopsis,
        characters,
        specialItems,
        sceneAndTime,
        context,
        promptTemplate,
        fullPrompt,
        currentData
      };

      console.log('[useImagePromptReasoning] 推理数据准备完成:', {
        targetIndex,
        targetTextLength: targetText.length,
        contextLines: context.split('\n').length,
        hasScene: !!scene,
        charactersCount: currentData.characters.length,
        specialItemsCount: currentData.specialItems.length,
        outlinesCount: currentData.outlines.length
      });

      return reasoningData;
    } catch (error) {
      console.error('[useImagePromptReasoning] 准备推理数据失败:', error);
      throw error;
    }
  }

  /**
   * 执行推理提示词生成
   * @param {Object} reasoningData - 推理数据
   * @returns {Promise<Object>} 推理结果
   */
  async function executeReasoning(reasoningData) {
    try {
      console.log('[useImagePromptReasoning] 开始执行推理');

      const result = await sendTextToLLMIndependent(
        "", // 不传递原始文本，避免重复
        reasoningData.fullPrompt, // 传递完整的 prompt
        {
          analyzeType: 'image-prompt-reasoning',
          skipLocalAPI: true // 强制使用远程 LLM
        }
      );

      if (result && result.success && result.text) {
        console.log('[useImagePromptReasoning] 推理完成，结果长度:', result.text.length);

        return {
          success: true,
          prompt: result.text.trim(),
          targetIndex: reasoningData.targetIndex,
          targetText: reasoningData.targetText,
          originalResponse: result
        };
      } else {
        throw new Error(result?.error || '推理失败，未获得有效结果');
      }
    } catch (error) {
      console.error('[useImagePromptReasoning] 执行推理失败:', error);
      return {
        success: false,
        error: error.message,
        targetIndex: reasoningData.targetIndex,
        targetText: reasoningData.targetText
      };
    }
  }

  /**
   * 显示测试窗口
   * @param {Object} reasoningData - 推理数据
   */
  function showTestWindow(reasoningData) {
    currentPromptData.value = reasoningData;
    fullPromptPreview.value = reasoningData.fullPrompt;
    showTestModal.value = true;
  }

  /**
   * 关闭测试窗口
   */
  function closeTestWindow() {
    showTestModal.value = false;
    currentPromptData.value = null;
    fullPromptPreview.value = '';
  }

  return {
    // 状态
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    showTestModal,
    fullPromptPreview,
    currentPromptData,

    // 方法
    getProjectInfo,
    loadCurrentData,
    getImagePromptTemplate,
    getContextForRow,
    findSceneForRow,
    formatCharacters,
    formatSynopsis,
    formatSceneAndTime,
    formatSpecialItems,
    prepareReasoningData,
    executeReasoning,
    showTestWindow,
    closeTestWindow,

    // 用户设置相关方法
    getUserImageSettings: userImageSettings,
    getContextLines: userImageSettings.getContextLines,
    setContextLines: userImageSettings.setContextLines
  };
}
