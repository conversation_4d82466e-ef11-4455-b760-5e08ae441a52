<template>
  <div
    v-if="show"
    class="modal-overlay"
    @click.self="$emit('cancel')"
  >
    <div class="modal-content">
      <div class="modal-header">
        <h3>确认覆盖预设</h3>
        <button
          class="close-button"
          @click="$emit('cancel')"
        >
          ×
        </button>
      </div>
      
      <div class="modal-body">
        <p class="warning-text">
          预设角色列表中已存在名为 <strong>"{{ characterName }}"</strong> 的角色。
        </p>
        
        <div class="comparison-container">
          <div class="comparison-item">
            <h4>当前预设描述：</h4>
            <div class="description-box existing">
              {{ existingDescription }}
            </div>
          </div>
          
          <div class="comparison-item">
            <h4>新的描述：</h4>
            <div class="description-box new">
              {{ newDescription }}
            </div>
          </div>
        </div>
        
        <p class="confirm-text">
          是否要用新的描述覆盖现有的预设角色？
        </p>
      </div>
      
      <div class="modal-footer">
        <button
          class="cancel-button"
          @click="$emit('cancel')"
        >
          取消
        </button>
        <button
          class="confirm-button"
          @click="$emit('confirm')"
        >
          确认覆盖
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  show: { type: Boolean, default: false },
  characterName: { type: String, default: '' },
  existingDescription: { type: String, default: '' },
  newDescription: { type: String, default: '' }
});

defineEmits(['confirm', 'cancel']);
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(20, 22, 30, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: #232136;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  border: 1px solid #44415a;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #44415a;
}

.modal-header h3 {
  margin: 0;
  color: #e0def4;
  font-size: 1.25em;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #908caa;
  font-size: 1.5em;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: color 0.2s, background 0.2s;
}

.close-button:hover {
  color: #e0def4;
  background: #44415a;
}

.modal-body {
  padding: 20px 24px;
  color: #e0def4;
}

.warning-text {
  margin: 0 0 20px 0;
  font-size: 1.1em;
  color: #f6c177;
}

.comparison-container {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comparison-item h4 {
  margin: 0 0 8px 0;
  color: #b8b5d6;
  font-size: 0.95em;
  font-weight: 500;
}

.description-box {
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #44415a;
  background: #2a273f;
  color: #e0def4;
  line-height: 1.5;
  min-height: 60px;
  white-space: pre-wrap;
  word-break: break-word;
}

.description-box.existing {
  border-left: 4px solid #eb6f92;
}

.description-box.new {
  border-left: 4px solid #9ccfd8;
}

.confirm-text {
  margin: 20px 0 0 0;
  font-weight: 500;
  color: #e0def4;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #44415a;
}

.cancel-button, .confirm-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.95em;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, transform 0.1s;
}

.cancel-button {
  background: #555c6b;
  color: #e0def4;
}

.cancel-button:hover {
  background: #666f7d;
}

.confirm-button {
  background: #eb6f92;
  color: white;
}

.confirm-button:hover {
  background: #f082a3;
  transform: translateY(-1px);
}

.cancel-button:active, .confirm-button:active {
  transform: translateY(0);
}
</style>
