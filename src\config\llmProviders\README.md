# LLM提供商配置重构

## 📋 概述

本次重构将原本集中在单个文件中的LLM配置按照不同的提供商分离到独立的JavaScript文件中，提高了代码的可维护性和扩展性。

## 🏗️ 新的文件结构

```
src/config/llmProviders/
├── index.js          # 统一导出接口
├── openrouter.js     # OpenRouter配置
├── google.js         # Google Gemini配置
├── openai.js         # OpenAI配置
├── anthropic.js      # Anthropic Claude配置
├── local.js          # 本地模型配置
└── README.md         # 说明文档
```

## 🔧 配置文件结构

每个提供商配置文件都包含以下标准结构：

```javascript
export const providerConfig = {
  // 提供商基本信息
  name: '提供商名称',
  id: '提供商ID',
  description: '提供商描述',
  
  // API配置
  api: {
    baseUrl: 'API基础URL',
    endpoint: 'API端点',
    headers: {}, // 默认请求头
    authHeader: '认证头名称',
    authPrefix: '认证前缀'
  },
  
  // 默认配置
  defaults: {
    model: '默认模型',
    temperature: 0.7,
    maxTokens: 4096,
    timeout: 30000
  },
  
  // 支持的模型列表
  models: [
    {
      id: '模型ID',
      name: '模型名称',
      description: '模型描述',
      maxTokens: 最大token数,
      pricing: { input: 输入价格, output: 输出价格 }
    }
  ],
  
  // 请求格式化函数
  formatRequest: (prompt, settings) => { /* 格式化逻辑 */ },
  
  // 响应解析函数
  parseResponse: (data) => { /* 解析逻辑 */ },
  
  // 错误处理函数
  handleError: (error, response) => { /* 错误处理逻辑 */ },
  
  // 配置验证函数
  validateConfig: (config) => { /* 验证逻辑 */ }
};
```

## 🚀 使用方法

### 1. 导入配置

```javascript
import { 
  getProviderConfig, 
  getApiKeyForProvider, 
  buildApiRequest, 
  parseApiResponse 
} from '../config/llmProviders/index.js';
```

### 2. 获取提供商配置

```javascript
// 获取单个提供商配置
const openaiConfig = getProviderConfig('openai');

// 获取所有提供商配置
const allConfigs = getAllProviderConfigs();

// 获取提供商列表（用于UI显示）
const providers = getProviderList();
```

### 3. 构建API请求

```javascript
// 构建API请求配置
const requestConfig = buildApiRequest('openai', prompt, settings);

// 发送请求
const response = await fetch(requestConfig.url, {
  method: requestConfig.method,
  headers: requestConfig.headers,
  body: requestConfig.body
});
```

### 4. 解析API响应

```javascript
const responseData = await response.json();
const parsedResponse = parseApiResponse('openai', responseData);
console.log(parsedResponse.content); // 提取的文本内容
```

## 🔄 迁移指南

### 从旧服务迁移

1. **导入新服务**：
```javascript
// 旧方式
import llmService from '../services/llmService.js';

// 新方式
import { performSemanticGrouping, performCustomPrompting } from '../services/llmServiceNew.js';
```

2. **使用迁移检查器**：
```javascript
import { quickMigrationCheck } from '../services/llmServiceMigration.js';

// 检查配置是否正常
const checkResult = await quickMigrationCheck(userSettings);
if (checkResult.isReady) {
  console.log('✅ 可以安全使用新服务');
} else {
  console.log('❌ 需要修复配置问题');
  console.log(checkResult.report);
}
```

### 向后兼容性

新服务完全向后兼容，现有的调用方式无需修改：

```javascript
// 这些调用方式仍然有效
const result = await performSemanticGrouping(subtitles, options);
const customResult = await performCustomPrompting(subtitles, prompt, options);
```

## 🎯 优势

### 1. **模块化设计**
- 每个提供商独立配置
- 易于添加新提供商
- 配置逻辑清晰分离

### 2. **统一接口**
- 所有提供商使用相同的接口
- 简化了服务层代码
- 便于维护和测试

### 3. **配置验证**
- 每个提供商都有独立的验证逻辑
- 更好的错误提示
- 提高了系统稳定性

### 4. **扩展性**
- 易于添加新模型
- 支持提供商特定功能
- 灵活的配置选项

## 🛠️ 添加新提供商

1. **创建配置文件**：
```javascript
// src/config/llmProviders/newprovider.js
export const newProviderConfig = {
  name: 'New Provider',
  id: 'newprovider',
  // ... 其他配置
};
```

2. **更新索引文件**：
```javascript
// src/config/llmProviders/index.js
import newProviderConfig from './newprovider.js';

export const providerConfigs = {
  // ... 现有提供商
  newprovider: newProviderConfig
};
```

3. **更新提供商列表**：
```javascript
export const providerList = [
  // ... 现有提供商
  {
    id: 'newprovider',
    name: 'New Provider',
    description: '新提供商描述',
    icon: 'ri-icon-name',
    popular: false
  }
];
```

## 🔍 调试和测试

### 使用迁移检查器

```javascript
import { LLMServiceMigrationChecker } from '../services/llmServiceMigration.js';

const checker = new LLMServiceMigrationChecker();
const results = await checker.checkAllProviders(userSettings);

// 查看详细结果
console.log('检查结果:', results);
console.log('报告:', checker.generateReport());
```

### 验证单个提供商

```javascript
import { validateProviderConfig } from '../config/llmProviders/index.js';

const errors = validateProviderConfig('openai', {
  apiKey: 'your-api-key',
  model: 'gpt-4',
  temperature: 0.7
});

if (errors.length === 0) {
  console.log('✅ 配置有效');
} else {
  console.log('❌ 配置错误:', errors);
}
```

## 📝 注意事项

1. **API密钥安全**：确保API密钥不会被意外提交到版本控制系统
2. **配置验证**：在使用前始终验证配置的有效性
3. **错误处理**：妥善处理API调用可能出现的各种错误
4. **性能考虑**：合理设置超时时间和重试机制

## 🤝 贡献

如需添加新的提供商或改进现有配置，请：

1. 遵循现有的配置文件结构
2. 添加适当的错误处理和验证
3. 更新相关文档
4. 进行充分测试
