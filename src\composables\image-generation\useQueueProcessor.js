/**
 * 队列处理器
 * 负责队列任务的调度和执行
 */

export function useQueueProcessor(imageQueue, imageState, imageExecution) {
  const { 
    taskQueue, 
    currentTask, 
    getNextTask, 
    updateTaskStatus, 
    removeFromQueue,
    clearQueue 
  } = imageQueue;
  
  const { 
    isGenerating, 
    isCancelling, 
    setIsGenerating 
  } = imageState;
  
  const { 
    executeImageGenerationForQueue 
  } = imageExecution;

  /**
   * 🔧 修复：统一的队列触发检查函数
   */
  const checkAndTriggerQueue = () => {
    // 🔧 修复：增加延迟时间，确保ComfyUI状态完全更新
    setTimeout(() => {
      const waitingTasks = taskQueue.filter(t => t.status === 'waiting');

      // 🔧 优化：只在开发模式下显示详细队列检查信息
      const isDev = process.env.NODE_ENV === 'development';
      if (isDev) {
        console.log('🔧 [队列处理] 检查队列触发条件:', {
          isGenerating: isGenerating.value,
          isCancelling: isCancelling.value,
          currentTask: currentTask.value?.id,
          queueLength: taskQueue.length,
          waitingTasksCount: waitingTasks.length,
          waitingTaskIds: waitingTasks.map(t => `${t.id}(row${t.rowIndex})`),
          hasWaitingTasks: waitingTasks.length > 0
        });
      }

      // 🔧 修复：更严格的条件检查
      const canProcessQueue = !isGenerating.value &&
                             !isCancelling.value &&
                             !currentTask.value &&
                             waitingTasks.length > 0;

      if (canProcessQueue) {
        console.log(`[QueueProcessor] 开始处理队列任务，等待任务数: ${waitingTasks.length}`);
        processQueue();
      } else if (isDev) {
        console.log(`[QueueProcessor] 队列处理条件不满足: isGenerating=${isGenerating.value}, isCancelling=${isCancelling.value}, currentTask=${currentTask.value?.id}, waitingTasks=${waitingTasks.length}`);

        // 🔧 新增：如果有等待任务但状态阻塞，尝试修复
        if (waitingTasks.length > 0 && !isGenerating.value && !currentTask.value && !isCancelling.value) {
          console.log('[QueueProcessor] 检测到状态阻塞，尝试强制触发');
          setTimeout(() => {
            processQueue();
          }, 500);
        }
      }
    }, 200); // 🔧 增加延迟时间到200ms，确保状态完全更新
  };

  /**
   * 处理队列 - FIFO调度
   */
  const processQueue = async () => {
    // 🔧 优化：简化日志输出
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) {
      console.log(`[QueueProcessor] 尝试处理队列: isGenerating=${isGenerating.value}, queueLength=${taskQueue.length}`);
    }

    // 检查是否可以处理队列
    if (isGenerating.value || isCancelling.value || currentTask.value) {
      if (isDev) console.log('[QueueProcessor] 跳过处理：存在活动任务或取消中');
      return;
    }

    // 获取下一个待处理任务
    const nextTask = getNextTask();
    if (!nextTask) {
      if (isDev) console.log('[QueueProcessor] 队列为空，无任务处理');
      return;
    }

    // 🔧 简洁日志：只显示任务开始信息
    console.log(`🚀 [队列处理] 开始任务: ${nextTask.id} (第${nextTask.rowIndex + 1}行)`);

    try {
      // 设置当前任务和状态
      currentTask.value = nextTask;
      setIsGenerating(true, `QueueProcessor-TaskStart-${nextTask.id}`);
      updateTaskStatus(nextTask.id, 'processing');

      // 🔧 关键修复：立即通知UI任务开始处理，更新行状态
      if (nextTask.onProgress) {
        if (isDev) console.log(`[QueueProcessor] 通知UI任务开始: ${nextTask.id} (第${nextTask.rowIndex + 1}行)`);
        nextTask.onProgress({
          stage: 'processing',
          progress: 0,
          message: '开始生成图像...',
          taskId: nextTask.id,
          rowIndex: nextTask.rowIndex,
          isQueued: false,        // 🔧 关键：不再是队列状态
          isProcessing: true,     // 🔧 关键：现在是处理状态
          isCompleted: false,
          isError: false,
          isCancelled: false
        });
      }

      // 🔧 增强的进度回调，确保任务完成时触发状态重置
      const enhancedOnProgress = (progressData) => {
        if (nextTask.onProgress) {
          nextTask.onProgress({
            ...progressData,
            taskId: nextTask.id,
            rowIndex: nextTask.rowIndex
          });
        }

        // 🔧 关键修复：检测任务完成状态
        if (progressData.isCompleted || progressData.stage === 'completed') {
          console.log(`[QueueProcessor] processQueue: Task ${nextTask.id} (Row ${nextTask.rowIndex}) completion signal detected via progress. Stage: ${progressData.stage}, isCompleted: ${progressData.isCompleted}`);
        }
      };

      // 执行任务
      const result = await executeImageGenerationForQueue({
        ...nextTask,
        onProgress: enhancedOnProgress
      });

      // 任务完成
      console.log(`[QueueProcessor] processQueue: Task ${nextTask.id} (Row ${nextTask.rowIndex}) executed. Success: ${result.success}`);
      updateTaskStatus(nextTask.id, 'completed', { result });

      // 🔧 关键修复：触发最终完成回调
      if (nextTask.onProgress) {
        nextTask.onProgress({
          stage: 'completed',
          progress: 100,
          message: '队列任务完成',
          taskId: nextTask.id,
          rowIndex: nextTask.rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: false,
          isCancelled: false,
          isQueued: false
        });
      }

    } catch (error) {
      // 任务失败
      console.error(`[QueueProcessor] processQueue: Task ${nextTask.id} (Row ${nextTask.rowIndex}) FAILED. Error: ${error.message}`);
      updateTaskStatus(nextTask.id, 'failed', { error: error.message });

      // 🔧 关键修复：触发错误完成回调
      if (nextTask.onProgress) {
        nextTask.onProgress({
          stage: 'error',
          progress: 0,
          message: error.message || '队列任务失败',
          taskId: nextTask.id,
          rowIndex: nextTask.rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: true,
          isCancelled: false,
          isQueued: false
        });
      }

    } finally {
      // 🔧 修复：确保状态完全清理
      console.log(`[QueueProcessor] processQueue: Finalizing task ${nextTask.id} (Row ${nextTask.rowIndex}, Status: ${nextTask.status})`);

      // 🔧 修复：清理状态
      currentTask.value = null;
      setIsGenerating(false, `QueueProcessor-TaskEnd-${nextTask.id}`);

      // 🔧 修复：移除已完成的任务（无论成功还是失败）
      if (nextTask.status === 'completed' || nextTask.status === 'failed' || nextTask.status === 'cancelled') {
        removeFromQueue(nextTask.id); // Log inside removeFromQueue will confirm
      }

      console.log(`[QueueProcessor] processQueue: Task ${nextTask.id} finalization complete. currentTask=${currentTask.value}, isGenerating=${isGenerating.value}, remainingQueue=${taskQueue.length}`);

      // 🔧 修复：延迟检查下一个任务，确保状态完全更新
      console.log(`[QueueProcessor] 任务完成: ${nextTask.id}，检查下一个任务`);

      // 🔍 监控任务完成后的选择状态
      console.log(`🔍 [队列处理器] 任务完成后状态监控:`, {
        taskId: nextTask.id,
        rowIndex: nextTask.rowIndex + 1,
        taskType: nextTask.type || '图像生成'
      });
      setTimeout(() => {
        const remainingWaitingTasks = taskQueue.filter(t => t.status === 'waiting');
        const isDev = process.env.NODE_ENV === 'development';

        if (isDev) {
          const taskIds = remainingWaitingTasks.map(t => `${t.id}(第${t.rowIndex + 1}行)`).join(', ');
          console.log(`[QueueProcessor] 剩余等待任务: ${remainingWaitingTasks.length}个 [${taskIds}]`);
        }

        if (remainingWaitingTasks.length > 0) {
          console.log(`[QueueProcessor] 发现剩余任务，继续处理队列`);
          checkAndTriggerQueue();
        } else {
          console.log(`[QueueProcessor] 所有任务已完成`);
        }
      }, 300); // 🔧 增加延迟时间，确保ComfyUI状态完全更新
    }
  };

  /**
   * 清空队列并停止处理
   */
  const clearQueueAndStop = async () => {
    console.log('🛑 [队列处理] 开始清空队列并停止处理...');

    try {
      // 如果有正在执行的任务，先取消
      if (currentTask.value && isGenerating.value) {
        console.log('🚫 [队列处理] 取消当前执行的任务:', currentTask.value.id);
        
        isCancelling.value = true;
        
        try {
          await imageExecution.cancelGeneration();
        } catch (error) {
          console.warn('⚠️ [队列处理] 取消当前任务失败:', error);
        }
        
        // 更新当前任务状态
        if (currentTask.value) {
          updateTaskStatus(currentTask.value.id, 'cancelled');
          currentTask.value = null;
        }
        
        isCancelling.value = false;
        setIsGenerating(false, '队列清空-取消当前任务');
      }

      // 清空队列
      await clearQueue();

      console.log('✅ [队列处理] 队列已清空并停止处理');

    } catch (error) {
      console.error('❌ [队列处理] 清空队列失败:', error);
      throw error;
    }
  };

  /**
   * 暂停队列处理
   */
  const pauseQueue = () => {
    console.log('⏸️ [队列处理] 暂停队列处理');
    // 通过设置标志位来暂停，具体实现可以根据需要扩展
  };

  /**
   * 恢复队列处理
   */
  const resumeQueue = () => {
    console.log('▶️ [队列处理] 恢复队列处理');
    checkAndTriggerQueue();
  };

  /**
   * 获取队列处理状态
   */
  const getProcessorStatus = () => {
    return {
      isProcessing: isGenerating.value,
      isCancelling: isCancelling.value,
      currentTask: currentTask.value,
      queueLength: taskQueue.length,
      waitingTasks: taskQueue.filter(t => t.status === 'waiting').length,
      processingTasks: taskQueue.filter(t => t.status === 'processing').length,
      canProcess: !isGenerating.value && !isCancelling.value && !currentTask.value
    };
  };

  /**
   * 重新处理失败的任务
   */
  const retryFailedTasks = () => {
    const failedTasks = taskQueue.filter(t => t.status === 'failed');
    
    console.log('🔄 [队列处理] 重新处理失败任务:', {
      count: failedTasks.length
    });

    failedTasks.forEach(task => {
      updateTaskStatus(task.id, 'waiting');
    });

    if (failedTasks.length > 0) {
      checkAndTriggerQueue();
    }
  };

  /**
   * 优先处理指定任务
   */
  const prioritizeTask = (taskId) => {
    const taskIndex = taskQueue.findIndex(t => t.id === taskId);
    if (taskIndex > 0) {
      // 将任务移到队列前面
      const task = taskQueue.splice(taskIndex, 1)[0];
      taskQueue.unshift(task);
      
      // 更新队列位置
      taskQueue.forEach((t, index) => {
        if (t.status === 'waiting') {
          t.queuePosition = index + 1;
        }
      });

      console.log('⬆️ [队列处理] 任务已优先处理:', {
        taskId,
        newPosition: 1
      });

      checkAndTriggerQueue();
    }
  };

  return {
    // 核心处理方法
    processQueue,
    checkAndTriggerQueue,

    // 控制方法
    clearQueueAndStop,
    pauseQueue,
    resumeQueue,
    retryFailedTasks,
    prioritizeTask,

    // 状态查询
    getProcessorStatus
  };
}
