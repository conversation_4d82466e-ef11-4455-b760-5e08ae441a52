/**
 * 选择状态变化追踪器
 * 用于监控和记录所有 isSelected 属性的变化
 */

// 全局追踪器状态
let isTracking = true;
let changeHistory = [];

/**
 * 记录选择状态变化
 * @param {number} rowIndex - 行索引
 * @param {boolean} oldValue - 旧值
 * @param {boolean} newValue - 新值
 * @param {string} source - 变化来源
 * @param {string} reason - 变化原因
 */
export function trackSelectionChange(rowIndex, oldValue, newValue, source, reason = '') {
  if (!isTracking) return;
  
  const timestamp = new Date().toISOString();
  const stackTrace = new Error().stack;
  
  const change = {
    timestamp,
    rowIndex: rowIndex + 1, // 显示为1-based
    oldValue,
    newValue,
    source,
    reason,
    stackTrace
  };
  
  changeHistory.push(change);
  
  // 控制台输出
  console.log(`🔍 [选择状态追踪] 第${rowIndex + 1}行状态变化:`, {
    from: oldValue,
    to: newValue,
    source,
    reason,
    timestamp
  });
  
  // 如果状态被清除，输出详细的调用栈
  if (oldValue === true && newValue === false) {
    console.warn(`⚠️ [选择状态清除] 第${rowIndex + 1}行选择状态被清除！`, {
      source,
      reason,
      stackTrace: stackTrace.split('\n').slice(1, 6) // 显示前5层调用栈
    });
  }
  
  // 限制历史记录数量
  if (changeHistory.length > 100) {
    changeHistory = changeHistory.slice(-50);
  }
}

/**
 * 获取变化历史
 */
export function getChangeHistory() {
  return [...changeHistory];
}

/**
 * 清除历史记录
 */
export function clearHistory() {
  changeHistory = [];
  console.log('🔍 [选择状态追踪] 历史记录已清除');
}

/**
 * 开始/停止追踪
 */
export function setTracking(enabled) {
  isTracking = enabled;
  console.log(`🔍 [选择状态追踪] 追踪${enabled ? '已启用' : '已禁用'}`);
}

/**
 * 创建响应式属性拦截器
 * @param {Object} row - 行对象
 * @param {number} rowIndex - 行索引
 * @param {string} source - 来源标识
 */
export function createSelectionInterceptor(row, rowIndex, source) {
  if (!row || typeof row !== 'object') return row;
  
  // 如果已经有拦截器，不重复添加
  if (row._hasSelectionInterceptor) return row;
  
  let currentValue = row.isSelected;
  
  Object.defineProperty(row, 'isSelected', {
    get() {
      return currentValue;
    },
    set(newValue) {
      const oldValue = currentValue;
      if (oldValue !== newValue) {
        trackSelectionChange(rowIndex, oldValue, newValue, source, '属性直接赋值');
      }
      currentValue = newValue;
    },
    enumerable: true,
    configurable: true
  });
  
  // 标记已添加拦截器
  row._hasSelectionInterceptor = true;
  
  console.log(`🔍 [选择状态追踪] 第${rowIndex + 1}行已添加拦截器 (${source})`);
  
  return row;
}

/**
 * 批量操作生命周期追踪
 */
export const batchOperationTracker = {
  start(operationType, selectedRows) {
    console.log(`🚀 [批量操作追踪] ${operationType}操作开始:`, {
      operationType,
      selectedRowCount: selectedRows.length,
      selectedRowIndexes: selectedRows.map((row, index) => ({ index: index + 1, isSelected: row.isSelected })),
      timestamp: new Date().toISOString()
    });
  },
  
  progress(operationType, currentIndex, totalCount) {
    console.log(`📊 [批量操作追踪] ${operationType}进度: ${currentIndex + 1}/${totalCount}`);
  },
  
  complete(operationType, result) {
    console.log(`✅ [批量操作追踪] ${operationType}操作完成:`, {
      operationType,
      result,
      timestamp: new Date().toISOString()
    });
  },
  
  cancel(operationType, reason) {
    console.log(`🚫 [批量操作追踪] ${operationType}操作取消:`, {
      operationType,
      reason,
      timestamp: new Date().toISOString()
    });
  }
};

// 导出到全局，方便调试
if (typeof window !== 'undefined') {
  window.selectionStateTracker = {
    getHistory: getChangeHistory,
    clearHistory,
    setTracking
  };
}
