const express = require('express');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const fsPromises = require('fs').promises;
const { createProxyMiddleware } = require('http-proxy-middleware');
const fetch = require('node-fetch');
const bodyParser = require('body-parser');

const router = express.Router();

// 创建本地文件夹
router.post('/create-folder', (req, res) => {
  console.log('收到创建文件夹请求:', req.body);
  const { folderName } = req.body;

  // 检查请求体
  if (!folderName) {
    console.log('错误: 缺少文件夹名称');
    return res.status(400).json({ error: '缺少文件夹名称' });
  }

  // 只允许单层文件夹名，防止路径穿越
  if (folderName.includes('/') || folderName.includes('\\')) {
    console.log('错误: 文件夹名称包含非法字符:', folderName);
    return res.status(400).json({ error: '文件夹名称不能包含路径分隔符' });
  }

  // 获取base参数，如果有将其与基础目录结合
  const { base = '' } = req.body;

  const baseDir = path.resolve(__dirname, '../../draft');
  // 生成完整的目标路径（包含 base）
  const targetBaseDir = base ? path.join(baseDir, base) : baseDir;
  const folderPath = path.join(targetBaseDir, folderName);

  console.log('基础目录:', baseDir);
  console.log('目标目录（含 base）:', targetBaseDir);
  console.log('最终文件夹路径:', folderPath);

  // 确保基础目录和目标基础目录存在
  if (!fs.existsSync(baseDir)) {
    console.log('基础目录不存在，正在创建:', baseDir);
    fs.mkdirSync(baseDir, { recursive: true });
    console.log('基础目录创建成功');
  }

  // 确保 targetBaseDir 存在（可能是多级目录）
  if (base && !fs.existsSync(targetBaseDir)) {
    console.log('目标基础目录不存在，正在创建:', targetBaseDir);
    fs.mkdirSync(targetBaseDir, { recursive: true });
    console.log('目标基础目录创建成功');
  }

  // 检查目标文件夹是否已存在
  if (fs.existsSync(folderPath)) {
    console.log('文件夹已存在:', folderPath);
    return res.status(409).json({ error: '文件夹已存在' });
  }

  console.log('正在创建文件夹:', folderPath);
  try {
    fs.mkdirSync(folderPath, { recursive: true });
    console.log('文件夹创建成功');
    res.json({ success: true, folderPath });
  } catch (err) {
    console.error('创建文件夹失败:', err);
    res.status(500).json({ error: err.message });
  }
});

// 获取所有项目文件夹，支持 base 参数
router.get('/list-folders', (req, res) => {
  console.log('收到获取文件夹列表请求:', req.query);
  const base = req.query.base || '';
  const baseDir = path.resolve(__dirname, '../../draft', base.replace(/^draft\/?/, ''));
  console.log('查询目录:', baseDir);

  if (!fs.existsSync(baseDir)) {
    console.log('目录不存在，返回空数组');
    return res.json([]);
  }

  try {
    const allFiles = fs.readdirSync(baseDir);
    console.log('目录下所有文件/文件夹:', allFiles);

    const folders = allFiles.filter(name => {
      try {
        return fs.statSync(path.join(baseDir, name)).isDirectory();
      } catch (err) {
        console.error(`检查文件夹状态失败: ${name}`, err);
        return false;
      }
    });

    console.log('筛选后的文件夹:', folders);
    res.json(folders);
  } catch (err) {
    console.error('获取文件夹列表失败:', err);
    res.status(500).json({ error: err.message });
  }
});

// 新增：重命名文件夹
router.post('/rename-folder', (req, res) => {
  const { oldName, newName, base } = req.body;
  console.log('重命名文件夹请求:', { oldName, newName, base });

  if (!oldName || !newName) {
    return res.status(400).json({ error: '缺少参数' });
  }
  if (oldName === newName) {
    return res.json({ success: true });
  }

  const baseDir = path.resolve(__dirname, '../../draft');
  console.log('基础目录路径:', baseDir);

  // 非常关键 - 处理多种路径情况
  let oldPath, newPath;

  // 先检查路径中是否包含斜杠，如果包含说明是复合路径
  if (oldName.includes('/') || oldName.includes('\\')) {
    // 路径直接包含斜杠，需要拆分处理
    console.log(`检测到复合路径: ${oldName}`);
    const parts = oldName.split(/[/\\]/);
    const actualFileName = parts.pop();
    const parentDir = parts.join('/');
    oldPath = path.join(baseDir, parentDir, actualFileName);
    newPath = path.join(baseDir, parentDir, newName);
    console.log(`处理复合路径结果: 父目录=${parentDir}, 文件名=${actualFileName}`);
  } else if (base) {
    // 含有项目目录（base），处理章节重命名
    console.log(`处理章节重命名: 项目=${base}, 旧章节=${oldName}, 新章节=${newName}`);
    oldPath = path.join(baseDir, base, oldName);
    newPath = path.join(baseDir, base, newName);
  } else {
    // 默认情况：直接在 draft 目录下重命名
    console.log(`直接重命名项目目录: 旧项目=${oldName}, 新项目=${newName}`);
    oldPath = path.join(baseDir, oldName);
    newPath = path.join(baseDir, newName);
  }

  try {
    // 检查路径存在性 - 增强日志
    console.log('重命名路径检查:', {
      oldPath,
      newPath,
      原请求参数: { oldName, newName, base },
      绝对路径检查: {
        baseDir,
        oldPathExists: fs.existsSync(oldPath),
        baseExists: base ? fs.existsSync(path.join(baseDir, base)) : '无base参数'
      }
    });

    // 列出目录内容以确认
    if (base) {
      const basePath = path.join(baseDir, base);
      if (fs.existsSync(basePath)) {
        console.log(`项目目录 "${base}" 存在，内容:`, fs.readdirSync(basePath));
      } else {
        console.error(`项目目录 "${base}" 不存在!`);
      }
    }

    if (!fs.existsSync(oldPath)) {
      console.error('原路径不存在:', oldPath);
      return res.status(404).json({
        error: '原文件夹不存在',
        details: { oldPath, base, requestParams: req.body }
      });
    }
    if (fs.existsSync(newPath)) {
      console.error('目标路径已存在:', newPath);
      return res.status(409).json({ error: '新文件夹已存在' });
    }

    // 执行重命名
    console.log('执行重命名:', oldPath, '->', newPath);
    fs.renameSync(oldPath, newPath);
    res.json({ success: true });
  } catch (err) {
    console.error('重命名发生错误:', err);
    res.status(500).json({ error: err.message });
  }
});

// 新增：删除文件夹
router.post('/delete-folder', (req, res) => {
  const { folderName } = req.body;
  if (!folderName) {
    return res.status(400).json({ error: '缺少参数' });
  }
  if (folderName.includes('/') || folderName.includes('\\')) {
    return res.status(400).json({ error: '文件夹名称不能包含路径分隔符' });
  }

  // 获取 base 参数，支持多级目录
  const { base = '' } = req.body;

  const baseDir = path.resolve(__dirname, '../../draft');
  // 生成完整的目标路径（包含 base）
  const targetBaseDir = base ? path.join(baseDir, base) : baseDir;

  // 确保目标目录存在
  if (!fs.existsSync(targetBaseDir)) {
    return res.status(404).json({ error: '目标目录不存在' });
  }

  const folderPath = path.join(targetBaseDir, folderName);
  try {
    if (!fs.existsSync(folderPath)) {
      return res.status(404).json({ error: '文件夹不存在' });
    }
    // 递归删除文件夹
    const rimraf = (dir) => {
      if (fs.existsSync(dir)) {
        fs.readdirSync(dir).forEach((file) => {
          const curPath = path.join(dir, file);
          if (fs.lstatSync(curPath).isDirectory()) {
            rimraf(curPath);
          } else {
            fs.unlinkSync(curPath);
          }
        });
        fs.rmdirSync(dir);
      }
    };
    rimraf(folderPath);
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// 新端点: 专门处理章节重命名
router.post('/rename-chapter', (req, res) => {
  const { oldName, newName, projectName } = req.body;
  console.log('章节重命名请求:', { oldName, newName, projectName });

  if (!oldName || !newName || !projectName) {
    return res.status(400).json({ error: '缺少参数' });
  }
  if (oldName === newName) {
    return res.json({ success: true });
  }

  // 章节重命名的关键是路径拼接正确
  const baseDir = path.resolve(__dirname, '../../draft');
  const oldPath = path.join(baseDir, projectName, oldName);
  const newPath = path.join(baseDir, projectName, newName);

  console.log('章节重命名路径:', {
    baseDir,
    projectName,
    oldName,
    newName,
    oldPath,
    newPath,
    oldPathExists: fs.existsSync(oldPath)
  });

  try {
    if (!fs.existsSync(oldPath)) {
      console.error('原章节路径不存在:', oldPath);
      return res.status(404).json({ error: '原章节文件夹不存在' });
    }
    if (fs.existsSync(newPath)) {
      console.error('目标章节路径已存在:', newPath);
      return res.status(409).json({ error: '新章节文件夹已存在' });
    }

    // 执行重命名
    fs.renameSync(oldPath, newPath);
    console.log('章节重命名成功:', oldPath, ' -> ', newPath);
    res.json({ success: true });
  } catch (err) {
    console.error('章节重命名出错:', err);
    res.status(500).json({ error: err.message });
  }
});

// 新增：保存项目特定的JSON文件 (例如 Global.json)
router.post('/save-project-file', async (req, res) => {
  const { projectTitle, fileName, content, filePath, chapterTitle } = req.body;
  console.log('保存项目文件请求:', { projectTitle, fileName, filePath, chapterTitle });

  if (!fileName || content === undefined) {
    return res.status(400).json({ success: false, error: '缺少 fileName 或 content 参数' });
  }

  // 验证 fileName
  if (!fileName.endsWith('.json') || fileName.includes('/') || fileName.includes('\\')) {
    return res.status(400).json({ success: false, error: '无效的文件名或类型。只允许 .json 文件，且文件名不能包含路径分隔符。' });
  }

  try {
    let targetPath;
    if (filePath) {
      // 如果前端传了完整路径，直接用
      targetPath = path.resolve(__dirname, '../../', filePath);
    } else if (projectTitle && chapterTitle) {
      // 如果有章节名，拼接章节目录
      targetPath = path.resolve(__dirname, '../../draft', projectTitle, chapterTitle, fileName);
    } else if (projectTitle) {
      // 兼容老逻辑
      targetPath = path.resolve(__dirname, '../../draft', projectTitle, fileName);
    } else {
      return res.status(400).json({ success: false, error: '缺少 projectTitle 或 filePath' });
    }

    // 确保目录存在
    await fs.promises.mkdir(path.dirname(targetPath), { recursive: true });

    // 写入文件
    await fs.promises.writeFile(targetPath, JSON.stringify(content, null, 2), 'utf8');
    console.log(`文件成功保存到: ${targetPath}`);
    res.json({ success: true, message: '文件保存成功', path: targetPath });
  } catch (error) {
    console.error(`保存文件 ${fileName} 失败:`, error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 添加文件上传处理 - 用于上传字幕和音频文件

// 处理文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    console.log('====== STORAGE DESTINATION 调试 ======');
    console.log('请求体原始内容:', req.body);
    console.log('Multer 文件信息:', file);

    // 从URL查询参数中获取项目和章节信息（作为备选）
    const urlParams = new URL(`http://example.com${req.url}`).searchParams;
    const projectFromUrl = urlParams.get('projectTitle');
    const chapterFromUrl = urlParams.get('chapterTitle');

    // 优先使用请求体中的数据，其次使用URL参数
    const projectTitle = req.body.projectTitle || projectFromUrl;
    const chapterTitle = req.body.chapterTitle || chapterFromUrl;

    console.log('参数解析结果:', {
      projectTitle,
      chapterTitle,
      fromBody: !!req.body.projectTitle,
      fromUrl: !!projectFromUrl
    });

    if (!projectTitle || !chapterTitle) {
      console.error('项目标题或章节标题缺失 (destination):', { projectTitle, chapterTitle });
      return cb(new Error('项目标题或章节标题缺失'));
    }

    // 构建保存目录
    const targetDir = path.resolve(__dirname, '../../draft', projectTitle, chapterTitle);
    console.log('目标保存目录:', targetDir);

    // 确保目录存在
    try {
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
        console.log('创建目录:', targetDir);
      }
      cb(null, targetDir);
    } catch (error) {
      console.error('创建目录失败:', error);
      cb(error);
    }
  },
  filename: (_req, file, cb) => {
    // 使用原始文件名
    console.log('设置文件名:', file.originalname);
    cb(null, file.originalname);
  }
});

// 处理文件上传
router.post('/upload-file', (req, res) => {
  console.log('接收到文件上传请求');
  console.log('请求体预览:', req.body); // 这可能是空的，因为multer还没解析

  // 使用单独的multer实例而不是共享实例
  const singleUpload = multer({
    storage,
    limits: {
      fileSize: 50 * 1024 * 1024, // 限制50MB
    },
    fileFilter: (_req, file, cb) => {
      // SRT和音频文件类型过滤
      console.log('上传文件类型:', file.mimetype, '文件名:', file.originalname);
      if (file.originalname.toLowerCase().endsWith('.srt') ||
          file.mimetype.startsWith('audio/')) {
        cb(null, true);
      } else {
        cb(new Error('仅支持SRT和音频文件'));
      }
    }
  }).single('file');

  singleUpload(req, res, (err) => {
    console.log('Multer处理完成，检查结果');
    console.log('请求体内容 (multer处理后):', req.body);

    if (err) {
      console.error('文件上传处理错误:', err);
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }

    if (!req.file) {
      console.error('没有文件被上传');
      return res.status(400).json({
        success: false,
        error: '没有文件被上传'
      });
    }

    const projectTitle = req.body.projectTitle;
    const chapterTitle = req.body.chapterTitle;

    console.log('文件上传参数检查:', {
      projectTitle,
      chapterTitle,
      'req.body类型': typeof req.body,
      '请求体内容': req.body,
      'req.file': req.file ? '存在' : '不存在'
    });

    if (!projectTitle || !chapterTitle) {
      console.error('项目标题或章节标题缺失:', { projectTitle, chapterTitle });
      return res.status(400).json({
        success: false,
        error: '项目标题或章节标题缺失',
        debug: {
          body: req.body,
          hasFile: !!req.file,
          contentType: req.headers['content-type']
        }
      });
    }

    console.log('文件上传成功:', {
      projectTitle,
      chapterTitle,
      fileName: req.file.originalname,
      fileSize: req.file.size
    });

    // 返回文件保存路径
    const relativePath = path.join('draft', projectTitle, chapterTitle, req.file.originalname)
      .replace(/\\/g, '/'); // 替换Windows路径分隔符为Web分隔符

    console.log('返回相对路径:', relativePath);

    res.json({
      success: true,
      path: relativePath,
      filename: req.file.originalname
    });
  });
});

// 检查是否已经有list-files接口，如果没有则添加
// 列出目录中的文件（只列出文件，不包括子目录）
router.get('/list-files', (req, res) => {
  const targetPath = req.query.path || '';
  console.log('列出目录文件请求:', targetPath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', targetPath);
  console.log('完整路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      console.log('目录不存在, 创建目录:', fullPath);
      fs.mkdirSync(fullPath, { recursive: true });
      return res.json([]);
    }

    // 获取目录内容
    const files = fs.readdirSync(fullPath).filter(item => {
      const itemPath = path.join(fullPath, item);
      // 只返回文件，不返回目录
      return fs.statSync(itemPath).isFile();
    });

    console.log('目录内文件:', files);
    res.json(files);
  } catch (error) {
    console.error('列出文件错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除文件
router.post('/delete-file', (req, res) => {
  const { filePath } = req.body;

  if (!filePath) {
    return res.status(400).json({ error: '缺少文件路径' });
  }

  console.log('删除文件请求:', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整删除路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ error: '文件不存在' });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      return res.status(400).json({ error: '目标不是文件' });
    }

    // 删除文件
    fs.unlinkSync(fullPath);
    console.log('文件删除成功:', fullPath);

    res.json({ success: true, message: '文件删除成功' });
  } catch (error) {
    console.error('删除文件错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 专用图片删除API
router.delete('/delete-image', (req, res) => {
  const { filePath } = req.body;

  if (!filePath) {
    return res.status(400).json({ error: '缺少图片文件路径' });
  }

  console.log('🗑️ [图片删除API] 删除图片请求:', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('🗑️ [图片删除API] 完整删除路径:', fullPath);

  try {
    // 安全检查：确保文件在允许的目录内
    const baseDir = path.resolve(__dirname, '../../');
    if (!fullPath.startsWith(baseDir)) {
      console.error('🗑️ [图片删除API] 安全检查失败: 路径不在允许范围内');
      return res.status(403).json({ error: '访问路径不允许' });
    }

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      console.warn('🗑️ [图片删除API] 文件不存在:', fullPath);
      return res.status(404).json({ error: '图片文件不存在' });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      console.error('🗑️ [图片删除API] 目标不是文件:', fullPath);
      return res.status(400).json({ error: '目标不是文件' });
    }

    // 检查文件类型（可选的安全措施）
    const fileExtension = path.extname(fullPath).toLowerCase();
    const allowedExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'];
    if (!allowedExtensions.includes(fileExtension)) {
      console.warn('🗑️ [图片删除API] 文件类型不是图片:', fileExtension);
      return res.status(400).json({ error: '文件类型不是支持的图片格式' });
    }

    // 删除文件
    fs.unlinkSync(fullPath);
    console.log('🗑️ [图片删除API] 图片删除成功:', fullPath);

    res.json({
      success: true,
      message: '图片删除成功',
      deletedFile: filePath
    });
  } catch (error) {
    console.error('🗑️ [图片删除API] 删除图片错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 读取文件内容
router.get('/read-file', (req, res) => {
  const { path: filePath } = req.query;

  if (!filePath) {
    return res.status(400).json({
      success: false,
      error: '缺少文件路径参数'
    });
  }

  console.log('读取文件内容请求:', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整文件路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在',
        path: fullPath
      });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      return res.status(400).json({
        success: false,
        error: '目标不是文件'
      });
    }

    // 读取文件内容
    const content = fs.readFileSync(fullPath, 'utf8');
    console.log('文件读取成功，内容长度:', content.length);

    res.json({
      success: true,
      content: content,
      path: filePath
    });
  } catch (error) {
    console.error('读取文件错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 提供文件内容的替代端点 (用于兼容性)
router.get('/file-content', (req, res) => {
  const { path: filePath } = req.query;

  if (!filePath) {
    return res.status(400).json({
      success: false,
      error: '缺少文件路径参数'
    });
  }

  console.log('获取文件内容请求 (兼容端点):', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整文件路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在'
      });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      return res.status(400).json({
        success: false,
        error: '目标不是文件'
      });
    }

    // 读取文件内容
    const content = fs.readFileSync(fullPath, 'utf8');
    console.log('文件读取成功，内容长度:', content.length);

    res.json({
      success: true,
      content: content,
      path: filePath
    });
  } catch (error) {
    console.error('读取文件错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 保存项目数据
router.post('/save-project-data', (req, res) => {
  const { projectTitle, chapterTitle, data } = req.body;

  if (!projectTitle || !chapterTitle) {
    return res.status(400).json({ error: '缺少项目名称或章节名称' });
  }

  console.log('保存项目数据请求:', { projectTitle, chapterTitle });

  // 构建数据文件路径
  const targetDir = path.resolve(__dirname, '../../draft', projectTitle, chapterTitle);
  const dataFilePath = path.join(targetDir, 'project-data.json');

  try {
    // 确保目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // 检查是否有合并行
    if (data?.rows) {
      const mergedRows = data.rows.filter(row => row.isMerged);
      if (mergedRows.length > 0) {
        console.log(`项目包含 ${mergedRows.length} 个合并行`);
      }
    }

    // 智能合并保存：如果客户端没有发送某些字段，则保留服务器端现有的字段
    let finalData = data;

    if (fs.existsSync(dataFilePath)) {
      try {
        const existingData = JSON.parse(fs.readFileSync(dataFilePath, 'utf8'));

        // 如果新数据缺少 srtContent 但现有数据有，则保留现有的 srtContent
        if (!data.srtContent && existingData.srtContent) {
          finalData = { ...data, srtContent: existingData.srtContent };
          console.log('智能合并：保留现有的 srtContent 字段，避免数据丢失');
        }

        // 可以在这里添加其他需要保留的字段

      } catch (error) {
        console.warn('读取现有项目数据失败，将直接保存新数据:', error.message);
        finalData = data;
      }
    }

    // 写入数据文件
    fs.writeFileSync(dataFilePath, JSON.stringify(finalData, null, 2));
    console.log('项目数据保存成功:', dataFilePath);

    res.json({ success: true, message: '项目数据保存成功' });
  } catch (error) {
    console.error('保存项目数据错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 🆕 强制重置ComfyUI状态的API端点
router.post('/force-reset-comfyui-state', (req, res) => {
  try {
    console.log('🔧 [API] 收到强制重置ComfyUI状态请求');

    // 这里可以添加更多的状态重置逻辑
    // 比如清理临时文件、重置配置等

    const resetResult = {
      success: true,
      message: '状态重置完成',
      timestamp: new Date().toISOString(),
      actions: [
        '清理了服务器端状态缓存',
        '重置了任务队列',
        '清理了临时数据'
      ]
    };

    console.log('🔧 [API] ComfyUI状态重置完成:', resetResult);
    res.json(resetResult);
  } catch (error) {
    console.error('🔧 [API] 强制重置ComfyUI状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: '状态重置失败'
    });
  }
});

// 🆕 验证ComfyUI状态一致性的API端点
router.get('/validate-comfyui-state', (req, res) => {
  try {
    console.log('🔍 [API] 收到验证ComfyUI状态请求');

    // 这里可以添加状态验证逻辑
    const validation = {
      isConsistent: true,
      issues: [],
      recommendations: [],
      serverState: {
        hasActiveConnections: false,
        activeTasks: 0,
        lastActivity: null
      },
      timestamp: new Date().toISOString()
    };

    console.log('🔍 [API] ComfyUI状态验证完成:', validation);
    res.json(validation);
  } catch (error) {
    console.error('🔍 [API] 验证ComfyUI状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: '状态验证失败'
    });
  }
});

// 确保设置目录存在
async function ensureSettingsDir() {
  const settingsDir = path.join(process.cwd(), 'userdata');
  try {
    await fsPromises.access(settingsDir);
  } catch {
    await fsPromises.mkdir(settingsDir, { recursive: true });
  }
  return settingsDir;
}

// 保存用户设置 - 增强版
router.post('/save-user-settings', async (req, res) => {
  console.log('===== 接收到保存设置请求 =====');
  console.log('请求头:', req.headers);
  console.log('请求体类型:', typeof req.body);
  console.log('请求体内容:', req.body);

  // 检查请求体是否为空
  if (!req.body || Object.keys(req.body).length === 0) {
    console.error('请求体为空或未正确解析');
    return res.status(400).json({
      success: false,
      error: '请求体为空或未正确解析'
    });
  }

  try {
    // 直接从 req.body 中获取参数，避免解构失败
    const settings = req.body.settings;
    const filename = req.body.filename;

    console.log('提取的参数:', { settings: settings ? '存在' : '不存在', filename });

    if (!settings) {
      console.error('缺少 settings 参数');
      return res.status(400).json({ success: false, error: '缺少 settings 参数' });
    }

    if (!filename) {
      console.error('缺少 filename 参数');
      return res.status(400).json({ success: false, error: '缺少 filename 参数' });
    }

    console.log('开始保存文件...');
    const settingsDir = await ensureSettingsDir();
    const filePath = path.join(settingsDir, filename);

    // 确保 settings 是字符串
    const settingsStr = typeof settings === 'string' ? settings : JSON.stringify(settings, null, 2);
    console.log('处理后的字符串长度:', settingsStr.length);

    await fsPromises.writeFile(filePath, settingsStr);
    console.log('文件写入成功:', filePath);

    res.json({
      success: true,
      path: filePath
    });
  } catch (error) {
    console.error('保存用户设置异常:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 加载用户设置
router.get('/load-user-settings', async (req, res) => {
  try {
    const { filename } = req.query;
    if (!filename) {
      return res.status(400).json({ success: false, error: '缺少文件名参数' });
    }

    const settingsDir = await ensureSettingsDir();
    const filePath = path.join(settingsDir, filename);

    try {
      const settings = await fsPromises.readFile(filePath, 'utf8');
      res.json({
        success: true,
        settings: JSON.parse(settings),
        path: filePath
      });
    } catch (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在时返回默认设置
        const defaultSettings = {
          theme: 'dark',
          language: 'zh-CN',
          autoSave: true,
          llm: {
            provider: 'openrouter',
            defaultModel: 'anthropic/claude-3-sonnet',
            temperature: 0.7,
            localUrl: 'http://localhost:8080'
          }
        };

        // 自动创建默认设置文件
        await fsPromises.writeFile(filePath, JSON.stringify(defaultSettings, null, 2));

        res.json({
          success: true,
          settings: defaultSettings,
          path: filePath
        });
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('加载用户设置出错:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 检查文件是否存在
router.get('/check-file-exists', (req, res) => {
  const { filePath } = req.query;
  console.log('检查文件是否存在:', filePath);

  if (!filePath) {
    return res.status(400).json({ error: '缺少文件路径参数' });
  }

  try {
    // 规范化路径，确保安全
    const fullPath = path.resolve(__dirname, '../../', filePath);
    // 确保文件路径在允许的目录内，避免目录遍历攻击
    const baseDir = path.resolve(__dirname, '../../');
    if (!fullPath.startsWith(baseDir)) {
      return res.status(403).json({ error: '访问路径不允许' });
    }

    const exists = fs.existsSync(fullPath);
    console.log('文件路径:', fullPath, '存在状态:', exists);

    res.json({ exists });
  } catch (error) {
    console.error('检查文件存在性出错:', error);
    res.status(500).json({ error: error.message });
  }
});

// 保存文本文件
router.post('/save-text-file', async (req, res) => {
  const { filePath, content } = req.body;

  if (!filePath || content === undefined) {
    return res.status(400).json({ success: false, error: '缺少文件路径或内容' });
  }

  console.log('保存文本文件请求:', { filePath, contentLength: content.length });

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整文件路径:', fullPath);

  try {
    // 确保目录存在
    const dirPath = path.dirname(fullPath);
    if (!fs.existsSync(dirPath)) {
      console.log('目录不存在，创建目录:', dirPath);
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // 写入文件
    await fsPromises.writeFile(fullPath, content, 'utf8');
    console.log('文件保存成功:', fullPath);

    res.json({ success: true, message: '文件保存成功', path: filePath });
  } catch (error) {
    console.error('保存文件错误:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 辅助函数：加载用户LLM设置
async function loadLLMSettings() {
  const settingsPath = path.join(process.cwd(), 'userdata', 'usersettings.json');
  try {
    const data = await fsPromises.readFile(settingsPath, 'utf8');
    const settings = JSON.parse(data);
    return settings.llm;
  } catch (error) {
    console.error('无法加载LLM设置:', error);
    // 返回一个安全的默认值或抛出错误
    return { provider: 'none' }; // 表示无有效配置
  }
}

// 辅助函数：调用LLM API (简化示例)
async function callLLMAPI(prompt, settings) {
  let apiUrl, headers, requestBody;
  const model = settings.defaultModel || ''; // 获取模型设置
  const apiKey = settings[`${settings.provider}ApiKey`] || ''; // 获取对应提供商的API Key
  const temperature = settings.temperature || 0.7;

  console.log(`[LLM Backend] 使用提供商: ${settings.provider}, 模型: ${model}`);

  switch (settings.provider) {
    case 'google':
      if (!apiKey) throw new Error('缺少 Google API Key');
      apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;
      headers = { 'Content-Type': 'application/json' };
      requestBody = {
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: { temperature }
      };
      break;

    case 'openrouter': {
      if (!apiKey) throw new Error('缺少 OpenRouter API Key');
      apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
      // OpenRouter 需要特定的模型格式 provider/model
      let openRouterModel = model.includes('/') ? model : `google/${model}`; // 尝试修正格式
      console.log(`[LLM Backend] OpenRouter 使用模型: ${openRouterModel}`);
      headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        // OpenRouter 可能需要这些头信息
        // 'HTTP-Referer': 'YOUR_SITE_URL',
        // 'X-Title': 'YOUR_APP_NAME'
      };
      requestBody = {
        model: openRouterModel,
        messages: [{ role: 'user', content: prompt }],
        temperature
      };
      break;
    }

    case 'openai':
      if (!apiKey) throw new Error('缺少 OpenAI API Key');
      apiUrl = 'https://api.openai.com/v1/chat/completions';
      headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      };
      requestBody = {
        model: model || 'gpt-3.5-turbo', // 默认模型
        messages: [{ role: 'user', content: prompt }],
        temperature
      };
      break;

    case 'anthropic':
       if (!apiKey) throw new Error('缺少 Anthropic API Key');
       apiUrl = 'https://api.anthropic.com/v1/messages';
       headers = {
         'Content-Type': 'application/json',
         'Authorization': `Bearer ${apiKey}`,
         'anthropic-version': '2023-06-01' // 必需
       };
       requestBody = {
         model: model || 'claude-3-haiku-20240307', // 默认模型
         max_tokens: 4000, // Anthropic 通常需要指定 max_tokens
         messages: [{ role: 'user', content: prompt }],
         temperature
       };
       break;

    case 'local':
       // 处理本地LLM，需要正确的 localUrl (通常包含完整路径)
       apiUrl = settings.localUrl; // 假设localUrl是完整的API端点
       if (!apiUrl) throw new Error('缺少本地 LLM URL (localUrl)');
       console.log(`[LLM Backend] 调用本地LLM: ${apiUrl}`);
       headers = { 'Content-Type': 'application/json' };
       // 本地LLM的请求体格式可能不同，这里使用类似OpenAI的格式作为示例
       requestBody = {
         model: model || 'local-model', // 可能需要指定模型
         messages: [{ role: 'user', content: prompt }],
         temperature,
         // 可能需要其他参数，如 stream: false
       };
       // 如果本地API需要密钥
       if (settings.localApiKey) {
         headers['Authorization'] = `Bearer ${settings.localApiKey}`;
       }
       break;

    default:
      throw new Error(`不支持的LLM提供商: ${settings.provider}`);
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[LLM Backend] ${settings.provider} API 请求失败: ${response.status}`, errorText);
      throw new Error(`${settings.provider} API 请求失败: ${response.status}. 响应: ${errorText}`);
    }

    const responseData = await response.json();
    console.log(`[LLM Backend] ${settings.provider} API 响应成功`);

    // 提取文本 (需要根据不同提供商调整)
    let extractedText = '';
    if (settings.provider === 'google') {
       extractedText = responseData.candidates?.[0]?.content?.parts?.[0]?.text || '';
    } else if (settings.provider === 'anthropic') {
       extractedText = responseData.content?.[0]?.text || '';
    } else { // OpenAI, OpenRouter, Local (假设类似OpenAI)
       extractedText = responseData.choices?.[0]?.message?.content || '';
    }

    if (!extractedText) {
        console.warn('[LLM Backend] 未能从API响应中提取文本:', responseData);
        // 可以尝试返回原始响应，让前端处理
        // throw new Error('未能从API响应中提取文本');
        return JSON.stringify(responseData); // 返回原始JSON字符串
    }

    return extractedText;

  } catch (error) {
    console.error(`[LLM Backend] 调用 ${settings.provider} API 时出错:`, error);
    throw error; // 将错误向上抛出
  }
}

// 辅助函数：为特定任务构建提示词
function buildPrompt(text, analyzeType) {
  switch (analyzeType) {
    case 'global-reasoning':
      return `
任务：分析剧本/故事文本，提取关键视觉元素。

请仔细阅读以下文本内容：
---
${text}
---

根据文本，提取以下信息，并以严格的JSON格式返回。请确保JSON结构完整且所有字符串值都使用双引号包裹。

1.  **角色列表 (characters)**: 数组形式。每个角色对象应包含：
    *   \`name\` (string): 角色名称。
    *   \`visualTraits\` (string): 角色的关键视觉描述（服装、外貌、武器、配饰等）。

2.  **时代/背景设定 (setting)**: 对象形式。应包含：
    *   \`name\` (string, optional): 时代或环境的名称（如"唐朝长安"、"赛博朋克都市"）。
    *   \`era\` (string, optional): 更具体的时代描述（如"盛唐时期"、"近未来"）。
    *   \`description\` (string): 对场景或背景的关键视觉描述。

3.  **武功/能力/特殊物品 (abilities)**: 数组形式。每个对象应包含：
    *   \`name\` (string): 武功、超能力或特殊物品的名称。
    *   \`visualEffect\` (string): 该能力或物品使用时的视觉效果描述。

如果某项信息在文本中未明确提及，请在对应字段留空字符串 "" 或省略该字段。
请确保输出的JSON是有效的。

输出示例 (仅为结构示例，内容需根据文本生成):
\`\`\`json
{
  "characters": [
    {
      "name": "李寻欢",
      "visualTraits": "身着白衣，手持飞刀，眼神忧郁"
    },
    {
      "name": "阿飞",
      "visualTraits": "快剑，沉默寡言，黑衣劲装"
    }
  ],
  "setting": {
    "name": "古龙江湖",
    "description": "风雪交加的小镇客栈，灯火昏黄"
  },
  "abilities": [
    {
      "name": "小李飞刀",
      "visualEffect": "一道银光闪过，快得无法看清"
    },
    {
      "name": "阿飞的快剑",
      "visualEffect": "剑光一闪，迅疾无比"
    }
  ]
}
\`\`\`

请直接输出JSON内容。
`;
    // 可以为其他 analyzeType 添加 case
    default:
      // 默认或通用提示词
      return `请分析以下文本：\n---\n${text}\n---`;
  }
}

// 新增：处理文本分析请求
router.post('/analyze-text', async (req, res) => {
  // 从请求体中解构 text, analyzeType, 以及我们期望的自定义 prompt
  const { text, analyzeType, prompt: customPromptFromRequest } = req.body;

  if (!text) { // analyzeType 可以暂时不作为硬性要求，如果提供了customPrompt
    return res.status(400).json({ success: false, error: '缺少 text 参数' });
  }

  // 如果没有自定义 prompt，但有 analyzeType，则按旧逻辑构建 prompt
  // 如果既没有自定义 prompt，也没有 analyzeType，则可能需要错误处理或默认行为
  if (!customPromptFromRequest && !analyzeType) {
    return res.status(400).json({ success: false, error: '缺少 analyzeType 或自定义 prompt 参数' });
  }

  console.log(`[API /analyze-text] 收到请求 - 类型: ${analyzeType || '自定义'}, 文本长度: ${text.length}`);
  if (customPromptFromRequest) {
    console.log(`[API /analyze-text] 收到自定义Prompt模板，长度: ${customPromptFromRequest.length}`);
  }

  try {
    // 1. 加载LLM设置
    const llmSettings = await loadLLMSettings();
    if (!llmSettings || llmSettings.provider === 'none') {
       return res.status(500).json({ success: false, error: '无法加载或未配置LLM设置' });
    }

    // 2. 构建最终的提示词给LLM
    let finalPromptForLLM;
    if (customPromptFromRequest) {
      // 如果前端提供了自定义prompt模板，使用它并替换占位符
      // 假设占位符是 [TEXT_PLACEHOLDER] 或 {text}
      finalPromptForLLM = customPromptFromRequest
                            .replace(/\[TEXT_PLACEHOLDER\]/g, text) // 兼容 [TEXT_PLACEHOLDER]
                            .replace(/\{text\}/g, text);          // 兼容 {text}
      console.log('[API /analyze-text] 使用前端提供的自定义Prompt模板并组合文本');
    } else {
      // 否则，使用旧的 buildPrompt 逻辑（基于 analyzeType）
      finalPromptForLLM = buildPrompt(text, analyzeType);
      console.log('[API /analyze-text] 使用后端内置的Prompt模板');
    }

    console.log(`[API /analyze-text] 最终发送给LLM的Prompt (前300字符): ${finalPromptForLLM.substring(0,300)}`);

    // 3. 调用LLM API
    console.log(`[API /analyze-text] 准备调用LLM: ${llmSettings.provider}`);
    const llmResponseText = await callLLMAPI(finalPromptForLLM, llmSettings); // 使用 finalPromptForLLM
    console.log(`[API /analyze-text] LLM 响应文本长度: ${llmResponseText.length}`);

    // 4. 处理响应 - 添加对'custom-prompt'的特殊处理
    let jsonData = null;
    let parseError = null;
    let rawOutput = llmResponseText; // 保留原始输出

    // 当analyzeType为'custom-prompt'时，不进行JSON解析，直接返回原始文本
    if (analyzeType === 'custom-prompt') {
      console.log('[API /analyze-text] 检测到custom-prompt类型，跳过JSON解析，直接返回原始文本');

      // 根据需求决定是否尝试解析为JSON - 这里我们不解析，直接返回原始文本
      res.json({
        success: true,
        data: null, // 不返回结构化数据，前端需要直接使用text
        text: rawOutput, // LLM返回的原始文本
        parseError: null
      });
      return;
    } else if (analyzeType === 'global-reasoning') {
      try {
        // 清理可能的Markdown代码块标记
        const cleanedText = llmResponseText
          .replace(/```json\n?/, '')
          .replace(/\n?```$/, '')
          .trim();
        jsonData = JSON.parse(cleanedText);
        console.log('[API /analyze-text] JSON 解析成功');
      } catch (err) {
        console.error('[API /analyze-text] JSON 解析失败:', err);
        parseError = err.message;
        // jsonData 保持为 null
      }
    }

    // 5. 返回结果
    res.json({
      success: true,
      data: jsonData, // 解析后的JSON数据，如果解析失败则为null
      text: rawOutput, // LLM返回的原始文本
      parseError: parseError // 如果JSON解析失败，包含错误信息
    });

  } catch (error) {
    console.error(`[API /analyze-text] 处理失败 - 类型: ${analyzeType || '自定义'}:`, error);
    res.status(500).json({
        success: false,
        error: `文本分析处理失败: ${error.message}`,
        provider: error.provider || undefined // 传递提供商信息（如果有）
     });
  }
});

// --- BEGIN Prompt Management API Endpoints ---
const PROMPTS_JSON_PATH = path.join(process.cwd(), 'userdata', 'prompts.json');

// Helper function: 读取 prompts.json
async function readPromptsJson() {
  const data = await fsPromises.readFile(PROMPTS_JSON_PATH, 'utf8');
  return JSON.parse(data);
}
// Helper function: 写入 prompts.json
async function writePromptsJson(json) {
  await fsPromises.writeFile(PROMPTS_JSON_PATH, JSON.stringify(json, null, 2), 'utf8');
}

// 获取 globalReasoning.default
router.get('/prompt/default', async (_req, res) => {
  try {
    const data = await readPromptsJson();
    if (data.globalReasoning?.default) {
      res.send(Array.isArray(data.globalReasoning.default)
        ? data.globalReasoning.default.join('\n')
        : data.globalReasoning.default);
    } else {
      res.status(404).send('prompts.json 缺少 globalReasoning.default');
    }
  } catch (error) {
    console.error('读取 prompts.json 失败:', error);
    res.status(500).send('读取 prompts.json 失败');
  }
});

// 获取 globalReasoning.user
router.get('/prompt/user', async (_req, res) => {
  try {
    const data = await readPromptsJson();
    if (data.globalReasoning?.user) {
      res.send(Array.isArray(data.globalReasoning.user)
        ? data.globalReasoning.user.join('\n')
        : data.globalReasoning.user);
    } else {
      res.status(404).send('prompts.json 缺少 globalReasoning.user');
    }
  } catch (error) {
    console.error('读取 prompts.json 失败:', error);
    res.status(500).send('读取 prompts.json 失败');
  }
});

// 保存 globalReasoning.user
router.post('/prompt/user', bodyParser.json(), async (req, res) => {
  const { prompt } = req.body;
  if (typeof prompt !== 'string') {
    return res.status(400).send('Invalid prompt content: must be a string.');
  }
  try {
    const data = await readPromptsJson();
    if (!data.globalReasoning) data.globalReasoning = {};
    // 存为多行数组
    data.globalReasoning.user = prompt.split(/\r?\n/);
    await writePromptsJson(data);
    res.status(200).send('User prompt saved successfully.');
  } catch (error) {
    console.error('Error saving user prompt:', error);
    res.status(500).send('Error saving user prompt.');
  }
});

// --- END Prompt Management API Endpoints ---

// 新增：保存 prompts.json 的 aiGrouping.user 等内容
router.post('/save-userdata-prompts', async (req, res) => {
  try {
    const promptsPath = path.join(process.cwd(), 'userdata', 'prompts.json');
    const content = req.body;
    if (!content) {
      return res.status(400).json({ success: false, error: '缺少内容' });
    }
    await fs.promises.writeFile(promptsPath, JSON.stringify(content, null, 2), 'utf8');
    res.json({ success: true });
  } catch (e) {
    res.status(500).json({ success: false, error: e.message });
  }
});

// 获取图片
router.get('/get-image', (req, res) => {
  const imagePath = req.query.path;
  if (!imagePath) {
    return res.status(400).json({ error: '缺少图片路径参数' });
  }

  const fullPath = path.resolve(imagePath);

  // 安全检查：确保路径在允许的目录内
  const allowedDir = path.resolve('./');
  if (!fullPath.startsWith(allowedDir)) {
    return res.status(403).json({ error: '访问被拒绝' });
  }

  // 检查文件是否存在
  if (!fs.existsSync(fullPath)) {
    return res.status(404).json({ error: '图片文件不存在' });
  }

  // 设置正确的Content-Type
  const ext = path.extname(fullPath).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };

  const mimeType = mimeTypes[ext] || 'application/octet-stream';
  res.setHeader('Content-Type', mimeType);

  // 发送文件
  res.sendFile(fullPath);
});



// 保存ComfyUI生成的图像
const imageUpload = multer({
  dest: 'temp/', // 临时目录
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB限制
  }
});

router.post('/save-generated-image', imageUpload.single('file'), (req, res) => {
  try {
    const { projectTitle, chapterTitle, subfolder = 'Img' } = req.body;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: '没有上传文件' });
    }

    if (!projectTitle || !chapterTitle) {
      return res.status(400).json({ error: '缺少项目标题或章节标题' });
    }

    // 构建目标目录路径
    const targetDir = path.join('./draft', projectTitle, chapterTitle, subfolder);

    // 确保目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log('🎨 [图像保存] 创建目录:', targetDir);
    }

    // 构建目标文件路径
    const targetPath = path.join(targetDir, file.originalname);

    // 移动文件到目标位置
    fs.renameSync(file.path, targetPath);

    console.log('🎨 [图像保存] 文件已保存:', {
      originalName: file.originalname,
      targetPath: targetPath,
      size: file.size
    });

    res.json({
      success: true,
      path: targetPath,
      filename: file.originalname,
      size: file.size
    });

  } catch (error) {
    console.error('🎨 [图像保存] 保存失败:', error);
    res.status(500).json({
      error: '保存图像失败',
      details: error.message
    });
  }
});

// 🆕 ComfyUI代理端点 - 增强错误处理和重试机制
router.all('/comfyui-proxy/*', async (req, res) => {
  const comfyuiPath = req.params[0]; // 获取路径参数
  const comfyuiUrl = `http://localhost:8188/${comfyuiPath}`;
  const maxRetries = 3; // 最大重试次数
  let lastError = null;

  // 🆕 重试循环
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🎨 [ComfyUI代理] 尝试 ${attempt}/${maxRetries}: ${req.method} ${comfyuiPath}`);

      const result = await attemptComfyUIRequest(req, res, comfyuiPath, comfyuiUrl);
      if (result.success) {
        return; // 成功则直接返回
      }

      lastError = result.error;

    } catch (error) {
      lastError = error;
      console.warn(`⚠️ [ComfyUI代理] 尝试 ${attempt}/${maxRetries} 失败:`, error.message);

      // 🆕 如果是最后一次尝试，或者是不可重试的错误，直接抛出
      if (attempt === maxRetries || !isRetryableError(error)) {
        throw error;
      }

      // 🆕 等待后重试（指数退避）
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      console.log(`⏳ [ComfyUI代理] 等待 ${delay}ms 后重试...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // 如果所有重试都失败了
  throw lastError || new Error('所有重试尝试都失败了');
});

// 🆕 判断错误是否可重试
function isRetryableError(error) {
  const retryableCodes = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT'];
  const retryableMessages = ['socket hang up', 'timeout', 'network error'];

  return retryableCodes.includes(error.code) ||
         retryableMessages.some(msg => error.message.toLowerCase().includes(msg)) ||
         error.name === 'AbortError';
}

// 🆕 单次ComfyUI请求尝试
async function attemptComfyUIRequest(req, res, comfyuiPath, comfyuiUrl) {
  // 🚨 紧急修复：阻止特定任务ID的无限循环
  if (comfyuiPath.includes('3a6767c8-8b3e-46a7-89aa-eb3a2adec0f1')) {
    console.log(`🚨 [ComfyUI代理] 阻止无限循环任务: ${comfyuiPath}`);
    res.status(404).json({
      success: false,
      error: '任务已停止',
      message: '该任务已被强制停止以防止无限循环'
    });
    return { success: true }; // 表示请求已处理
  }

    // 🆕 智能频率控制 - 根据端点类型设置不同的限制
  const frequencyControlledEndpoints = ['queue', 'history'];
  const needsFrequencyControl = frequencyControlledEndpoints.some(endpoint =>
    comfyuiPath.includes(endpoint)
  );

  // 🆕 system_stats端点不受频率限制（用于连接检查）
  const isSystemStats = comfyuiPath.includes('system_stats');
  const shouldApplyFrequencyControl = needsFrequencyControl && !isSystemStats;

  if (shouldApplyFrequencyControl) {
    const now = Date.now();
    const requestKey = `comfyui_${comfyuiPath.split('/')[0]}`;
    const lastRequest = global[requestKey] || 0;

    // 根据端点类型设置不同的间隔
    let minInterval = 1000; // 默认1秒
    if (comfyuiPath.includes('queue')) {
      minInterval = 2000; // queue端点2秒
    }

    if (now - lastRequest < minInterval) {
      const waitTime = Math.ceil((minInterval - (now - lastRequest)) / 1000);
      console.log(`🚦 [ComfyUI代理] 请求过于频繁，需等待 ${waitTime}s: ${comfyuiPath}`);
      res.status(429).json({
        success: false,
        error: '请求过于频繁',
        message: `请等待 ${waitTime} 秒后重试`,
        retryAfter: waitTime
      });
      return { success: true }; // 表示请求已处理
    }
    global[requestKey] = now;
  }

  // 🆕 特殊处理system_stats端点 - 提供增强的服务器状态检查
  if (comfyuiPath === 'system_stats') {
    console.log('🔍 [ComfyUI代理] 处理服务器状态检查请求');
    console.log('🔍 [ComfyUI代理] 目标URL:', comfyuiUrl);
    console.log('🔍 [ComfyUI代理] 请求方法:', req.method);
    console.log('🔍 [ComfyUI代理] 请求头:', req.headers);

    try {
      // 使用AbortController实现超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.warn('⏰ [ComfyUI代理] 请求超时，中止连接');
        controller.abort();
      }, 5000);

      console.log('🔍 [ComfyUI代理] 发送请求到ComfyUI...');
      const response = await fetch(comfyuiUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      });

      clearTimeout(timeoutId);
      console.log('🔍 [ComfyUI代理] ComfyUI响应状态:', response.status, response.statusText);
      console.log('🔍 [ComfyUI代理] ComfyUI响应头:', Object.fromEntries(response.headers.entries()));

      // 设置CORS头
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      if (response.ok) {
        const data = await response.json();
        console.log('✅ [ComfyUI代理] ComfyUI服务器可用，数据:', data);
        const responseData = {
          success: true,
          available: true,
          data: data,
          message: 'ComfyUI服务器运行正常',
          timestamp: Date.now()
        };
        console.log('🔍 [ComfyUI代理] 返回响应:', responseData);
        res.json(responseData);
        return { success: true }; // 表示请求已处理
      } else {
        console.warn(`⚠️ [ComfyUI代理] ComfyUI服务器响应异常: ${response.status} ${response.statusText}`);
        const errorResponse = {
          success: false,
          available: false,
          error: 'ComfyUI服务器响应异常',
          status: response.status,
          statusText: response.statusText,
          timestamp: Date.now()
        };
        console.log('🔍 [ComfyUI代理] 返回错误响应:', errorResponse);
        res.status(503).json(errorResponse);
        return { success: true }; // 表示请求已处理
      }
    } catch (error) {
      console.error('❌ [ComfyUI代理] 请求异常:', error);
      console.error('   错误类型:', error.name);
      console.error('   错误消息:', error.message);
      console.error('   错误堆栈:', error.stack);

      let errorResponse;
      if (error.name === 'AbortError') {
        console.warn('⚠️ [ComfyUI代理] ComfyUI服务器连接超时');
        errorResponse = {
          success: false,
          available: false,
          error: 'ComfyUI服务器连接超时',
          message: '服务器响应超时，请检查ComfyUI是否正在运行',
          timestamp: Date.now()
        };
      } else {
        console.warn('⚠️ [ComfyUI代理] ComfyUI服务器不可用:', error.message);
        errorResponse = {
          success: false,
          available: false,
          error: 'ComfyUI服务器不可用',
          message: error.message || error.toString() || 'Unknown error',
          errorType: error.name || 'UnknownError',
          timestamp: Date.now()
        };
      }

      console.log('🔍 [ComfyUI代理] 返回异常响应:', errorResponse);
      res.status(503).json(errorResponse);
      return { success: true }; // 表示请求已处理
    }
  }

  console.log(`🎨 [ComfyUI代理] ${req.method} ${comfyuiUrl}`);

  // 🆕 增强的请求选项 - 添加超时和错误处理
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    console.warn(`⏰ [ComfyUI代理] 请求超时，中止连接: ${comfyuiPath}`);
    controller.abort();
  }, 30000); // 30秒超时，适合大型工作流

  const fetchOptions = {
    method: req.method,
    signal: controller.signal,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Connection': 'keep-alive', // 🆕 保持连接活跃
      'User-Agent': 'txt2video-comfyui-client'
    }
  };

  // 如果有请求体，添加到选项中
  if (req.method !== 'GET' && req.method !== 'HEAD') {
    fetchOptions.body = JSON.stringify(req.body);
    console.log(`🎨 [ComfyUI代理] 请求体大小: ${fetchOptions.body.length} 字节`);
  }

  // 添加查询参数
  const url = new URL(comfyuiUrl);
  Object.keys(req.query).forEach(key => {
    url.searchParams.append(key, req.query[key]);
  });

  // 发送请求到ComfyUI
  console.log(`🎨 [ComfyUI代理] 发送请求: ${req.method} ${url.toString()}`);

  let response;
  try {
    response = await fetch(url.toString(), fetchOptions);
    clearTimeout(timeoutId);
  } catch (fetchError) {
    clearTimeout(timeoutId);

    // 🆕 详细的错误分析和处理
    if (fetchError.name === 'AbortError') {
      console.error(`⏰ [ComfyUI代理] 请求超时: ${comfyuiPath}`);
      throw new Error('ComfyUI请求超时，请检查服务器状态或减少工作流复杂度');
    } else if (fetchError.code === 'ECONNRESET') {
      console.error(`🔌 [ComfyUI代理] 连接被重置: ${comfyuiPath}`, fetchError.message);
      throw new Error('ComfyUI服务器连接被重置，可能是工作流过于复杂或服务器负载过高');
    } else if (fetchError.code === 'ECONNREFUSED') {
      console.error(`❌ [ComfyUI代理] 连接被拒绝: ${comfyuiPath}`, fetchError.message);
      throw new Error('无法连接到ComfyUI服务器，请确认服务器正在运行');
    } else {
      console.error(`❌ [ComfyUI代理] 网络错误: ${comfyuiPath}`, fetchError);
      throw new Error(`网络请求失败: ${fetchError.message}`);
    }
  }

  // 🆕 增强的响应处理
  console.log(`🎨 [ComfyUI代理] 响应状态: ${response.status} ${response.statusText}`);

  // 设置CORS头
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (response.ok) {
    // 复制响应头
    response.headers.forEach((value, key) => {
      if (key !== 'content-encoding') { // 避免压缩问题
        res.header(key, value);
      }
    });

    // 返回响应数据
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      try {
        const data = await response.json();
        console.log(`✅ [ComfyUI代理] 成功响应 (${comfyuiPath}):`, data);
        res.status(response.status).json(data);
        return { success: true }; // 表示请求已处理
      } catch (parseError) {
        console.error(`❌ [ComfyUI代理] JSON解析失败 (${comfyuiPath}):`, parseError);
        res.status(502).json({
          success: false,
          error: '服务器响应格式错误',
          details: parseError.message
        });
        return { success: true }; // 表示请求已处理
      }
    } else {
      const buffer = await response.arrayBuffer();
      console.log(`✅ [ComfyUI代理] 二进制响应 (${comfyuiPath}): ${buffer.byteLength} 字节`);
      res.status(response.status).send(Buffer.from(buffer));
      return { success: true }; // 表示请求已处理
    }
  } else {
    // 🆕 错误响应处理
    let errorText;
    try {
      errorText = await response.text();
    } catch (readError) {
      errorText = `无法读取错误响应: ${readError.message}`;
    }

    console.error(`❌ [ComfyUI代理] 错误响应 (${comfyuiPath}): ${response.status}`, errorText);

    // 🆕 根据错误状态码提供更具体的错误信息
    let errorMessage = `ComfyUI服务器错误: ${response.status}`;
    if (response.status === 400) {
      errorMessage = '请求参数错误，请检查工作流配置';
    } else if (response.status === 500) {
      errorMessage = 'ComfyUI内部错误，可能是工作流配置问题或模型加载失败';
    } else if (response.status === 503) {
      errorMessage = 'ComfyUI服务暂时不可用，请稍后重试';
    }

    res.status(response.status).json({
      success: false,
      error: errorMessage,
      details: errorText,
      statusCode: response.status
    });
    return { success: true }; // 表示请求已处理
  }
}



// 处理OPTIONS预检请求
router.options('/comfyui-proxy/*', (req, res) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.sendStatus(200);
});

/**
 * 🎯 新增：移动ComfyUI图像到项目目录
 * POST /api/local/move-comfyui-image
 */
router.post('/move-comfyui-image', async (req, res) => {
  try {
    const { sourceFilename, targetPath, projectTitle, chapterTitle } = req.body;

    if (!sourceFilename || !targetPath) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      });
    }

    console.log(`🚚 [图像移动] 开始移动: ${sourceFilename} -> ${targetPath}`);

    const path = require('path');
    const fs = require('fs').promises;

    // ComfyUI输出目录
    const comfyuiOutputDir = path.join(require('os').homedir(), 'Documents', 'ComfyUI', 'output');
    const sourcePath = path.join(comfyuiOutputDir, sourceFilename);

    // 项目目标目录
    const targetFullPath = path.join(__dirname, '../../', targetPath);
    const targetDir = path.dirname(targetFullPath);

    // 检查源文件是否存在
    try {
      await fs.access(sourcePath);
    } catch (error) {
      console.log(`⚠️ [图像移动] 源文件不存在: ${sourcePath}`);
      return res.status(404).json({
        success: false,
        error: '源文件不存在'
      });
    }

    // 确保目标目录存在
    await fs.mkdir(targetDir, { recursive: true });

    // 移动文件（复制后删除源文件）
    await fs.copyFile(sourcePath, targetFullPath);
    await fs.unlink(sourcePath); // 删除源文件

    console.log(`✅ [图像移动] 移动成功: ${targetFullPath}`);

    res.json({
      success: true,
      path: targetPath,
      message: '图像移动成功'
    });

  } catch (error) {
    console.error('[图像移动] 移动失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '图像移动失败'
    });
  }
});

// 🆕 清除频率限制缓存的端点（用于调试）
router.post('/clear-rate-limit', (req, res) => {
  try {
    // 清除所有ComfyUI相关的频率限制缓存
    Object.keys(global).forEach(key => {
      if (key.startsWith('comfyui_')) {
        delete global[key];
      }
    });

    console.log('🧹 [频率限制] 已清除所有频率限制缓存');
    res.json({
      success: true,
      message: '频率限制缓存已清除'
    });
  } catch (error) {
    console.error('❌ [频率限制] 清除缓存失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 🆕 WebSocket代理端点 - 使用更简单的配置
const wsProxy = createProxyMiddleware({
  target: 'http://localhost:8188',
  changeOrigin: true,
  ws: true,
  pathFilter: '/comfyui-ws',
  pathRewrite: {
    '^/comfyui-ws': '/ws'
  },
  onError: (err, req, res) => {
    console.error('❌ [WebSocket代理] 错误:', err.message);
  },
  onProxyReqWs: (proxyReq, req, socket, options, head) => {
    console.log('🔌 [WebSocket代理] 代理请求:', req.url);
  },
  onOpen: (proxySocket, head, req) => {
    console.log('✅ [WebSocket代理] 连接建立');
  },
  onClose: (res, socket, head) => {
    console.log('🔌 [WebSocket代理] 连接关闭');
  }
});

/**
 * 🎯 移动ComfyUI图像到项目目录
 * POST /api/local/move-comfyui-image
 */
router.post('/move-comfyui-image', async (req, res) => {
  try {
    const { sourceFilename, targetPath, projectTitle, chapterTitle } = req.body;

    if (!sourceFilename || !targetPath) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      });
    }

    console.log(`🚚 [图像移动] 开始移动: ${sourceFilename} -> ${targetPath}`);

    // ComfyUI输出目录
    const comfyuiOutputDir = path.join(require('os').homedir(), 'Documents', 'ComfyUI', 'output');
    const sourcePath = path.join(comfyuiOutputDir, sourceFilename);

    // 项目目标目录
    const targetFullPath = path.join(__dirname, '../../', targetPath);
    const targetDir = path.dirname(targetFullPath);

    // 检查源文件是否存在
    try {
      await fsPromises.access(sourcePath);
    } catch (error) {
      console.log(`⚠️ [图像移动] 源文件不存在: ${sourcePath}`);
      return res.status(404).json({
        success: false,
        error: '源文件不存在'
      });
    }

    // 确保目标目录存在
    await fsPromises.mkdir(targetDir, { recursive: true });

    // 移动文件（复制后删除源文件）
    await fsPromises.copyFile(sourcePath, targetFullPath);
    await fsPromises.unlink(sourcePath); // 删除源文件

    console.log(`✅ [图像移动] 移动成功: ${targetFullPath}`);

    res.json({
      success: true,
      path: targetPath,
      message: '图像移动成功'
    });

  } catch (error) {
    console.error('[图像移动] 移动失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '图像移动失败'
    });
  }
});

// 应用WebSocket代理
router.use('/comfyui-ws', wsProxy);

module.exports = router;