<template>
  <Teleport to="body">
    <div
      v-if="modelValue"
      class="dialog-overlay"
      @click.self="$emit('update:modelValue', false)"
    >
      <div
        class="dialog-content"
        :class="dialogClass"
        :style="{ width }"
      >
        <div class="dialog-header">
          <h3 class="dialog-title">
            {{ title }}
          </h3>
          <button
            class="close-btn"
            @click="$emit('update:modelValue', false)"
          >
            <i class="ri-close-line" />
          </button>
        </div>
        <div class="dialog-body">
          <slot />
        </div>
        <div class="dialog-footer">
          <slot name="footer" />
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
export default {
  name: 'BaseDialog',
  
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: '80vw'
    },
    dialogClass: {
      type: String,
      default: ''
    }
  },

  emits: ['update:modelValue']
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(17, 17, 27, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.dialog-content {
  background: #1e1e2e;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(17, 17, 27, 0.25);
  max-width: 98vw;
  max-height: 98vh;
  width: v-bind(width);
  height: 90vh;
  display: flex;
  flex-direction: column;
  animation: dialog-fade-in 0.2s ease-out;
}

@keyframes dialog-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #313244;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title {
  color: #cdd6f4;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #6c7086;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  color: #cdd6f4;
  background: #313244;
}

.close-btn i {
  font-size: 1.2rem;
}

.dialog-body {
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

.dialog-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #313244;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
</style> 