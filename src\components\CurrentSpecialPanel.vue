<template>
  <div class="special-panel-content">
    <CharacterTable
      :items="items"
      :show-actions="true"
      @edit="onEdit"
      @delete="onDelete"
    />
  </div>
</template>

<script setup>
import CharacterTable from './CharacterTable.vue';
defineProps({
  items: { type: Array, required: true, default: () => [] },
  onEdit: { type: Function, required: true },
  onDelete: { type: Function, required: true }
});
</script>

<style scoped>
.special-panel-content {
  /* 移除了重复的边框和背景，让父级面板处理样式 */
}
</style>