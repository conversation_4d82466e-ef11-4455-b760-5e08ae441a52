<template>
  <div class="grid-cell select-cell">
    <div class="cell-content">
      <div class="select-controls">
        <!-- 选择控件 -->
        <div class="select-area">
          <div
            class="select-checkbox"
            :class="{
              'selected': isSelected,
              'disabled': isLocked
            }"
            @click="toggleSelect"
            :title="isLocked ? '该行已锁定，无法选择' : '点击选择/取消选择'"
          />
        </div>

        <!-- 合并分拆按钮 -->
        <div class="button-container">
          <button
            class="merge-button"
            :class="{
              'disabled': isFirstRow || isLocked || hasLockedRows
            }"
            @click="mergeUp"
            :disabled="isFirstRow || isLocked || hasLockedRows"
            :title="getButtonTitle('merge')"
          >
            向上合并
          </button>
          <button
            class="split-button"
            :class="{
              'disabled': !isMerged || isLocked,
              'active': isMerged && !isLocked
            }"
            @click="splitDown"
            :disabled="!isMerged || isLocked"
            :title="getButtonTitle('split')"
          >
            向下分拆
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SelectCell',
  emits: {
    'toggle-select': null,
    'merge-up': null,
    'split-down': null,
    'show-locked-message': null
  },
  props: {
    isSelected: {
      type: Boolean,
      default: false
    },
    isFirstRow: {
      type: Boolean,
      default: false
    },
    isMerged: {
      type: Boolean,
      default: false
    },
    // 新增：行锁定状态
    isLocked: {
      type: Boolean,
      default: false
    },
    // 新增：是否有锁定的行（影响合并操作）
    hasLockedRows: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleSelect() {
      if (this.isLocked) {
        // 如果行已锁定，显示提示并阻止选择
        this.$emit('show-locked-message');
        return;
      }
      this.$emit('toggle-select');
    },
    mergeUp() {
      if (this.isFirstRow || this.isLocked || this.hasLockedRows) {
        return;
      }
      this.$emit('merge-up');
    },
    splitDown() {
      if (!this.isMerged || this.isLocked) {
        return;
      }
      this.$emit('split-down');
    },

    getButtonTitle(type) {
      if (type === 'merge') {
        if (this.isLocked) {
          return '该行已锁定，无法合并';
        }
        if (this.hasLockedRows) {
          return '存在锁定行，无法进行合并操作';
        }
        if (this.isFirstRow) {
          return '第一行无法向上合并';
        }
        return '向上合并到前一行';
      } else if (type === 'split') {
        if (this.isLocked) {
          return '该行已锁定，无法分拆';
        }
        if (!this.isMerged) {
          return '该行未合并，无法分拆';
        }
        return '将合并行分拆为独立行';
      }
      return '';
    }
  }
}
</script>

<style scoped>
.grid-cell {
  /* 🔧 强制保持表格单元格布局 */
  display: table-cell !important;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
  /* 🔧 防止布局干扰 - 移除冲突的flex属性 */
  float: none !important;
  position: static !important;
  /* 🔧 移除强制宽度设置，让列宽度规则生效 */
  /* width: auto !important; 注释掉，让fit-content生效 */
}

.select-cell {
  /* 🔧 改为内容自适应宽度，节省空间 */
  width: fit-content;
  min-width: fit-content;
  max-width: 120px;
}

.cell-content {
  width: 100%;
  height: 100%;
  background-color: #252525;
  padding: 5px;
  box-sizing: border-box;
}

/* 选中列的选择控件和按钮样式 */
.select-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  padding: 10px 2px;
}

/* 选择区域 */
.select-area {
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
}

.select-checkbox {
  width: 20px;
  height: 20px;
  min-width: 20px;
  border: 2px solid #00BCD4;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.select-checkbox.selected {
  background-color: #00BCD4;
}

.select-checkbox.disabled {
  border-color: #666;
  background-color: #333;
  cursor: not-allowed;
  opacity: 0.5;
}

.select-checkbox.disabled::after {
  content: '🔒';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  color: #999;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.merge-button, .split-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 5px;
  font-size: 0.7rem;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s;
}

.merge-button:hover, .split-button:hover {
  background-color: #444;
}

.merge-button.disabled, .split-button.disabled {
  background-color: #222;
  color: #666;
  cursor: not-allowed;
}

.split-button.active {
  background-color: #2196F3;
  color: white;
}

.split-button.active:hover {
  background-color: #1E88E5;
}

/* 🔧 强制布局稳定性媒体查询 */
@media (max-width: 768px) {
  .grid-cell {
    display: table-cell !important;
    float: none !important;
    position: static !important;
  }

  .select-controls {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
  }
}

@media (max-width: 480px) {
  .select-controls {
    gap: 8px;
    padding: 8px 2px;
  }

  .button-container {
    width: 100%;
  }

  .merge-button, .split-button {
    font-size: 0.65rem;
    padding: 2px 4px;
  }
}

</style>