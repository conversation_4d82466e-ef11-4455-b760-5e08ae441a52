/**
 * ComfyUI图像生成管理
 * 负责参数注入、工作流执行和图像生成
 */

import { ref, computed } from 'vue';

export function useComfyUIGeneration() {
  // 状态管理
  const isGenerating = ref(false);
  const generationProgress = ref(0);
  const generationStatus = ref('');
  const generatedImages = ref([]);
  const error = ref(null);

  /**
   * 简化的参数注入 - 只注入基本参数，利用ComfyUI原生批量功能
   * @param {Object} workflow - 原始工作流
   * @param {Object} nodeMapping - 节点映射
   * @param {Object} parameters - 生成参数
   * @returns {Object} 修改后的工作流
   */
  function injectParametersToWorkflow(workflow, nodeMapping, parameters) {
    // 深拷贝工作流，避免修改原始数据
    const modifiedWorkflow = JSON.parse(JSON.stringify(workflow));

    console.log('[ComfyUI生成] 开始注入基本参数:', parameters);

    try {
      // 1. 注入正向提示词
      if (parameters.positivePrompt && nodeMapping.positivePromptNode) {
        const nodeId = nodeMapping.positivePromptNode;
        if (modifiedWorkflow[nodeId]) {
          modifiedWorkflow[nodeId].inputs.text = parameters.positivePrompt;
          console.log(`[ComfyUI生成] 注入正向提示词到节点 ${nodeId}`);
        }
      }

      // 2. 注入负向提示词
      if (parameters.negativePrompt && nodeMapping.negativePromptNode) {
        const nodeId = nodeMapping.negativePromptNode;
        if (modifiedWorkflow[nodeId]) {
          modifiedWorkflow[nodeId].inputs.text = parameters.negativePrompt;
          console.log(`[ComfyUI生成] 注入负向提示词到节点 ${nodeId}`);
        }
      }

      // 3. 注入文件名前缀（包含批次信息）
      if (nodeMapping.saveImageNode) {
        const nodeId = nodeMapping.saveImageNode;
        if (modifiedWorkflow[nodeId]) {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          modifiedWorkflow[nodeId].inputs.filename_prefix =
            parameters.filenamePrefix || `generated_${timestamp}`;
          console.log(`[ComfyUI生成] 注入文件名前缀到节点 ${nodeId}`);
        }
      }

      console.log('[ComfyUI生成] 基本参数注入完成');
      return modifiedWorkflow;

    } catch (error) {
      console.error('[ComfyUI生成] 参数注入失败:', error);
      throw new Error(`参数注入失败: ${error.message}`);
    }
  }

  /**
   * 验证生成参数
   * @param {Object} parameters - 生成参数
   * @param {Object} nodeMapping - 节点映射
   * @returns {Object} 验证结果
   */
  function validateGenerationParameters(parameters, nodeMapping) {
    const issues = [];
    const warnings = [];

    // 检查必需参数
    if (!parameters.positivePrompt) {
      issues.push('缺少正向提示词');
    }

    if (!nodeMapping.positivePromptNode) {
      issues.push('未配置正向提示词节点');
    }

    if (!nodeMapping.saveImageNode) {
      issues.push('未配置图像保存节点');
    }

    // 检查批次大小
    if (parameters.batchSize) {
      if (parameters.batchSize < 1 || parameters.batchSize > 10) {
        issues.push('批次大小必须在1-10之间');
      }
      if (!nodeMapping.batchSizeNode) {
        warnings.push('未配置批次大小节点，将使用工作流默认值');
      }
    }

    // 检查图像尺寸
    if (parameters.width && parameters.height) {
      if (parameters.width < 64 || parameters.height < 64) {
        warnings.push('图像尺寸过小，可能影响生成质量');
      }
      if (parameters.width > 2048 || parameters.height > 2048) {
        warnings.push('图像尺寸较大，生成时间可能较长');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      warnings
    };
  }

  /**
   * 生成默认参数
   * @param {Object} userSettings - 用户设置
   * @param {Object} overrides - 覆盖参数
   * @returns {Object} 生成参数
   */
  function createGenerationParameters(userSettings, overrides = {}) {
    const defaultParams = {
      positivePrompt: '',
      negativePrompt: 'Bad quality,worst quality,normal quality,low-res,sketch,poor design,deformed,disfigured,soft,bad composition,simple design',
      width: 1024,
      height: 1024,
      steps: 35,
      cfg: 7,
      sampler: 'euler',
      batchSize: 1,
      seed: Math.floor(Math.random() * 1000000),
      filenamePrefix: 'ComfyUI_generated'
    };

    // 合并用户设置
    if (userSettings?.comfyui?.defaultSettings) {
      Object.assign(defaultParams, userSettings.comfyui.defaultSettings);
    }

    // 合并用户的批次大小设置
    if (userSettings?.comfyui?.batchSize) {
      defaultParams.batchSize = userSettings.comfyui.batchSize;
    }

    // 应用覆盖参数
    return { ...defaultParams, ...overrides };
  }

  /**
   * 提交单个生成任务到ComfyUI
   * @param {string} serverUrl - ComfyUI服务器地址
   * @param {Object} workflow - 工作流
   * @returns {Promise<Object>} 生成结果
   */
  async function submitSingleTask(serverUrl, workflow) {
    try {
      const response = await fetch('/api/local/comfyui/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serverUrl,
          workflow
        })
      });

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || '任务提交失败');
      }
    } catch (error) {
      console.error('[ComfyUI生成] 单个任务提交失败:', error);
      throw error;
    }
  }

  /**
   * 利用ComfyUI原生批量功能提交多个任务
   * @param {string} serverUrl - ComfyUI服务器地址
   * @param {Object} workflow - 工作流
   * @param {number} batchSize - 批次大小
   * @param {Function} onProgress - 进度回调
   * @returns {Promise<Array>} 生成结果数组
   */
  async function submitBatchTasks(serverUrl, workflow, batchSize, onProgress) {
    const results = [];

    console.log(`[ComfyUI生成] 开始批量提交 ${batchSize} 个任务`);

    for (let i = 0; i < batchSize; i++) {
      try {
        // 为每个任务创建独立的工作流副本
        const taskWorkflow = JSON.parse(JSON.stringify(workflow));

        // 更新文件名前缀以区分不同的生成
        Object.keys(taskWorkflow).forEach(nodeId => {
          const node = taskWorkflow[nodeId];
          if (node.class_type === 'SaveImage' && node.inputs.filename_prefix) {
            node.inputs.filename_prefix = `${node.inputs.filename_prefix}_${i + 1}`;
          }
        });

        console.log(`[ComfyUI生成] 提交第 ${i + 1}/${batchSize} 个任务`);

        const result = await submitSingleTask(serverUrl, taskWorkflow);
        results.push({
          taskIndex: i + 1,
          result: result
        });

        // 调用进度回调
        if (onProgress) {
          onProgress({
            completed: i + 1,
            total: batchSize,
            percentage: Math.round(((i + 1) / batchSize) * 100)
          });
        }

        // 添加小延迟避免过快提交
        if (i < batchSize - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

      } catch (error) {
        console.error(`[ComfyUI生成] 第 ${i + 1} 个任务失败:`, error);
        results.push({
          taskIndex: i + 1,
          error: error.message
        });
      }
    }

    console.log(`[ComfyUI生成] 批量提交完成，成功: ${results.filter(r => !r.error).length}/${batchSize}`);
    return results;
  }

  /**
   * 执行图像生成流程 - 利用ComfyUI原生批量功能
   * @param {Object} options - 生成选项
   * @returns {Promise<Array>} 生成的图像列表
   */
  async function generateImages(options) {
    const {
      workflow,
      nodeMapping,
      parameters,
      serverUrl,
      onStatusUpdate
    } = options;

    isGenerating.value = true;
    generationProgress.value = 0;
    generationStatus.value = '准备生成...';
    error.value = null;

    try {
      // 1. 验证参数
      onStatusUpdate?.('验证参数...');
      const validation = validateGenerationParameters(parameters, nodeMapping);
      if (!validation.isValid) {
        throw new Error(`参数验证失败: ${validation.issues.join(', ')}`);
      }
      generationProgress.value = 10;

      // 2. 注入基本参数
      onStatusUpdate?.('准备工作流...');
      const modifiedWorkflow = injectParametersToWorkflow(workflow, nodeMapping, parameters);
      generationProgress.value = 20;

      // 3. 根据批次大小决定生成方式
      const batchSize = parameters.batchSize || 1;

      if (batchSize === 1) {
        // 单张生成
        onStatusUpdate?.('生成图像...');
        const result = await submitSingleTask(serverUrl, modifiedWorkflow);
        generationProgress.value = 100;
        return [result];
      } else {
        // 批量生成 - 利用ComfyUI原生队列功能
        onStatusUpdate?.(`批量生成 ${batchSize} 张图像...`);

        const results = await submitBatchTasks(serverUrl, modifiedWorkflow, batchSize, (progress) => {
          // 更新进度：20% + 80% * 当前进度
          generationProgress.value = 20 + Math.round(80 * (progress.percentage / 100));
          onStatusUpdate?.(`生成进度: ${progress.completed}/${progress.total}`);
        });

        return results;
      }

    } catch (err) {
      error.value = err.message;
      generationStatus.value = '生成失败';
      throw err;
    } finally {
      isGenerating.value = false;
      // If there's an error, status should reflect failure, otherwise completion.
      if (error.value) {
        generationStatus.value = '生成失败';
      } else {
        generationStatus.value = '已完成';
      }
      generationProgress.value = 0; // Reset progress
    }
  }

  return {
    // 状态
    isGenerating,
    generationProgress,
    generationStatus,
    generatedImages,
    error,

    // 方法
    injectParametersToWorkflow,
    validateGenerationParameters,
    createGenerationParameters,
    submitSingleTask,
    submitBatchTasks,
    generateImages
  };
}
