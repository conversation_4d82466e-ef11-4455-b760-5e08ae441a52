<template>
  <div class="creation-container">
    <!-- 使用步骤导航组件 -->
    <StepsNavigationBar
      v-if="showStepsNav"
      :project-title="projectData && projectData.title ? projectData.title : '未命名项目'"
      :chapter-title="currentChapter"
      :current-step="currentStep"
      :has-srt-file="!!(projectData?.data?.srtFilePath)"
      :has-audio-file="!!(projectData?.data?.audioFilePath)"
      :use-builtin-audio="!!(projectData?.data?.useBuiltinAudio)"
      @go-back="goBack"
      @next-step="goToNextStep"
      @update-title="updateProjectTitle"
      @navigate-to-step="navigateToStep"
      @show-error="showErrorMessage"
    />

    <!-- 添加Toast通知组件 -->
    <ToastNotification
      ref="toast"
      :message="toastMessage"
      :title="toastTitle"
      :type="toastType"
    />

    <!-- 导入文件界面 -->
    <div class="import-content">
      <!-- 音频选项 -->
      <div class="audio-options">
        <button
          :class="['option-button', { active: audioOption === 'custom' }]"
          @click="selectAudioOption('custom')"
        >
          导入自己的音频
        </button>
        <button
          :class="['option-button', { active: audioOption === 'builtin' }]"
          @click="selectAudioOption('builtin')"
        >
          使用内置的音频
        </button>
      </div>

      <!-- 文件上传区域 -->
      <div class="import-files">
        <!-- SRT文件上传组件 -->
        <FileUploader
          title="字幕文件 (srt)"
          icon="ri-file-text-line"
          main-text="导入本地字幕"
          :sub-text="srtUploadStatus === '文件未找到' ? '文件不存在，请重新上传 (仅支持srt格式)' : '单击或拖动文件到此区域，仅支持srt格式'"
          accept-types=".srt"
          input-id="srt-file"
          file-icon-class="ri-file-text-line file-icon-small"
          :upload-status="srtUploadStatus"
          :upload-progress="srtUploadProgress"
          :file-name="srtFileName"
          :file-path="projectData?.data?.srtFilePath"
          :project-title="projectData?.title"
          :chapter-title="currentChapter"
          @file-selected="handleSrtFileSelected"
          @delete-file="deleteSrtFile"
        />

        <!-- 音频文件上传组件（仅在自定义音频选项时显示） -->
        <FileUploader
          v-if="audioOption === 'custom'"
          title="音频文件 (mp3)"
          icon="ri-headphone-line"
          main-text="导入本地音频"
          :sub-text="audioUploadStatus === '文件未找到' ? '文件不存在，请重新上传 (支持MP3, WAV等格式)' : '单击或拖动文件到此区域，支持MP3, WAV等格式'"
          accept-types="audio/*"
          input-id="audio-file"
          file-icon-class="ri-music-fill file-icon-small"
          :upload-status="audioUploadStatus"
          :upload-progress="audioUploadProgress"
          :file-name="audioFileName"
          :file-path="projectData?.data?.audioFilePath"
          :project-title="projectData?.title"
          :chapter-title="currentChapter"
          @file-selected="handleAudioFileSelected"
          @delete-file="deleteAudioFile"
        />
      </div>

      <!-- 文件状态提示和重新扫描按钮 -->
      <div
        class="file-status-prompt"
        v-if="showFileStatusPrompt"
      >
        <p><i class="ri-error-warning-line" /> 部分文件可能在系统外被删除或移动</p>
        <button
          @click="rescanFiles"
          class="rescan-button"
        >
          <i class="ri-refresh-line" /> 重新扫描文件
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import StepsNavigationBar from './StepsNavigationBar.vue';
import ToastNotification from './ToastNotification.vue';
import FileUploader from './FileUploader.vue';
import logger from '../utils/logger.js';

export default {
  name: 'CreationPage',
  components: {
    StepsNavigationBar,
    ToastNotification,
    FileUploader
  },
  props: {
    // 兼容旧版本使用project对象的方式
    project: {
      type: Object,
      required: false,
      default: () => ({})
    },
    // 新增：路由参数
    projectTitle: {
      type: String,
      required: false,
      default: ''
    },
    chapterTitle: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      projectData: null,
      audioFileName: '',
      srtFileName: '',
      audioFile: null,
      srtFile: null,
      audioUploadStatus: '',
      srtUploadStatus: '',
      audioUploadProgress: 0,
      srtUploadProgress: 0,
      showStepsNav: true,
      currentStep: 'import',
      audioOption: 'custom', // 'custom' 或 'builtin'
      toastMessage: '',
      toastTitle: '',
      toastType: 'info',
      currentChapter: null,
      showFileStatusPrompt: false
    }
  },
  computed: {
    computedTitle() {
      if (this.projectData && this.projectData.title) {
        if (this.currentChapter) {
          return `${this.projectData.title}/${this.currentChapter}`;
        }
        return this.projectData.title;
      }
      return '未命名项目';
    },
    canProceed() {
      return !!this.projectData.data.srtFilePath &&
             (this.audioOption === 'custom' ? !!this.projectData.data.audioFilePath : true);
    }
  },
  created() {
    // 初始化项目数据
    if (this.projectTitle && this.chapterTitle) {
      // 如果有路由参数，优先使用路由参数初始化
      logger.debug(`从路由参数加载项目: ${this.projectTitle}/${this.chapterTitle}`);

      // 检查是否已有项目数据（从其他组件传递过来）
      if (this.project && this.project.data) {
        logger.debug('发现现有项目数据，保留其内容');
        this.projectData = {
          title: this.projectTitle,
          data: {
            ...this.project.data,  // 保留现有数据
            currentChapter: this.chapterTitle  // 更新章节信息
          }
        };
      } else {
        // 如果没有现有数据，创建新的
        this.projectData = {
          title: this.projectTitle,
          data: { currentChapter: this.chapterTitle }
        };
      }
      this.currentChapter = this.chapterTitle;
    } else {
      // 兼容旧方式：直接传递 project 对象
      logger.debug('从project对象加载项目');
      this.projectData = {
        ...this.project,
        data: this.project.data || {}
      };

      // 从project对象中获取章节信息
      if (this.project && this.project.currentChapter) {
        this.currentChapter = this.project.currentChapter;
        logger.debug(`从project对象获取章节信息: ${this.currentChapter}`);
      }
    }

    // 确保data对象存在
    if (!this.projectData.data) {
      this.projectData.data = {};
    }

    // 从URL参数获取章节信息
    const query = this.$route?.query;
    if (query && query.chapterTitle) {
      this.currentChapter = query.chapterTitle;
    }

    logger.business(`创作页面初始化完成: ${this.projectData.title}/${this.currentChapter}`);

    // 加载项目数据
    this.$nextTick(() => {
      this.loadProjectData();
    });
  },
  methods: {
    selectAudioOption(option) {
      this.audioOption = option;
      // 选择内置音频时立即设置标志
      if (option === 'builtin') {
        this.projectData.data.useBuiltinAudio = true;
      } else {
        this.projectData.data.useBuiltinAudio = false;
      }
    },

    extractFileName(path) {
      if (!path) return '';
      const parts = path.split(/[/\\]/);
      return parts[parts.length - 1];
    },

    triggerSrtFileSelect() {
      document.getElementById('srt-file').click();
    },

    triggerAudioFileSelect() {
      document.getElementById('audio-file').click();
    },

    handleDragOver(_event, type) {
      if (type === 'srt') {
        this.isDraggingSrt = true;
      } else if (type === 'audio') {
        this.isDraggingAudio = true;
      }
    },

    handleDragLeave(_event, type) {
      if (type === 'srt') {
        this.isDraggingSrt = false;
      } else if (type === 'audio') {
        this.isDraggingAudio = false;
      }
    },

    handleFileDrop(event, type) {
      if (type === 'srt') {
        this.isDraggingSrt = false;
      } else if (type === 'audio') {
        this.isDraggingAudio = false;
      }

      const file = event.dataTransfer.files[0];
      if (!file) return;

      if (type === 'srt') {
        this.handleSrtFileSelected(file);
      } else if (type === 'audio') {
        this.handleAudioFileSelected(file);
      }
    },

    async handleSrtFileSelected(event) {
      let file;

      // 检查参数是事件对象还是直接传递的文件对象
      if (event.target && event.target.files) {
        // 是事件对象
        file = event.target.files[0];
      if (!file) return;

      // 重置file input，确保同一文件可以重复选择
      if (event.target) {
        event.target.value = '';
      }
      } else if (event instanceof File) {
        // 直接传递的文件对象
        file = event;
      } else {
        logger.error('无效的文件选择事件或文件对象');
        return;
      }

      logger.business(`选择SRT文件: ${file.name} (${(file.size / 1024).toFixed(1)}KB)`);

      this.srtFile = file;
      this.srtFileName = file.name;

      // 上传字幕文件到服务器
      await this.uploadSrtFile(file);
    },

    async handleAudioFileSelected(event) {
      let file;

      // 检查参数是事件对象还是直接传递的文件对象
      if (event.target && event.target.files) {
        // 是事件对象
        file = event.target.files[0];
      if (!file) return;

      // 重置file input，确保同一文件可以重复选择
      if (event.target) {
        event.target.value = '';
      }
      } else if (event instanceof File) {
        // 直接传递的文件对象
        file = event;
      } else {
        logger.error('无效的文件选择事件或文件对象');
        return;
      }

      logger.business(`选择音频文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(1)}MB)`);

      this.audioFile = file;
      this.audioFileName = file.name;

      // 上传文件到服务器
      await this.uploadAudioFile(file);
    },

    async uploadSrtFile(file) {
      logger.business(`开始上传SRT文件: ${this.projectData.title}/${this.currentChapter}`);
      if (!this.projectData.title || !this.currentChapter) {
        this.showErrorMessage('项目名称或章节名称缺失，无法上传（请返回重新选择项目和章节）');
        this.srtUploadStatus = '上传失败';
        return;
      }

      // 检查是否已有已上传的SRT文件，如果有则先删除
      if (this.projectData?.data?.srtFilePath &&
          this.projectData.data.srtFilePath !== file.name) {
        logger.debug(`检测到已存在SRT文件，准备删除: ${this.projectData.data.srtFilePath}`);

        try {
          // 删除旧文件
          const deleteUrl = '/api/local/delete-file';
          const response = await fetch(deleteUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ filePath: this.projectData.data.srtFilePath })
          });

          if (!response.ok) {
            logger.warn('删除旧文件失败，继续上传新文件');
          } else {
            logger.debug('旧SRT文件已删除');
          }
        } catch (error) {
          logger.warn('删除旧文件出错，继续上传新文件:', error);
        }
      }

      this.srtUploadStatus = '上传中...';
      this.srtUploadProgress = 10;

      logger.debug(`准备上传SRT文件: ${file.name} (${(file.size / 1024).toFixed(1)}KB)`);

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      // 项目标题和章节标题必须是字符串
      formData.append('projectTitle', String(this.projectData.title));
      formData.append('chapterTitle', String(this.currentChapter));

      logger.verbose('FormData创建完成，包含文件和项目信息');

      const progressInterval = setInterval(() => {
        if (this.srtUploadProgress < 90) {
          this.srtUploadProgress += 10;
        }
      }, 200);

      try {
        // 构建上传URL（添加查询参数作为备选）
        const uploadUrl = `/api/local/upload-file?projectTitle=${encodeURIComponent(this.projectData.title)}&chapterTitle=${encodeURIComponent(this.currentChapter)}`;
        logger.api(`发送SRT上传请求: ${file.name}`);

        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData
        });

        clearInterval(progressInterval);
        logger.debug(`上传响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          logger.error('SRT上传失败，服务器响应:', errorText);
          throw new Error(`上传失败: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        logger.debug('SRT上传响应数据接收完成');

        if (!result.success) {
          throw new Error(result.error || '上传失败');
        }

        this.srtUploadProgress = 100;
        this.srtUploadStatus = '上传成功';

        // 更新项目数据
        if (!this.projectData.data) {
          this.projectData.data = {};
        }

        // 保存文件路径
        this.projectData.data.srtFilePath = result.path;
        this.projectData.data.srtFile = file.name;

        logger.business(`SRT文件上传成功: ${result.path}`);

        // 重要：上传成功后，主动扫描目录确保获取最新文件
        const targetPath = `draft/${this.projectData.title}/${this.currentChapter}`;
        await this.scanDirectoryForFiles(targetPath);

        // 保存项目数据
        await this.saveProjectData();

        // 新增：设置SRT更新标志，通知ContentCreationStudio组件
        this.setSrtUpdateFlag();
      } catch (error) {
        logger.error('上传SRT文件失败:', error);
        clearInterval(progressInterval);
        this.srtUploadStatus = '上传失败';
        this.srtUploadProgress = 0;
        this.showErrorMessage('上传文件失败: ' + error.message);
      }
    },

    // 设置SRT文件已更新的标志，供ContentCreationStudio组件检测
    setSrtUpdateFlag() {
      if (!this.projectData?.title || !this.currentChapter) return;

      try {
        // 使用localStorage保存更新标志
        const updateKey = `srt_updated_${this.projectData.title}_${this.currentChapter}`;
        localStorage.setItem(updateKey, Date.now().toString());

        logger.debug(`设置SRT更新标志: ${updateKey}`);

        // 清除之前的合并状态标志，强制ContentCreationStudio重新解析
        const mergeKey = `project_${this.projectData.title}_${this.currentChapter}_hasMerged`;
        localStorage.removeItem(mergeKey);

        logger.debug('清除合并状态标志，将自动加载新的SRT内容');

        // 清除新SRT可用标志，避免混淆
        const newSrtKey = `new_srt_available_${this.projectData.title}_${this.currentChapter}`;
        localStorage.removeItem(newSrtKey);
      } catch (error) {
        logger.error('设置SRT更新标志失败:', error);
      }
    },

    async uploadAudioFile(file) {
      logger.business(`开始上传音频文件: ${this.projectData.title}/${this.currentChapter}`);
      if (!this.projectData.title || !this.currentChapter) {
        this.showErrorMessage('项目名称或章节名称缺失，无法上传（请返回重新选择项目和章节）');
        this.audioUploadStatus = '上传失败';
        return;
      }

      // 检查是否已有已上传的音频文件，如果有则先删除
      if (this.projectData?.data?.audioFilePath &&
          this.projectData.data.audioFilePath !== file.name) {
        logger.debug(`检测到已存在音频文件，准备删除: ${this.projectData.data.audioFilePath}`);

        try {
          // 删除旧文件
          const deleteUrl = '/api/local/delete-file';
          console.log('发送删除请求到:', deleteUrl);
          const response = await fetch(deleteUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ filePath: this.projectData.data.audioFilePath })
          });

          if (!response.ok) {
            console.warn('删除旧文件失败，继续上传新文件');
          } else {
            console.log('旧音频文件已删除');
          }
        } catch (error) {
          console.warn('删除旧文件出错，继续上传新文件:', error);
        }
      }

      this.audioUploadStatus = '上传中...';
      this.audioUploadProgress = 10;

      console.log('准备上传音频文件:', file.name, file.size);

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      // 确保是字符串
      formData.append('projectTitle', String(this.projectData.title));
      formData.append('chapterTitle', String(this.currentChapter));

      // 检查FormData是否正确创建
      console.log('FormData创建检查:');
      for (const pair of formData.entries()) {
        console.log(`  - ${pair[0]}: ${pair[0] === 'file' ? pair[1].name : pair[1]}`);
      }

      const progressInterval = setInterval(() => {
        if (this.audioUploadProgress < 90) {
          this.audioUploadProgress += 10;
        }
      }, 200);

      try {
        // 构建上传URL（添加查询参数作为备选）
        const uploadUrl = `/api/local/upload-file?projectTitle=${encodeURIComponent(this.projectData.title)}&chapterTitle=${encodeURIComponent(this.currentChapter)}`;
        console.log('发送上传请求到:', uploadUrl);
        console.log('上传参数:', {
          projectTitle: this.projectData.title,
          chapterTitle: this.currentChapter,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type || '未知'
        });

        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData
        });

        clearInterval(progressInterval);
        console.log('上传响应状态:', response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('上传失败，服务器响应:', errorText);
          throw new Error(`上传失败: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log('上传响应数据:', result);

        if (!result.success) {
          throw new Error(result.error || '上传失败');
        }

        this.audioUploadProgress = 100;
        this.audioUploadStatus = '上传成功';

        // 更新项目数据
        if (!this.projectData.data) {
          this.projectData.data = {};
        }

        // 保存文件路径
        this.projectData.data.audioFilePath = result.path;
        this.projectData.data.audioFile = file.name;

        console.log('音频文件已上传至:', result.path);

        // 重要：上传成功后，主动扫描目录确保获取最新文件
        const targetPath = `draft/${this.projectData.title}/${this.currentChapter}`;
        await this.scanDirectoryForFiles(targetPath);

        // 保存项目数据
        await this.saveProjectData();
      } catch (error) {
        console.error('上传音频文件失败:', error);
        clearInterval(progressInterval);
        this.audioUploadStatus = '上传失败';
        this.audioUploadProgress = 0;
        this.showErrorMessage('上传文件失败: ' + error.message);
      }
    },

    async deleteSrtFile() {
      if (!confirm('确定要删除已上传的字幕文件吗？')) {
        return;
      }

      try {
        if (this.projectData?.data?.srtFilePath) {
          // 构建文件路径
          const filePath = this.projectData.data.srtFilePath;

          // 发送删除请求到服务器
          const response = await fetch('/api/local/delete-file', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filePath })
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '删除文件失败');
          }
        }

        // 从项目数据中完全移除文件信息
        if (this.projectData.data) {
          delete this.projectData.data.srtFilePath;
          delete this.projectData.data.srtFile;
        }

        // 彻底重置相关状态
        this.srtFile = null;
      this.srtFileName = '';
        this.srtUploadStatus = '';
        this.srtUploadProgress = 0;

        // 保存更新后的项目数据
        await this.saveProjectData();

        console.log('SRT文件已成功删除，状态已重置');
      } catch (error) {
        console.error('删除SRT文件失败:', error);
        this.showErrorMessage('删除文件失败: ' + error.message);
      }
    },

    async deleteAudioFile() {
      if (!confirm('确定要删除已上传的音频文件吗？')) {
        return;
      }

      try {
        if (this.projectData?.data?.audioFilePath) {
          // 构建文件路径
          const filePath = this.projectData.data.audioFilePath;

          // 发送删除请求到服务器
          const response = await fetch('/api/local/delete-file', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filePath })
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '删除文件失败');
          }
        }

        // 从项目数据中完全移除文件信息
        if (this.projectData.data) {
          delete this.projectData.data.audioFilePath;
          delete this.projectData.data.audioFile;
        }

        // 彻底重置相关状态
        this.audioFile = null;
      this.audioFileName = '';
        this.audioUploadStatus = '';
        this.audioUploadProgress = 0;

        // 保存更新后的项目数据
        await this.saveProjectData();

        console.log('音频文件已成功删除，状态已重置');
      } catch (error) {
        console.error('删除音频文件失败:', error);
        this.showErrorMessage('删除文件失败: ' + error.message);
      }
    },

    goBack() {
      this.$emit('navigate', 'home');
    },

    goToNextStep() {
      // 检查是否可以进入下一步
      if (this.currentStep === 'import') {
        // 检查是否上传了必要文件
        if (!this.projectData.data.srtFilePath) {
          this.showErrorMessage('请先上传字幕文件');
          return;
        }

        // 如果选择使用内置音频，已在selectAudioOption中设置标志
        if (this.audioOption === 'custom' && !this.projectData.data.audioFilePath) {
          this.showErrorMessage('请先上传音频文件');
          return;
        }

        // 当SRT和MP3文件都已上传，导航到ContentCreationStudio.vue
        console.log('文件已上传，导航到ContentCreationStudio');

        // 导航到ContentCreationStudio
        this.$emit('navigate', 'studio', this.projectData);
        return;
      }

      // 更新当前步骤
      if (this.currentStep === 'import') {
        this.currentStep = 'edit';
      } else if (this.currentStep === 'edit') {
        this.currentStep = 'preview';
      }
    },

    navigateToStep(stepName) {
      // 导航到指定步骤
      if (stepName === 'edit') {
        // 检查是否上传了必要文件
        if (!this.projectData.data.srtFilePath) {
          this.showErrorMessage('请先上传字幕文件');
          return;
        }

        // 如果选择使用内置音频，已在selectAudioOption中设置标志
        if (this.audioOption === 'custom' && !this.projectData.data.audioFilePath) {
          this.showErrorMessage('请先上传音频文件');
          return;
        }

        // 当SRT和MP3文件都已上传，导航到ContentCreationStudio.vue
        console.log('导航到步骤2，跳转到ContentCreationStudio');

        // 导航到ContentCreationStudio
        this.$emit('navigate', 'studio', this.projectData);
        return;
      } else if (['import', 'preview'].includes(stepName)) {
        this.currentStep = stepName;
      }
    },

    showErrorMessage(message) {
      // 使用Toast显示警告
      this.toastType = 'error';
      this.toastTitle = '提示';
      this.toastMessage = message;
      this.$nextTick(() => {
        this.$refs.toast.show();
      });
    },

    updateProjectTitle(title) {
      if (this.projectData) {
        // 检查是否是章节标题更新
        if (this.currentChapter) {
          console.log('更新章节标题:', title);
          this.currentChapter = title;
        } else {
          // 更新项目标题
          console.log('更新项目标题:', title);
          this.projectData.title = title;
        }
        // 需要保存项目数据
      }
    },

    // 加载项目数据的方法
    async loadProjectData() {
      if (!this.projectData || !this.projectData.title || !this.currentChapter) {
        logger.warn('加载项目数据失败：缺少必要信息');
        return;
      }

      try {
        // 构建路径
        const projectPath = this.projectData.title;
        const chapterPath = this.currentChapter;
        const targetPath = `draft/${projectPath}/${chapterPath}`;

        // 初始化先获取目录中的实际文件列表，以便与project-data.json做比较
        const filesResponse = await fetch(`/api/local/list-files?path=${encodeURIComponent(targetPath)}`);
        let directoryFiles = [];
        if (filesResponse.ok) {
          directoryFiles = await filesResponse.json();
          console.log('目录中的实际文件:', directoryFiles);
        }

        // 检查是否存在project-data.json文件
        console.log('检查项目数据文件是否存在:', `${targetPath}/project-data.json`);
        const projectDataResponse = await fetch(`/api/local/read-file?path=${encodeURIComponent(`${targetPath}/project-data.json`)}`);

        if (projectDataResponse.ok) {
          const result = await projectDataResponse.json();
          if (result.success && result.content) {
            console.log('已找到项目数据文件，解析保存的数据');
            const savedData = JSON.parse(result.content);
            let needsUpdate = false;

            // 更新项目数据，保留已有的合并状态等信息
            if (savedData) {
              // 保留其他重要数据，如行数据(合并状态等)
              if (savedData.rows) {
                this.projectData.data.rows = savedData.rows;
                console.log('从保存的文件中加载行数据，行数:', savedData.rows.length);
              }

              // 保留角色选择数据
              if (savedData.currentSelectedCharacters) {
                this.projectData.data.currentSelectedCharacters = savedData.currentSelectedCharacters;
                console.log('从保存的文件中加载角色选择数据:', savedData.currentSelectedCharacters);
              }

              if (savedData.useBuiltinAudio) {
                this.projectData.data.useBuiltinAudio = savedData.useBuiltinAudio;
                this.audioOption = 'builtin';
              }

              // 检查目录中实际SRT文件是否与保存的不一致
              const srtFileInDir = directoryFiles.find(file => file.toLowerCase().endsWith('.srt'));
              const savedSrtFile = savedData.srtFile;

              if (srtFileInDir && (!savedSrtFile || srtFileInDir !== savedSrtFile)) {
                console.log('检测到新的SRT文件:', srtFileInDir, '替换旧文件:', savedSrtFile);
                needsUpdate = true;
              } else if (savedData.srtFilePath) {
                // 验证SRT文件是否真实存在
                const srtExistsResponse = await fetch(`/api/local/check-file-exists?filePath=${encodeURIComponent(savedData.srtFilePath)}`);
                const srtExistsResult = await srtExistsResponse.json();

                if (srtExistsResult.exists) {
                  this.projectData.data.srtFilePath = savedData.srtFilePath;
                  this.projectData.data.srtFile = savedData.srtFile;
                  this.srtFileName = savedData.srtFile;
                  this.srtUploadStatus = '上传成功';
                  this.srtUploadProgress = 100;
                } else {
                  console.warn('SRT文件不存在，路径:', savedData.srtFilePath);
                  this.projectData.data.srtFilePath = null;
                  this.projectData.data.srtFile = null;
                  this.srtFileName = '';
                  this.srtUploadStatus = '文件未找到';
                  this.srtUploadProgress = 0;
                  needsUpdate = true;
                }
              }

              // 检查目录中实际音频文件是否与保存的不一致
              const audioFileInDir = directoryFiles.find(file =>
                file.toLowerCase().endsWith('.mp3') ||
                file.toLowerCase().endsWith('.wav') ||
                file.toLowerCase().endsWith('.m4a')
              );
              const savedAudioFile = savedData.audioFile;

              if (audioFileInDir && (!savedAudioFile || audioFileInDir !== savedAudioFile)) {
                console.log('检测到新的音频文件:', audioFileInDir, '替换旧文件:', savedAudioFile);
                needsUpdate = true;
              } else if (savedData.audioFilePath) {
                // 验证音频文件是否真实存在
                const audioExistsResponse = await fetch(`/api/local/check-file-exists?filePath=${encodeURIComponent(savedData.audioFilePath)}`);
                const audioExistsResult = await audioExistsResponse.json();

                if (audioExistsResult.exists) {
                  this.projectData.data.audioFilePath = savedData.audioFilePath;
                  this.projectData.data.audioFile = savedData.audioFile;
                  this.audioFileName = savedData.audioFile;
                  this.audioUploadStatus = '上传成功';
                  this.audioUploadProgress = 100;
                } else {
                  console.warn('音频文件不存在，路径:', savedData.audioFilePath);
                  this.projectData.data.audioFilePath = null;
                  this.projectData.data.audioFile = null;
                  this.audioFileName = '';
                  this.audioUploadStatus = '文件未找到';
                  this.audioUploadProgress = 0;
                  needsUpdate = true;
                }
              }
            }

            // 如果有文件不一致或不存在，重新扫描目录
            if (needsUpdate || (!this.projectData.data.srtFilePath || (!this.projectData.data.audioFilePath && !this.projectData.data.useBuiltinAudio))) {
              console.log('检测到文件变化或缺失，从目录重新获取文件信息');
              await this.scanDirectoryForFiles(targetPath);
            } else {
              console.log('所有文件都与记录一致，无需更新');
            }
          }
        } else {
          // 如果没有找到project-data.json或读取失败，才从目录内容获取文件
          console.log('未找到项目数据文件或读取失败，尝试从目录内容获取文件信息');
          await this.scanDirectoryForFiles(targetPath);
        }

        // 保存项目数据
        await this.saveProjectData();

        // 更新文件状态提示
        this.updateFileStatusPrompt();
      } catch (error) {
        console.error('加载项目数据出错:', error);
        this.updateFileStatusPrompt();
      }
    },

    // 扫描目录查找SRT和音频文件的辅助方法
    async scanDirectoryForFiles(targetPath) {
      const response = await fetch(`/api/local/list-files?path=${encodeURIComponent(targetPath)}`);
      if (!response.ok) {
        console.error('无法加载项目文件');
        return;
      }

      const files = await response.json();
      console.log('目录内容:', files);

      // 检查是否有SRT和音频文件
      if (!this.projectData.data) {
        this.projectData.data = {};
      }

      // 查找SRT文件
      const srtFile = files.find(file => file.toLowerCase().endsWith('.srt'));
      if (srtFile) {
        this.projectData.data.srtFilePath = `${targetPath}/${srtFile}`;
        this.projectData.data.srtFile = srtFile;
        this.srtFileName = srtFile;
        this.srtUploadStatus = '上传成功';
        this.srtUploadProgress = 100;
      }

      // 查找音频文件
      const audioFile = files.find(file =>
        file.toLowerCase().endsWith('.mp3') ||
        file.toLowerCase().endsWith('.wav') ||
        file.toLowerCase().endsWith('.m4a')
      );
      if (audioFile) {
        this.projectData.data.audioFilePath = `${targetPath}/${audioFile}`;
        this.projectData.data.audioFile = audioFile;
        this.audioFileName = audioFile;
        this.audioUploadStatus = '上传成功';
        this.audioUploadProgress = 100;
      }
    },

    // 保存项目数据 - 使用统一的数据持久化系统
    async saveProjectData() {
      if (!this.projectData || !this.projectData.title) return;

      try {
        // 使用统一的数据持久化系统
        const { saveProject } = await import('../composables/useDataPersistence.js').then(module => module.useDataPersistence());

        await saveProject(this.projectData, {
          showMessages: false, // CreationPage有自己的消息系统
          debounceDelay: 100   // 使用快速防抖
        });

        logger.business('项目数据已保存');
      } catch (error) {
        logger.error('保存项目数据失败:', error);
      }
    },

    // 重新扫描文件方法
    async rescanFiles() {
      if (!this.projectData || !this.projectData.title || !this.currentChapter) {
        console.log('无法扫描：缺少必要信息');
        return;
      }

      try {
        const projectPath = this.projectData.title;
        const chapterPath = this.currentChapter;
        const targetPath = `draft/${projectPath}/${chapterPath}`;

        // 重设文件状态
        this.srtUploadStatus = '';
        this.audioUploadStatus = '';
        this.srtUploadProgress = 0;
        this.audioUploadProgress = 0;

        // 执行扫描
        await this.scanDirectoryForFiles(targetPath);

        // 保存更新后的数据
        await this.saveProjectData();

        // 更新提示状态
        this.updateFileStatusPrompt();

        alert('文件扫描完成');
      } catch (error) {
        console.error('扫描文件失败:', error);
      }
    },

    // 更新文件状态提示显示
    updateFileStatusPrompt() {
      // 当SRT文件或音频文件状态为"文件未找到"时显示提示
      const srtMissing = this.srtUploadStatus === '文件未找到';
      const audioMissing = this.audioOption === 'custom' && this.audioUploadStatus === '文件未找到';
      this.showFileStatusPrompt = srtMissing || audioMissing;
    },
  },

  // 添加监视器来更新文件状态提示
  watch: {
    srtUploadStatus() {
      this.updateFileStatusPrompt();
    },
    audioUploadStatus() {
      this.updateFileStatusPrompt();
    },
    audioOption() {
      this.updateFileStatusPrompt();
    }
  }
}
</script>

<style scoped>
/* 创作页面 */
.creation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 1.5rem;
}

/* 导入文件界面 */
.import-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 2rem;
}

.audio-options {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

.option-button {
  padding: 0.75rem 2rem;
  background-color: #27273e;
  color: #cdd6f4;
  border: 1px solid #333355;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  max-width: 300px;
}

.option-button:hover, .option-button.active {
  background-color: #2d2d44;
  border-color: #2dc0f0;
}

.option-button.active {
  color: #2dc0f0;
}

.import-files {
  display: flex;
  gap: 2rem;
  justify-content: space-around;
  flex-wrap: wrap;
}

.file-section {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.file-section h2 {
  font-size: 1.2rem;
  color: #f5c2e7;
  text-align: center;
  margin-bottom: 1rem;
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  background-color: #1d1d2c;
  border: 2px dashed #444466;
  border-radius: 8px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-upload-area:hover {
  border-color: #2dc0f0;
  background-color: #202035;
}

.file-icon {
  font-size: 2.5rem;
  color: #f5c2e7;
}

.file-text {
  text-align: center;
}

.file-main-text {
  font-size: 1.2rem;
  color: #cdd6f4;
  margin-bottom: 0.5rem;
}

.file-sub-text {
  font-size: 0.9rem;
  color: #7f849c;
}

.file-upload-area.dragging {
  background-color: #202040;
  border-color: #2dc0f0;
}

.upload-status {
  margin-top: 1rem;
  padding: 0.8rem;
  background-color: #1d1d2c;
  border-radius: 4px;
  border-left: 3px solid #f5c2e7;
}

.status-text {
  font-size: 0.9rem;
  color: #cdd6f4;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 6px;
  background-color: #333344;
  border-radius: 3px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #f5c2e7;
  transition: width 0.3s ease;
}

.upload-success-status {
  border-left-color: #2dc0f0;
}

.uploaded-file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem;
  margin-top: 1rem;
  background-color: #1d1d2c;
  border-radius: 4px;
  border-left: 3px solid #2dc0f0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-icon-small {
  font-size: 1.2rem;
  color: #f5c2e7;
}

.file-name {
  font-size: 0.9rem;
  color: #cdd6f4;
}

.upload-success {
  font-size: 0.8rem;
  color: #2dc0f0;
  margin-left: 0.5rem;
}

.delete-file-btn {
  background: none;
  border: none;
  color: #f38ba8;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.delete-file-btn:hover {
  color: #f5c2e7;
  transform: scale(1.1);
}

.error-text {
  color: #f38ba8;
  font-weight: bold;
}

.file-status-prompt {
  background-color: #302c42;
  border-left: 4px solid #f38ba8;
  padding: 0.8rem;
  margin: 1rem 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.file-status-prompt p {
  color: #cdd6f4;
  margin: 0;
}

.file-status-prompt i {
  color: #f38ba8;
  margin-right: 0.5rem;
}

.rescan-button {
  background-color: #27273e;
  color: #cdd6f4;
  border: 1px solid #444466;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rescan-button:hover {
  background-color: #2d2d44;
  border-color: #2dc0f0;
  color: #2dc0f0;
}

@media (max-width: 768px) {
  .import-files {
    flex-direction: column;
    align-items: center;
  }

  .file-section {
    width: 100%;
    max-width: 100%;
  }
}
</style>

