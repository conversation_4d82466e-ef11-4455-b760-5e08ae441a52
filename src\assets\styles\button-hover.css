/* 按钮悬停效果全局样式 */
.btn,
button,
.action-button,
.control-button,
.select-header-btn,
.merge-button,
.split-button,
.editor-button,
.save-button,
.vertical-manage-btn,
.top-button,
.image-item {
  transition: all 0.2s ease-in-out;
}

.btn:hover,
button:hover,
.action-button:hover,
.control-button:hover,
.select-header-btn:hover,
.merge-button:hover,
.split-button:hover,
.editor-button:hover,
.save-button:hover,
.vertical-manage-btn:hover,
.top-button:hover,
.image-item:hover {
  filter: brightness(1.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 统一发光边框效果 */
.display-window:hover,
.image-item:hover {
  box-shadow: 0 0 0 2px #0fbcaa, 0 0 8px rgba(15, 188, 170, 0.5);
  transform: none;
}

/* 酷炫渐变按钮样式，可全局调用 */
.fancy-gradient-btn {
  background: linear-gradient(90deg, #ff6a00, #ee0979);
  color: #fff;
  border: none;
  border-radius: 22px;
  padding: 0.7rem 1.5rem;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.fancy-gradient-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
} 