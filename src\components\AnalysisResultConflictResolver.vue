<template>
  <div class="conflict-resolver-backdrop">
    <div class="conflict-resolver-modal">
      <h3>分析结果冲突对比（{{ typeLabel }}）</h3>
      <div v-if="conflictList.length > 0">
        <table class="conflict-table">
          <thead>
            <tr>
              <th>名称</th>
              <th>旧内容</th>
              <th>新内容</th>
              <th>选择保留</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in conflictList"
              :key="item.name"
            >
              <td class="name-cell">
                {{ item.name }}
              </td>
              <td class="desc-cell old-desc">
                <pre>{{ item.old ? item.old.description : '-' }}</pre>
              </td>
              <td class="desc-cell new-desc">
                <pre>{{ item.new ? item.new.description : '-' }}</pre>
              </td>
              <td class="choice-cell">
                <label>
                  <input
                    type="radio"
                    :name="'choice-' + item.name"
                    value="old"
                    v-model="item.choice"
                  > 保留旧
                </label>
                <label v-if="item.new">
                  <input
                    type="radio"
                    :name="'choice-' + item.name"
                    value="new"
                    v-model="item.choice"
                  > 保留新
                </label>
                <label v-if="!item.old && item.new">
                  <input
                    type="radio"
                    :name="'choice-' + item.name"
                    value="add"
                    v-model="item.choice"
                  > 新增
                </label>
              </td>
            </tr>
          </tbody>
        </table>
        <button
          @click="confirmMerge"
          :disabled="!allResolved"
        >
          确认合并
        </button>
        <button
          @click="emitNoChange"
          class="cancel-btn"
        >
          取消
        </button>
      </div>
      <div v-else>
        <p>无冲突或新增项。</p>
        <button @click="emitNoChange">
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive, watch } from 'vue';
const props = defineProps({
  oldItems: { type: Array, required: true },
  newItems: { type: Array, required: true },
  type: { type: String, required: true } // 例如 'characters', 'specialItems' 等
});
const emit = defineEmits(['resolve', 'cancel']);

const typeLabel = computed(() => {
  switch (props.type) {
    case 'characters': return '角色';
    case 'specialItems': return '特殊物品';
    case 'techniques': return '功法';
    case 'scenes': return '场景';
    default: return props.type;
  }
});

// 构建冲突对比列表
const conflictList = reactive([]);

function buildConflictList() {
  conflictList.length = 0;
  const oldMap = Object.fromEntries(props.oldItems.map(i => [i.name, i]));
  const newMap = Object.fromEntries(props.newItems.map(i => [i.name, i]));
  const allNames = Array.from(new Set([...Object.keys(oldMap), ...Object.keys(newMap)]));
  allNames.forEach(name => {
    const oldItem = oldMap[name] || null;
    const newItem = newMap[name] || null;
    let choice = oldItem && !newItem ? 'old' : newItem && !oldItem ? 'add' : 'new';
    conflictList.push({ name, old: oldItem, new: newItem, choice });
  });
}

watch(() => [props.oldItems, props.newItems], buildConflictList, { immediate: true });

const allResolved = computed(() => conflictList.every(item => item.choice));

function confirmMerge() {
  // 合并结果：按用户选择生成新列表
  const merged = conflictList.map(item => {
    if (item.choice === 'old') return item.old;
    if (item.choice === 'new' || item.choice === 'add') return item.new;
    return null;
  }).filter(Boolean);
  emit('resolve', merged);
}
function emitNoChange() {
  emit('cancel');
}
</script>

<style scoped>
.conflict-resolver-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(30, 28, 47, 0.85);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.conflict-resolver-modal {
  background: #232136;
  border-radius: 8px;
  padding: 2rem;
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 24px #0008;
  z-index: 10000;
  position: relative;
}
.conflict-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
  table-layout: fixed;
}
.conflict-table th, .conflict-table td {
  border: 1px solid #393952;
  padding: 0.7rem 0.7rem;
  text-align: left;
  vertical-align: top;
}
.name-cell {
  min-width: 70px;
  font-weight: bold;
  color: #cba6f7;
  background: #232136;
}
.desc-cell {
  min-width: 220px;
  max-width: 350px;
  word-break: break-all;
  white-space: pre-wrap;
  background: #232136;
}
.old-desc {
  background: #232136;
  border-right: 1px solid #393952;
}
.new-desc {
  background: #1e1c2f;
}
.desc-cell pre {
  margin: 0;
  font-size: 1em;
  background: none;
  color: #b5bfe2;
  white-space: pre-wrap;
  word-break: break-word;
}
.choice-cell {
  min-width: 120px;
  text-align: left;
  background: #232136;
}
button {
  background: linear-gradient(90deg, #29908b 0%, #a23a5a 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
}
button:disabled {
  background: #393952;
  color: #b5bfe2;
  cursor: not-allowed;
}
button.cancel-btn {
  background: #393952;
  color: #b5bfe2;
  margin-left: 1rem;
}
button.cancel-btn:hover {
  background: #5a2a5a;
  color: #fff;
}
</style> 