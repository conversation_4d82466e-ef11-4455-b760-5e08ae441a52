/**
 * 数据管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的数据管理相关功能
 * 
 * 功能：
 * - 项目数据加载和初始化
 * - 数据验证和修复
 * - 数据保存和持久化
 * - 角色状态管理
 * - 测试数据生成
 * - 文件读取和处理
 */

import { reactive } from 'vue';

export function useDataManagementStudio() {
  // 数据管理状态
  const dataState = reactive({
    isLoading: false,
    loadingMessage: '',
    lastLoadTime: null,
    dataValidationResults: null
  });

  /**
   * 从文件系统加载项目数据
   * @param {Object} context - 组件上下文
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   */
  const loadProjectDataFromFile = async (context, projectTitle, chapterTitle) => {
    return await context.dataPersistence.loadProjectDataFromFile(context, projectTitle, chapterTitle);
  };

  /**
   * 立即保存项目数据（不使用防抖）
   * @param {Object} context - 组件上下文
   */
  const saveProjectDataImmediately = async (context) => {
    await context.dataPersistence.saveProjectDataImmediately(context);
  };

  /**
   * 简化的图片数据验证
   * @param {Object} context - 组件上下文
   */
  const simpleImageDataValidation = (context) => {
    // 使用新的数据验证工具
    const result = context.dataValidationUtils.validateImageData(context.rows);
    dataState.dataValidationResults = result;
    return result;
  };

  /**
   * 修复图像数据结构
   * @param {Object} context - 组件上下文
   */
  const fixImageDataStructure = (context) => {
    // 使用新的数据验证工具
    const result = context.dataValidationUtils.fixImageStructure(context.rows);

    if (result.fixedCount > 0) {
      saveProjectDataImmediately(context);
      context.showSuccessMessage('数据修复完成', `已修复${result.fixedCount}行的图片数据结构，确保数据一致性`);
    } else {
      context.showInfoMessage('数据检查', '图片数据结构正常，无需修复');
    }

    return result;
  };

  /**
   * 保存全局选择的角色状态到项目数据
   * @param {Object} context - 组件上下文
   */
  const saveGlobalSelectedCharacters = (context) => {
    console.log('[Performance] saveGlobalSelectedCharacters 被调用');
    console.log('[Performance] 当前 currentSelectedCharacters:', context.currentSelectedCharacters);

    if (context.projectData && context.projectData.data) {
      // 保存当前选中的角色到项目数据
      context.projectData.data.currentSelectedCharacters = context.currentSelectedCharacters;

      // 使用防抖保存
      context.debouncedSaveProject();
      console.log('[Performance] 全局选择角色状态已保存到项目数据');
    } else {
      console.error('[Performance] 无法保存全局选择角色状态: projectData 或 projectData.data 不存在');
    }
  };

  /**
   * 从项目数据加载全局选择的角色状态
   * @param {Object} context - 组件上下文
   */
  const loadGlobalSelectedCharacters = (context) => {
    // 确保 currentSelectedCharacters 始终是一个数组
    if (context.projectData && context.projectData.data &&
        Array.isArray(context.projectData.data.currentSelectedCharacters)) {
      context.currentSelectedCharacters = [...context.projectData.data.currentSelectedCharacters]; // 创建副本
    } else {
      context.currentSelectedCharacters = []; // 默认为空数组
    }

    console.log('[Performance] loadGlobalSelectedCharacters 完成，当前角色:', context.currentSelectedCharacters);
  };



  /**
   * 强制从SRT文件读取内容
   * @param {Object} context - 组件上下文
   */
  const forceReadSrtFile = async (context) => {
    if (!context.projectData || !context.projectData.data || !context.projectData.data.srtFilePath) {
      context.showErrorMessage('项目数据中没有SRT文件路径');
      return false;
    }

    try {
      console.log('强制从SRT文件读取内容:', context.projectData.data.srtFilePath);

      // 读取SRT文件内容
      const srtContent = await readFileContent(context, context.projectData.data.srtFilePath);

      if (!srtContent) {
        console.error('SRT文件内容为空');
        return false;
      }

      // 解析SRT内容
      const subtitles = parseSrtContent(srtContent);

      if (!subtitles || subtitles.length === 0) {
        console.error('无法解析SRT内容或内容为空');
        return false;
      }

      // 转换为行数据格式
      convertSubtitlesToRows(context, subtitles, true);

      console.log('成功从SRT文件加载数据，行数:', context.rows.length);
      return true;

    } catch (error) {
      console.error('强制读取SRT文件失败:', error);
      context.showErrorMessage('读取SRT文件失败: ' + error.message);
      return false;
    }
  };

  /**
   * 强制初始化数据
   * @param {Object} context - 组件上下文
   */
  const forceInitializeData = async (context) => {
    console.log('强制初始化数据...');

    if (!context.projectData || !context.projectData.data) {
      context.showErrorMessage('项目数据不完整');
      return false;
    }

    // 检查是否有SRT文件路径
    if (!context.projectData.data.srtFilePath) {
      console.error('项目数据中没有SRT文件路径');
      context.showErrorMessage('项目数据中没有SRT文件路径，请先上传SRT文件');
      return false;
    }

    // 直接从SRT文件加载内容
    const success = await forceReadSrtFile(context);

    if (success) {
      console.log('成功强制加载SRT文件内容并初始化数据');
      context.showSuccessMessage('数据加载成功', `成功加载 ${context.rows.length} 条字幕`);
      return true;
    } else {
      console.error('强制初始化数据失败');
      context.showErrorMessage('无法初始化数据，请检查SRT文件是否存在或格式是否正确');
      return false;
    }
  };

  /**
   * 读取文件内容
   * @param {Object} context - 组件上下文
   * @param {string} filePath - 文件路径
   */
  const readFileContent = async (context, filePath) => {
    try {
      console.log('读取文件内容:', filePath);
      const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(filePath)}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('文件内容读取成功，长度:', result.content.length);
        return result.content;
      } else {
        throw new Error(result.message || '读取文件失败');
      }
    } catch (error) {
      console.error('读取文件内容失败:', error);
      throw error;
    }
  };

  /**
   * 解析SRT内容
   * @param {string} srtContent - SRT文件内容
   */
  const parseSrtContent = (srtContent) => {
    try {
      if (!srtContent || typeof srtContent !== 'string') {
        console.error('SRT内容无效');
        return [];
      }

      console.log('开始解析SRT内容，长度:', srtContent.length);

      // 清理内容：移除BOM和多余的空白字符
      const cleanContent = srtContent.replace(/^\uFEFF/, '').trim();

      if (!cleanContent) {
        console.error('清理后的SRT内容为空');
        return [];
      }

      // 分割字幕块，支持不同的换行符
      const blocks = cleanContent.split(/\n\s*\n/).filter(block => block.trim());
      console.log('分割后的字幕块数量:', blocks.length);

      const subtitles = [];

      blocks.forEach((block, blockIndex) => {
        try {
          const lines = block.trim().split('\n');

          if (lines.length < 3) {
            console.warn(`字幕块 ${blockIndex + 1} 格式不完整，跳过:`, lines);
            return;
          }

          // 解析序号
          const indexLine = lines[0].trim();
          const index = parseInt(indexLine, 10);

          if (isNaN(index)) {
            console.warn(`字幕块 ${blockIndex + 1} 序号无效:`, indexLine);
            return;
          }

          // 解析时间轴
          const timeLine = lines[1].trim();
          const timeMatch = timeLine.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})/);

          if (!timeMatch) {
            console.warn(`字幕块 ${blockIndex + 1} 时间格式无效:`, timeLine);
            return;
          }

          // 计算时间（毫秒）
          const startTime = parseInt(timeMatch[1]) * 3600000 + parseInt(timeMatch[2]) * 60000 + parseInt(timeMatch[3]) * 1000 + parseInt(timeMatch[4]);
          const endTime = parseInt(timeMatch[5]) * 3600000 + parseInt(timeMatch[6]) * 60000 + parseInt(timeMatch[7]) * 1000 + parseInt(timeMatch[8]);
          const duration = endTime - startTime;

          // 解析文本内容（可能有多行）
          const textLines = lines.slice(2);
          const text = textLines.join('\n').trim();

          if (!text) {
            console.warn(`字幕块 ${blockIndex + 1} 文本内容为空`);
            return;
          }

          subtitles.push({
            index,
            startTime,
            endTime,
            duration,
            text
          });

        } catch (error) {
          console.error(`解析字幕块 ${blockIndex + 1} 时出错:`, error, block);
        }
      });

      console.log('SRT解析完成，有效字幕数量:', subtitles.length);
      return subtitles;

    } catch (error) {
      console.error('解析SRT内容失败:', error);
      return [];
    }
  };

  /**
   * 转换字幕数据为行数据
   * @param {Object} context - 组件上下文
   * @param {Array} subtitles - 字幕数据数组
   * @param {boolean} forceRefresh - 是否强制刷新
   */
  const convertSubtitlesToRows = (context, subtitles, forceRefresh = false) => {
    if (!subtitles || subtitles.length === 0) {
      console.warn('没有字幕数据可转换');
      if (context.uiStateStudio && context.uiStateStudio.setLoadingState) {
        context.uiStateStudio.setLoadingState(false, '未找到字幕数据');
      }
      return;
    }

    console.log(`开始转换字幕数据为行数据，字幕数量: ${subtitles.length}, 强制刷新: ${forceRefresh}`);

    // 保存当前的 currentSelectedCharacters
    const currentSelectedCharacters = context.projectData?.data?.currentSelectedCharacters;

    if (forceRefresh) {
      console.log('强制刷新模式：创建全新的行数据');
      // 强制刷新：创建全新的行数据
      context.rows = subtitles.map(subtitle => ({
        ...subtitle,
        keywords: '',
        tags: [],
        imageSrc: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        imageAlt: '',
        thumbnails: [],

        // 🔧 修复：确保图像生成相关的状态属性完整初始化
        isGenerating: false,
        isQueued: false,
        isInferring: false,
        generationProgress: 0,
        generationMessage: '',
        queueTaskId: '',
        queuePosition: 0,
        _isCancelling: false,

        isSelected: false, // 🔧 新行默认不选中
        isLocked: false    // 🔧 新行默认不锁定
      }));
    } else {
      // 智能合并：保留现有数据，只更新字幕信息
      console.log('智能合并模式：保留现有数据，只更新字幕信息');

      const newRows = subtitles.map((subtitle, index) => {
        const existingRow = context.rows[index];

        if (existingRow) {
          // 保留现有行的所有数据，只更新字幕相关字段
          return {
            ...existingRow,
            index: subtitle.index,
            startTime: subtitle.startTime,
            endTime: subtitle.endTime,
            duration: subtitle.duration,
            text: subtitle.text
          };
        } else {
          // 新增行
          return {
            ...subtitle,
            keywords: '',
            tags: [],
            imageSrc: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
            imageAlt: '',
            thumbnails: [],
            isGenerating: false,
            generationProgress: 0,
            generationMessage: '',
            isSelected: false,
            isLocked: false
          };
        }
      });

      context.rows = newRows;
    }

    // 🔧 修复：更新项目数据，使用深拷贝避免引用问题
    if (context.projectData?.data) {
      context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));

      // 恢复 currentSelectedCharacters
      if (currentSelectedCharacters) {
        context.projectData.data.currentSelectedCharacters = currentSelectedCharacters;
        console.log('convertSubtitlesToRows - 保留 currentSelectedCharacters:', currentSelectedCharacters);
      }

      context.debouncedSaveProject();
    }

    if (context.uiStateStudio && context.uiStateStudio.setLoadingState) {
      context.uiStateStudio.setLoadingState(false);
    }
    console.log(`字幕数据转换完成，总行数: ${context.rows.length}`);
  };

  /**
   * 清理错误的排队状态
   * @param {Object} context - 组件上下文
   */
  const cleanupErrorQueueStates = (context) => {
    console.log('🧹 [状态清理] 开始清理错误的排队状态...');

    let cleanedCount = 0;

    context.rows.forEach((row, index) => {
      // 检查是否有错误的排队状态：isQueued为true但没有queueTaskId
      if (row.isQueued && (!row.queueTaskId || row.queueTaskId === '')) {
        console.log(`🧹 [状态清理] 发现第${index + 1}行有错误的排队状态，正在清理...`);

        // 清理错误状态
        row.isQueued = false;
        row.queuePosition = 0;
        row.queueTaskId = '';
        row.generationMessage = '';

        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`🧹 [状态清理] 共清理了${cleanedCount}行的错误排队状态`);
      // 立即保存清理后的数据
      saveProjectDataImmediately(context);
    } else {
      console.log('🧹 [状态清理] 未发现需要清理的排队状态');
    }
  };

  return {
    // 状态
    dataState,

    // 数据加载方法
    loadProjectDataFromFile,
    forceReadSrtFile,
    forceInitializeData,
    readFileContent,

    // 数据保存方法
    saveProjectDataImmediately,

    // 数据验证方法
    simpleImageDataValidation,
    fixImageDataStructure,

    // 角色状态管理
    saveGlobalSelectedCharacters,
    loadGlobalSelectedCharacters,

    // 数据处理方法
    parseSrtContent,
    convertSubtitlesToRows,

    // 工具方法
    cleanupErrorQueueStates
  };
}
