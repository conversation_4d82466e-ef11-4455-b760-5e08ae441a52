<template>
  <div class="panel scene-panel">
    <h4 class="panel-title">
      当前场景 ({{ items.length }})
    </h4>
    <table class="scene-table auto-width-table">
      <thead>
        <tr>
          <th class="column-range">
            Line Range
          </th>
          <th class="column-location">
            Location
          </th>
          <th class="column-time">
            Time
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="items.length === 0">
          <td
            colspan="3"
            class="no-data-cell"
          >
            暂无数据
          </td>
        </tr>
        <tr
          v-for="(item, index) in items"
          :key="item.id || item.range || index"
          class="scene-table-row"
        >
          <td class="cell-range">
            {{ item.range }}
          </td>
          <td class="cell-location">
            {{ item.location }}
          </td>
          <td class="cell-time">
            {{ item.time }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
defineProps({
  items: { type: Array, required: true, default: () => [] }
});
</script>

<style scoped>
.scene-panel {
  background: #232136;
  border-radius: 6px;
  padding: 0;
  margin-top: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.panel-title {
  font-size: 1.15em;
  color: #e0def4;
  font-weight: 600;
  margin: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #44415a;
}
.scene-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #2a273f;
  color: #e0def4;
  table-layout: auto;
}
.scene-table th,
.scene-table td {
  border: 1px solid #44415a;
  padding: 10px 12px;
  text-align: left;
  vertical-align: middle;
}
.scene-table th {
  background-color: #232136;
  font-weight: 600;
  font-size: 0.9em;
  color: #b8b5d6;
}
.column-range { min-width: 90px; white-space: nowrap; }
.column-location { min-width: 120px; white-space: normal; }
.column-time { min-width: 90px; white-space: nowrap; }
.cell-range, .cell-location, .cell-time {
  white-space: pre-line;
  word-break: break-word;
  font-size: 0.98em;
  max-width: 100%;
  overflow: hidden;
}
.no-data-cell {
  text-align: center;
  color: #908caa;
  font-style: italic;
  background: #232136;
}
</style> 