<template>
  <div class="grid-row data-row">
    <slot />
  </div>
</template>

<script>
export default {
  name: '<PERSON>ridRow'
}
</script>

<style scoped>
.grid-row {
  display: table-row;
}

.data-row {
  height: 250px;
}

.data-row .grid-cell {
  height: 250px;
  vertical-align: top;
  background-color: #252525;
}

.cell-content {
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
}
</style> 