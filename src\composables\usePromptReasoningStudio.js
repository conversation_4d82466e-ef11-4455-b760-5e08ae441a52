/**
 * 提示词推理管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的提示词推理相关功能
 * 
 * 功能：
 * - 单行提示词推理
 * - 提示词预览
 * - 角色标签自动应用
 * - 图像提示词生成器设置
 * - 关键词清除和管理
 * - 推理数据格式化
 */

import { reactive } from 'vue';

export function usePromptReasoningStudio() {
  // 推理状态管理
  const reasoningState = reactive({
    isProcessing: false,
    currentRowIndex: -1,
    lastReasoningResult: null,
    previewData: null
  });

  /**
   * 处理单行提示词推理
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   * @param {boolean} isBatchMode - 是否为批量模式
   */
  const handleInferPrompt = async (context, index, isBatchMode = false) => {
    console.log('[ImagePromptReasoning] 🚀 直接发送推理请求，行索引:', index, '批量模式:', isBatchMode);

    try {
      // 检查行索引是否有效
      if (index < 0 || index >= context.rows.length) {
        throw new Error(`无效的行索引: ${index}`);
      }

      // 检查是否有行数据
      if (!context.rows || context.rows.length === 0) {
        throw new Error('没有可用的行数据');
      }

      const row = context.rows[index];

      // 检查行是否被锁定
      if (row.isLocked) {
        const errorMsg = '该行已锁定，无法推理提示词。请先解锁该行再进行操作。';
        if (!isBatchMode) {
          context.showErrorMessage(errorMsg, '推理操作被阻止');
        }
        throw new Error(errorMsg);
      }

      // 设置推理状态
      reasoningState.isProcessing = true;
      reasoningState.currentRowIndex = index;

      // 设置行级别的推理状态，确保按钮动画正常显示
      row.isInferring = true;

      // 批量模式下不设置全局加载状态，避免状态冲突
      if (!isBatchMode) {
        context.isImagePromptLoading = true;
        context.showInfoMessage('推理中', `正在为第${index + 1}行生成图像提示词...`);
      }

      // 准备推理数据（上下文行数将从用户设置中自动获取）
      const reasoningData = await context.imagePromptReasoning.prepareReasoningData(
        context.rows,
        index
      );

      console.log('[ImagePromptReasoning] 推理数据准备完成，直接发送请求');

      // 直接执行推理，无需确认弹窗
      const result = await context.imagePromptReasoning.executeReasoning(reasoningData);

      if (result.success) {
        console.log('[ImagePromptReasoning] 推理成功，应用结果到行数据');

        const targetIndex = result.targetIndex;
        let processedPrompt = result.prompt;

        if (targetIndex >= 0 && targetIndex < context.rows.length && processedPrompt) {
          console.log('[ImagePromptReasoning] 开始处理推理结果:', processedPrompt);

          // 检查LLM返回的结果是否包含角色占位符
          if (processedPrompt && (processedPrompt.includes('{character}') || processedPrompt.includes('{Character}'))) {
            console.log('[ImagePromptReasoning] LLM结果包含角色占位符，执行角色识别:', processedPrompt);

            try {
              // 导入角色识别功能
              const { useCharacterRecognition } = await import('../composables/useCharacterRecognition.js');
              const { recognizeCharactersInPrompt } = useCharacterRecognition();

              // 执行角色识别
              const recognitionResult = recognizeCharactersInPrompt(processedPrompt, context.availableCharactersForPrompt);

              if (recognitionResult && recognitionResult.autoSelectedTags && recognitionResult.autoSelectedTags.length > 0) {
                console.log('[ImagePromptReasoning] 角色识别成功，移除占位符并应用标签:', {
                  原始: processedPrompt,
                  处理后: recognitionResult.replacedPrompt,
                  标签: recognitionResult.autoSelectedTags
                });

                // 使用移除占位符后的提示词
                processedPrompt = recognitionResult.replacedPrompt;

                // 应用角色标签
                await handleApplyCharacterTags(context, {
                  rowIndex: targetIndex,
                  autoSelectedTags: recognitionResult.autoSelectedTags,
                  replacedPrompt: recognitionResult.replacedPrompt
                });
              }
            } catch (error) {
              console.error('[ImagePromptReasoning] 角色识别处理失败:', error);
              // 如果角色识别失败，继续使用原始提示词
            }
          }

          // 获取当前行的关键词元数据
          const currentRow = context.rows[targetIndex];
          const metadata = context.initializeKeywordsMetadata(currentRow);

          // 将处理后的AI生成的提示词添加到手动内容中（因为这是用户主动触发的AI推理）
          const existingManual = metadata.manualContent || '';
          const newManualContent = existingManual ?
            `${existingManual}, ${processedPrompt}` :
            processedPrompt;

          // 通过正确的关键词更新机制来更新
          context.handleKeywordsUpdated({
            rowIndex: targetIndex,
            keywords: newManualContent,
            isAutoGenerated: false  // 标记为手动内容，因为是用户主动触发的AI推理
          });

          // 批量模式下不显示成功消息，避免消息过多
          if (!isBatchMode) {
            context.showSuccessMessage('推理成功', `已为第${targetIndex + 1}行生成图像提示词`);
          }

          // 保存推理结果
          reasoningState.lastReasoningResult = {
            success: true,
            targetIndex: targetIndex,
            prompt: processedPrompt,
            originalResult: result
          };

          // 返回推理结果供批量处理使用
          return reasoningState.lastReasoningResult;
        }
      } else {
        throw new Error(result.error || '推理失败');
      }

    } catch (error) {
      console.error('[ImagePromptReasoning] 推理提示词失败:', error);

      // 批量模式下不显示错误消息，避免消息过多
      if (!isBatchMode) {
        context.showErrorMessage('推理失败: ' + error.message);
      }

      // 返回错误结果供批量处理使用
      const errorResult = {
        success: false,
        error: error.message,
        targetIndex: index
      };

      reasoningState.lastReasoningResult = errorResult;
      return errorResult;

    } finally {
      // 清理行级别的推理状态
      if (index >= 0 && index < context.rows.length) {
        const row = context.rows[index];
        row.isInferring = false;

        // 批量模式下不自动取消选中状态，由批量处理系统管理
        if (!isBatchMode && row.isSelected) {
          row.isSelected = false;
          console.log(`🔧 [状态管理] 行${index + 1} 推理完成，自动取消选中状态`);
        }
      }

      // 批量模式下不重置全局加载状态
      if (!isBatchMode) {
        context.isImagePromptLoading = false;
      }

      // 重置推理状态
      reasoningState.isProcessing = false;
      reasoningState.currentRowIndex = -1;
    }
  };

  /**
   * 处理提示词预览
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handlePreviewPrompt = async (context, index) => {
    console.log('[ImagePromptReasoning] 👁️ 预览提示词，行索引:', index);

    try {
      // 检查行索引是否有效
      if (index < 0 || index >= context.rows.length) {
        throw new Error(`无效的行索引: ${index}`);
      }

      // 检查是否有行数据
      if (!context.rows || context.rows.length === 0) {
        throw new Error('没有可用的行数据');
      }

      // 准备推理数据（上下文行数将从用户设置中自动获取）
      const reasoningData = await context.imagePromptReasoning.prepareReasoningData(
        context.rows,
        index
      );

      console.log('[ImagePromptReasoning] 推理数据准备完成，显示预览窗口');

      // 保存预览数据
      reasoningState.previewData = reasoningData;

      // 使用新的UI状态管理系统显示预览窗口
      if (context.uiStateStudio && context.uiStateStudio.openImagePromptTestModal) {
        context.uiStateStudio.openImagePromptTestModal(reasoningData, reasoningData.fullPrompt);
      } else {
        // 兼容旧版本的直接设置方式
        console.warn('uiStateStudio不可用，使用直接设置方式');
        if (context.uiStateStudio && context.uiStateStudio.uiState) {
          context.uiStateStudio.uiState.currentPromptData = reasoningData;
          context.uiStateStudio.uiState.fullPromptPreview = reasoningData.fullPrompt;
          context.uiStateStudio.uiState.showImagePromptTestModal = true;
        } else {
          // 最后的兼容方案
          context.currentPromptData = reasoningData;
          context.fullPromptPreview = reasoningData.fullPrompt;
          context.showImagePromptTestModal = true;
        }
      }

    } catch (error) {
      console.error('[ImagePromptReasoning] 预览提示词失败:', error);
      context.showErrorMessage('预览提示词失败: ' + error.message);
    }
  };

  /**
   * 处理确认发送提示词
   * @param {Object} context - 组件上下文
   * @param {Object} promptData - 提示词数据
   */
  const handleConfirmSendPrompt = async (context, promptData) => {
    console.log('[ImagePromptReasoning] 确认发送提示词，目标行:', promptData.targetIndex);

    try {
      // 使用新的UI状态管理系统设置加载状态
      if (context.uiStateStudio && context.uiStateStudio.setImagePromptLoading) {
        context.uiStateStudio.setImagePromptLoading(true);
      } else if (context.uiStateStudio && context.uiStateStudio.uiState) {
        context.uiStateStudio.uiState.isImagePromptLoading = true;
      } else {
        context.isImagePromptLoading = true;
      }

      // 执行推理
      const result = await context.imagePromptReasoning.executeReasoning(promptData);

      if (result.success) {
        console.log('[ImagePromptReasoning] 推理成功，应用结果到行数据');

        // 将生成的提示词应用到目标行的 keywords 字段
        const targetIndex = result.targetIndex;
        if (targetIndex >= 0 && targetIndex < context.rows.length) {
          // 获取当前行的关键词元数据
          const currentRow = context.rows[targetIndex];
          const metadata = context.initializeKeywordsMetadata(currentRow);

          // 在应用LLM结果之前，先进行角色识别处理
          let processedPrompt = result.prompt;

          // 确保 processedPrompt 是字符串
          if (typeof processedPrompt !== 'string') {
            console.warn('[ImagePromptReasoning] processedPrompt 不是字符串，尝试转换:', typeof processedPrompt, processedPrompt);
            processedPrompt = String(processedPrompt || '');
          }

          // 检查LLM返回的结果是否包含角色占位符
          if (processedPrompt && (processedPrompt.includes('{character}') || processedPrompt.includes('{Character}'))) {
            console.log('[ImagePromptReasoning] LLM结果包含角色占位符，执行角色识别:', processedPrompt);

            try {
              // 导入角色识别功能
              const { useCharacterRecognition } = await import('../composables/useCharacterRecognition.js');
              const { recognizeCharactersInPrompt } = useCharacterRecognition();

              // 执行角色识别
              const recognitionResult = recognizeCharactersInPrompt(processedPrompt, context.availableCharactersForPrompt);

              if (recognitionResult && recognitionResult.autoSelectedTags && recognitionResult.autoSelectedTags.length > 0) {
                console.log('[ImagePromptReasoning] 角色识别成功，移除占位符并应用标签:', {
                  原始: processedPrompt,
                  处理后: recognitionResult.replacedPrompt,
                  标签: recognitionResult.autoSelectedTags
                });

                // 使用移除占位符后的提示词
                processedPrompt = recognitionResult.replacedPrompt;

                // 应用角色标签
                await handleApplyCharacterTags(context, {
                  rowIndex: targetIndex,
                  autoSelectedTags: recognitionResult.autoSelectedTags,
                  replacedPrompt: recognitionResult.replacedPrompt
                });
              }
            } catch (error) {
              console.error('[ImagePromptReasoning] 角色识别处理失败:', error);
              // 如果角色识别失败，继续使用原始提示词
            }
          }

          // 将处理后的AI生成的提示词添加到手动内容中（因为这是用户主动触发的AI推理）
          const existingManual = metadata.manualContent || '';
          const newManualContent = existingManual ?
            `${existingManual}, ${processedPrompt}` :
            processedPrompt;

          // 通过正确的关键词更新机制来更新
          context.handleKeywordsUpdated({
            rowIndex: targetIndex,
            keywords: newManualContent,
            isAutoGenerated: false  // 标记为手动内容，因为是用户主动触发的AI推理
          });

          // 显示成功消息
          context.showSuccessMessage('推理成功', `已为第${targetIndex + 1}行生成图像提示词`);
        }

        // 关闭测试窗口
        handleCloseTestModal(context);

      } else {
        throw new Error(result.error || '推理失败');
      }

    } catch (error) {
      console.error('[ImagePromptReasoning] 发送提示词失败:', error);
      context.showErrorMessage('推理失败: ' + error.message);
    } finally {
      // 使用新的UI状态管理系统重置加载状态
      if (context.uiStateStudio && context.uiStateStudio.setImagePromptLoading) {
        context.uiStateStudio.setImagePromptLoading(false);
      } else if (context.uiStateStudio && context.uiStateStudio.uiState) {
        context.uiStateStudio.uiState.isImagePromptLoading = false;
      } else {
        context.isImagePromptLoading = false;
      }
    }
  };

  /**
   * 处理关闭测试窗口
   * @param {Object} context - 组件上下文
   */
  const handleCloseTestModal = (context) => {
    console.log('[ImagePromptReasoning] 关闭测试窗口');

    // 使用新的UI状态管理系统关闭测试窗口
    if (context.uiStateStudio && context.uiStateStudio.closeImagePromptTestModal) {
      context.uiStateStudio.closeImagePromptTestModal();
    } else {
      // 兼容旧版本的直接设置方式
      console.warn('uiStateStudio不可用，使用直接设置方式');
      if (context.uiStateStudio && context.uiStateStudio.uiState) {
        context.uiStateStudio.uiState.showImagePromptTestModal = false;
        context.uiStateStudio.uiState.currentPromptData = null;
        context.uiStateStudio.uiState.fullPromptPreview = '';
        context.uiStateStudio.uiState.isImagePromptLoading = false;
      } else {
        // 最后的兼容方案
        context.showImagePromptTestModal = false;
        context.currentPromptData = null;
        context.fullPromptPreview = '';
        context.isImagePromptLoading = false;
      }
    }

    // 清除预览数据
    reasoningState.previewData = null;
  };

  /**
   * 处理角色标签自动应用
   * @param {Object} context - 组件上下文
   * @param {Object} event - 事件数据
   */
  const handleApplyCharacterTags = async (context, event) => {
    console.log('[CharacterRecognition] 处理角色标签自动应用:', event);

    const { rowIndex, autoSelectedTags, replacedPrompt } = event;

    if (rowIndex < 0 || rowIndex >= context.rows.length) {
      console.warn('[CharacterRecognition] 无效的行索引:', rowIndex);
      return;
    }

    if (!autoSelectedTags || autoSelectedTags.length === 0) {
      console.log('[CharacterRecognition] 没有需要应用的标签');
      return;
    }

    try {
      console.log('[CharacterRecognition] 开始应用角色标签到行:', { rowIndex, autoSelectedTags, replacedPrompt });

      // 获取目标行
      const targetRow = context.rows[rowIndex];

      // 确保行有selectedTags属性
      if (!targetRow.selectedTags || !Array.isArray(targetRow.selectedTags)) {
        targetRow.selectedTags = [];
      }

      // 如果有替换后的提示词，更新行的关键词字段（移除占位符）
      if (replacedPrompt !== undefined && replacedPrompt !== targetRow.keywords) {
        console.log('[CharacterRecognition] 更新提示词，移除占位符:', {
          原始: targetRow.keywords,
          替换后: replacedPrompt
        });
        targetRow.keywords = replacedPrompt;
      }

      // 合并自动识别的标签到现有标签中（避免重复）
      const existingTags = targetRow.selectedTags || [];
      const newTags = [...new Set([...existingTags, ...autoSelectedTags])];

      // 更新行的标签选择状态
      targetRow.selectedTags = newTags;

      console.log('[CharacterRecognition] 已更新行标签:', {
        rowIndex,
        oldTags: existingTags,
        newTags: newTags
      });

      // 触发标签选择变化事件，更新关键词
      context.handleTagSelectionChanged({
        rowIndex: rowIndex,
        selectedTags: newTags,
        isAutoGenerated: true  // 标记为自动生成
      });

      // 🔧 修复：保存项目数据，使用深拷贝避免引用问题
      if (context.projectData && context.projectData.data) {
        context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));
        context.debouncedSaveProject();
      }

      // 显示成功消息
      context.showSuccessMessage(
        '角色识别完成',
        `已为第${rowIndex + 1}行自动选中${autoSelectedTags.length}个角色标签`
      );

      console.log('[CharacterRecognition] 角色标签应用完成');

    } catch (error) {
      console.error('[CharacterRecognition] 应用角色标签失败:', error);
      context.showErrorMessage('应用角色标签失败: ' + error.message);
    }
  };

  /**
   * 处理图像提示词应用到行数据
   * @param {Object} context - 组件上下文
   * @param {Array} results - 推理结果数组
   */
  const handleApplyPromptsToRows = (context, results) => {
    console.log('应用图像提示词到行数据:', results);

    if (!results || results.length === 0) {
      context.showErrorMessage('没有生成的提示词可以应用');
      return;
    }

    let appliedCount = 0;

    // 将生成的提示词应用到对应的行
    results.forEach((result) => {
      if (result.success && result.prompt) {
        const selectedRowIndex = context.rows.findIndex(row =>
          row.isSelected && row.description === result.targetText
        );

        if (selectedRowIndex >= 0) {
          // 将生成的提示词添加到行的keywords字段
          const currentKeywords = context.rows[selectedRowIndex].keywords || '';
          const newKeywords = currentKeywords ?
            `${currentKeywords}, ${result.prompt}` :
            result.prompt;

          context.rows[selectedRowIndex].keywords = newKeywords;
          appliedCount++;
        }
      }
    });

    if (appliedCount > 0) {
      // 保存更新的数据
      if (context.projectData.data) {
        context.projectData.data.rows = context.rows;
        context.debouncedSaveProject();
      }

      context.showSuccessMessage(
        '图像提示词应用成功',
        `已为 ${appliedCount} 行应用图像提示词`
      );

      // 关闭抽屉
      context.showImagePromptDrawer = false;
    } else {
      context.showErrorMessage('没有成功应用任何图像提示词');
    }
  };

  /**
   * 处理打开图像提示词生成器设置
   * @param {Object} context - 组件上下文
   */
  const handleOpenImagePromptSettings = (context) => {
    console.log('打开图像提示词生成器设置');

    // 使用新的UI状态管理系统打开图像提示词抽屉
    if (context.uiStateStudio && context.uiStateStudio.openImagePromptDrawer) {
      context.uiStateStudio.openImagePromptDrawer(context);
    } else {
      // 兼容旧版本的直接设置方式
      console.warn('uiStateStudio不可用，使用直接设置方式');
      if (context.uiStateStudio && context.uiStateStudio.uiState) {
        context.uiStateStudio.uiState.showImagePromptDrawer = true;
      } else {
        context.showImagePromptDrawer = true;
      }
    }
  };

  /**
   * 处理清除所有行的关键词
   * @param {Object} context - 组件上下文
   */
  const handleClearAllKeywords = (context) => {
    console.log('清除所有行的关键词');

    if (!context.rows || context.rows.length === 0) {
      context.showInfoMessage('提示', '没有可清除的行数据');
      return;
    }

    // 确认对话框
    if (confirm(`确定要清除所有 ${context.rows.length} 行的提示词吗？此操作不可撤销。`)) {
      let clearedCount = 0;

      // 清除所有行的关键词
      context.rows.forEach((row, index) => {
        if (row.keywords && row.keywords.trim()) {
          // 清除关键词
          row.keywords = '';

          // 清除关键词元数据
          if (row.keywordsMetadata) {
            row.keywordsMetadata.manualContent = '';
            row.keywordsMetadata.autoContent = '';
            row.keywordsMetadata.lastModified = new Date().toISOString();
          }

          clearedCount++;
          console.log(`已清除第${index + 1}行的关键词`);
        }
      });

      // 保存更改
      if (clearedCount > 0) {
        context.debouncedSaveProject();
        context.showSuccessMessage('清除完成', `已清除 ${clearedCount} 行的提示词`);
      } else {
        context.showInfoMessage('提示', '没有找到需要清除的提示词');
      }
    }
  };

  /**
   * 获取格式化的文本内容供全局推理使用
   * @param {Object} context - 组件上下文
   * @returns {string} 格式化的文本内容
   */
  const getFormattedTextForReasoning = (context) => {
    if (!context.rows || context.rows.length === 0) {
      return '';
    }

    // 创建一个连续的纯文本表示，不包含镜头编号等标识
    const formattedText = context.rows.map((row) => {
      // 合并行需要特殊处理
      if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
        // 包含合并行的信息，但不添加标识
        const mergedTexts = row.mergedRows.map(mr => mr.description || '').filter(t => t.trim());
        const combinedText = mergedTexts.join('\n');
        return row.description || combinedText;
      }

      // 只返回描述文本，不添加镜头编号等标识
      let fullText = row.description || '';

      return fullText;
    }).join('\n\n');

    return formattedText;
  };

  /**
   * 打开图像提示词生成器
   * @param {Object} context - 组件上下文
   */
  const openImagePromptGenerator = (context) => {
    console.log('[ImagePromptReasoning] 打开图像提示词生成器');

    // 检查是否有选中的行
    if (!context.selectedRows || context.selectedRows.length === 0) {
      context.showErrorMessage('请先选择需要生成图像提示词的行');
      return;
    }

    // 准备上下文文本
    context.globalReasoningText = getFormattedTextForReasoning(context);

    // 打开抽屉
    context.showImagePromptDrawer = true;
  };

  return {
    // 状态
    reasoningState,

    // 推理方法
    handleInferPrompt,
    handlePreviewPrompt,
    handleConfirmSendPrompt,
    handleCloseTestModal,
    handleApplyCharacterTags,

    // 工具方法
    handleApplyPromptsToRows,
    handleOpenImagePromptSettings,
    handleClearAllKeywords,
    getFormattedTextForReasoning,
    openImagePromptGenerator
  };
}
