/**
 * LLM提供商配置统一导出
 * 提供所有LLM提供商的配置和管理功能
 */

import openrouterConfig from './openrouter.js';
import googleConfig from './google.js';
import openaiConfig from './openai.js';
import anthropicConfig from './anthropic.js';
import localConfig from './local.js';

// 所有提供商配置的映射
export const providerConfigs = {
  openrouter: openrouterConfig,
  google: googleConfig,
  openai: openaiConfig,
  anthropic: anthropicConfig,
  local: localConfig
};

// 提供商列表（用于UI显示）
export const providerList = [
  {
    id: 'openrouter',
    name: 'OpenRouter',
    description: '通过OpenRouter访问多种AI模型',
    icon: 'ri-route-line',
    popular: true
  },
  {
    id: 'google',
    name: 'Google Gemini',
    description: 'Google的Gemini系列大语言模型',
    icon: 'ri-google-line',
    popular: true
  },
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'OpenAI的GPT系列大语言模型',
    icon: 'ri-openai-line',
    popular: true
  },
  {
    id: 'anthropic',
    name: 'Anthropic Claude',
    description: 'Anthropic的Claude系列大语言模型',
    icon: 'ri-robot-line',
    popular: false
  },
  {
    id: 'local',
    name: '本地模型',
    description: '本地部署的大语言模型',
    icon: 'ri-computer-line',
    popular: false
  }
];

/**
 * 获取指定提供商的配置
 * @param {string} providerId - 提供商ID
 * @returns {Object|null} 提供商配置对象
 */
export function getProviderConfig(providerId) {
  return providerConfigs[providerId] || null;
}

/**
 * 获取所有提供商配置
 * @returns {Object} 所有提供商配置的映射
 */
export function getAllProviderConfigs() {
  return providerConfigs;
}

/**
 * 获取提供商列表
 * @param {boolean} popularOnly - 是否只返回热门提供商
 * @returns {Array} 提供商列表
 */
export function getProviderList(popularOnly = false) {
  if (popularOnly) {
    return providerList.filter(provider => provider.popular);
  }
  return providerList;
}

/**
 * 验证提供商配置
 * @param {string} providerId - 提供商ID
 * @param {Object} config - 配置对象
 * @returns {Array} 错误列表，空数组表示验证通过
 */
export function validateProviderConfig(providerId, config) {
  const providerConfig = getProviderConfig(providerId);
  if (!providerConfig) {
    return [`不支持的提供商: ${providerId}`];
  }
  
  if (typeof providerConfig.validateConfig === 'function') {
    return providerConfig.validateConfig(config);
  }
  
  return [];
}

/**
 * 获取提供商的默认配置
 * @param {string} providerId - 提供商ID
 * @returns {Object|null} 默认配置对象
 */
export function getProviderDefaults(providerId) {
  const providerConfig = getProviderConfig(providerId);
  return providerConfig ? providerConfig.defaults : null;
}

/**
 * 获取提供商支持的模型列表
 * @param {string} providerId - 提供商ID
 * @returns {Array} 模型列表
 */
export function getProviderModels(providerId) {
  const providerConfig = getProviderConfig(providerId);
  return providerConfig ? providerConfig.models : [];
}

/**
 * 根据用户设置获取API密钥
 * @param {Object} userSettings - 用户设置对象
 * @param {string} providerId - 提供商ID
 * @returns {string} API密钥
 */
export function getApiKeyForProvider(userSettings, providerId) {
  if (!userSettings) {
    console.error("提供的用户设置无效");
    return '';
  }

  let apiKey = '';
  switch(providerId) {
    case 'openrouter':
      apiKey = userSettings.openrouterApiKey;
      break;
    case 'google':
      apiKey = userSettings.googleApiKey;
      break;
    case 'openai':
      apiKey = userSettings.openaiApiKey;
      break;
    case 'anthropic':
      apiKey = userSettings.anthropicApiKey;
      break;
    case 'local':
      apiKey = userSettings.localApiKey;
      break;
    default:
      console.warn(`未知的提供商: ${providerId}`);
      apiKey = '';
  }

  if (!apiKey) {
    console.warn(`未找到${providerId}提供商的API密钥`);
  } else {
    console.log(`成功获取${providerId}提供商的API密钥`);
  }

  return apiKey;
}

/**
 * 构建API请求配置
 * @param {string} providerId - 提供商ID
 * @param {Object} prompt - 提示词对象或字符串
 * @param {Object} settings - 设置对象
 * @returns {Object} API请求配置
 */
export function buildApiRequest(providerId, prompt, settings) {
  const providerConfig = getProviderConfig(providerId);
  if (!providerConfig) {
    throw new Error(`不支持的提供商: ${providerId}`);
  }
  
  // 使用提供商特定的格式化函数
  const requestBody = providerConfig.formatRequest(prompt, settings);
  
  // 构建headers
  const headers = { ...providerConfig.api.headers };
  
  // 添加认证信息
  if (providerConfig.api.authHeader && settings.apiKey) {
    headers[providerConfig.api.authHeader] = `${providerConfig.api.authPrefix}${settings.apiKey}`;
  }
  
  // 构建URL
  let url;
  if (providerId === 'google') {
    url = providerConfig.buildApiUrl(settings.model, settings.apiKey);
  } else if (providerId === 'local') {
    url = providerConfig.buildApiUrl(settings.localUrl);
  } else {
    url = providerConfig.buildApiUrl();
  }
  
  return {
    url,
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
    timeout: providerConfig.defaults.timeout
  };
}

/**
 * 解析API响应
 * @param {string} providerId - 提供商ID
 * @param {Object} responseData - API响应数据
 * @returns {Object} 解析后的响应对象
 */
export function parseApiResponse(providerId, responseData) {
  const providerConfig = getProviderConfig(providerId);
  if (!providerConfig) {
    throw new Error(`不支持的提供商: ${providerId}`);
  }
  
  return providerConfig.parseResponse(responseData);
}

/**
 * 处理API错误
 * @param {string} providerId - 提供商ID
 * @param {Error} error - 错误对象
 * @param {Response} response - HTTP响应对象
 * @returns {Error} 处理后的错误对象
 */
export function handleApiError(providerId, error, response) {
  const providerConfig = getProviderConfig(providerId);
  if (!providerConfig) {
    return new Error(`不支持的提供商: ${providerId}`);
  }
  
  return providerConfig.handleError(error, response);
}

// 默认导出
export default {
  providerConfigs,
  providerList,
  getProviderConfig,
  getAllProviderConfigs,
  getProviderList,
  validateProviderConfig,
  getProviderDefaults,
  getProviderModels,
  getApiKeyForProvider,
  buildApiRequest,
  parseApiResponse,
  handleApiError
};
