/**
 * useGroupingOperations.js
 * 处理AI分组、导入导出等高级分组操作
 */
import { ref } from 'vue';
import llmService from '../services/llmService';
import { validateGroupingContent, validateImportedText } from '../utils/contentValidation';

export function useGroupingOperations() {
  // 状态
  const isProcessingAI = ref(false);
  const isExporting = ref(false);
  const aiError = ref('');
  const exportError = ref('');
  const aiGroupingResult = ref([]);

  /**
   * 执行AI分组
   * @param {Array} subtitleItems 字幕项数组
   * @param {Object} options 选项（maxGroupSize等）
   * @returns {Promise<Object>} 分组结果
   */
  async function performAIGrouping(subtitleItems, options = {}) {
    if (!subtitleItems || subtitleItems.length === 0) {
      aiError.value = '没有可用的字幕内容';
      return { success: false, error: aiError.value };
    }

    isProcessingAI.value = true;
    aiError.value = '';
    aiGroupingResult.value = [];

    try {
      // 准备要处理的字幕数据
      const subtitleData = subtitleItems.map(item => ({
        id: item.id,
        content: item.content || item.description,
        originalIndex: item.originalIndex,
        isMerged: item.isMerged
      }));

      console.log('准备处理字幕数据:', subtitleData.length, '条');

      // 获取用户设置的LLM
      const userLLMSettings = await getUserLLMSettings();

      const llmOptions = {
        forceUseApi: true,
        userSettings: userLLMSettings,
        maxGroupSize: options.maxGroupSize || 3,
        groupingDirection: options.groupingDirection || 'auto'
      };

      let response;

      // 判断是使用默认提示词还是自定义提示词
      if (options.useCustomPrompt && options.customPrompt) {
        // 使用自定义提示词
        console.log('使用自定义提示词');

        // 准备提示词
        const textContent = subtitleData.map(item => item.content).join('\n');
        const numberOfLines = subtitleData.length;

        let primaryLengthTarget = "2 or 3 subtitles";
        const userMaxGroupSize = parseInt(options.maxGroupSize || 3);
        if (userMaxGroupSize > 0) {
          primaryLengthTarget = `approximately ${userMaxGroupSize} subtitles`;
          if (userMaxGroupSize === 1) {
            primaryLengthTarget = "1 subtitle (but only for exceptionally impactful lines)";
          }
        }

        // 替换模板变量
        let finalPrompt = options.customPrompt
          .replace(/\{primaryLengthTarget\}/g, primaryLengthTarget)
          .replace(/\{numberOfLines\}/g, numberOfLines)
          .replace(/\{textContent\}/g, textContent);

        response = await llmService.performCustomPrompting(subtitleData, finalPrompt, llmOptions);
      } else {
        // 使用默认提示词
        console.log('使用默认提示词');
        response = await llmService.performSemanticGrouping(subtitleData, llmOptions);
      }

      if (response && response.groupingResult && response.groupingResult.length > 0) {
        aiGroupingResult.value = response.groupingResult;
        return {
          success: true,
          result: response.groupingResult,
          originalResponse: response.originalResponse
        };
      } else {
        aiError.value = 'AI未能生成分组方案，请稍后重试或检查字幕内容。';
        return { success: false, error: aiError.value };
      }
    } catch (err) {
      console.error('AI分组处理失败:', err);
      aiError.value = err.message || '处理请求时发生错误';
      return { success: false, error: aiError.value };
    } finally {
      isProcessingAI.value = false;
    }
  }

  /**
   * 应用AI分组结果
   * @param {Array} subtitleItems 字幕项数组
   * @param {Array} groupingResult 分组结果
   * @returns {Object} 处理结果
   */
  function applyAIGroupingResult(subtitleItems, groupingResult) {
    try {
      // 首先验证分组内容是否与原始字幕匹配
      const validation = validateGroupingContent(subtitleItems, groupingResult, {
        similarityThreshold: 0.8,
        allowPartialMatch: true,
        strictMode: false
      });

      // 只要有错误或警告都禁止合并
      if (!validation.isValid || (validation.warnings && validation.warnings.length > 0)) {
        console.error('AI分组内容验证失败或有警告:', [...(validation.errors || []), ...(validation.warnings || [])]);
        return {
          success: false,
          error: `AI分组内容存在问题：${[...(validation.errors || []), ...(validation.warnings || [])].join('; ')}`,
          validationDetails: validation
        };
      }

      // 应用分组（只进行合并/分割操作，不修改内容）
      const newSubtitleItems = [];

      groupingResult.forEach(group => {
        if (!Array.isArray(group) || group.length === 0) {
          return; // 跳过无效分组
        }

        if (group.length === 1) {
          // 单项分组，查找原始匹配项
          const matchedItem = findItemByContentStrict(subtitleItems, group[0]);
          if (matchedItem) {
            newSubtitleItems.push(JSON.parse(JSON.stringify(matchedItem)));
          }
        } else {
          // 多项分组，创建合并项（使用原始内容）
          const matchedItems = group
            .map(sentence => findItemByContentStrict(subtitleItems, sentence))
            .filter(Boolean);

          if (matchedItems.length > 0) {
            const mergedItem = createMergedItemSafe(matchedItems);
            newSubtitleItems.push(mergedItem);
          }
        }
      });

      // 重新编号
      newSubtitleItems.forEach((item, index) => {
        item.id = index + 1;
      });

      return {
        success: true,
        subtitleItems: newSubtitleItems,
        message: `已安全应用AI分组，生成 ${newSubtitleItems.length} 个项目`,
        validationDetails: validation
      };
    } catch (error) {
      console.error('应用AI分组结果时出错:', error);
      return { success: false, error: `应用分组失败: ${error.message}` };
    }
  }

  /**
   * 导出字幕文本
   * @param {Array} subtitleItems 字幕项数组
   * @returns {Promise<Object>} 导出结果
   */
  async function exportSubtitleText(subtitleItems) {
    console.log('开始导出字幕文本，接收到的数据类型:', typeof subtitleItems);

    if (!subtitleItems) {
      exportError.value = '没有可用的字幕内容';
      console.error('导出字幕文本失败: 字幕项为空');
      return { success: false, error: exportError.value };
    }

    if (!Array.isArray(subtitleItems)) {
      exportError.value = '字幕数据格式不正确';
      console.error('导出字幕文本失败: 字幕项不是数组', subtitleItems);
      return { success: false, error: exportError.value };
    }

    if (subtitleItems.length === 0) {
      exportError.value = '没有可用的字幕内容';
      console.error('导出字幕文本失败: 字幕项数组为空');
      return { success: false, error: exportError.value };
    }

    isExporting.value = true;
    exportError.value = '';

    try {
      // 收集所有文本并格式化
      console.log('开始收集格式化文本项...');
      const formattedItems = collectFormattedItems(subtitleItems);
      console.log(`收集到 ${formattedItems.length} 条格式化文本项`);

      if (formattedItems.length === 0) {
        exportError.value = '无法从字幕中提取有效内容';
        console.error('导出字幕文本失败: 无法提取有效内容');
        return { success: false, error: exportError.value };
      }

      const formattedText = formattedItems.join('\n');

      // 复制到剪贴板
      try {
        await navigator.clipboard.writeText(formattedText);
        console.log('已复制所有文本到剪贴板');
      } catch (clipboardError) {
        console.warn('复制到剪贴板失败:', clipboardError);
        // 继续执行，不中断流程
      }

      // 尝试获取项目和章节信息
      const { projectTitle, chapterTitle } = getProjectAndChapterInfo();
      console.log('获取到项目和章节信息:', { projectTitle, chapterTitle });

      // 如果有项目和章节信息，尝试保存到文件
      if (projectTitle && chapterTitle) {
        console.log('尝试保存文本到文件...');
        const saveResult = await saveTextToFile(formattedText, projectTitle, chapterTitle, formattedItems.length);
        if (saveResult.success) {
          console.log('保存文件成功:', saveResult.path);
          return {
            success: true,
            message: `已成功导出 ${formattedItems.length} 条文本！\n\n文件已保存到: ${saveResult.path}\n同时已复制到剪贴板`,
            path: saveResult.path,
            itemCount: formattedItems.length
          };
        } else {
          console.warn('保存文件失败:', saveResult.error);
          return {
            success: true,
            message: `已导出 ${formattedItems.length} 条文本！\n\n保存文件失败: ${saveResult.error}\n但内容已复制到剪贴板`,
            itemCount: formattedItems.length,
            clipboardOnly: true
          };
        }
      } else {
        console.log('未获取到项目和章节信息，仅复制到剪贴板');
        return {
          success: true,
          message: `已导出 ${formattedItems.length} 条文本！\n\n内容已复制到剪贴板`,
          itemCount: formattedItems.length,
          clipboardOnly: true
        };
      }
    } catch (error) {
      console.error('导出文本过程出错:', error);
      exportError.value = `导出文本时出错: ${error.message}`;
      return { success: false, error: exportError.value };
    } finally {
      isExporting.value = false;
    }
  }

  /**
   * 解析导入的内容（用于AI分组的原始文本格式）
   * @param {String} content 导入的分组文本
   * @param {Array} subtitleItems 当前字幕项数组
   * @returns {Object} 解析和合并结果
   */
  function parseImportedContent(content, subtitleItems) {
    console.log('parseImportedContent - 开始解析AI分组原始文本');

    // 从 subtitleItems 中提取原始数据
    const rawSrtRows = subtitleItems.map(item => ({
      index: item.rowIndex || item.id,
      originalIndex: item.originalIndex,
      description: item.content,
      startTime: item.startTime,
      endTime: item.endTime,
      duration: item.duration,
      tags: item.originalRow?.tags || [],
      keywords: item.originalRow?.keywords || ''
    }));

    console.log('parseImportedContent - 提取的原始数据行数:', rawSrtRows.length);

    // 使用相同的导入分组逻辑
    return importGroupingByOrder(content, rawSrtRows);
  }

  /**
   * 导入分组文本，严格顺序映射原始字幕，合并/分拆结构与 ContentCreationStudio.vue 完全一致
   * @param {String} content 导入的分组文本
   * @param {Array} rawSrtRows 原始字幕数组（顺序不变）
   * @returns {Object} 解析和合并结果
   */
  function importGroupingByOrder(content, rawSrtRows) {
    // 1. 校验文本内容
    const validation = validateImportedText(content, rawSrtRows);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('\n'),
        validationDetails: validation
      };
    }
    // 2. 拆分文本为分组（每组为一行或多行）
    const groupRegex = /\d+\.\s*([^\n]+)/g;
    const parsedGroups = [];
    let match;
    while ((match = groupRegex.exec(content)) !== null) {
      const groupContent = match[1].trim();
      const sentences = groupContent.split(/,|\n/).map(s => s.trim()).filter(s => s.length > 0);
      if (sentences.length > 0) parsedGroups.push(sentences);
    }
    // 3. 顺序映射原始字幕，生成 rows，严格模拟 ContentCreationStudio.vue 手动合并结构
    const rows = [];
    let srtIndex = 0;
    for (let i = 0; i < parsedGroups.length; i++) {
      const group = parsedGroups[i];
      const mergedRows = [];
      let startTime = null, endTime = null;

      // 安全检查：确保分组中的子句数量不超过剩余的原始字幕数量
      if (srtIndex + group.length > rawSrtRows.length) {
         console.error(`导入分组错误: 分组 ${i + 1} (${group.length} 项) 超出原始字幕范围 (剩余 ${rawSrtRows.length - srtIndex} 项). 这应该在校验阶段被捕获.`);
         // 由于校验已通过，理论上不会发生此情况，此处作为额外的日志和防御性编程
         break; // 遇到错误就停止处理后续分组
      }

      for (let j = 0; j < group.length; j++) {
        const orig = rawSrtRows[srtIndex];

        // orig 理论上应该存在，因为上面做了长度检查
        if (orig) {
          // 构建 mergedRows 子项结构，包含所有 ContentCreationStudio.vue 可能需要的字段
          mergedRows.push({
            index: orig.index, // 原始SRT中的索引
            originalIndex: orig.originalIndex, // 原始SRT中的originalIndex
            description: orig.description, // 原始文本
            content: orig.content || orig.description || '', // 确保 content 字段存在
            startTime: orig.startTime, // 原始时间
            endTime: orig.endTime,     // 原始时间
            duration: orig.duration,   // 原始时长
            isMerged: false,           // 子项永远是未合并状态
            tags: orig.tags || [],     // 保留 tags 字段，从原始数据获取或给默认空数组
            keywords: orig.keywords || '', // 保留 keywords 字段，从原始数据获取或给默认空字符串
            imageSrc: orig.imageSrc || 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', // 默认图片
            imageAlt: orig.imageAlt || '', // 默认空
            isImageLocked: orig.isImageLocked || false, // 默认 false
            thumbnails: orig.thumbnails || [] // 默认空数组
          });

          // 计算合并项的时间区间（取所有子项的最早开始时间和最晚结束时间）
          if (startTime === null || (orig.startTime !== undefined && orig.startTime < startTime)) startTime = orig.startTime;
          if (endTime === null || (orig.endTime !== undefined && orig.endTime > endTime)) endTime = orig.endTime;

        } else {
           // 如果原始字幕不够了，虽然有前置检查，这里还是加一个日志
           console.error(`导入分组错误: 原始字幕数量不足，无法匹配分组 ${i + 1} 的第 ${j + 1} 项. srtIndex: ${srtIndex}, rawSrtRows.length: ${rawSrtRows.length}`);
           break; // 遇到问题停止当前分组处理
        }

        srtIndex++; // 移动到下一个原始字幕
      }

      // 计算合并项的 duration
      const duration = (endTime !== null && startTime !== null) ? (endTime - startTime) : 0;

      // 构建主合并项结构，严格模拟 ContentCreationStudio.vue 结构
      const isCurrentGroupMerged = mergedRows.length > 1; // 判断当前分组是否为合并项
      const firstOriginalItem = mergedRows.length > 0 ? mergedRows[0] : null; // 获取第一个子项（用于构建 originalState 和主索引）

      rows.push({
        // ContentCreationStudio.vue 需要的顶级字段
        isSelected: false, // 默认不选中
        description: mergedRows.map(r => r.description).join('\n'), // 合并文本，用换行符连接
        content: mergedRows.map(r => r.description).join('\n'), // content 和 description 一致
        keywords: firstOriginalItem?.keywords || '', // 取第一个子项的 keywords 或默认空
        imageSrc: firstOriginalItem?.imageSrc || 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', // 取第一个子项的 imageSrc 或默认
        imageAlt: firstOriginalItem?.imageAlt || '', // 取第一个子项的 imageAlt 或默认空
        isImageLocked: firstOriginalItem?.isImageLocked || false, // 取第一个子项的 isImageLocked 或默认 false
        thumbnails: firstOriginalItem?.thumbnails || [], // 取第一个子项的 thumbnails 或默认空数组

        // 时间和索引信息
        startTime: startTime ?? 0, // 合并后的开始时间，确保有默认值
        endTime: endTime ?? 0,     // 合并后的结束时间，确保有默认值
        duration: duration,        // 合并后的时长
        index: rows.length + 1, // 在新列表中的索引，从1开始
        originalIndex: firstOriginalItem?.originalIndex ?? 0, // 取第一个子项的 originalIndex

        // 🔥 统一数据结构：不再创建 originalState

        // 合并状态和 mergedRows
        isMerged: isCurrentGroupMerged, // 标记是否为合并项
        mergedRows: mergedRows, // 🔥 统一数据结构：所有行都有 mergedRows，非合并项包含自身

        tags: firstOriginalItem?.tags || [], // 取第一个子项的 tags 或默认空数组

        // 不包含其他如 rowId 等内部用于渲染或管理的字段，只保留 ContentCreationStudio.vue project-data.json 结构中的字段
      });
    }

    // 校验通过的总数检查
    if (srtIndex !== rawSrtRows.length) {
        console.warn(`导入分组警告: 处理完所有分组后，原始字幕索引(${srtIndex})与总数(${rawSrtRows.length})不匹配。可能存在未使用的原始字幕。这应该由 validateImportedText 报告警告.`);
        // 这个问题应该由 validateImportedText 报告警告
    }

    return {
      success: true,
      rows,
      message: '导入分组并顺序合并成功',
      validationDetails: validation
    };
  }

  // 辅助函数

  /**
   * 获取用户设置中的LLM配置
   */
  async function getUserLLMSettings() {
    try {
      console.log('开始获取用户LLM设置...');

      // 1. 首先从服务器API获取，这应该是最准确的来源
      try {
        console.log('尝试从服务器API获取设置...');
        const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.settings && result.settings.llm) {
            console.log('从服务器API获取到用户LLM设置');
            return result.settings.llm;
          }
        }
      } catch (err) {
        console.warn('从服务器API获取设置失败:', err);
      }

      // 2. 尝试从localStorage读取设置
      const settingsStr = localStorage.getItem('usersettings');
      if (settingsStr) {
        const settings = JSON.parse(settingsStr);
        if (settings && settings.llm) {
          console.log('从localStorage获取到用户LLM设置');
          return settings.llm;
        }
      }

      // 如果都失败，返回null
      console.warn('未能获取有效的用户LLM设置，将使用默认设置');
      return null;
    } catch (err) {
      console.error('获取用户LLM设置失败:', err);
      return null;
    }
  }

  /**
   * 根据内容查找字幕项（原有的宽松匹配）
   */
  function findItemByContent(subtitleItems, content) {
    return subtitleItems.find(item =>
      item.content === content ||
      item.description === content
    );
  }

  /**
   * 根据内容严格查找字幕项（用于安全分组）
   */
  function findItemByContentStrict(subtitleItems, content) {
    // 首先尝试完全匹配
    for (const item of subtitleItems) {
      const itemContent = item.content || item.description || '';
      if (itemContent === content) {
        return item;
      }
    }

    // 如果没有完全匹配，尝试宽松匹配
    return findItemByContent(subtitleItems, content);
  }

  /**
   * 创建安全的合并项（使用统一的 mergedRows 数据结构）
   */
  function createMergedItemSafe(items) {
    const containerItem = items[0];
    const mergedRows = items.map(item => ({
      originalIndex: item.originalIndex,
      index: item.index,
      description: item.content || item.description || '',
      startTime: item.startTime,
      endTime: item.endTime,
      duration: item.duration,
      isMerged: false
    }));
    // 计算时间区间
    const startTime = Math.min(...mergedRows.map(r => r.startTime || 0));
    const endTime = Math.max(...mergedRows.map(r => r.endTime || 0));
    const duration = endTime - startTime;
    // 合并项
    const mergedItem = {
      id: 0,
      content: mergedRows.map(r => r.description).join('\n'),
      isMerged: true,
      originalIndex: containerItem.originalIndex,
      rowIndex: containerItem.index,
      startTime,
      endTime,
      duration,
      originalRow: {
        description: mergedRows.map(r => r.description).join('\n'),
        isMerged: true,
        mergedRows: mergedRows
      }
    };
    return mergedItem;
  }

  /**
   * 收集格式化的文本项目
   */
  function collectFormattedItems(subtitleItems) {
    const items = [];

    // 检查是否是特殊格式
    if (!Array.isArray(subtitleItems)) {
      console.error('收集格式化文本：无效的字幕项数组', subtitleItems);
      return items;
    }

    // 处理每个字幕项
    subtitleItems.forEach((item, index) => {
      // 检查是否有有效内容
      if (!item) {
        console.warn(`收集格式化文本：第 ${index + 1} 项为空`);
        return;
      }

      // 获取内容
      let content = '';
      if (item.content) {
        content = item.content;
      } else if (item.description) {
        content = item.description;
      } else if (item.originalRow && item.originalRow.description) {
        content = item.originalRow.description;
      }

      if (content) {
        // 处理合并句子，将换行符替换为逗号
        const formattedContent = content.replace(/\n/g, ',');
        items.push(`${index + 1}. ${formattedContent}`);
      }
    });

    return items;
  }

  /**
   * 获取项目名和章节名
   */
  function getProjectAndChapterInfo() {
    try {
      // 尝试从URL获取项目和章节信息
      const urlParams = new URLSearchParams(window.location.search);
      let projectTitle = urlParams.get('project') || '';
      let chapterTitle = urlParams.get('chapter') || '';

      if (!projectTitle || !chapterTitle) {
        // 如果URL中没有，尝试从localStorage获取
        const currentProject = localStorage.getItem('currentProject');
        const currentChapter = localStorage.getItem('currentChapter');

        if (currentProject) projectTitle = currentProject;
        if (currentChapter) chapterTitle = currentChapter;
      }

      return { projectTitle, chapterTitle };
    } catch (error) {
      console.error('获取项目和章节信息失败:', error);
      return { projectTitle: '', chapterTitle: '' };
    }
  }

  /**
   * 保存文本到文件
   */
  async function saveTextToFile(text, projectTitle, chapterTitle, itemCount) {
    try {
      // 构建保存路径
      const savePath = `draft/${projectTitle}/${chapterTitle}/exported-text.txt`;
      console.log('准备保存文本到文件:', savePath);

      // 调用API保存文件
      const response = await fetch('/api/local/save-text-file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath: savePath, content: text })
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, path: savePath, itemCount };
      } else {
        console.error('保存文件失败:', result.error);
        return { success: false, error: result.error, itemCount };
      }
    } catch (error) {
      console.error('保存文件过程中出错:', error);
      return { success: false, error: error.message, itemCount };
    }
  }

  return {
    // 状态
    isProcessingAI,
    isExporting,
    aiError,
    exportError,
    aiGroupingResult,

    // 方法
    performAIGrouping,
    applyAIGroupingResult,
    exportSubtitleText,
    importGroupingByOrder,
    parseImportedContent
  };
}

