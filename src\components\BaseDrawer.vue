<template>
  <transition name="drawer-slide">
    <div
      v-if="show"
      class="drawer-mask"
      @click.self="closeDrawer"
    >
      <div
        class="base-drawer"
        :style="{ width: drawerWidth }"
      >
        <button
          class="drawer-close-btn"
          @click="closeDrawer"
        >
          <i class="ri-arrow-left-line" />
        </button>
        <div class="drawer-content">
          <slot />
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'BaseDrawer',
  props: {
    show: { type: Boolean, required: true },
    drawerWidth: {
      type: String,
      default: '650px' // Default width if not provided. Changed from 400px
    }
  },
  emits: ['update:show'],
  methods: {
    closeDrawer() {
      console.log('[BaseDrawer] closeDrawer called');
      this.$emit('update:show', false);
    }
  }
}
</script>

<style scoped>
.drawer-slide-enter-active, .drawer-slide-leave-active {
  transition: transform 0.35s cubic-bezier(.4,2,.3,1), opacity 0.25s;
}
.drawer-slide-enter-from, .drawer-slide-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
.drawer-slide-enter-to, .drawer-slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}
.drawer-mask {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.18);
  z-index: 1999;
  display: flex;
  align-items: stretch;
}
.base-drawer {
  position: fixed;
  top: 0;
  left: 0;
  /* width: 650px; */ /* Controlled by prop now */
  height: 100vh;
  background: #232136;
  box-shadow: -4px 0 32px #0004;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  padding: 2.5rem 0 1.5rem 0;
  z-index: 2000;
  border-top-right-radius: 18px;
  border-bottom-right-radius: 18px;
  /* 调试边框移除 */
  /* border: 2px solid yellow !important; */
}

/* 添加新的包装器样式，确保内容铺满宽度 */
.drawer-content {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  /* 调试边框移除 */
  /* border: 2px solid purple !important; */
}

.drawer-close-btn {
  align-self: flex-end;
  margin-bottom: 2rem;
  background: none;
  border: none;
  color: #b5bfe2;
  font-size: 1.3rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: color 0.18s;
}
.drawer-close-btn:hover {
  color: #a23a5a;
}
</style> 