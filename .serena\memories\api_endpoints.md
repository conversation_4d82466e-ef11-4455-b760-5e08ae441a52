# API端点参考

## 🌐 后端API端点

### 健康检查
- `GET /api/health` - 服务器健康状态检查

### 本地通信API (`/api/local`)
- `GET /load-user-settings` - 加载用户设置
- `POST /save-user-settings` - 保存用户设置
- `POST /save-project-data` - 保存项目数据
- `POST /save-project-file` - 保存项目文件
- `GET /read-file` - 读取文件
- `POST /upload-file` - 上传文件
- `GET /list-files` - 列出文件
- `GET /list-folders` - 列出文件夹
- `POST /create-folder` - 创建文件夹
- `POST /rename-folder` - 重命名文件夹
- `POST /delete-folder` - 删除文件夹
- `POST /delete-file` - 删除文件

### LLM相关API
- `POST /analyze-text` - 文本分析
- `GET /prompt/default` - 获取默认提示词
- `GET /prompt/user` - 获取用户提示词
- `POST /prompt/user` - 保存用户提示词

### ComfyUI集成API (`/api/local/comfyui`)
- `POST /test-connection` - 测试ComfyUI连接
- `POST /prompt` - 发送生成提示
- `GET /queue` - 获取队列状态
- `GET /history` - 获取历史记录
- `GET /history/:prompt_id` - 获取特定提示历史
- `POST /interrupt` - 中断生成
- `GET /models` - 获取可用模型
- `GET /view` - 查看生成的图像
- `POST /proxy` - ComfyUI代理请求

### 图像管理API
- `GET /get-image` - 获取图像
- `POST /save-generated-image` - 保存生成的图像
- `POST /move-comfyui-image` - 移动ComfyUI图像
- `DELETE /delete-image` - 删除图像

## 🔌 WebSocket连接
- WebSocket服务器运行在与HTTP服务器相同的端口
- 用于实时通信和状态更新
- 支持ComfyUI进度监控

## 📡 代理配置
- ComfyUI API代理：`/api/local/comfyui-proxy/*`
- 支持所有HTTP方法（GET、POST、PUT、DELETE等）
- 自动处理CORS和WebSocket升级