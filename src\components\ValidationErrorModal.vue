<template>
  <BaseModal
    :show="show"
    @update:show="$emit('update:show', false)"
    title="内容验证失败"
  >
    <div class="validation-error-modal">
      <!-- 错误概述 -->
      <div class="error-summary">
        <div class="error-icon">
          <i class="ri-error-warning-fill" />
        </div>
        <div class="error-message">
          <h3>{{ errorTitle }}</h3>
          <p>{{ errorMessage }}</p>
        </div>
      </div>

      <!-- 详细错误信息 -->
      <div
        class="error-details"
        v-if="validationDetails"
      >
        <div class="details-section">
          <h4>验证统计</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">匹配项目:</span>
              <span class="stat-value success">{{ validationDetails.matchedCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">未匹配项目:</span>
              <span class="stat-value error">{{ validationDetails.unmatchedCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">错误数量:</span>
              <span class="stat-value error">{{ (validationDetails.errors || []).length }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">警告数量:</span>
              <span class="stat-value warning">{{ (validationDetails.warnings || []).length }}</span>
            </div>
          </div>
        </div>

        <!-- 错误列表 -->
        <div
          class="details-section"
          v-if="validationDetails.errors && validationDetails.errors.length > 0"
        >
          <h4>具体错误</h4>
          <div class="error-list">
            <div 
              v-for="(error, index) in validationDetails.errors.slice(0, 5)" 
              :key="index"
              class="error-item"
            >
              <i class="ri-close-circle-fill error-icon" />
              <span>{{ error }}</span>
            </div>
            <div
              v-if="validationDetails.errors.length > 5"
              class="more-errors"
            >
              还有 {{ validationDetails.errors.length - 5 }} 个错误...
            </div>
          </div>
        </div>

        <!-- 对比详情区块 -->
        <div
          class="details-section"
          v-if="validationDetails.details && validationDetails.details.length > 0"
        >
          <h4>对比详情</h4>
          <div class="compare-list">
            <div
              v-for="(group, gIdx) in validationDetails.details"
              :key="gIdx"
              class="compare-group"
            >
              <div
                v-for="(sentence, sIdx) in group.sentences"
                :key="sIdx"
                class="compare-row"
                :class="{ unmatched: !sentence.matched }"
              >
                <div class="compare-label">
                  分组{{ gIdx + 1 }}：
                </div>
                <div class="compare-content">
                  <div><b>导入内容：</b><span>{{ sentence.sentence }}</span></div>
                  <div><b>原始字幕：</b><span>{{ sentence.originalContent || '无匹配' }}</span></div>
                  <div><b>相似度：</b><span>{{ (sentence.similarity * 100).toFixed(1) }}%</span></div>
                  <div><b>比对结果：</b><span :style="{color: sentence.matched ? '#9ccfd8' : '#eb6f92'}">{{ sentence.matched ? '匹配' : '未匹配' }}</span></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 警告列表 -->
        <div
          class="details-section"
          v-if="validationDetails.warnings && validationDetails.warnings.length > 0"
        >
          <h4>警告信息</h4>
          <div class="warning-list">
            <div 
              v-for="(warning, index) in validationDetails.warnings.slice(0, 3)" 
              :key="index"
              class="warning-item"
            >
              <i class="ri-alert-fill warning-icon" />
              <span>{{ warning }}</span>
            </div>
            <div
              v-if="validationDetails.warnings.length > 3"
              class="more-warnings"
            >
              还有 {{ validationDetails.warnings.length - 3 }} 个警告...
            </div>
          </div>
        </div>
      </div>

      <!-- 解决方案建议 -->
      <!-- 已删除 -->
    </div>

    <template #footer>
      <div class="modal-actions">
        <button
          class="btn btn-primary"
          @click="$emit('update:show', false)"
        >
          我知道了
        </button>
      </div>
    </template>
  </BaseModal>
</template>

<script>
import BaseModal from './BaseModal.vue';

export default {
  name: 'ValidationErrorModal',
  components: {
    BaseModal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    errorType: {
      type: String,
      default: 'general', // 'import', 'ai', 'general'
      validator: value => ['import', 'ai', 'general'].includes(value)
    },
    errorTitle: {
      type: String,
      default: '验证失败'
    },
    errorMessage: {
      type: String,
      default: '内容验证未通过，无法继续处理'
    },
    validationDetails: {
      type: Object,
      default: null
    }
  },
  emits: ['update:show']
};
</script>

<style scoped>
.validation-error-modal {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.error-summary {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: #2a1f3d;
  border-radius: 8px;
  border-left: 4px solid #eb6f92;
}

.error-summary .error-icon {
  color: #eb6f92;
  font-size: 24px;
  margin-top: 2px;
}

.error-message h3 {
  color: #e0def4;
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.error-message p {
  color: #908caa;
  margin: 0;
  line-height: 1.5;
}

.error-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.details-section h4 {
  color: #e0def4;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #232136;
  border-radius: 6px;
}

.stat-label {
  color: #908caa;
  font-size: 13px;
}

.stat-value {
  font-weight: 600;
  font-size: 14px;
}

.stat-value.success {
  color: #9ccfd8;
}

.stat-value.error {
  color: #eb6f92;
}

.stat-value.warning {
  color: #f6c177;
}

.error-list, .warning-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item, .warning-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: #232136;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.error-item {
  border-left: 3px solid #eb6f92;
}

.warning-item {
  border-left: 3px solid #f6c177;
}

.error-item .error-icon {
  color: #eb6f92;
  font-size: 14px;
  margin-top: 1px;
}

.warning-item .warning-icon {
  color: #f6c177;
  font-size: 14px;
  margin-top: 1px;
}

.error-item span, .warning-item span {
  color: #e0def4;
  flex: 1;
}

.more-errors, .more-warnings {
  text-align: center;
  color: #908caa;
  font-size: 12px;
  font-style: italic;
  padding: 8px;
}

.compare-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.compare-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.compare-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: #232136;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.compare-row.unmatched {
  border-left: 3px solid #eb6f92;
}

.compare-label {
  color: #908caa;
  font-weight: 600;
}

.compare-content {
  flex: 1;
}

.compare-content div {
  margin-bottom: 4px;
}

.compare-content b {
  color: #e0def4;
  font-weight: 600;
}

.compare-content span {
  color: #e0def4;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #9ccfd8;
  color: #191724;
}

.btn-primary:hover {
  background: #7fb3b8;
}
</style>
