<template>
  <div class="secondary-navbar">
    <div class="logo-section">
      绘图: {{ drawingCount }}/{{ totalDrawings }}
      <!-- 🔍 调试：直接显示prop值 -->
      <span style="font-size: 10px; color: #666; margin-left: 10px;">
        [{{ batchImageGenerationButtonText }}]
      </span>
    </div>
    <button 
      v-for="tab in tabs" 
      :key="tab.id"
      :class="['tab-button', { active: activeTab === tab.id }]"
      @click="changeTab(tab.id)"
    >
      {{ tab.name }}
    </button>
    
    <div class="right-actions">
      <button
        v-for="action in actions"
        :key="action.id"
        :class="getActionButtonClass(action)"
        :disabled="action.isProcessing && !action.canCancel"
        @click="handleAction(action.id)"
      >
        <div class="flex items-center space-x-2">
          <!-- 加载动画 -->
          <div
            v-if="action.isProcessing"
            class="animate-spin rounded-full h-3 w-3 border-2 border-current border-t-transparent"
          ></div>

          <!-- 按钮文本 -->
          <span>{{ getActionButtonText(action) }}</span>

          <!-- 进度指示 -->
          <span
            v-if="action.isProcessing && action.progress > 0"
            class="text-xs opacity-75"
          >
            {{ Math.round(action.progress) }}%
          </span>
        </div>

        <!-- 进度条 -->
        <div
          v-if="action.isProcessing && action.progress > 0"
          class="progress-bar"
          :style="{ width: action.progress + '%' }"
        ></div>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SecondaryNavbar',
  props: {
    drawingCount: {
      type: Number,
      default: 0
    },
    totalDrawings: {
      type: Number,
      default: 129
    },
    activeTab: {
      type: String,
      default: 'match-tags'
    },
    // 🆕 批量操作状态
    batchInferenceState: {
      type: Object,
      default: () => ({
        isProcessing: false,
        canCancel: false,
        buttonText: '1. 批量推理',
        progress: 0,
        currentTask: null
      })
    },
    // 🎯 批量生图状态
    batchImageGenerationState: {
      type: Object,
      default: () => ({
        isActive: false,
        buttonState: 'idle',
        progress: 0
      })
    },
    batchImageGenerationButtonText: {
      type: String,
      default: '2. 批量生图'
    },

  },
  data() {
    return {
      tabs: [
        { id: 'adjust-shots', name: '调整分镜' },
        { id: 'match-tags', name: '全局推理' },
        { id: 'manage-tags', name: '管理标签' },
        { id: 'generate-intro', name: '待定' }
      ]
    }
  },
  computed: {
    actions() {
      const timestamp = new Date().toISOString();
      console.log('🔍 [SecondaryNavbar] actions计算属性开始:', { timestamp });

      const inferenceState = this.batchInferenceState?.value || this.batchInferenceState || {};

      console.log('🔍 [SecondaryNavbar] props检查:', {
        batchImageGenerationButtonText: this.batchImageGenerationButtonText,
        batchImageGenerationState: this.batchImageGenerationState,
        timestamp
      });

      const actions = [
        {
          id: 'smart-reasoning',
          name: '1. 批量推理',
          isProcessing: inferenceState.isProcessing || false,
          canCancel: inferenceState.canCancel || false,
          progress: inferenceState.progress || 0,
          buttonText: inferenceState.buttonText || '1. 批量推理'
        },
        {
          id: 'generate-images',
          name: '2. 批量生图',
          isProcessing: this.batchImageGenerationState?.isActive || false,
          canCancel: this.batchImageGenerationState?.isActive && this.batchImageGenerationState?.buttonState !== 'cancelling',
          progress: this.batchImageGenerationState?.progress || 0,
          buttonText: this.batchImageGenerationButtonText || '2. 批量生图'
        },
        { id: 'enlarge-images', name: '3. 放大配图' }
      ];

      console.log('🔍 [SecondaryNavbar] actions计算结果:', {
        actions,
        generateImagesAction: actions.find(a => a.id === 'generate-images'),
        timestamp
      });

      return actions;
    }
  },

  watch: {
    batchImageGenerationButtonText: {
      handler(newVal, oldVal) {
        const timestamp = new Date().toISOString();
        console.log('🔄 [SecondaryNavbar] batchImageGenerationButtonText变化:', {
          from: oldVal,
          to: newVal,
          timestamp
        });
      },
      immediate: true
    },

    batchImageGenerationState: {
      handler(newVal, oldVal) {
        const timestamp = new Date().toISOString();
        console.log('🔄 [SecondaryNavbar] batchImageGenerationState变化:', {
          from: oldVal,
          to: newVal,
          timestamp
        });
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {
    // 🔍 添加全局调试函数
    window.debugSecondaryNavbar = () => {
      const timestamp = new Date().toISOString();
      console.log('🔍 [SecondaryNavbar调试] 当前状态:', {
        props: {
          batchImageGenerationButtonText: this.batchImageGenerationButtonText,
          batchImageGenerationState: this.batchImageGenerationState
        },
        computed: {
          actions: this.actions
        },
        generateImagesAction: this.actions.find(a => a.id === 'generate-images'),
        timestamp
      });
    };
    console.log('🔍 [SecondaryNavbar] 全局调试函数已注册: window.debugSecondaryNavbar()');
  },

  methods: {
    changeTab(tabId) {
      this.$emit('tab-change', tabId);
    },
    handleAction(actionId) {
      console.log('🔥 [SecondaryNavbar] 按钮被点击:', actionId);
      console.log('🔥 [SecondaryNavbar] 发送action事件:', actionId);
      this.$emit('action', actionId);
    },

    /**
     * 获取按钮样式类
     */
    getActionButtonClass(action) {
      const baseClass = 'action-btn';
      const classes = [baseClass];

      if (action.isProcessing) {
        classes.push('processing');
        if (action.canCancel) {
          classes.push('cancellable');
        }
      }

      return classes.join(' ');
    },

    getActionButtonText(action) {
      const timestamp = new Date().toISOString();
      console.log('🔍 [SecondaryNavbar] getActionButtonText被调用:', {
        actionId: action.id,
        actionButtonText: action.buttonText,
        actionName: action.name,
        propValue: this.batchImageGenerationButtonText,
        timestamp
      });

      const result = action.buttonText || action.name;

      console.log('🔍 [SecondaryNavbar] getActionButtonText返回:', {
        actionId: action.id,
        result,
        timestamp
      });

      return result;
    },

    /**
     * 更新操作状态
     */
    updateActionState(actionId, state) {
      const action = this.actions.find(a => a.id === actionId);
      if (action) {
        Object.assign(action, state);
      }
    }
  }
}
</script>

<style scoped>
/* 次级导航栏样式 */
.secondary-navbar {
  display: flex;
  align-items: center;
  background-color: #1a1a1a;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #333;
}

.secondary-navbar .logo-section {
  font-size: 0.9rem;
  color: #cccccc;
  margin-right: 1rem;
}

.tab-button {
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  position: relative;
  margin-right: 0.5rem;
  transition: all 0.2s ease;
}

.tab-button.active {
  background-color: #1e40af;
  border: 1px solid #3b82f6;
  color: #ffffff;
  font-weight: 500;
}

.right-actions {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover, .tab-button:hover {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}

/* 处理中状态样式 */
.action-btn.processing {
  background-color: #1e40af !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
  /* 🔧 修复：确保状态变化立即生效 */
  transition: all 0.1s ease !important;
}

.action-btn.processing:hover {
  background-color: #1d4ed8 !important;
  border-color: #2563eb !important;
}

/* 可取消状态样式 */
.action-btn.cancellable {
  background-color: #dc2626 !important;
  border-color: #ef4444 !important;
  /* 🔧 修复：可取消状态优先级最高 */
  transition: all 0.1s ease !important;
}

.action-btn.cancellable:hover {
  background-color: #b91c1c !important;
  border-color: #dc2626 !important;
}

/* 🔧 修复：确保非处理状态能正确恢复 */
.action-btn:not(.processing):not(.cancellable) {
  background-color: #222 !important;
  border-color: #333 !important;
  color: #aaa !important;
  transition: all 0.1s ease !important;
}

/* 禁用状态样式 */
.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 进度条样式 */
.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease;
}

/* Flex布局工具类 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.text-xs {
  font-size: 0.75rem;
}

.opacity-75 {
  opacity: 0.75;
}

/* 旋转动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rounded-full {
  border-radius: 50%;
}

.h-3 {
  height: 0.75rem;
}

.w-3 {
  width: 0.75rem;
}

.border-2 {
  border-width: 2px;
}

.border-current {
  border-color: currentColor;
}

.border-t-transparent {
  border-top-color: transparent;
}
</style> 