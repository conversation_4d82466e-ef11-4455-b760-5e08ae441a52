/**
 * 批量操作管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的批量操作协调和管理功能
 * 
 * 功能：
 * - 批量推理操作协调
 * - 批量取消操作管理
 * - 队列管理操作
 * - 批量操作状态同步
 * 
 * 注意：批量图像生成功能已被移除
 */

import { reactive } from 'vue';

export function useBatchOperationsStudio() {
  // 批量操作状态管理
  const batchState = reactive({
    isProcessing: false,
    currentOperation: null,
    lastOperationResult: null,
    batchMonitoringTimer: null,
    originalSelectedRows: []
  });

  /**
   * 批量推理操作 - 修复版本
   * @param {Object} context - 组件上下文
   */
  const performBatchInference = async (context) => {
    console.log('🧠 [批量推理管理] 开始批量推理操作');

    // 检查是否正在处理中，如果是则执行批量取消操作
    if (context.batchOperationUI.isBatchInferenceProcessing.value) {
      console.log('🧠 [批量推理管理] 检测到正在处理中，执行批量取消操作');
      await performBatchInferenceCancel(context);
      return;
    }

    // 验证选中的行
    const validation = context.batchOperationUI.validateUserSelection(
      context.selectedRows,
      context.showToast,
      'inference'
    );

    if (!validation.isValid) {
      return;
    }

    const selectedRows = validation.validRows;
    batchState.currentOperation = 'inference';

    // 定义推理函数
    const inferenceFunction = async (row, index) => {
      console.log(`🧠 [批量推理管理] 开始处理第 ${index + 1} 行推理`);
      
      // 调用现有的推理方法
      const result = await context.promptReasoningStudio.performSingleInference(context, row);
      
      console.log(`🧠 [批量推理管理] 第 ${index + 1} 行推理完成:`, {
        success: result?.success !== false,
        hasKeywords: !!(result?.keywords),
        keywordsLength: result?.keywords?.length || 0
      });
      
      return result;
    };

    // 定义进度回调
    const onProgress = (progressInfo) => {
      console.log(`🧠 [批量推理管理] 进度更新:`, progressInfo);
      
      // 更新UI进度
      context.batchOperationUI.updateBatchInferenceProgress({
        current: progressInfo.current,
        total: progressInfo.total,
        message: progressInfo.message
      });
    };

    try {
      // 启动批量推理UI状态
      console.log('🧠 [批量推理管理] 启动UI状态，选中行数量:', selectedRows.length);
      context.batchOperationUI.startBatchInference({
        taskInfo: { totalRows: selectedRows.length },
        onStart: () => {
          console.log('🧠 [批量推理管理] UI状态启动回调执行');
          context.showInfoMessage('批量推理', `开始为 ${selectedRows.length} 行生成图像提示词...`);
          syncButtonStates(context); // 同步按钮状态

          // 🔧 强制触发UI更新
          console.log('🔧 [批量推理管理] 强制触发UI更新');
          context.$forceUpdate && context.$forceUpdate();
        }
      });

      // 定义单行完成回调
      const onRowComplete = (row, index, result, error) => {
        if (error) {
          console.error(`🧠 [批量推理管理] 第 ${index + 1} 行推理失败:`, error);
        } else {
          console.log(`🧠 [批量推理管理] 第 ${index + 1} 行推理成功`);
        }

        // 自动取消选中已完成的行
        row.isSelected = false;
      };

      // 执行顺序批量推理
      const result = await context.batchInferenceSequential.startBatchInference(
        selectedRows,
        inferenceFunction,
        onProgress,
        onRowComplete
      );

      // 完成批量推理UI状态
      context.batchOperationUI.completeBatchInference({
        onComplete: () => {
          const successCount = result.processed - result.failed;
          syncButtonStates(context); // 同步按钮状态
          context.showSuccessMessage(
            '批量推理完成',
            `成功为 ${successCount}/${result.total} 行生成图像提示词`
          );
        }
      });

      // 保存项目数据
      context.debouncedSaveProject();
      batchState.lastOperationResult = result;

    } catch (error) {
      console.error('🧠 [批量推理管理] 批量推理失败:', error);
      
      // 重置UI状态
      context.batchOperationUI.completeBatchInference({ error: true });
      syncButtonStates(context);
      
      context.showErrorMessage('批量推理失败', error.message);
    } finally {
      batchState.currentOperation = null;
    }
  };

  /**
   * 批量推理取消操作
   * @param {Object} context - 组件上下文
   */
  const performBatchInferenceCancel = async (context) => {
    console.log('🚫 [批量推理管理] 开始批量推理取消操作');

    try {
      // 取消批量推理
      await context.batchInferenceSequential.cancelBatchInference();
      
      // 完成批量推理UI状态（取消状态）
      context.batchOperationUI.completeBatchInference({ cancelled: true });
      syncButtonStates(context);
      
      context.showInfoMessage('批量推理已取消', '批量推理操作已被用户取消');
      
    } catch (error) {
      console.error('🚫 [批量推理管理] 批量取消失败:', error);
      context.showErrorMessage('批量推理取消失败', error.message);
    }
  };

  /**
   * 批量取消操作（通用）
   * @param {Object} context - 组件上下文
   */
  const performBatchCancel = async (context) => {
    console.log('🚫 [批量操作管理] 开始批量取消操作');

    // 根据当前操作类型执行相应的取消操作
    if (batchState.currentOperation === 'inference') {
      await performBatchInferenceCancel(context);
    } else {
      console.log('🚫 [批量操作管理] 没有正在进行的批量操作需要取消');
    }
  };

  /**
   * 处理取消排队
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleCancelQueue = async (context, index) => {
    console.log(`🚫 [批量操作管理] 取消第 ${index + 1} 行的排队任务`);

    try {
      // 调用图像生成管理器的取消生成方法（包含取消排队逻辑）
      // 注意：handleCancelGeneration 需要 context 和 index 两个参数
      await context.imageGenerationStudio.handleCancelGeneration(context, index);

      // 同步按钮状态
      syncButtonStates(context);

    } catch (error) {
      console.error(`🚫 [批量操作管理] 取消第 ${index + 1} 行排队失败:`, error);
      // 使用正确的错误消息显示方法，修复参数顺序：showErrorMessage(message, title)
      if (context.showErrorMessage) {
        context.showErrorMessage(error.message, '取消排队失败');
      } else {
        console.error('🚫 [批量操作管理] showErrorMessage 方法不存在');
      }
    }
  };

  /**
   * 处理清空队列
   * @param {Object} context - 组件上下文
   */
  const handleClearQueue = async (context) => {
    console.log('🚫 [批量操作管理] 清空所有队列任务');

    try {
      // 调用图像生成服务的清空队列方法
      await context.imageGeneration.clearQueue();

      // 同步按钮状态
      syncButtonStates(context);

      context.showSuccessMessage('队列已清空', '所有排队中的任务已被清除');

    } catch (error) {
      console.error('🚫 [批量操作管理] 清空队列失败:', error);
      // 修复参数顺序：showErrorMessage(message, title)
      if (context.showErrorMessage) {
        context.showErrorMessage(error.message, '清空队列失败');
      } else {
        console.error('🚫 [批量操作管理] showErrorMessage 方法不存在');
      }
    }
  };

  /**
   * 同步按钮状态
   * @param {Object} context - 组件上下文
   */
  const syncButtonStates = (context) => {
    // 更新批量操作状态
    const oldState = batchState.isAnyBatchProcessing;
    batchState.isAnyBatchProcessing =
      context.batchOperationUI.isAnyBatchProcessing.value;

    console.log('🔄 [批量操作管理] 按钮状态已同步:', {
      旧状态: oldState,
      新状态: batchState.isAnyBatchProcessing,
      currentOperation: batchState.currentOperation,
      UI状态详情: {
        isAnyBatchProcessing: context.batchOperationUI?.isAnyBatchProcessing?.value,
        batchInferenceProcessing: context.batchOperationUI?.isBatchInferenceProcessing?.value
      }
    });
  };

  return {
    // 状态
    batchState,

    // 批量操作方法
    performBatchInference,
    performBatchInferenceCancel,
    performBatchCancel,

    // 队列管理方法
    handleClearQueue,
    handleCancelQueue,

    // 工具方法
    syncButtonStates
  };
}
