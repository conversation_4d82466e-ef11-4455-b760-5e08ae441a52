/**
 * 本地 LLM 提供商配置
 * 支持本地部署的大语言模型
 */

export const localConfig = {
  // 提供商基本信息
  name: 'Local Model',
  id: 'local',
  description: '本地部署的大语言模型',
  
  // API配置
  api: {
    baseUrl: 'http://localhost:8080', // 默认本地地址
    endpoint: '/v1/chat/completions',
    headers: {
      'Content-Type': 'application/json'
    },
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  
  // 默认配置
  defaults: {
    model: 'local-model',
    temperature: 0.7,
    maxTokens: 4096,
    timeout: 60000, // 本地模型可能需要更长时间
    url: 'http://localhost:8080'
  },
  
  // 支持的模型列表（本地模型通常需要用户自定义）
  models: [
    {
      id: 'local-model',
      name: '本地模型',
      description: '本地部署的大语言模型',
      maxTokens: 4096,
      pricing: { input: 0, output: 0 }
    },
    {
      id: 'llama-2-7b',
      name: 'Llama 2 7B',
      description: 'Meta的Llama 2 7B模型',
      maxTokens: 4096,
      pricing: { input: 0, output: 0 }
    },
    {
      id: 'llama-2-13b',
      name: 'Llama 2 13B',
      description: 'Meta的Llama 2 13B模型',
      maxTokens: 4096,
      pricing: { input: 0, output: 0 }
    },
    {
      id: 'codellama-7b',
      name: 'Code Llama 7B',
      description: '专门用于代码生成的Llama模型',
      maxTokens: 4096,
      pricing: { input: 0, output: 0 }
    }
  ],
  
  // 请求格式化函数
  formatRequest: (prompt, settings) => {
    // 检查是否是结构化的prompt（包含instruction和content）
    let messages;
    if (typeof prompt === 'object' && prompt.instruction && prompt.content) {
      messages = [
        { role: 'system', content: prompt.instruction },
        { role: 'user', content: prompt.content }
      ];
    } else {
      // 向后兼容：如果是字符串prompt，直接使用
      messages = [{ role: 'user', content: prompt }];
    }

    return {
      model: settings.model || 'local-model',
      messages: messages,
      temperature: settings.temperature || 0.7,
      max_tokens: settings.maxOutputTokens || 4096,
      stream: false
    };
  },
  
  // 响应解析函数
  parseResponse: (data) => {
    // 支持多种本地API格式
    if (data.choices && data.choices[0]) {
      // OpenAI兼容格式
      return {
        content: data.choices[0].message.content.trim(),
        usage: data.usage,
        model: data.model
      };
    } else if (data.response) {
      // Ollama格式
      return {
        content: data.response.trim(),
        usage: null,
        model: data.model
      };
    } else if (data.text) {
      // 简单文本格式
      return {
        content: data.text.trim(),
        usage: null,
        model: 'local-model'
      };
    }
    
    throw new Error('本地API响应格式无效');
  },
  
  // 错误处理
  handleError: (error, response) => {
    if (response && response.status === 404) {
      return new Error('本地模型服务未找到，请检查服务是否正在运行');
    }
    if (response && response.status === 500) {
      return new Error('本地模型服务内部错误');
    }
    if (error.code === 'ECONNREFUSED') {
      return new Error('无法连接到本地模型服务，请检查服务地址和端口');
    }
    return new Error(`本地模型API错误: ${error.message}`);
  },
  
  // 配置验证
  validateConfig: (config) => {
    const errors = [];
    
    if (!config.localUrl) {
      errors.push('缺少本地服务URL');
    }
    
    if (!config.model) {
      errors.push('未指定模型');
    }
    
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('温度值应在0-2之间');
    }
    
    // 验证URL格式
    if (config.localUrl) {
      try {
        new URL(config.localUrl);
      } catch (e) {
        errors.push('本地服务URL格式无效');
      }
    }
    
    return errors;
  },
  
  // 构建完整的API URL
  buildApiUrl: (localUrl) => {
    const baseUrl = localUrl || localConfig.defaults.url;
    return `${baseUrl}${localConfig.api.endpoint}`;
  },
  
  // 健康检查
  healthCheck: async (localUrl) => {
    try {
      const url = `${localUrl || localConfig.defaults.url}/health`;
      const response = await fetch(url, { 
        method: 'GET',
        timeout: 5000 
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }
};

export default localConfig;
