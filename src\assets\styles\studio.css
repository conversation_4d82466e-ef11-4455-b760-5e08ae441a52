.studio-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #121212;
  color: #e0e0e0;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 主要内容区域样式 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 中间面板样式 */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  border-right: 1px solid #333;
}

.panel-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background-color: #222;
  border-bottom: 1px solid #333;
}

.selected {
  font-size: 0.85rem;
  color: #aaa;
  margin-right: 1rem;
}

.section-title {
  font-size: 0.9rem;
  font-weight: bold;
}

.script-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  position: relative;
}

.scene-label {
  background-color: #333;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  display: inline-block;
}

textarea {
  flex: 1;
  background-color: #222;
  border: 1px solid #444;
  border-radius: 4px;
  color: #e0e0e0;
  padding: 0.5rem;
  resize: none;
  margin-bottom: 0.5rem;
}

.counter {
  position: absolute;
  bottom: 40px;
  left: 10px;
  font-size: 0.8rem;
  color: #aaa;
}

.editor-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.editor-button {
  background-color: #333;
  border: none;
  color: #e0e0e0;
  padding: 0.3rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.page-navigation {
  display: flex;
  gap: 0.2rem;
}

.page-navigation button {
  background-color: #333;
  border: none;
  color: #e0e0e0;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  cursor: pointer;
}

.image-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 40px;
  grid-template-rows: auto 1fr 1fr;
  gap: 4px;
  flex: 1;
  min-height: 180px;
  position: relative;
}

.image-top-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 5px;
  height: 30px;
}

.top-bar-buttons {
  display: flex;
  gap: 5px;
}

.top-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}

.image-layout {
  display: flex;
  gap: 4px;
  width: 100%;
  min-height: 180px;
}

.main-image {
  flex: 2;
  background-color: #000;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.thumbnails-area {
  flex: 1;
  display: flex;
  gap: 4px;
}

.thumbnails-scroll-container {
  flex: 1;
  overflow-y: auto;
  max-height: 100%;
  background-color: #1a1a1a;
  border-radius: 2px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #444 #222;
}

.thumbnails-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.thumbnails-scroll-container::-webkit-scrollbar-track {
  background: #222;
  border-radius: 4px;
}

.thumbnails-scroll-container::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: minmax(80px, 1fr);
  gap: 4px;
  padding: 4px;
  min-height: 200px;
}

.image-item {
  background-color: #000;
  border-radius: 2px;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 80px;
}

.sequence-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #ff3d7f;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 右侧面板样式 */
.right-panel {
  width: 25%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
}

.tabs-header {
  display: flex;
  background-color: #222;
  border-bottom: 1px solid #333;
}

.tab {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  cursor: pointer;
}

.tab.active {
  background-color: #333;
  color: #fff;
}

.preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.preview-area {
  flex: 1;
  background-color: #222;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
}

.control-button {
  background-color: #333;
  border: none;
  color: #e0e0e0;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.multi-person-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.person-count {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-size: 0.85rem;
}

.save-button {
  background-color: #2196f3;
  border: none;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

/* 选择状态栏样式 */
.selection-status-bar {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #1e1e2c;
  border-bottom: 1px solid #333355;
}

.selection-info {
  font-size: 0.9rem;
  color: #aaa;
}

/* 完全重构Excel风格网格布局 - 支持动态宽度调整 */
.excel-grid-layout {
  display: table !important;
  width: 100% !important;
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  background-color: #1a1a1a !important;
  table-layout: fixed !important; /* 🔧 固定布局，确保列宽度稳定 */
  /* 🔧 增加最小宽度，确保所有列都有足够空间 */
  min-width: 1000px !important; /* 🔧 从800px增加到1000px */
}

.grid-row {
  display: table-row;
  /* 🔧 强制保持表格行布局 */
  width: 100%;
}

.grid-cell {
  /* 🔧 强制保持表格单元格布局 - 最高优先级 */
  display: table-cell !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-right: 1px solid #333 !important;
  border-bottom: 1px solid #333 !important;
  color: #e0e0e0 !important;
  box-sizing: border-box !important;
  font-size: 0.9rem !important;
  padding: 0 !important;
  overflow: visible !important; /* 🔧 确保内容可见 */
  /* 🔧 防止布局干扰 - 最高优先级 */
  float: none !important;
  position: static !important;
  /* 🔧 防止内容被压缩成直线 */
  white-space: normal !important;
  word-wrap: break-word !important;
}

.header-row {
  background-color: #1e1e1e;
  height: 40px;
  font-weight: bold;
}

.header-row .grid-cell {
  border-bottom: 2px solid #444;
  padding: 8px;
}

.data-row {
  height: 250px;
}

.data-row .grid-cell {
  height: 250px;
  vertical-align: top;
  background-color: #252525;
}

/* 🔧 修复列宽度分配 - 确保总和为100% */
/* 🔧 版本：2025-06-15-16:30 - 紧急修复宽度总和超过100%的问题 */

/* 🔧 重新设计列宽度 - 基于实际内容需求而非标题长度 */
.select-cell-header, .select-cell {
  width: 6% !important; /* 🔧 增加到6%，确保"选择"文字水平显示 */
  min-width: 100px !important; /* 🔧 增加到100px，确保选择控件和文字都正常显示 */
  max-width: 140px !important;
  white-space: nowrap !important; /* 🔧 强制文字水平显示 */
}

.index-header, .index-cell {
  width: 5% !important; /* 🔧 增加到5%，确保序号正常显示 */
  min-width: 70px !important; /* 🔧 增加到70px，确保序号清晰 */
  max-width: 90px !important;
  white-space: nowrap !important;
}

.tags-header, .tags-cell {
  width: 8% !important; /* 🔧 增加到8%，标签需要更多空间 */
  min-width: 120px !important; /* 🔧 增加到120px，确保标签正常显示 */
  max-width: 180px !important;
}

.description-header, .description-cell {
  width: 15% !important; /* 🔧 大幅增加到15%，根据长文本内容需求 */
  min-width: 200px !important; /* 🔧 大幅增加到200px，确保长文本可读 */
  max-width: 400px !important; /* 🔧 允许更大的最大宽度 */
  white-space: normal !important; /* 🔧 允许文本正常换行 */
  word-wrap: break-word !important; /* 🔧 长单词自动换行 */
}

.operation-header, .operation-cell {
  width: 10% !important;
  min-width: 120px !important; /* 🔧 增加到120px，确保操作按钮正常显示 */
  white-space: nowrap !important;
  border-right: none !important;
}

/* 🔧 重新分配剩余空间 - 总和=56% (100%-6%-5%-8%-15%-10%) */
.keyword-header, .keyword-cell {
  /* 🔧 关键词列获得最多空间 - 25% (减少5%) */
  width: 25% !important;
  min-width: 200px !important; /* 🔧 增加最小宽度确保关键词可读 */
}

.image-header, .image-cell {
  /* 🔧 主图列 - 16% (减少2%) */
  width: 16% !important;
  min-width: 140px !important; /* 🔧 增加最小宽度确保图片正常显示 */
}

.optional-image-header, .optional-image-cell {
  /* 🔧 可选图片列 - 15% (减少5%) */
  width: 15% !important;
  min-width: 160px !important; /* 🔧 增加最小宽度确保图片正常显示 */
}

/* 🔧 新的验证：6+5+8+15+10+25+16+15 = 100% ✅ */

/* 内部内容容器 */
.cell-content {
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
}

/* 动态宽度布局优化已合并到上面的.grid-cell选择器中 */

/* 🔧 确保选择列内容水平显示 */
.select-cell .cell-content,
.select-cell-header .cell-content {
  overflow: visible !important;
  white-space: nowrap !important;
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* 🔧 确保描述列内容正常显示和换行 */
.description-cell .cell-content,
.description-header .cell-content {
  overflow: visible !important;
  white-space: normal !important; /* 🔧 允许正常换行 */
  word-wrap: break-word !important;
  text-align: left !important; /* 🔧 左对齐提高可读性 */
  padding: 8px !important; /* 🔧 增加内边距 */
  line-height: 1.4 !important; /* 🔧 改善行高 */
  width: 100% !important;
  height: 100% !important;
}

/* 确保操作列内容不被截断 */
.operation-cell .cell-content {
  overflow: visible !important;
  white-space: nowrap !important;
}

/* 确保关键词列能够充分利用剩余空间 */
.keyword-cell .cell-content {
  width: 100% !important;
  min-width: 0 !important; /* 允许flex收缩 */
}

.select-header-btn {
  background-color: #2a2a2a;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 0.9rem;
  cursor: pointer;
  margin: 0 auto;
  text-align: center;
  display: block;
  width: 80%;
}

.select-header-btn:hover {
  background-color: #3a3a3a;
}

.image-header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 2px;
}

.image-title {
  font-size: 14px;
  font-weight: bold;
  color: #e0e0e0;
}

.image-controls {
  display: flex;
  gap: 8px;
}

.control-icon-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}

.lock-button {
  background-color: #2a2a2a;
}

.clear-button {
  background-color: #444;
}

/* 画面关键词样式 */
.keyword-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  border-radius: 2px;
  overflow: hidden;
}

.keyword-textarea {
  flex: 1;
  background-color: #111;
  border: none;
  color: #e0e0e0;
  font-size: 14px;
  padding: 10px;
  resize: none;
  width: 100%;
  outline: none;
  min-height: 180px;
}

/* 选中列的选择控件和按钮样式 */
.select-controls {
  display: flex;
  flex-direction: column; /* 🔧 修复：改为垂直排列，与组件样式保持一致 */
  align-items: center; /* 🔧 修复：居中对齐 */
  justify-content: flex-start;
  gap: 10px; /* 🔧 修复：增加间距与组件保持一致 */
  padding: 10px 2px;
}

.select-checkbox {
  width: 18px;
  height: 18px;
  min-width: 18px;
  border: 1px solid #00BCD4;
  border-radius: 3px;
  background-color: transparent;
  cursor: pointer;
  margin-top: 10px;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.merge-button, .split-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 5px;
  font-size: 0.7rem;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
}

.merge-button:hover, .split-button:hover {
  background-color: #444;
}

/* 原文描述样式 */
.description-box {
  width: 100%;
  height: 100%;
  background-color: #111;
  border: none;
  border-radius: 3px;
  overflow-y: auto;
  position: relative;
}

.readonly-text {
  color: #e0e0e0;
  font-size: 14px;
  padding: 10px;
  line-height: 1.5;
  white-space: pre-wrap;
  user-select: text;
  cursor: text;
  height: 100%;
  overflow-y: auto;
}

/* 标签区域样式 */
.tags-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 8px;
  overflow-y: auto;
}

.vertical-manage-btn {
  width: 40px;
  background-color: #2a2a2a;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  color: white;
  font-size: 14px;
  letter-spacing: 2px;
  user-select: none;
}

/* 主图区域调整 */
.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  border-radius: 2px;
  position: relative;
}

.main-image {
  flex: 1;
  background-color: #000;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* 可选图片区域样式 */
.optional-image-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  display: flex;
  gap: 4px;
}

/* 操作列样式 */
.operation-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 8px;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.operation-button {
  width: 100%;
  background-color: #333;
  border: none;
  color: white;
  padding: 6px 0;
  border-radius: 3px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.operation-button:hover {
  background-color: #444;
}

/* 🔧 移除重复的highlight样式定义，使用OperationCell.vue中的样式 */

/* 🔧 强化响应式布局 - 防止列压缩成直线 */
@media (max-width: 768px) {
  .excel-grid-layout {
    /* 保持表格布局，允许水平滚动 */
    display: table !important;
    table-layout: fixed !important;
    overflow-x: auto !important;
    min-width: 1000px !important; /* 🔧 与主布局保持一致 */
  }

  .grid-row {
    display: table-row !important;
  }

  .grid-cell {
    display: table-cell !important;
    float: none !important;
    position: static !important;
    white-space: normal !important; /* 🔧 防止内容被压缩 */
  }

  /* 🔧 在小屏幕上保持新的最小宽度设置 */
  .select-cell-header, .select-cell {
    min-width: 100px !important; /* 🔧 与主设置保持一致 */
    white-space: nowrap !important;
  }

  .index-header, .index-cell {
    min-width: 70px !important; /* 🔧 与主设置保持一致 */
  }

  .description-header, .description-cell {
    min-width: 200px !important; /* 🔧 与主设置保持一致 */
    white-space: normal !important;
  }

  .tags-header, .tags-cell {
    min-width: 120px !important;
  }

  .operation-header, .operation-cell {
    min-width: 120px !important;
  }
}

@media (max-width: 480px) {
  .excel-grid-layout {
    min-width: 800px; /* 保持足够宽度以容纳重新分配的列 */
  }

  /* 内容自适应列在小屏幕上的限制 */
  .select-cell-header, .select-cell {
    max-width: 100px;
  }

  .index-header, .index-cell {
    max-width: 60px;
  }

  .tags-header, .tags-cell {
    max-width: 150px;
  }

  .description-header, .description-cell {
    max-width: 250px;
  }

  /* 需要空间的列保持比例但调整最小宽度 */
  .keyword-header, .keyword-cell {
    min-width: 150px;
  }

  .image-header, .image-cell {
    min-width: 120px;
  }

  .optional-image-header, .optional-image-cell {
    min-width: 150px;
  }
}

@media (max-width: 320px) {
  .excel-grid-layout {
    min-width: 700px; /* 在极小屏幕上适当减少最小宽度 */
  }

  /* 内容自适应列在极小屏幕上的进一步限制 */
  .select-cell-header, .select-cell {
    max-width: 80px;
  }

  .index-header, .index-cell {
    max-width: 50px;
  }

  .tags-header, .tags-cell {
    max-width: 120px;
  }

  .description-header, .description-cell {
    max-width: 200px;
  }

  /* 需要空间的列进一步调整最小宽度 */
  .keyword-header, .keyword-cell {
    min-width: 120px;
  }

  .image-header, .image-cell {
    min-width: 100px;
  }

  .optional-image-header, .optional-image-cell {
    min-width: 120px;
  }
}