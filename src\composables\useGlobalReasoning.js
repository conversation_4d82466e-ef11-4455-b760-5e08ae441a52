import { ref } from 'vue';

export function useGlobalReasoning() {
  const llmConfig = ref(null);
  const reasoningResult = ref(null);
  const isLoading = ref(false);
  const error = ref(null);
  const statusMessage = ref('');

  // 1. 获取LLM配置
  async function fetchLLMConfig() {
    try {
      // 修改为调用API端点，而不是直接fetch静态文件
      const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');
      if (!response.ok) {
        throw new Error(`Failed to fetch LLM config from API: ${response.statusText}`);
      }
      const result = await response.json(); // API返回的数据结构是 { success: true, settings: {...} }
      
      if (result.success && result.settings && result.settings.llm) {
        llmConfig.value = result.settings.llm;
        console.log('LLM Config loaded from API:', llmConfig.value);
      } else {
        // 如果API调用成功但返回的数据不符合预期（例如success:false或缺少settings.llm）
        throw new Error(result.error || 'API did not return valid LLM settings.');
      }
    } catch (err) {
      // 更新错误日志和注释，以反映配置是通过API加载的
      console.error('Error fetching LLM config from API or processing it:', err);
      // error.value = '无法通过API加载LLM配置，请检查网络连接和后端API状态。'; // 如果需要向用户显示错误，可以取消注释并更新此消息
      llmConfig.value = {
        provider: "local", 
        defaultModel: "local-model", 
        localUrl: "http://localhost:8080/v1/chat/completions", 
        localApiKey: "your-local-api-key-if-needed"
      };
      console.warn('Using fallback LLM config due to error:', llmConfig.value);
    }
  }

  // 3. 执行推理 (修改后)
  async function performReasoning(text, promptContent) {
    isLoading.value = true;
    error.value = null;
    reasoningResult.value = null; // 清空旧结果
    statusMessage.value = '调用本地分析服务...'; // 更新状态消息

    console.log('[useGlobalReasoning] performReasoning called with text (first 200 chars):', typeof text === 'string' ? text.substring(0, 200) : text);
    console.log('[useGlobalReasoning] performReasoning called with promptContent (first 200 chars):', typeof promptContent === 'string' ? promptContent.substring(0, 200) : promptContent);

    try {
      const body = { 
        text, 
        analyzeType: 'global-reasoning' 
      };
      if (promptContent) {
        body.prompt = promptContent; // 如果提供了 promptContent，则添加到请求体
      }
      console.log('[useGlobalReasoning] Request body BEFORE stringify:', JSON.parse(JSON.stringify(body)));

      const stringifiedBody = JSON.stringify(body);
      console.log('[useGlobalReasoning] Request body AFTER stringify, BEFORE fetch:', stringifiedBody);

      const response = await fetch('/api/local/analyze-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: stringifiedBody // 使用包含可选 prompt 的请求体
      });

      if (!response.ok) {
        let errorDetails = `分析服务错误: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorDetails = errorData.error;
          }
        } catch (parseErr) {
          console.warn('无法解析错误响应体:', parseErr);
        }
        throw new Error(errorDetails);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '后端分析失败，未提供错误详情');
      }

      statusMessage.value = '分析完成，正在处理结果...';
      if (result.parseError) {
        console.warn('后端返回结果，但JSON解析失败:', result.parseError);
        reasoningResult.value = { rawOutput: result.text, parseError: true };
        error.value = `AI返回了结果，但格式无法解析: ${result.parseError}`;
      } else if (result.data) {
        reasoningResult.value = result.data;
        error.value = null;
      } else {
        console.warn('后端分析成功，但未返回预期的JSON数据，显示原始文本。');
        reasoningResult.value = { rawOutput: result.text, parseError: false };
      }

    } catch (err) {
      console.error('全局推理处理失败:', err);
      error.value = `全局推理处理失败: ${err.message}`;
      reasoningResult.value = null;
    } finally {
      isLoading.value = false;
      statusMessage.value = '';
    }
  }

  fetchLLMConfig();

  return {
    llmConfig,
    reasoningResult,
    isLoading,
    error,
    statusMessage,
    performReasoning,
    fetchLLMConfig,
  };
}