/**
 * 角色名称自动识别和标签选择功能
 * 用于处理 LLM 返回的图像提示词中的角色名称识别和自动标签选择
 */

import { ref, computed } from 'vue';

export function useCharacterRecognition() {
  // 识别结果状态
  const recognitionResult = ref({
    recognizedCharacters: [],     // 识别出的角色名称列表
    replacedPrompt: '',          // 替换占位符后的提示词
    autoSelectedTags: [],        // 自动选中的标签列表
    hasPlaceholders: false,      // 是否包含占位符
    processingStatus: 'idle'     // 处理状态: idle, processing, completed, error
  });

  // 角色名称匹配模式
  const characterPatterns = {
    // {character}角色名称 或 {Character}角色名称 模式 - 匹配占位符后跟的角色名称
    // 使用更广泛的中文字符范围：\u4e00-\u9fff 包含所有常用中文字符
    // 允许占位符和角色名称之间有任意数量的空白字符（包括空格、制表符等）：\s*
    // 这样无论有空格还是无空格都能匹配：{Character}高贤 或 {Character} 高贤 或 {Character}  高贤
    placeholderWithName: /\{[Cc]haracter\}\s*([\u4e00-\u9fff]{2,4})/gi,
    // 单独的 {character} 或 {Character} 占位符模式
    placeholder: /\{[Cc]haracter\}/gi
  };

  /**
   * 从可用角色列表中查找匹配的角色
   * @param {string} characterName - 要查找的角色名称
   * @param {Array} availableCharacters - 可用的角色列表
   * @returns {Object|null} 匹配的角色对象或null
   */
  function findMatchingCharacter(characterName, availableCharacters) {
    if (!availableCharacters || !Array.isArray(availableCharacters)) {
      return null;
    }

    console.log('[CharacterRecognition] 🔍 查找匹配角色:', {
      characterName,
      availableCharactersCount: availableCharacters.length,
      availableCharacters: availableCharacters
    });

    // 精确匹配
    let match = availableCharacters.find(char => {
      // 提取角色名称，处理各种可能的数据结构
      let charName = '';
      if (typeof char === 'string') {
        charName = char;
      } else if (char && typeof char === 'object') {
        // 尝试从对象中提取名称
        charName = char.name || char.characterName || char.title || '';
        // 如果提取的仍然是对象，尝试进一步提取
        if (typeof charName === 'object' && charName.name) {
          charName = charName.name;
        }
      }

      console.log('[CharacterRecognition] 精确匹配检查:', {
        char,
        charName,
        charNameType: typeof charName,
        characterName
      });

      return typeof charName === 'string' && charName === characterName;
    });

    if (match) {
      console.log('[CharacterRecognition] ✅ 精确匹配成功:', match);
      return match;
    }

    // 模糊匹配（包含关系）
    match = availableCharacters.find(char => {
      // 提取角色名称，处理各种可能的数据结构
      let charName = '';
      if (typeof char === 'string') {
        charName = char;
      } else if (char && typeof char === 'object') {
        // 尝试从对象中提取名称
        charName = char.name || char.characterName || char.title || '';
        // 如果提取的仍然是对象，尝试进一步提取
        if (typeof charName === 'object' && charName.name) {
          charName = charName.name;
        }
      }

      console.log('[CharacterRecognition] 模糊匹配检查:', {
        char,
        charName,
        charNameType: typeof charName,
        characterName
      });

      // 确保 charName 是字符串
      if (typeof charName !== 'string' || charName.length === 0) {
        console.log('[CharacterRecognition] ⚠️ 角色名称不是有效字符串:', charName, typeof charName);
        return false;
      }

      return charName.includes(characterName) || characterName.includes(charName);
    });

    if (match) return match;

    // 别名匹配（如果角色有别名）
    match = availableCharacters.find(char => {
      if (typeof char === 'object' && char.aliases && Array.isArray(char.aliases)) {
        return char.aliases.some(alias => 
          alias === characterName || 
          alias.includes(characterName) || 
          characterName.includes(alias)
        );
      }
      return false;
    });

    return match;
  }

  /**
   * 从提示词中识别角色占位符和角色名称，并用角色描述替换
   * @param {string} prompt - 图像提示词
   * @param {Array} availableCharacters - 可用的角色列表
   * @returns {Object} 识别结果
   */
  function recognizeCharactersInPrompt(prompt, availableCharacters = []) {
    recognitionResult.value.processingStatus = 'processing';

    try {
      // 确保 prompt 是字符串
      const promptString = typeof prompt === 'string' ? prompt : String(prompt || '');
      console.log('[CharacterRecognition] 输入提示词类型检查:', {
        originalType: typeof prompt,
        originalValue: prompt,
        convertedString: promptString
      });

      const result = {
        recognizedCharacters: [],
        replacedPrompt: promptString,
        autoSelectedTags: [],
        hasPlaceholders: false,
        matchedPatterns: []
      };

      // 1. 检查是否包含 {character}角色名称 模式
      const placeholderWithNameMatches = [...promptString.matchAll(characterPatterns.placeholderWithName)];

      console.log('[CharacterRecognition] 正则匹配结果:', {
        promptString,
        pattern: characterPatterns.placeholderWithName,
        matches: placeholderWithNameMatches,
        matchCount: placeholderWithNameMatches.length,
        matchDetails: placeholderWithNameMatches.map(match => ({
          fullMatch: match[0],
          characterName: match[1],
          hasSpaces: /\s/.test(match[0])
        }))
      });

      if (placeholderWithNameMatches.length > 0) {
        result.hasPlaceholders = true;
        let replacedPrompt = promptString;
        const recognizedCharacters = [];
        const autoSelectedTags = [];

        // 处理每个匹配的 {character}角色名称
        placeholderWithNameMatches.forEach((match, index) => {
          const fullMatch = match[0]; // 完整匹配，如 "{character}高贤"
          const characterName = match[1]; // 角色名称，如 "高贤"

          console.log(`[CharacterRecognition] 处理第${index + 1}个占位符:`, {
            fullMatch,
            characterName,
            replacedPromptType: typeof replacedPrompt,
            replacedPromptValue: replacedPrompt
          });

          // 在可用角色列表中查找匹配的角色
          const matchedCharacter = findMatchingCharacter(characterName, availableCharacters);

          if (matchedCharacter) {
            // 确保 replacedPrompt 是字符串
            if (typeof replacedPrompt !== 'string') {
              console.error('[CharacterRecognition] replacedPrompt 不是字符串!', {
                type: typeof replacedPrompt,
                value: replacedPrompt
              });
              replacedPrompt = String(replacedPrompt || '');
            }

            // 移除 {character}角色名称 占位符，不替换为描述
            const beforeReplace = replacedPrompt;
            replacedPrompt = replacedPrompt.replace(fullMatch, '').trim();

            console.log(`[CharacterRecognition] 替换第${index + 1}个占位符:`, {
              before: beforeReplace,
              after: replacedPrompt,
              removed: fullMatch
            });

            // 记录识别结果
            recognizedCharacters.push({
              originalText: fullMatch,
              characterName: characterName,
              matchedCharacter: matchedCharacter,
              matchType: 'placeholderWithName'
            });

            // 添加到自动选择标签
            autoSelectedTags.push(characterName);

            console.log('[CharacterRecognition] 识别角色并移除占位符:', {
              original: fullMatch,
              characterName: characterName,
              removedPlaceholder: true
            });
          } else {
            console.log('[CharacterRecognition] 未找到匹配角色:', characterName);
          }
        });

        result.matchedPatterns.push({
          type: 'placeholderWithName',
          matches: placeholderWithNameMatches.map(m => m[0]),
          count: placeholderWithNameMatches.length
        });

        result.recognizedCharacters = recognizedCharacters;
        result.replacedPrompt = replacedPrompt;
        result.autoSelectedTags = [...new Set(autoSelectedTags)]; // 去重
      }

      // 2. 检查是否还有单独的 {character} 占位符（没有跟角色名称）
      const remainingPlaceholders = result.replacedPrompt.match(characterPatterns.placeholder);

      console.log('[CharacterRecognition] 检查剩余占位符:', {
        replacedPrompt: result.replacedPrompt,
        remainingPlaceholders,
        availableCharactersCount: availableCharacters.length
      });

      if (remainingPlaceholders && availableCharacters.length > 0) {
        // 如果还有单独的占位符，用第一个可用角色的描述替换
        const firstAvailable = availableCharacters[0];
        const characterDescription = typeof firstAvailable === 'string' ?
          firstAvailable : (firstAvailable.description || firstAvailable.name || '');
        const characterName = typeof firstAvailable === 'string' ?
          firstAvailable : (firstAvailable.name || firstAvailable.characterName || '');

        console.log('[CharacterRecognition] 处理剩余占位符:', {
          firstAvailable,
          characterDescription,
          characterDescriptionType: typeof characterDescription,
          characterName,
          characterNameType: typeof characterName
        });

        if (characterDescription) {
          // 确保 characterDescription 是字符串
          const descriptionString = typeof characterDescription === 'string' ?
            characterDescription : String(characterDescription || '');

          console.log('[CharacterRecognition] 替换剩余占位符:', {
            before: result.replacedPrompt,
            descriptionString,
            pattern: characterPatterns.placeholder
          });

          result.replacedPrompt = result.replacedPrompt.replace(characterPatterns.placeholder, descriptionString);
          result.autoSelectedTags.push(characterName);
          result.recognizedCharacters.push({
            originalText: '{character}',
            characterName: characterName,
            matchedCharacter: firstAvailable,
            characterDescription: characterDescription,
            matchType: 'placeholder'
          });

          result.hasPlaceholders = true;
          result.matchedPatterns.push({
            type: 'placeholder',
            matches: remainingPlaceholders,
            count: remainingPlaceholders.length
          });
        }
      }

      // 如果没有找到任何占位符，直接返回原始提示词
      if (!result.hasPlaceholders) {
        recognitionResult.value = {
          ...result,
          processingStatus: 'completed'
        };
        return result;
      }

      // 更新状态
      recognitionResult.value = {
        ...result,
        processingStatus: 'completed'
      };

      console.log('[CharacterRecognition] 识别完成:', result);
      return result;

    } catch (error) {
      console.error('[CharacterRecognition] 识别过程出错:', error);
      recognitionResult.value.processingStatus = 'error';
      const promptString = typeof prompt === 'string' ? prompt : String(prompt || '');
      return {
        recognizedCharacters: [],
        replacedPrompt: promptString,
        autoSelectedTags: [],
        hasPlaceholders: false,
        error: error.message
      };
    }
  }

  /**
   * 应用角色标签选择到指定行
   * @param {number} rowIndex - 行索引
   * @param {Array} autoSelectedTags - 要自动选中的标签列表
   * @param {Function} tagSelectionHandler - 标签选择处理函数
   * @returns {Promise<boolean>} 是否成功应用
   */
  async function applyCharacterTagsToRow(rowIndex, autoSelectedTags, tagSelectionHandler) {
    if (!autoSelectedTags || autoSelectedTags.length === 0) {
      console.log('[CharacterRecognition] 没有需要应用的标签');
      return false;
    }

    try {
      console.log('[CharacterRecognition] 开始应用标签到行:', { rowIndex, autoSelectedTags });

      // 模拟标签选择事件
      for (const tagName of autoSelectedTags) {
        if (typeof tagSelectionHandler === 'function') {
          await tagSelectionHandler({
            rowIndex: rowIndex,
            tagName: tagName,
            isSelected: true,
            selectedTags: autoSelectedTags,
            isAutoGenerated: true  // 标记为自动生成
          });
          console.log('[CharacterRecognition] 已应用标签:', tagName);
        }
      }

      return true;
    } catch (error) {
      console.error('[CharacterRecognition] 应用标签失败:', error);
      return false;
    }
  }

  /**
   * 重置识别状态
   */
  function resetRecognitionState() {
    recognitionResult.value = {
      recognizedCharacters: [],
      replacedPrompt: '',
      autoSelectedTags: [],
      hasPlaceholders: false,
      processingStatus: 'idle'
    };
  }

  // 计算属性
  const hasRecognizedCharacters = computed(() => 
    recognitionResult.value.recognizedCharacters.length > 0
  );

  const isProcessing = computed(() => 
    recognitionResult.value.processingStatus === 'processing'
  );

  const isCompleted = computed(() => 
    recognitionResult.value.processingStatus === 'completed'
  );

  const hasError = computed(() => 
    recognitionResult.value.processingStatus === 'error'
  );

  return {
    // 状态
    recognitionResult,
    
    // 计算属性
    hasRecognizedCharacters,
    isProcessing,
    isCompleted,
    hasError,
    
    // 方法
    recognizeCharactersInPrompt,
    applyCharacterTagsToRow,
    resetRecognitionState,
    findMatchingCharacter
  };
}
