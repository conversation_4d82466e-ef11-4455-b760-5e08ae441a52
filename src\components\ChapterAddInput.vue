<template>
  <div class="chapter-top-actions">
    <button
      class="add-chapter-btn"
      @click="toggleAddInput"
    >
      <i class="ri-add-line" />
      <span>新建章节</span>
    </button>
    <input
      v-if="showAddChapterInput"
      class="chapter-input"
      type="text"
      v-model="localTitle"
      placeholder="输入章节标题"
      @input="onInput"
      @keyup.enter="confirmAdd"
    >
    <button
      v-if="showAddChapterInput"
      class="confirm-chapter-btn"
      @click="confirmAdd"
    >
      添加
    </button>
    <button
      v-if="showAddChapterInput"
      class="cancel-chapter-btn"
      @click="cancelAdd"
    >
      取消
    </button>
  </div>
</template>

<script>
import { ref, watch } from 'vue';

export default {
  props: {
    showAddChapterInput: Boolean,
    newChapterTitle: {
      type: String,
      default: ''
    }
  },
  emits: ['update:show-add-chapter-input', 'update:new-chapter-title', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const localTitle = ref(props.newChapterTitle);
    
    watch(() => props.newChapterTitle, (val) => {
      localTitle.value = val;
    });
    
    function onInput(e) {
      emit('update:new-chapter-title', e.target.value);
    }

    function toggleAddInput() {
      emit('update:show-add-chapter-input', !props.showAddChapterInput);
    }
    
    function confirmAdd() {
      emit('confirm');
    }
    
    function cancelAdd() {
      emit('cancel');
    }
    
    return {
      localTitle,
      onInput,
      toggleAddInput,
      confirmAdd,
      cancelAdd
    };
  }
};
</script>

<style scoped>
.chapter-top-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 1rem;
}

.add-chapter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.7rem 1.2rem;
  background: #292945;
  color: #e0def4;
  border: none;
  border-radius: 22px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
}

.add-chapter-btn:hover {
  background: #393552;
  transform: translateY(-2px);
}

.chapter-input {
  flex: 1 1 220px;
  min-width: 180px;
  max-width: 260px;
  padding: 0.7rem 1.2rem;
  border-radius: 22px;
  border: 1.5px solid #444;
  background: #181825;
  color: #fff;
  font-size: 1.08rem;
  outline: none;
  height: 44px;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.chapter-input:focus {
  border-color: #a23a5a;
}

.confirm-chapter-btn, .cancel-chapter-btn {
  min-width: 70px;
  padding: 0 1.5rem;
  height: 44px;
  border-radius: 22px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px 0 rgba(34, 34, 59, 0.134);
  letter-spacing: 1px;
  border: none;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}

.confirm-chapter-btn {
  background: linear-gradient(90deg, #29908b 0%, #a23a5a 100%);
  color: #fff;
}
.confirm-chapter-btn:hover {
  background: linear-gradient(90deg, #a23a5a 0%, #29908b 100%);
  color: #fff;
  box-shadow: 0 6px 24px 0 rgba(162, 58, 90, 0.2);
}

.cancel-chapter-btn {
  background: #292945;
  color: #b5bfe2;
  margin-left: 0.1rem;
}
.cancel-chapter-btn:hover {
  background: #393552;
  color: #f5c2e7;
  box-shadow: 0 6px 24px 0 rgba(57, 53, 82, 0.2);
}

@media (max-width: 600px) {
  .chapter-top-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.7rem;
  }
  .chapter-input, .confirm-chapter-btn, .cancel-chapter-btn {
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
  }
}
</style>
