<template>
  <div class="grid-row header-row">
    <div class="grid-cell select-cell-header">
      <!-- 🔧 恢复原有的下拉菜单设计 -->
      <div class="batch-select-dropdown" @click.stop>
        <button
          class="select-header-btn"
          @click="toggleDropdown"
          :title="'批量选择操作'"
        >
          选中
          <i class="ri-arrow-down-s-line" :class="{ 'rotated': showDropdown }" />
        </button>
        <div v-if="showDropdown" class="dropdown-menu">
          <button
            class="dropdown-item select-all"
            @click="selectAll"
            title="选中所有未锁定的行"
          >
            <i class="ri-checkbox-multiple-line" />
            全选
          </button>
          <button
            class="dropdown-item clear-selection"
            @click="clearSelection"
            title="取消所有行的选中状态"
          >
            <i class="ri-checkbox-blank-line" />
            清空
          </button>
        </div>
      </div>
    </div>
    <div class="grid-cell index-header">
      序号
    </div>
    <div class="grid-cell description-header">
      原文描述
    </div>
    <div class="grid-cell tags-header">
      标签
    </div>
    <div class="grid-cell keyword-header">
      <div class="keyword-header-content">
        <span class="keyword-title">画面关键词</span>
        <button
          class="clear-all-button"
          @click="clearAllKeywords"
          title="清除所有行的提示词"
        >
          <i class="ri-delete-bin-line" />
        </button>
      </div>
    </div>
    <div class="grid-cell image-header">
      本镜配图
    </div>
    <div class="grid-cell optional-image-header">
      可选图
    </div>
    <div class="grid-cell operation-header">
      <div class="operation-header-content">
        <span class="operation-title">操作</span>
        <button
          class="settings-button"
          @click="openImagePromptSettings"
          title="图像提示词生成器设置"
        >
          <i class="ri-settings-3-line" />
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GridHeader',
  emits: {
    'open-image-prompt-settings': null,
    'clear-all-keywords': null,
    'select-all': null,
    'clear-selection': null
  },
  data() {
    return {
      showDropdown: false
    };
  },
  mounted() {
    // 点击外部关闭下拉菜单
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    openImagePromptSettings() {
      this.$emit('open-image-prompt-settings');
    },
    clearAllKeywords() {
      this.$emit('clear-all-keywords');
    },
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    selectAll() {
      this.$emit('select-all');
      this.showDropdown = false; // 执行操作后关闭下拉菜单
    },
    clearSelection() {
      this.$emit('clear-selection');
      this.showDropdown = false; // 执行操作后关闭下拉菜单
    }
  }
}
</script>

<style scoped>
.grid-row {
  display: table-row;
  /* 🔧 强制保持表格行布局 */
  width: 100%;
}

.header-row {
  background-color: #1e1e1e;
  height: 40px;
  font-weight: bold;
  /* 🔧 确保头部行始终保持横向布局 */
  display: table-row !important;
}

.grid-cell {
  /* 🔧 强制保持表格单元格布局，防止变为块级元素 */
  display: table-cell !important;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 2px solid #444;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 8px;
  float: none !important;
  position: relative;
}

.select-cell-header {
  /* 🔧 改为内容自适应宽度，节省空间 */
  width: fit-content;
  min-width: fit-content;
  max-width: 120px;
}

.index-header {
  /* 🔧 改为内容自适应宽度，节省空间 */
  width: fit-content;
  min-width: fit-content;
  max-width: 80px;
}

.description-header {
  /* 🔧 描述列改为内容自适应 */
  width: fit-content;
  min-width: 150px;
  max-width: 300px;
}

.tags-header {
  /* 🔧 标签列改为内容自适应 */
  width: fit-content;
  min-width: fit-content;
  max-width: 200px;
}

.keyword-header {
  /* 🔧 关键词列获得最多空间 */
  width: 40%;
  min-width: 200px;
}

/* 关键词列标题内容布局 */
.keyword-header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.keyword-title {
  font-weight: bold;
}

/* 清除所有按钮样式 */
.clear-all-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.clear-all-button:hover {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.clear-all-button i {
  font-size: 12px;
}

.image-header {
  /* 🔧 主图列需要足够空间 */
  width: 20%;
  min-width: 150px;
}

.optional-image-header {
  /* 🔧 可选图片列需要足够空间 */
  width: 25%;
  min-width: 180px;
}

.operation-header {
  /* 🔧 操作列保持内容自适应 */
  width: fit-content;
  min-width: fit-content;
  white-space: nowrap;
  border-right: none;
}

/* 操作列标题内容布局 */
.operation-header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.operation-title {
  font-weight: bold;
}

/* 设置按钮样式 - 优化尺寸和可点击性 */
.settings-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px; /* 增加内边距，提高可点击区域 */
  border-radius: 4px; /* 稍微增大圆角 */
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px; /* 增大按钮宽度 */
  height: 28px; /* 增大按钮高度 */
  min-width: 28px; /* 确保最小宽度 */
  min-height: 28px; /* 确保最小高度 */
}

.settings-button:hover {
  color: #3e8fb0;
  background-color: rgba(62, 143, 176, 0.15); /* 稍微增强悬停效果 */
  transform: scale(1.05); /* 添加轻微的缩放效果 */
}

.settings-button i {
  font-size: 16px; /* 增大图标尺寸 */
}

/* 响应式设计 - 小屏幕设备优化 */
@media (max-width: 768px) {
  .settings-button {
    width: 32px; /* 在小屏幕上进一步增大按钮 */
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    padding: 10px; /* 增加触摸区域 */
  }

  .settings-button i {
    font-size: 18px; /* 在小屏幕上增大图标 */
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .settings-button {
    width: 36px; /* 触摸设备上确保足够大的点击区域 */
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    padding: 12px;
  }

  .settings-button i {
    font-size: 20px; /* 触摸设备上使用更大的图标 */
  }
}

/* 🔧 强制横向布局 - 防止在任何屏幕尺寸下变为垂直排列 */
@media (max-width: 480px) {
  /* 确保表格行始终保持横向布局 */
  .grid-row {
    display: table-row !important;
  }

  /* 确保表格单元格始终保持横向排列 */
  .grid-cell {
    display: table-cell !important;
    float: none !important;
    position: static !important;
  }

  /* 在极小屏幕上适当缩小选中按钮和序号列 */
  .select-cell-header {
    min-width: 60px;
    font-size: 0.8rem;
  }

  .index-header {
    min-width: 45px;
    font-size: 0.8rem;
  }

  /* 缩小选中按钮的字体和内边距 */
  .select-header-btn {
    font-size: 0.75rem;
    padding: 2px 4px;
    gap: 2px;
  }

  .select-header-btn i {
    font-size: 10px;
  }
}

/* 🔧 超小屏幕优化 */
@media (max-width: 320px) {
  .select-cell-header {
    min-width: 50px;
  }

  .index-header {
    min-width: 40px;
  }

  .select-header-btn {
    font-size: 0.7rem;
    padding: 1px 2px;
  }
}

/* 🔧 恢复原有的下拉菜单样式 */
.batch-select-dropdown {
  position: relative;
  width: 100%;
}

.select-header-btn {
  background: none;
  border: 1px solid #3e8fb0;
  color: #e0e0e0;
  font-size: 0.9rem;
  cursor: pointer;
  width: 100%;
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.select-header-btn:hover {
  background-color: #3e8fb0;
  color: white;
}

.select-header-btn i {
  font-size: 12px;
  transition: transform 0.2s;
}

.select-header-btn i.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #2a2a2a;
  border: 1px solid #3e8fb0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
  margin-top: 2px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background-color: transparent;
  border: none;
  color: #e0e0e0;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: #3e8fb0;
  color: white;
}

.dropdown-item.select-all i {
  color: #4ade80;
}

.dropdown-item.clear-selection i {
  color: #f59e0b;
}

.dropdown-item:hover i {
  color: white;
}
</style>



 




 
