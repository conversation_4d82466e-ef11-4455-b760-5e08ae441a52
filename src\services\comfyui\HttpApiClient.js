/**
 * VI. 核心模块: HttpApiClient
 * 
 * 职责：
 * - 封装对ComfyUI HTTP API的调用（主要是 /prompt 和 /view）
 * - 处理HTTP请求的构建、发送和响应解析
 * - 处理HTTP错误
 */

export class HttpApiClient {
  constructor(config = {}) {
    this.config = {
      base_url: config.base_url || 'http://localhost:8188',
      request_timeout: config.request_timeout || 30000
    };

    console.log(`🌐 [HttpApiClient] 初始化完成: ${this.config.base_url}`);
  }

  /**
   * 提交工作流到ComfyUI
   * @param {Object} workflow_api_json - 工作流API JSON
   * @returns {Promise<Object|null>} 解析后的JSON响应或null
   */
  async postPrompt(workflow_api_json) {
    try {
      console.log('📤 [HttpApiClient] 提交工作流...');

      // 构建符合服务器端期望的请求体
      const requestBody = {
        serverUrl: this.config.base_url,
        workflow: workflow_api_json,
        client_id: this.generateClientId()
      };

      console.log('📤 [HttpApiClient] 请求体:', {
        serverUrl: requestBody.serverUrl,
        workflowNodeCount: Object.keys(workflow_api_json).length,
        client_id: requestBody.client_id
      });

      const response = await this.makeRequest('/prompt', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        const result = await response.json();

        // 检查ComfyUI返回的错误
        if (result.error) {
          console.error('❌ [HttpApiClient] ComfyUI工作流验证失败:', result.error);
          console.error('❌ [HttpApiClient] 节点错误详情:', result.node_errors);

          // 提供更详细的错误信息
          if (result.node_errors) {
            for (const [nodeId, nodeError] of Object.entries(result.node_errors)) {
              console.error(`❌ [HttpApiClient] 节点 ${nodeId} (${nodeError.class_type}) 错误:`, nodeError.errors);
            }
          }

          throw new Error(`ComfyUI工作流验证失败: ${result.error.message}`);
        }

        console.log('✅ [HttpApiClient] 工作流提交成功:', result);
        return result;
      } else {
        const errorText = await response.text();
        console.error('❌ [HttpApiClient] 服务器错误响应:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

    } catch (error) {
      console.error('❌ [HttpApiClient] 提交工作流失败:', error);
      return null;
    }
  }

  /**
   * 中断当前执行的任务
   * @returns {Promise<Object|null>} 响应结果或null
   */
  async interrupt() {
    try {
      console.log('🛑 [HttpApiClient] 发送中断命令到:', this.config.base_url);

      const requestBody = {
        serverUrl: this.config.base_url
      };

      console.log('🛑 [HttpApiClient] 请求体:', requestBody);

      const response = await fetch('/api/local/comfyui/interrupt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('🛑 [HttpApiClient] 响应状态:', response.status, response.statusText);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ [HttpApiClient] 中断命令发送成功:', result);
        return result;
      } else {
        const errorText = await response.text();
        console.error('❌ [HttpApiClient] 中断命令响应错误:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error(`❌ [HttpApiClient] 发送中断命令失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取文件（图像、视频等）
   * @param {string} filename - 文件名
   * @param {string} subfolder - 子文件夹
   * @param {string} type - 文件类型
   * @returns {Promise<Uint8Array|null>} 文件字节数据或null
   */
  async getView(filename, subfolder = '', type = 'output') {
    try {
      console.log(`📥 [HttpApiClient] 获取文件: ${filename}`);

      const params = new URLSearchParams({
        filename,
        type
      });

      if (subfolder) {
        params.append('subfolder', subfolder);
      }

      // 🆕 使用新的后端代理路由
      params.append('serverUrl', this.config.serverUrl);
      const proxyUrl = `http://localhost:8091/api/local/comfyui/view?${params.toString()}`;

      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: {
          'Accept': 'image/*'
        }
      });

      if (response.ok) {
        const arrayBuffer = await response.arrayBuffer();
        console.log(`✅ [HttpApiClient] 文件获取成功: ${filename}`);
        return new Uint8Array(arrayBuffer);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error(`❌ [HttpApiClient] 获取文件失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取队列状态
   * @returns {Promise<Object|null>} 队列状态或null
   */
  async getQueue() {
    try {
      console.log('📊 [HttpApiClient] 获取队列状态...');

      const response = await this.makeRequest('/queue', {
        method: 'GET'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ [HttpApiClient] 队列状态获取成功');
        return result;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error('❌ [HttpApiClient] 获取队列状态失败:', error);
      return null;
    }
  }

  /**
   * 中断当前执行
   * @returns {Promise<Object|null>} 响应结果或null
   */
  async postInterrupt() {
    try {
      console.log('🛑 [HttpApiClient] 发送中断请求...');

      const response = await this.makeRequest('/interrupt', {
        method: 'POST'
      });

      if (response.ok) {
        // interrupt API可能返回空响应
        let result = { success: true };
        try {
          const text = await response.text();
          if (text.trim()) {
            result = JSON.parse(text);
          }
        } catch (parseError) {
          // 忽略解析错误，使用默认结果
        }
        
        console.log('✅ [HttpApiClient] 中断请求发送成功');
        return result;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error('❌ [HttpApiClient] 发送中断请求失败:', error);
      return null;
    }
  }

  /**
   * 获取历史记录
   * @param {string} prompt_id - 任务ID（可选）
   * @returns {Promise<Object|null>} 历史记录或null
   */
  async getHistory(prompt_id = null) {
    try {
      const endpoint = prompt_id ? `/history/${prompt_id}` : '/history';
      console.log(`📚 [HttpApiClient] 获取历史记录: ${endpoint}`);

      const response = await this.makeRequest(endpoint, {
        method: 'GET'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ [HttpApiClient] 历史记录获取成功');

        // 🔧 关键修复：处理后端代理的响应包装
        // 后端代理返回格式: {success: true, status: 200, data: {...}}
        // 我们需要提取data字段中的实际ComfyUI历史数据
        if (result.success && result.data) {
          console.log('🔧 [HttpApiClient] 检测到后端代理包装，提取data字段');
          console.log('🔍 [HttpApiClient] 原始响应结构:', {
            hasSuccess: !!result.success,
            hasData: !!result.data,
            dataKeys: result.data ? Object.keys(result.data) : []
          });
          return result.data; // 返回实际的ComfyUI历史数据
        } else {
          // 兼容直接返回的情况
          console.log('🔧 [HttpApiClient] 直接返回历史数据（无包装）');
          return result;
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error('❌ [HttpApiClient] 获取历史记录失败:', error);
      return null;
    }
  }

  /**
   * 获取系统统计信息
   * @returns {Promise<Object|null>} 系统统计信息或null
   */
  async getSystemStats() {
    try {
      console.log('📊 [HttpApiClient] 获取系统统计信息...');

      const response = await this.makeRequest('/system_stats', {
        method: 'GET'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ [HttpApiClient] 系统统计信息获取成功');
        return result;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      console.error('❌ [HttpApiClient] 获取系统统计信息失败:', error);
      return null;
    }
  }

  /**
   * 执行HTTP请求
   * @param {string} endpoint - API端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Response>} 响应对象
   */
  async makeRequest(endpoint, options = {}) {
    // 使用ComfyUI代理端点避免CORS问题
    let proxyUrl = `/api/local/comfyui${endpoint}`;

    // 🔧 修复：为历史记录API添加serverUrl参数
    if (endpoint.includes('/history')) {
      const separator = endpoint.includes('?') ? '&' : '?';
      proxyUrl += `${separator}serverUrl=${encodeURIComponent(this.config.base_url)}`;
    }

    console.log(`🌐 [HttpApiClient] 发送请求: ${options.method || 'GET'} ${proxyUrl}`);

    // 设置超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.request_timeout);

    try {
      const requestOptions = {
        ...options,
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          ...options.headers
        }
      };

      // 只在有body时设置Content-Type
      if (options.body) {
        requestOptions.headers['Content-Type'] = 'application/json';
      }

      const response = await fetch(proxyUrl, requestOptions);

      clearTimeout(timeoutId);

      console.log(`🌐 [HttpApiClient] 响应状态: ${response.status} ${response.statusText}`);
      return response;

    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }

      console.error(`❌ [HttpApiClient] 请求失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🆕 获取图像URL（不下载数据）
   * @param {string} filename - 文件名
   * @param {string} subfolder - 子文件夹
   * @param {string} type - 文件类型
   * @returns {string} 图像URL
   */
  getImageUrl(filename, subfolder = '', type = 'output') {
    const params = new URLSearchParams({
      filename,
      type,
      serverUrl: this.config.serverUrl
    });

    if (subfolder) {
      params.append('subfolder', subfolder);
    }

    return `http://localhost:8091/api/local/comfyui/view?${params.toString()}`;
  }

  /**
   * 生成客户端ID
   * @returns {string} 客户端ID
   */
  generateClientId() {
    return 'client_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
  }

  /**
   * 检查服务器连接
   * @returns {Promise<boolean>} 是否连接成功
   */
  async checkConnection() {
    try {
      const stats = await this.getSystemStats();
      return stats !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取配置信息
   * @returns {Object} 配置信息
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log(`🌐 [HttpApiClient] 配置已更新: ${this.config.base_url}`);
  }
}
