/**
 * 通用设置重新加载服务
 * 负责在设置更改后自动重新加载相关服务配置
 */

class SettingsReloaderService {
  constructor() {
    this.serviceMap = new Map();
    this.isInitialized = false;
    this.reloadHistory = [];
  }

  /**
   * 初始化服务映射
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    console.log('🔄 [设置重载器] 初始化服务映射...');

    try {
      // 注册ComfyUI服务
      await this.registerService('comfyui', {
        settingsFile: 'comfyui-settings.json',
        settingsPath: 'comfyui',
        serviceLoader: async () => {
          const { default: comfyuiService } = await import('@/services/comfyuiImageGeneration.js');
          return comfyuiService;
        },
        reloadMethod: 'reloadSettingsFromFile',
        watchedFields: ['batchSize', 'seedValue', 'serverUrl', 'currentWorkflow', 'nodeMapping', 'defaultSettings']
      });

      // 注册LLM服务（如果存在）
      await this.registerService('llm', {
        settingsFile: 'llm-settings.json',
        settingsPath: 'llm',
        serviceLoader: async () => {
          try {
            const { default: llmService } = await import('@/services/llmService.js');
            return llmService;
          } catch (error) {
            console.warn('🔄 [设置重载器] LLM服务不存在，跳过注册');
            return null;
          }
        },
        reloadMethod: 'reloadSettings',
        watchedFields: ['apiKey', 'model', 'temperature', 'maxTokens', 'baseUrl']
      });

      // 注册其他服务...
      // 可以根据需要添加更多服务

      this.isInitialized = true;
      console.log('✅ [设置重载器] 初始化完成，已注册服务:', Array.from(this.serviceMap.keys()));

    } catch (error) {
      console.error('❌ [设置重载器] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册服务
   */
  async registerService(serviceName, config) {
    try {
      console.log(`🔄 [设置重载器] 注册服务: ${serviceName}`);
      
      // 验证配置
      if (!config.settingsFile || !config.serviceLoader) {
        throw new Error(`服务 ${serviceName} 配置无效`);
      }

      // 尝试加载服务实例
      const serviceInstance = await config.serviceLoader();
      
      if (serviceInstance) {
        this.serviceMap.set(serviceName, {
          ...config,
          serviceInstance,
          lastReload: null
        });
        console.log(`✅ [设置重载器] 服务 ${serviceName} 注册成功`);
      } else {
        console.warn(`⚠️ [设置重载器] 服务 ${serviceName} 实例为空，跳过注册`);
      }

    } catch (error) {
      console.error(`❌ [设置重载器] 注册服务 ${serviceName} 失败:`, error);
      // 不抛出错误，允许其他服务继续注册
    }
  }

  /**
   * 重新加载指定服务的设置
   */
  async reloadServiceSettings(serviceName, changedFields = []) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const serviceConfig = this.serviceMap.get(serviceName);
    if (!serviceConfig) {
      console.warn(`⚠️ [设置重载器] 未找到服务: ${serviceName}`);
      return false;
    }

    try {
      console.log(`🔄 [设置重载器] 重新加载服务设置: ${serviceName}`, {
        changedFields,
        watchedFields: serviceConfig.watchedFields
      });

      // 检查是否有监听的字段发生变化
      const shouldReload = changedFields.length === 0 || 
        changedFields.some(field => serviceConfig.watchedFields?.includes(field));

      if (!shouldReload) {
        console.log(`🔄 [设置重载器] 服务 ${serviceName} 无需重新加载，变化字段不在监听范围内`);
        return true;
      }

      // 调用服务的重新加载方法
      const reloadMethod = serviceConfig.reloadMethod || 'reloadSettings';
      const serviceInstance = serviceConfig.serviceInstance;

      if (typeof serviceInstance[reloadMethod] === 'function') {
        const success = await serviceInstance[reloadMethod]();
        
        if (success) {
          // 记录重新加载历史
          const reloadRecord = {
            serviceName,
            timestamp: new Date().toISOString(),
            changedFields,
            success: true
          };
          
          serviceConfig.lastReload = reloadRecord;
          this.reloadHistory.push(reloadRecord);
          
          console.log(`✅ [设置重载器] 服务 ${serviceName} 设置重新加载成功`);
          
          // 获取更新后的状态进行验证
          if (typeof serviceInstance.getStatus === 'function') {
            const status = serviceInstance.getStatus();
            console.log(`🔍 [设置重载器] 服务 ${serviceName} 更新后状态:`, status);
          }
          
          return true;
        } else {
          console.warn(`⚠️ [设置重载器] 服务 ${serviceName} 设置重新加载失败`);
          return false;
        }
      } else {
        console.warn(`⚠️ [设置重载器] 服务 ${serviceName} 没有 ${reloadMethod} 方法`);
        return false;
      }

    } catch (error) {
      console.error(`❌ [设置重载器] 重新加载服务 ${serviceName} 设置时出错:`, error);
      
      // 记录失败的重新加载历史
      const reloadRecord = {
        serviceName,
        timestamp: new Date().toISOString(),
        changedFields,
        success: false,
        error: error.message
      };
      
      this.reloadHistory.push(reloadRecord);
      return false;
    }
  }

  /**
   * 重新加载所有服务的设置
   */
  async reloadAllServiceSettings(changedFields = []) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log('🔄 [设置重载器] 重新加载所有服务设置', { changedFields });

    const results = {};
    const serviceNames = Array.from(this.serviceMap.keys());

    for (const serviceName of serviceNames) {
      try {
        results[serviceName] = await this.reloadServiceSettings(serviceName, changedFields);
      } catch (error) {
        console.error(`❌ [设置重载器] 重新加载服务 ${serviceName} 失败:`, error);
        results[serviceName] = false;
      }
    }

    const successCount = Object.values(results).filter(Boolean).length;
    console.log(`🔄 [设置重载器] 批量重新加载完成: ${successCount}/${serviceNames.length} 个服务成功`);

    return results;
  }

  /**
   * 根据设置文件自动重新加载相关服务
   */
  async reloadBySettingsFile(settingsFileName, changedFields = []) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log(`🔄 [设置重载器] 根据设置文件重新加载: ${settingsFileName}`, { changedFields });

    // 查找使用该设置文件的服务
    const relatedServices = [];
    for (const [serviceName, config] of this.serviceMap.entries()) {
      if (config.settingsFile === settingsFileName) {
        relatedServices.push(serviceName);
      }
    }

    if (relatedServices.length === 0) {
      console.log(`🔄 [设置重载器] 没有找到使用设置文件 ${settingsFileName} 的服务`);
      return {};
    }

    console.log(`🔄 [设置重载器] 找到相关服务:`, relatedServices);

    const results = {};
    for (const serviceName of relatedServices) {
      results[serviceName] = await this.reloadServiceSettings(serviceName, changedFields);
    }

    return results;
  }

  /**
   * 获取重新加载历史
   */
  getReloadHistory(limit = 10) {
    return this.reloadHistory.slice(-limit);
  }

  /**
   * 获取服务状态
   */
  getServiceStatus() {
    const status = {};
    for (const [serviceName, config] of this.serviceMap.entries()) {
      status[serviceName] = {
        registered: true,
        lastReload: config.lastReload,
        watchedFields: config.watchedFields,
        hasReloadMethod: typeof config.serviceInstance[config.reloadMethod] === 'function'
      };
    }
    return status;
  }

  /**
   * 清理重新加载历史
   */
  clearReloadHistory() {
    this.reloadHistory = [];
    console.log('🔄 [设置重载器] 重新加载历史已清理');
  }
}

// 创建单例实例
const settingsReloader = new SettingsReloaderService();

export default settingsReloader;
