/**
 * 数据合并和分拆功能模块
 * 专门处理行数据的合并和分拆逻辑
 */

import { ref } from 'vue';

export function useMergeAndSplit() {
  // 状态管理
  const isProcessing = ref(false);
  const lastOperation = ref(null);

  /**
   * 数据处理器接口
   * 为不同类型的数据提供统一的处理接口
   */
  class DataProcessor {
    constructor(type) {
      this.type = type;
    }

    // 合并时的数据继承逻辑
    merge(prevData, currentData) {
      // 避免 ESLint 未使用参数警告
      console.log(`${this.type} processor merge called with:`, { prevData, currentData });
      throw new Error(`${this.type} processor must implement merge method`);
    }

    // 分拆时的数据恢复逻辑
    split(mergedData) {
      // 避免 ESLint 未使用参数警告
      console.log(`${this.type} processor split called with:`, mergedData);
      throw new Error(`${this.type} processor must implement split method`);
    }

    // 数据验证
    validate(data) {
      // 基础验证，子类可以重写
      return data != null;
    }

    // 深拷贝数据
    cloneData(data) {
      return JSON.parse(JSON.stringify(data));
    }
  }

  /**
   * 文本数据处理器
   */
  class TextDataProcessor extends DataProcessor {
    constructor() {
      super('text');
    }

    merge(prevData, currentData) {
      // 简单文本合并：用换行符连接
      return {
        value: prevData.value + '\n' + currentData.value,
        reason: '文本合并：换行符连接'
      };
    }

    split(mergedData) {
      // 按换行符分割文本
      return mergedData.split('\n');
    }
  }

  /**
   * 标签数据处理器 - 修复版本，恢复正确的 Tags 逻辑
   */
  class TagsDataProcessor extends DataProcessor {
    constructor() {
      super('tags');
    }

    merge(prevData, currentData) {
      const prevTags = prevData || [];
      const currentTags = currentData || [];

      let finalTags, reason;

      // 🔥 修复：恢复正确的 Tags 合并逻辑
      // 只有目标行没有选择 Tags 时，才显示源行的 Tags
      if (prevTags.length === 0 && currentTags.length > 0) {
        finalTags = [...currentTags];
        reason = '数据继承：目标行无标签，继承源行标签';
      } else if (prevTags.length > 0) {
        // 🔥 关键修复：如果目标行已经有 Tags，则保留目标行的 Tags，不合并源行的 Tags
        finalTags = [...prevTags];
        reason = '保留目标行原有标签（不合并源行标签）';
      } else {
        // 两行都无标签
        finalTags = [];
        reason = '两行都无标签，保持空状态';
      }

      return { value: finalTags, reason };
    }

    split(mergedData) {
      // 🔥 修复：Tags 分拆时返回原始数据
      // 每行恢复到自己的原始 Tags 状态
      return Array.isArray(mergedData) ? [...mergedData] : [];
    }
  }

  /**
   * 关键词数据处理器
   */
  class KeywordsDataProcessor extends DataProcessor {
    constructor() {
      super('keywords');
    }

    merge(prevData, currentData) {
      const prevKeywords = prevData.keywords || '';
      const currentKeywords = currentData.keywords || '';
      const prevMetadata = prevData.metadata || { autoContent: '', manualContent: '', lastManualEdit: 0 };
      const currentMetadata = currentData.metadata || { autoContent: '', manualContent: '', lastManualEdit: 0 };

      let finalKeywords, finalMetadata, reason;

      if (!prevKeywords && currentKeywords) {
        finalKeywords = currentKeywords;
        finalMetadata = { ...currentMetadata };
        reason = '数据继承：目标行无关键词，继承源行关键词';
      } else if (prevKeywords && currentKeywords) {
        finalKeywords = prevKeywords;
        finalMetadata = {
          autoContent: prevMetadata.autoContent || currentMetadata.autoContent || '',
          manualContent: prevMetadata.manualContent || currentMetadata.manualContent || '',
          lastManualEdit: Math.max(prevMetadata.lastManualEdit || 0, currentMetadata.lastManualEdit || 0)
        };
        reason = '两行都有关键词，保留目标行关键词，合并元数据';
      } else if (prevKeywords && !currentKeywords) {
        finalKeywords = prevKeywords;
        finalMetadata = { ...prevMetadata };
        reason = '保留目标行原有关键词';
      } else {
        finalKeywords = '';
        finalMetadata = {
          autoContent: prevMetadata.autoContent || currentMetadata.autoContent || '',
          manualContent: prevMetadata.manualContent || currentMetadata.manualContent || '',
          lastManualEdit: Math.max(prevMetadata.lastManualEdit || 0, currentMetadata.lastManualEdit || 0)
        };
        reason = '两行都无关键词，保持空状态，合并元数据';
      }

      return {
        value: { keywords: finalKeywords, metadata: finalMetadata },
        reason
      };
    }

    split(mergedData) {
      return {
        keywords: mergedData.keywords || '',
        metadata: JSON.parse(JSON.stringify(mergedData.metadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }))
      };
    }
  }

  /**
   * 图片数据处理器 - 修复版本，完全遵循 Keywords 成功模式
   */
  class ImageDataProcessor extends DataProcessor {
    constructor() {
      super('image');
      this.defaultImage = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    }

    // 🔥 修复：完全遵循 Keywords 的判断模式
    hasImageData(data) {
      // 🔥 关键修复：像 Keywords 一样，先提取原始值，再判断
      const imageSrc = data.imageSrc;
      // 简单明确的判断：有值且不是默认图片
      return imageSrc && imageSrc !== this.defaultImage;
    }

    merge(prevData, currentData) {
      // 🔥 关键修复：完全遵循 Keywords 的数据提取模式
      // Keywords: const prevKeywords = prevData.keywords || '';
      // Image: 保持原始值，不要过早转换
      const prevImageSrc = prevData.imageSrc;
      const currentImageSrc = currentData.imageSrc;
      const prevImageAlt = prevData.imageAlt || '';
      const currentImageAlt = currentData.imageAlt || '';
      const prevIsImageLocked = prevData.isImageLocked || false;
      const currentIsImageLocked = currentData.isImageLocked || false;

      // 🔥 修复：使用与 Keywords 完全相同的判断逻辑
      const prevHasImage = this.hasImageData(prevData);
      const currentHasImage = this.hasImageData(currentData);

      let finalImageSrc, finalImageAlt, finalIsImageLocked, reason;

      if (!prevHasImage && currentHasImage) {
        // 数据继承：目标行无图片，继承源行图片
        finalImageSrc = currentImageSrc;
        finalImageAlt = currentImageAlt;
        finalIsImageLocked = currentIsImageLocked;
        reason = '数据继承：目标行无图片，继承源行图片';
      } else if (prevHasImage && currentHasImage) {
        // 两行都有图片：保留目标行图片，合并元数据
        finalImageSrc = prevImageSrc;
        finalImageAlt = prevImageAlt || currentImageAlt; // 智能合并 alt 文本
        finalIsImageLocked = prevIsImageLocked || currentIsImageLocked; // 智能合并锁定状态
        reason = '两行都有图片，保留目标行图片，合并元数据';
      } else if (prevHasImage && !currentHasImage) {
        // 保留目标行原有图片
        finalImageSrc = prevImageSrc;
        finalImageAlt = prevImageAlt;
        finalIsImageLocked = prevIsImageLocked;
        reason = '保留目标行原有图片';
      } else {
        // 两行都无图片：保持默认状态，但智能合并元数据
        finalImageSrc = this.defaultImage;
        finalImageAlt = prevImageAlt || currentImageAlt; // 保留任何存在的 alt 文本
        finalIsImageLocked = prevIsImageLocked || currentIsImageLocked; // 保留任何锁定状态
        reason = '两行都无图片，保持默认状态，合并元数据';
      }

      return {
        value: {
          imageSrc: finalImageSrc,
          imageAlt: finalImageAlt,
          isImageLocked: finalIsImageLocked
        },
        reason
      };
    }

    // 🔥 修复：完全遵循 Keywords 的分拆模式
    split(mergedData) {
      // 🔥 关键修复：像 Keywords 一样，直接返回存储的值或默认值
      return {
        imageSrc: mergedData.imageSrc || this.defaultImage,
        imageAlt: mergedData.imageAlt || '',
        isImageLocked: mergedData.isImageLocked || false
      };
    }
  }

  /**
   * 缩略图数据处理器 - 修复版本，完全遵循 Keywords 成功模式
   */
  class ThumbnailsDataProcessor extends DataProcessor {
    constructor() {
      super('thumbnails');
    }

    // 🔥 修复：完全遵循 Keywords 的判断模式
    hasThumbnailData(data) {
      // 🔥 关键修复：像 Keywords 一样，先提取原始值，再判断
      const thumbnails = data.thumbnails;
      // 简单明确的判断：是数组且长度大于0
      return Array.isArray(thumbnails) && thumbnails.length > 0;
    }

    // 🔥 修复：安全的缩略图清理，不修改原始数据
    cleanThumbnails(thumbnails) {
      if (!Array.isArray(thumbnails)) return [];

      return thumbnails.map(thumb => {
        if (thumb && typeof thumb === 'object') {
          // 🔥 关键修复：创建深拷贝，避免修改原始数据
          const cleanThumb = JSON.parse(JSON.stringify(thumb));
          cleanThumb.isMain = false; // 确保缩略图不是主图
          return cleanThumb;
        }
        return thumb;
      });
    }

    // 🔥 新增：缩略图去重函数
    deduplicateThumbnails(thumbnails) {
      if (!Array.isArray(thumbnails)) return [];

      const seen = new Set();
      return thumbnails.filter(thumb => {
        if (!thumb || typeof thumb !== 'object' || !thumb.src) {
          return false; // 过滤无效的缩略图
        }

        // 使用 src 和 alt 的组合作为唯一标识
        const key = `${thumb.src}|${thumb.alt || ''}`;
        if (seen.has(key)) {
          return false; // 重复的缩略图，过滤掉
        }

        seen.add(key);
        return true;
      });
    }

    merge(prevData, currentData) {
      // 🔥 关键修复：完全遵循 Keywords 的数据提取模式
      // Keywords: const prevKeywords = prevData.keywords || '';
      // Thumbnails: 保持原始值，不要过早转换
      const prevThumbnails = prevData.thumbnails;
      const currentThumbnails = currentData.thumbnails;
      const prevGeneratedImages = prevData.generatedImages;
      const currentGeneratedImages = currentData.generatedImages;

      // 🔥 修复：使用与 Keywords 完全相同的判断逻辑
      const prevHasThumbnails = this.hasThumbnailData(prevData);
      const currentHasThumbnails = this.hasThumbnailData(currentData);

      let finalThumbnails, finalGeneratedImages, reason;

      if (!prevHasThumbnails && currentHasThumbnails) {
        // 数据继承：目标行无缩略图，继承源行缩略图
        finalThumbnails = this.cleanThumbnails(currentThumbnails || []);
        finalGeneratedImages = Array.isArray(currentGeneratedImages) ? [...currentGeneratedImages] : [];
        reason = '数据继承：目标行无缩略图，继承源行缩略图';
      } else if (prevHasThumbnails && currentHasThumbnails) {
        // 两行都有缩略图：智能合并，遵循 Keywords 的合并元数据模式
        const safePrevThumbnails = Array.isArray(prevThumbnails) ? prevThumbnails : [];
        const safeCurrentThumbnails = Array.isArray(currentThumbnails) ? currentThumbnails : [];

        // 🔥 关键修复：先合并再去重，防止重复缩略图
        const mergedThumbnails = [...safePrevThumbnails, ...safeCurrentThumbnails];
        const deduplicatedThumbnails = this.deduplicateThumbnails(mergedThumbnails);
        finalThumbnails = this.cleanThumbnails(deduplicatedThumbnails);

        const safePrevGenerated = Array.isArray(prevGeneratedImages) ? prevGeneratedImages : [];
        const safeCurrentGenerated = Array.isArray(currentGeneratedImages) ? currentGeneratedImages : [];
        finalGeneratedImages = [...safePrevGenerated, ...safeCurrentGenerated];
        reason = `两行都有缩略图，智能合并并去重（${mergedThumbnails.length} → ${finalThumbnails.length}）`;
      } else if (prevHasThumbnails && !currentHasThumbnails) {
        // 保留目标行原有缩略图
        finalThumbnails = this.cleanThumbnails(prevThumbnails || []);

        // 🔥 修复：仍然智能合并生成图片（类似 Keywords 的元数据合并）
        const safePrevGenerated = Array.isArray(prevGeneratedImages) ? prevGeneratedImages : [];
        const safeCurrentGenerated = Array.isArray(currentGeneratedImages) ? currentGeneratedImages : [];
        finalGeneratedImages = [...safePrevGenerated, ...safeCurrentGenerated];
        reason = '保留目标行原有缩略图，合并生成图片';
      } else {
        // 两行都无缩略图：保持空状态，但智能合并生成图片
        finalThumbnails = [];

        const safePrevGenerated = Array.isArray(prevGeneratedImages) ? prevGeneratedImages : [];
        const safeCurrentGenerated = Array.isArray(currentGeneratedImages) ? currentGeneratedImages : [];
        finalGeneratedImages = [...safePrevGenerated, ...safeCurrentGenerated];
        reason = '两行都无缩略图，保持空状态，合并生成图片';
      }

      return {
        value: {
          thumbnails: finalThumbnails,
          generatedImages: finalGeneratedImages
        },
        reason
      };
    }

    // 🔥 修复：完全遵循 Keywords 的分拆模式
    split(mergedData) {
      // 🔥 关键修复：像 Keywords 一样，直接返回存储的值或默认值
      return {
        thumbnails: Array.isArray(mergedData.thumbnails) ? [...mergedData.thumbnails] : [],
        generatedImages: Array.isArray(mergedData.generatedImages) ? [...mergedData.generatedImages] : []
      };
    }
  }

  // 创建数据处理器实例
  const processors = {
    text: new TextDataProcessor(),
    tags: new TagsDataProcessor(),
    keywords: new KeywordsDataProcessor(),
    image: new ImageDataProcessor(),
    thumbnails: new ThumbnailsDataProcessor()
  };

  /**
   * 🔥 新增：统一的数据状态检测函数
   * 根据用户需求定义："有数据"意味着包含任何非空的 selectedTags、keywords、imageSrc 或 thumbnails
   */
  function hasAnyData(row) {
    // 检查标签
    const hasTags = Array.isArray(row.selectedTags) && row.selectedTags.length > 0;

    // 检查关键词
    const hasKeywords = row.keywords && row.keywords.trim().length > 0;

    // 检查图片（非默认图片）
    const hasImage = row.imageSrc && row.imageSrc !== processors.image.defaultImage;

    // 检查缩略图
    const hasThumbnails = Array.isArray(row.thumbnails) && row.thumbnails.length > 0;

    return hasTags || hasKeywords || hasImage || hasThumbnails;
  }

  /**
   * 🔥 新增：详细的数据状态分析函数（用于调试）
   */
  function analyzeDataState(row, label = '') {
    const state = {
      hasTags: Array.isArray(row.selectedTags) && row.selectedTags.length > 0,
      hasKeywords: row.keywords && row.keywords.trim().length > 0,
      hasImage: row.imageSrc && row.imageSrc !== processors.image.defaultImage,
      hasThumbnails: Array.isArray(row.thumbnails) && row.thumbnails.length > 0,
      hasAnyData: hasAnyData(row)
    };

    if (process.env.NODE_ENV === 'development' && label) {
      const imageStatus = row.imageSrc ? (row.imageSrc === processors.image.defaultImage ? '默认图片' : '有图片') : '无图片';
      console.log(`[DataState] ${label}:`, {
        ...state,
        tagsCount: row.selectedTags?.length || 0,
        keywordsLength: row.keywords?.length || 0,
        thumbnailsCount: row.thumbnails?.length || 0,
        imageSrc: imageStatus
      });
    }

    return state;
  }

  /**
   * 🔥 新增：获取16种场景的描述
   */
  function getScenarioDescription(scenarioId) {
    const scenarios = {
      1: 'isMerged=false+无数据 → isMerged=false+无数据',
      2: 'isMerged=false+无数据 → isMerged=true+无数据',
      3: 'isMerged=false+无数据 → isMerged=false+有数据',
      4: 'isMerged=false+无数据 → isMerged=true+有数据',
      5: 'isMerged=false+有数据 → isMerged=false+无数据',
      6: 'isMerged=false+有数据 → isMerged=true+无数据',
      7: 'isMerged=false+有数据 → isMerged=false+有数据',
      8: 'isMerged=false+有数据 → isMerged=true+有数据',
      9: 'isMerged=true+无数据 → isMerged=false+无数据',
      10: 'isMerged=true+无数据 → isMerged=true+无数据',
      11: 'isMerged=true+无数据 → isMerged=false+有数据',
      12: 'isMerged=true+无数据 → isMerged=true+有数据',
      13: 'isMerged=true+有数据 → isMerged=false+无数据',
      14: 'isMerged=true+有数据 → isMerged=true+无数据',
      15: 'isMerged=true+有数据 → isMerged=false+有数据',
      16: 'isMerged=true+有数据 → isMerged=true+有数据'
    };
    return scenarios[scenarioId] || `未知场景${scenarioId}`;
  }

  /**
   * 统一的数据处理接口
   */
  function processColumnData(columnType, prevData, currentData, operation = 'merge') {
    const processor = processors[columnType];
    if (!processor) {
      throw new Error(`Unknown column type: ${columnType}`);
    }

    if (operation === 'merge') {
      return processor.merge(prevData, currentData);
    } else if (operation === 'split') {
      return processor.split(prevData);
    } else {
      throw new Error(`Unknown operation: ${operation}`);
    }
  }

  /**
   * 向上合并字幕行
   * @param {Number} index 当前行索引
   * @param {Array} rows 所有字幕行数据
   * @param {Function} showErrorMessage 错误消息显示函数
   * @param {Function} showSuccessMessage 成功消息显示函数
   * @returns {Object} 处理结果，包含更新后的行数据
   */
  const mergeUp = (index, rows, showErrorMessage, showSuccessMessage) => {
    // 验证参数
    if (index <= 0 || !rows || rows.length < 2) {
      showErrorMessage('无法合并第一行或数据不完整');
      return { success: false, rows };
    }

    isProcessing.value = true;
    lastOperation.value = { type: 'merge', index, timestamp: Date.now() };

    try {
      // 创建行数据的深拷贝
      const newRows = JSON.parse(JSON.stringify(rows));
      const currentRow = newRows[index];
      const prevRow = newRows[index - 1];

      // 确保两行都有 originalIndex
      if (!currentRow.originalIndex) {
        currentRow.originalIndex = currentRow.index || index;
      }
      if (!prevRow.originalIndex) {
        prevRow.originalIndex = prevRow.index || (index - 1);
      }

      // 🔥 关键修复：确保目标行的原始数据被正确保存
      // 当目标行是已合并行时，需要确保其 mergedRows 中包含了正确的原始数据
      if (!prevRow.mergedRows || prevRow.mergedRows.length === 0) {
        // 🔥 调试：记录目标行原始数据保存
        if (process.env.NODE_ENV === 'development') {
          console.log('[MergeUp] 🔥 保存目标行原始数据:', {
            originalIndex: prevRow.originalIndex,
            原始图片: prevRow.imageSrc && prevRow.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
            原始imageAlt: prevRow.imageAlt || '无',
            原始isImageLocked: prevRow.isImageLocked || false,
            原始标签: prevRow.selectedTags?.length || 0
          });
        }

        // 目标行是普通行，创建其 mergedRows
        prevRow.mergedRows = [createMergedRowItem(prevRow)];
      } else {
        // 🔥 修复：目标行已经是合并行，验证其原始数据是否正确保存
        const targetOriginalItem = prevRow.mergedRows.find(item => item.originalIndex === prevRow.originalIndex);
        if (!targetOriginalItem) {
          // 如果没有找到目标行的原始数据，需要重新创建
          console.warn('[MergeUp] 🔥 目标行缺少原始数据，重新创建');
          prevRow.mergedRows.unshift(createMergedRowItem({
            ...prevRow,
            // 🔥 重要：使用目标行的第一个 mergedRows 项作为原始数据源
            ...(prevRow.mergedRows[0] || {})
          }));
        }
      }

      // 🔥 修复：保存源行的原始数据，用于智能继承处理
      let currentRowOriginalData = currentRow;

      // 添加当前行到 prevRow 的 mergedRows
      if (currentRow.mergedRows && currentRow.mergedRows.length > 0) {
        // 🔥 调试：记录合并行的处理过程
        if (process.env.NODE_ENV === 'development') {
          console.log('[MergeUp] 🔥 处理合并源行:', {
            originalIndex: currentRow.originalIndex,
            mergedRows数量: currentRow.mergedRows.length,
            当前显示图片: currentRow.imageSrc && currentRow.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
            第一个mergedRows项图片: currentRow.mergedRows[0]?.imageSrc && currentRow.mergedRows[0]?.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
            第一个mergedRows项imageSrc: currentRow.mergedRows[0]?.imageSrc,
            第一个mergedRows项imageAlt: currentRow.mergedRows[0]?.imageAlt || '无',
            第一个mergedRows项isImageLocked: currentRow.mergedRows[0]?.isImageLocked || false
          });
        }

        // 🔥 关键修复：如果源行是已合并行，需要保存其所有原始项目
        // 但是要修复可能损坏的图片和缩略图数据
        const correctedMergedRows = currentRow.mergedRows.map(item => {
          // 检查是否需要修复这个 mergedRows 项的数据
          if (item.originalIndex === currentRow.originalIndex) {
            const needImageFix = item.imageSrc === processors.image.defaultImage &&
                                currentRow.imageSrc && currentRow.imageSrc !== processors.image.defaultImage;
            const needThumbnailsFix = (!item.thumbnails || item.thumbnails.length === 0) &&
                                     currentRow.thumbnails && currentRow.thumbnails.length > 0;

            if (needImageFix || needThumbnailsFix) {
              if (process.env.NODE_ENV === 'development') {
                console.log('[MergeUp] 🔥 修复损坏的 mergedRows 数据:', {
                  originalIndex: item.originalIndex,
                  修复图片: needImageFix ? '是' : '否',
                  修复缩略图: needThumbnailsFix ? '是' : '否',
                  原始损坏imageSrc: needImageFix ? item.imageSrc : '无需修复',
                  修复后imageSrc: needImageFix ? currentRow.imageSrc : item.imageSrc,
                  原始缩略图数量: item.thumbnails?.length || 0,
                  修复后缩略图数量: needThumbnailsFix ? currentRow.thumbnails.length : (item.thumbnails?.length || 0)
                });
              }

              return {
                ...item,
                // 修复图片数据
                imageSrc: needImageFix ? currentRow.imageSrc : item.imageSrc,
                imageAlt: needImageFix ? (currentRow.imageAlt || '') : item.imageAlt,
                isImageLocked: needImageFix ? (currentRow.isImageLocked || false) : item.isImageLocked,
                // 修复缩略图数据
                thumbnails: needThumbnailsFix ? JSON.parse(JSON.stringify(currentRow.thumbnails)) : item.thumbnails,
                generatedImages: needThumbnailsFix ? JSON.parse(JSON.stringify(currentRow.generatedImages || [])) : item.generatedImages
              };
            }
          }
          return item;
        });

        prevRow.mergedRows = prevRow.mergedRows.concat(correctedMergedRows);

        // 🔥 关键修复：对于已合并行，优先使用当前显示的数据作为智能继承的源数据
        // 因为 mergedRows[0] 中的数据可能在之前的操作中被损坏
        const firstMergedItem = currentRow.mergedRows[0];
        if (firstMergedItem) {
          // 🔥 修复：优先使用当前显示的图片数据，如果当前显示是默认图片才使用 mergedRows 中的数据
          const useCurrentImageData = currentRow.imageSrc && currentRow.imageSrc !== processors.image.defaultImage;

          currentRowOriginalData = {
            ...currentRow,
            selectedTags: firstMergedItem.selectedTags,
            keywords: firstMergedItem.keywords,
            keywordsMetadata: firstMergedItem.keywordsMetadata,
            // 🔥 关键修复：图片数据优先使用当前显示的数据
            imageSrc: useCurrentImageData ? currentRow.imageSrc : firstMergedItem.imageSrc,
            imageAlt: useCurrentImageData ? currentRow.imageAlt : firstMergedItem.imageAlt,
            isImageLocked: useCurrentImageData ? currentRow.isImageLocked : firstMergedItem.isImageLocked,
            // 🔥 关键修复：缩略图数据也优先使用当前显示的数据
            thumbnails: (currentRow.thumbnails && currentRow.thumbnails.length > 0) ? currentRow.thumbnails : firstMergedItem.thumbnails,
            generatedImages: (currentRow.generatedImages && currentRow.generatedImages.length > 0) ? currentRow.generatedImages : firstMergedItem.generatedImages
          };

          // 🔥 调试：验证智能继承的源数据
          if (process.env.NODE_ENV === 'development') {
            console.log('[MergeUp] 🔥 合并行智能继承源数据（修复后）:', {
              originalIndex: currentRowOriginalData.originalIndex,
              使用当前显示图片: useCurrentImageData ? '是' : '否',
              当前显示imageSrc: currentRow.imageSrc,
              mergedRows中imageSrc: firstMergedItem.imageSrc,
              最终智能继承图片: currentRowOriginalData.imageSrc && currentRowOriginalData.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
              最终智能继承imageSrc: currentRowOriginalData.imageSrc,
              最终智能继承imageAlt: currentRowOriginalData.imageAlt || '无',
              最终智能继承isImageLocked: currentRowOriginalData.isImageLocked || false,
              // 🔥 新增：缩略图数据验证
              当前显示缩略图数量: currentRow.thumbnails?.length || 0,
              mergedRows中缩略图数量: firstMergedItem.thumbnails?.length || 0,
              最终智能继承缩略图数量: currentRowOriginalData.thumbnails?.length || 0,
              最终智能继承生成图片数量: currentRowOriginalData.generatedImages?.length || 0
            });
          }
        }
      } else {
        // 🔥 关键修复：源行是普通行，直接保存其数据
        // 🔥 调试：记录源行原始数据（保存前）
        if (process.env.NODE_ENV === 'development') {
          console.log('[MergeUp] 🔥 源行原始数据（保存前）:', {
            originalIndex: currentRow.originalIndex,
            原始图片: currentRow.imageSrc && currentRow.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
            原始imageSrc: currentRow.imageSrc,
            原始imageAlt: currentRow.imageAlt || '无',
            原始isImageLocked: currentRow.isImageLocked || false,
            原始标签: currentRow.selectedTags?.length || 0
          });
        }

        const sourceItem = createMergedRowItem(currentRow);
        prevRow.mergedRows.push(sourceItem);

        // 🔥 调试：验证源行数据保存（保存后）
        if (process.env.NODE_ENV === 'development') {
          console.log('[MergeUp] 🔥 普通源行数据保存（保存后）:', {
            originalIndex: sourceItem.originalIndex,
            保存的图片: sourceItem.imageSrc && sourceItem.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
            保存的imageSrc: sourceItem.imageSrc,
            保存的imageAlt: sourceItem.imageAlt || '无',
            保存的isImageLocked: sourceItem.isImageLocked || false,
            数据完整性验证: sourceItem.imageSrc === currentRow.imageSrc ? '✅ 正确' : '❌ 错误'
          });
        }
      }

      // 按原始索引排序合并项
      prevRow.mergedRows.sort((a, b) =>
        (a.originalIndex !== undefined && b.originalIndex !== undefined)
          ? a.originalIndex - b.originalIndex
          : (a.index || 0) - (b.index || 0)
      );

      // 🔥 新增：使用统一的数据状态检测进行场景分析（简化版）
      // const targetHasData = hasAnyData(prevRow);
      // const sourceHasData = hasAnyData(currentRowOriginalData);
      // const targetIsMerged = prevRow.isMerged || false;
      // const sourceIsMerged = currentRow.isMerged || false;

      // 🔥 修复：使用源行的原始数据进行智能继承处理
      const mergeResults = processAllColumns(prevRow, currentRowOriginalData);

      // 应用合并结果
      applyMergeResults(prevRow, mergeResults);

      // 同步数据到 mergedRows
      syncDataToMergedRows(prevRow, mergeResults);

      // 设置合并标记
      prevRow.isMerged = true;
      prevRow.mergedRowsCount = prevRow.mergedRows.length;

      // 更新合并后行的显示内容
      updateMergedRowDisplay(prevRow);

      // 更新时间范围
      updateTimeRange(prevRow);

      // 🔥 关键修复：清理目标行数据，移除不必要字段（如 isInferring 等）
      const cleanedPrevRow = cleanRowData(prevRow);
      Object.keys(prevRow).forEach(key => delete prevRow[key]);
      Object.assign(prevRow, cleanedPrevRow);

      // 从数组中移除当前行
      newRows.splice(index, 1);

      // 重新编号所有行
      renumberRows(newRows);

      showSuccessMessage('合并成功', `第${index + 1}行已与上一行合并`);
      return { success: true, rows: newRows };

    } catch (error) {
      console.error('合并行时出错:', error);
      showErrorMessage('合并失败: ' + error.message);
      return { success: false, rows };
    } finally {
      isProcessing.value = false;
    }
  };

  /**
   * 向下拆分已合并的字幕行
   * @param {Number} index 当前行索引
   * @param {Array} rows 所有字幕行数据
   * @param {Function} showErrorMessage 错误消息显示函数
   * @param {Function} showSuccessMessage 成功消息显示函数
   * @returns {Object} 处理结果，包含更新后的行数据
   */
  const splitDown = (index, rows, showErrorMessage, showSuccessMessage) => {
    if (index < 0 || !rows || rows.length === 0) {
      showErrorMessage('无效的索引或数据不完整');
      return { success: false, rows };
    }

    const currentRow = rows[index];

    // 检查是否是合并行
    if (!currentRow.isMerged || !currentRow.mergedRows || currentRow.mergedRows.length <= 1) {
      showErrorMessage('此行不是合并行或没有足够的合并项可分拆');
      return { success: false, rows };
    }

    isProcessing.value = true;
    lastOperation.value = { type: 'split', index, timestamp: Date.now() };

    try {
      // 创建行数据的深拷贝
      const newRows = JSON.parse(JSON.stringify(rows));
      const targetRow = newRows[index];

      // 确保 mergedRows 按 originalIndex 排序
      targetRow.mergedRows.sort((a, b) =>
        (a.originalIndex !== undefined && b.originalIndex !== undefined)
          ? a.originalIndex - b.originalIndex
          : (a.index || 0) - (b.index || 0)
      );

      const allMergedItems = [...targetRow.mergedRows];
      const firstItem = allMergedItems[0];
      const itemsToRestore = allMergedItems.slice(1);

      // 🔥 关键修复：恢复第一个项目为当前行
      // restoreRowFromMergedItem 现在已经完全清理并重建了 targetRow
      restoreRowFromMergedItem(targetRow, firstItem);

      // 🔥 注意：restoreRowFromMergedItem 已经完全清理了 targetRow，
      // 所以不需要额外的清理步骤

      // 创建独立行
      const rowsToInsert = itemsToRestore.map(item => {
        const newRow = createIndependentRow(item);

        // 🔥 关键修复：清理新创建的独立行数据
        return cleanRowData(newRow);
      });

      // 插入新行到当前位置之后
      newRows.splice(index + 1, 0, ...rowsToInsert);

      // 重新编号所有行
      renumberRows(newRows);

      showSuccessMessage('分拆完成', `已将合并行分拆为 ${newRows.length - rows.length + 1} 行`);
      return { success: true, rows: newRows };

    } catch (error) {
      console.error('分拆过程中发生错误:', error);
      showErrorMessage('分拆过程中发生错误: ' + error.message);
      return { success: false, rows };
    } finally {
      isProcessing.value = false;
    }
  };

  /**
   * 辅助函数：创建 mergedRows 项
   * 🔥 修复：确保完全遵循 Keywords 的数据保存模式，并防止不必要字段的添加
   */
  function createMergedRowItem(row) {
    // 🔥 关键修复：只保存必要的字段，防止数据污染
    const cleanedRow = {
      // 基础字段
      originalIndex: row.originalIndex,
      index: row.index,
      description: row.description || '',
      startTime: row.startTime,
      endTime: row.endTime,
      duration: row.duration,

      // 🔥 修复：标签数据保存 - 遵循 Keywords 模式
      selectedTags: Array.isArray(row.selectedTags) ? [...row.selectedTags] : [],

      // 🔥 修复：关键词数据保存 - 遵循 Keywords 模式
      keywords: row.keywords || '',
      keywordsMetadata: row.keywordsMetadata ?
        JSON.parse(JSON.stringify(row.keywordsMetadata)) :
        { autoContent: '', manualContent: '', lastManualEdit: 0 },

      // 🔥 关键修复：图片数据保存 - 确保所有图片相关字段都被正确保存
      imageSrc: row.imageSrc || processors.image.defaultImage,
      imageAlt: row.imageAlt || '',
      isImageLocked: row.isImageLocked || false,

      // 🔥 关键修复：缩略图数据保存 - 深拷贝确保数据安全
      thumbnails: Array.isArray(row.thumbnails) ?
        JSON.parse(JSON.stringify(row.thumbnails)) : [],
      generatedImages: Array.isArray(row.generatedImages) ?
        JSON.parse(JSON.stringify(row.generatedImages)) : [],

      // 其他必要字段
      tags: Array.isArray(row.tags) ? [...row.tags] : [],
      isSelected: row.isSelected || false,
      isMerged: false
    };

    // 🔥 新增：调试验证 - 确保图片数据被正确保存
    if (process.env.NODE_ENV === 'development') {
      console.log('[createMergedRowItem] 🔥 数据保存验证:', {
        originalIndex: cleanedRow.originalIndex,
        原始imageSrc: row.imageSrc,
        保存的imageSrc: cleanedRow.imageSrc,
        原始imageAlt: row.imageAlt,
        保存的imageAlt: cleanedRow.imageAlt,
        原始isImageLocked: row.isImageLocked,
        保存的isImageLocked: cleanedRow.isImageLocked,
        原始thumbnails数量: Array.isArray(row.thumbnails) ? row.thumbnails.length : 0,
        保存的thumbnails数量: cleanedRow.thumbnails.length,
        图片数据完整性: (cleanedRow.imageSrc === (row.imageSrc || processors.image.defaultImage)) ? '✅' : '❌'
      });
    }

    return cleanedRow;
  }

  /**
   * 辅助函数：处理所有列的数据合并
   */
  function processAllColumns(prevRow, currentRow) {
    const results = {};

    // 处理标签
    results.tags = processColumnData('tags', prevRow.selectedTags, currentRow.selectedTags);

    // 处理关键词
    results.keywords = processColumnData('keywords',
      { keywords: prevRow.keywords, metadata: prevRow.keywordsMetadata },
      { keywords: currentRow.keywords, metadata: currentRow.keywordsMetadata }
    );

    // 处理图片
    results.image = processColumnData('image',
      { imageSrc: prevRow.imageSrc, imageAlt: prevRow.imageAlt, isImageLocked: prevRow.isImageLocked },
      { imageSrc: currentRow.imageSrc, imageAlt: currentRow.imageAlt, isImageLocked: currentRow.isImageLocked }
    );

    // 处理缩略图
    results.thumbnails = processColumnData('thumbnails',
      { thumbnails: prevRow.thumbnails, generatedImages: prevRow.generatedImages },
      { thumbnails: currentRow.thumbnails, generatedImages: currentRow.generatedImages }
    );

    return results;
  }

  /**
   * 辅助函数：应用合并结果到目标行
   */
  function applyMergeResults(prevRow, results) {
    // 应用标签结果
    prevRow.selectedTags = results.tags.value;

    // 应用关键词结果
    prevRow.keywords = results.keywords.value.keywords;
    prevRow.keywordsMetadata = results.keywords.value.metadata;

    // 应用图片结果
    prevRow.imageSrc = results.image.value.imageSrc;
    prevRow.imageAlt = results.image.value.imageAlt;
    prevRow.isImageLocked = results.image.value.isImageLocked;

    // 应用缩略图结果
    prevRow.thumbnails = results.thumbnails.value.thumbnails;
    prevRow.generatedImages = results.thumbnails.value.generatedImages;

    // 记录处理原因（用于调试）
    if (process.env.NODE_ENV === 'development') {
      console.log('[MergeUp] 智能数据继承结果:', {
        标签处理: results.tags.reason,
        关键词处理: results.keywords.reason,
        图片处理: results.image.reason,
        缩略图处理: results.thumbnails.reason
      });
    }
  }

  /**
   * 辅助函数：同步标签数据到 mergedRows
   * 🔥 关键修复：不同步标签数据，防止数据污染
   */
  // eslint-disable-next-line no-unused-vars
  function syncTagsToMergedRows(targetItem, results) {
    // 🔥 修复：不同步标签数据到 mergedRows，保持原始数据纯净
    // mergedRows 中每个项目应该保存各自的原始标签，不应该被智能合并结果覆盖
    if (process.env.NODE_ENV === 'development') {
      console.log('[syncTagsToMergedRows] 🔥 标签同步策略（防污染版）:', {
        originalIndex: targetItem.originalIndex,
        原始标签保持不变: targetItem.selectedTags?.length || 0,
        合并结果标签数量: results.tags?.value?.length || 0,
        策略: '保持原始标签纯净，不同步智能合并结果'
      });
    }
    // 注释掉原有的同步逻辑，防止数据污染
    // if (results.tags && results.tags.value) {
    //   targetItem.selectedTags = JSON.parse(JSON.stringify(results.tags.value));
    // }
  }

  /**
   * 辅助函数：同步关键词数据到 mergedRows
   * 🔥 关键修复：不同步关键词数据，防止数据污染
   */
  // eslint-disable-next-line no-unused-vars
  function syncKeywordsToMergedRows(targetItem, results) {
    // 🔥 修复：不同步关键词数据到 mergedRows，保持原始数据纯净
    // mergedRows 中每个项目应该保存各自的原始关键词，不应该被智能合并结果覆盖
    if (process.env.NODE_ENV === 'development') {
      console.log('[syncKeywordsToMergedRows] 🔥 关键词同步策略（防污染版）:', {
        originalIndex: targetItem.originalIndex,
        原始关键词保持不变: targetItem.keywords ? '有' : '无',
        合并结果关键词: results.keywords?.value?.keywords ? '有' : '无',
        策略: '保持原始关键词纯净，不同步智能合并结果'
      });
    }
    // 注释掉原有的同步逻辑，防止数据污染
    // if (results.keywords && results.keywords.value) {
    //   targetItem.keywords = results.keywords.value.keywords || '';
    //   targetItem.keywordsMetadata = JSON.parse(JSON.stringify(results.keywords.value.metadata || {}));
    // }
  }

  /**
   * 辅助函数：同步图片数据到 mergedRows
   * 🔥 关键修复：不同步图片数据，防止数据污染
   */
  // eslint-disable-next-line no-unused-vars
  function syncImageToMergedRows(targetItem, results) {
    // 🔥 修复：不同步图片数据到 mergedRows，保持原始数据纯净
    // mergedRows 中每个项目应该保存各自的原始图片，不应该被智能合并结果覆盖
    if (process.env.NODE_ENV === 'development') {
      console.log('[syncImageToMergedRows] 🔥 图片同步策略（防污染版）:', {
        originalIndex: targetItem.originalIndex,
        原始图片保持不变: targetItem.imageSrc && targetItem.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        合并结果图片: results.image?.value?.imageSrc && results.image.value.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        策略: '保持原始图片纯净，不同步智能合并结果'
      });
    }
    // 注释掉原有的同步逻辑，防止数据污染
    // if (results.image && results.image.value) {
    //   targetItem.imageSrc = results.image.value.imageSrc;
    //   targetItem.imageAlt = results.image.value.imageAlt || '';
    //   targetItem.isImageLocked = results.image.value.isImageLocked || false;
    // }
  }

  /**
   * 辅助函数：同步缩略图数据到 mergedRows
   * 🔥 关键修复：不同步缩略图数据到目标行的 mergedRows 项，防止数据污染
   * mergedRows 中每个项目应该保存各自的原始数据，而不是合并后的混合数据
   */
  // eslint-disable-next-line no-unused-vars
  function syncThumbnailsToMergedRows(targetItem, results) {
    // 🔥 修复：不同步缩略图数据，防止数据污染
    // mergedRows 的设计原则：每行保存自己的原始数据，不应该被其他行的数据污染
    // 智能合并的结果只应该应用到目标行的显示状态，不应该覆盖 mergedRows 中的原始数据
    if (results.thumbnails && results.thumbnails.value) {
      // 🔥 关键修复：不同步缩略图，保持原始数据纯净
      // targetItem.thumbnails = JSON.parse(JSON.stringify(results.thumbnails.value.thumbnails || [])); // 🔥 禁用：防止数据污染

      // 🔥 只同步生成图片，因为生成图片是可以智能合并的元数据
      targetItem.generatedImages = JSON.parse(JSON.stringify(results.thumbnails.value.generatedImages || []));

      if (process.env.NODE_ENV === 'development') {
        console.log('[syncThumbnailsToMergedRows] 🔥 缩略图同步策略（防污染版）:', {
          originalIndex: targetItem.originalIndex,
          原始缩略图保持不变: targetItem.thumbnails?.length || 0,
          生成图片已同步: targetItem.generatedImages?.length || 0,
          合并结果缩略图数量: results.thumbnails.value.thumbnails?.length || 0,
          策略: '保持原始缩略图纯净，只同步生成图片元数据'
        });
      }
    }
  }

  /**
   * 辅助函数：同步数据到 mergedRows
   * 🔥 关键修复：不同步任何数据到 mergedRows，防止数据污染
   * mergedRows 的设计原则：每个项目保存各自的原始数据，不应该被智能合并结果覆盖
   */
  function syncDataToMergedRows(prevRow, results) {
    if (!prevRow.mergedRows || prevRow.mergedRows.length === 0) {
      return;
    }

    // 🔥 关键修复：不同步任何数据到 mergedRows，保持原始数据纯净
    // 智能合并的结果只应该应用到目标行的显示状态，不应该污染 mergedRows 中的原始数据
    // 这样分开时每行都能恢复为其真实的原始状态，不会包含其他行的数据

    if (process.env.NODE_ENV === 'development') {
      console.log('[syncDataToMergedRows] 🔥 数据同步策略（防污染版）:', {
        策略: '不同步任何数据到 mergedRows，保持原始数据纯净',
        mergedRows数量: prevRow.mergedRows.length,
        智能合并结果: {
          标签处理: results.tags?.reason || '无',
          关键词处理: results.keywords?.reason || '无',
          图片处理: results.image?.reason || '无',
          缩略图处理: results.thumbnails?.reason || '无'
        }
      });
    }

    // 注释掉原有的同步逻辑，防止数据污染
    // const targetItem = prevRow.mergedRows.find(item => item.originalIndex === prevRow.originalIndex);
    // if (targetItem) {
    //   syncTagsToMergedRows(targetItem, results);
    //   syncKeywordsToMergedRows(targetItem, results);
    //   syncImageToMergedRows(targetItem, results);
    //   syncThumbnailsToMergedRows(targetItem, results);
    // }

    // 🔥 新增：验证 mergedRows 数据完整性（防污染版）
    if (process.env.NODE_ENV === 'development') {
      validateMergedRowsIntegrity(prevRow);
    }
  }

  /**
   * 🔥 新增：验证 mergedRows 数据完整性函数
   * 确保每个项目的 originalIndex 与其实际数据的对应关系正确
   */
  function validateMergedRowsIntegrity(mergedRow) {
    if (!mergedRow.mergedRows || mergedRow.mergedRows.length === 0) {
      return;
    }

    console.log('[validateMergedRowsIntegrity] 🔥 数据完整性验证开始:', {
      合并行originalIndex: mergedRow.originalIndex,
      mergedRows数量: mergedRow.mergedRows.length
    });

    mergedRow.mergedRows.forEach((item, index) => {
      console.log(`[validateMergedRowsIntegrity] 🔥 项目${index + 1}数据验证:`, {
        originalIndex: item.originalIndex,
        图片数据: item.imageSrc && item.imageSrc !== processors.image.defaultImage ? '有原始图片' : '无图片',
        缩略图数据: item.thumbnails?.length + '个原始缩略图',
        imageAlt: item.imageAlt || '无',
        isImageLocked: item.isImageLocked || false,
        标签数据: item.selectedTags?.length + '个原始标签',
        关键词数据: item.keywords ? '有原始关键词' : '无关键词',
        数据纯净性: '✅ 保持原始状态'
      });
    });

    // 验证 originalIndex 的唯一性
    const originalIndexes = mergedRow.mergedRows.map(item => item.originalIndex);
    const uniqueIndexes = [...new Set(originalIndexes)];
    if (originalIndexes.length !== uniqueIndexes.length) {
      console.error('[validateMergedRowsIntegrity] ❌ 发现重复的 originalIndex:', {
        原始数组: originalIndexes,
        去重后: uniqueIndexes
      });
    } else {
      console.log('[validateMergedRowsIntegrity] ✅ originalIndex 唯一性验证通过');
    }
  }

  /**
   * 辅助函数：更新合并后行的显示内容
   */
  function updateMergedRowDisplay(prevRow) {
    let combinedDescription = '';
    for (let i = 0; i < prevRow.mergedRows.length; i++) {
      if (i > 0) combinedDescription += '\n';
      combinedDescription += prevRow.mergedRows[i].description;
    }
    prevRow.description = combinedDescription;
  }

  /**
   * 辅助函数：更新时间范围
   */
  function updateTimeRange(prevRow) {
    if (prevRow.mergedRows.length > 0) {
      const startTimes = prevRow.mergedRows.map(row => row.startTime);
      const endTimes = prevRow.mergedRows.map(row => row.endTime);

      prevRow.startTime = Math.min(...startTimes);
      prevRow.endTime = Math.max(...endTimes);
      prevRow.duration = prevRow.endTime - prevRow.startTime;
    }
  }

  /**
   * 辅助函数：从 mergedRows 项恢复行数据
   */
  function restoreRowFromMergedItem(targetRow, item) {
    // 🔥 调试：记录恢复前的数据状态
    if (process.env.NODE_ENV === 'development') {
      console.log('[restoreRowFromMergedItem] 🔥 数据恢复开始:', {
        originalIndex: item.originalIndex,
        恢复前图片: targetRow.imageSrc && targetRow.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        恢复前缩略图: targetRow.thumbnails?.length || 0,
        原始数据图片: item.imageSrc && item.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        原始数据缩略图: item.thumbnails?.length || 0,
        原始数据imageAlt: item.imageAlt || '无',
        原始数据isImageLocked: item.isImageLocked || false
      });
    }

    // 🔥 关键修复：完全清除 targetRow 的所有字段，然后重新构建
    // 这确保不会有任何残留的合并数据
    const originalKeys = Object.keys(targetRow);
    originalKeys.forEach(key => delete targetRow[key]);

    // 🔥 重新构建行数据，只包含必要的字段
    targetRow.description = item.description || '';
    targetRow.originalIndex = item.originalIndex;
    targetRow.index = item.index; // 保持原有的 index
    targetRow.startTime = item.startTime;
    targetRow.endTime = item.endTime;
    targetRow.duration = item.duration;

    // 使用数据处理器恢复各列数据
    targetRow.selectedTags = processors.tags.split(item.selectedTags);

    const keywordsData = processors.keywords.split({ keywords: item.keywords, metadata: item.keywordsMetadata });
    targetRow.keywords = keywordsData.keywords;
    targetRow.keywordsMetadata = keywordsData.metadata;

    const imageData = processors.image.split({ imageSrc: item.imageSrc, imageAlt: item.imageAlt, isImageLocked: item.isImageLocked });
    targetRow.imageSrc = imageData.imageSrc;
    targetRow.imageAlt = imageData.imageAlt;
    targetRow.isImageLocked = imageData.isImageLocked;

    const thumbnailsData = processors.thumbnails.split({ thumbnails: item.thumbnails, generatedImages: item.generatedImages });
    targetRow.thumbnails = thumbnailsData.thumbnails;
    targetRow.generatedImages = thumbnailsData.generatedImages;

    targetRow.tags = Array.isArray(item.tags) ? [...item.tags] : [];
    targetRow.isSelected = item.isSelected || false;

    // 🔥 关键修复：设置为独立行状态
    targetRow.isMerged = false;
    targetRow.mergedRows = []; // 独立行不应该有 mergedRows

    // 🔥 调试：记录恢复后的数据状态
    if (process.env.NODE_ENV === 'development') {
      console.log('[restoreRowFromMergedItem] 🔥 数据恢复完成:', {
        originalIndex: targetRow.originalIndex,
        恢复后图片: targetRow.imageSrc && targetRow.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        恢复后缩略图: targetRow.thumbnails?.length || 0,
        恢复后imageAlt: targetRow.imageAlt || '无',
        恢复后isImageLocked: targetRow.isImageLocked || false,
        图片数据验证: targetRow.imageSrc === item.imageSrc ? '✅ 正确' : '❌ 错误',
        缩略图数据验证: targetRow.thumbnails?.length === item.thumbnails?.length ? '✅ 正确' : '❌ 错误',
        最终字段数量: Object.keys(targetRow).length
      });
    }
  }

  /**
   * 辅助函数：创建独立行
   */
  function createIndependentRow(item) {
    // 🔥 调试：记录创建独立行的原始数据
    if (process.env.NODE_ENV === 'development') {
      console.log('[createIndependentRow] 🔥 创建独立行:', {
        originalIndex: item.originalIndex,
        原始图片数据: item.imageSrc && item.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        原始缩略图数据: item.thumbnails?.length || 0,
        原始imageAlt: item.imageAlt || '无',
        原始isImageLocked: item.isImageLocked || false,
        原始关键词: item.keywords ? '有' : '无'
      });
    }

    const newRow = {
      originalIndex: item.originalIndex,
      index: item.originalIndex, // 临时设置，后面会重新编号
      description: item.description || '',
      startTime: item.startTime,
      endTime: item.endTime,
      duration: item.duration,
      isMerged: false,
      isSelected: false,
      mergedRows: [] // 🔥 修复：独立行不应该有 mergedRows 数据
    };

    // 使用数据处理器恢复各列数据
    newRow.selectedTags = processors.tags.split(item.selectedTags);

    const keywordsData = processors.keywords.split({ keywords: item.keywords, metadata: item.keywordsMetadata });
    newRow.keywords = keywordsData.keywords;
    newRow.keywordsMetadata = keywordsData.metadata;

    const imageData = processors.image.split({ imageSrc: item.imageSrc, imageAlt: item.imageAlt, isImageLocked: item.isImageLocked });
    newRow.imageSrc = imageData.imageSrc;
    newRow.imageAlt = imageData.imageAlt;
    newRow.isImageLocked = imageData.isImageLocked;

    const thumbnailsData = processors.thumbnails.split({ thumbnails: item.thumbnails, generatedImages: item.generatedImages });
    newRow.thumbnails = thumbnailsData.thumbnails;
    newRow.generatedImages = thumbnailsData.generatedImages;

    newRow.tags = Array.isArray(item.tags) ? [...item.tags] : [];

    // 🔥 调试：验证独立行创建结果
    if (process.env.NODE_ENV === 'development') {
      console.log('[createIndependentRow] 🔥 独立行创建完成:', {
        originalIndex: newRow.originalIndex,
        最终图片数据: newRow.imageSrc && newRow.imageSrc !== processors.image.defaultImage ? '有图片' : '无图片',
        最终缩略图数据: newRow.thumbnails?.length || 0,
        最终imageAlt: newRow.imageAlt || '无',
        最终isImageLocked: newRow.isImageLocked || false,
        图片数据验证: newRow.imageSrc === item.imageSrc ? '✅ 正确' : '❌ 错误',
        缩略图数据验证: newRow.thumbnails?.length === item.thumbnails?.length ? '✅ 正确' : '❌ 错误'
      });
    }

    return newRow;
  }

  /**
   * 辅助函数：重新编号所有行
   */
  function renumberRows(rows) {
    rows.forEach((row, idx) => {
      row.index = idx + 1;
      // 同时更新 mergedRows 中的 index
      if (row.mergedRows && row.mergedRows.length > 0) {
        row.mergedRows.forEach(subRow => {
          subRow.index = idx + 1;
        });
      }
    });
  }

  /**
   * 🔥 新增：数据清理函数 - 移除不必要的字段
   * 确保行数据只包含必要的字段，防止数据污染
   */
  function cleanRowData(row) {
    // 定义允许的字段列表
    const allowedFields = [
      // 基础字段
      'originalIndex', 'index', 'description', 'startTime', 'endTime', 'duration',
      // 标签和关键词
      'selectedTags', 'keywords', 'keywordsMetadata',
      // 图片相关
      'imageSrc', 'imageAlt', 'isImageLocked',
      // 缩略图相关
      'thumbnails', 'generatedImages',
      // 其他必要字段
      'tags', 'isSelected', 'isMerged', 'mergedRows', 'mergedRowsCount'
    ];

    // 创建清理后的对象
    const cleanedRow = {};
    allowedFields.forEach(field => {
      if (Object.hasOwn(row, field)) {
        cleanedRow[field] = row[field];
      }
    });

    // 🔥 调试：记录被移除的字段（简化版）
    if (process.env.NODE_ENV === 'development') {
      const removedFields = Object.keys(row).filter(key => !allowedFields.includes(key));
      if (removedFields.length > 0) {
        // console.log('[cleanRowData] 🧹 移除不必要字段:', {
        //   originalIndex: row.originalIndex,
        //   removedFields: removedFields
        // });
      }
    }

    return cleanedRow;
  }

  /**
   * 🔥 新增：批量清理行数据
   */
  function cleanAllRowsData(rows) {
    if (!Array.isArray(rows)) return rows;

    return rows.map(row => {
      const cleanedRow = cleanRowData(row);

      // 同时清理 mergedRows 中的数据
      if (cleanedRow.mergedRows && Array.isArray(cleanedRow.mergedRows)) {
        cleanedRow.mergedRows = cleanedRow.mergedRows.map(mergedItem => cleanRowData(mergedItem));
      }

      return cleanedRow;
    });
  }

  return {
    // 状态
    isProcessing,
    lastOperation,

    // 主要功能
    mergeUp,
    splitDown,

    // 数据处理器
    processors,
    processColumnData,

    // 工具方法
    cloneData: (data) => JSON.parse(JSON.stringify(data)),

    // 辅助函数（用于测试和调试）
    createMergedRowItem,
    processAllColumns,
    applyMergeResults,
    restoreRowFromMergedItem,
    createIndependentRow,
    renumberRows,

    // 🔥 新增：数据清理函数
    cleanRowData,
    cleanAllRowsData,
    hasAnyData,
    analyzeDataState,
    getScenarioDescription,

    // 🔥 新增：数据完整性验证函数
    validateMergedRowsIntegrity
  };
}
