# 有声漫画生成器 (txt2video)

这是一个基于Vue.js的现代化Web应用程序，可以创建有声漫画，支持网页端和桌面端（通过Electron）使用。

## 功能特点

- 创建和管理有声漫画项目
- 上传音频文件和SRT字幕文件
- 自动生成漫画风格的图像，配合音频创建有声漫画
- 支持导出最终视频作品
- 跨平台支持：Web端和桌面端（Windows、macOS、Linux）

## 技术栈

- **前端**：Vue.js 3
- **后端**：Express.js
- **桌面端**：Electron
- **样式**：自定义CSS（支持暗色主题）
- **图标**：Remix Icon

## 系统要求

- Node.js 16.x 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge等）
- 用于桌面版本：Windows 10+, macOS 10.14+, 或 Linux

## 安装

1. 克隆仓库
```bash
git clone https://github.com/yourusername/txt2video.git
cd txt2video
```

2. 安装依赖
```bash
npm install
```

## 运行应用

### Web应用（开发模式）

一键启动前端和后端服务：
```bash
npm run dev-full
```

或分开启动：

1. 启动后端API服务
```bash
npm run start
```

2. 启动前端开发服务器（在另一个终端中）
```bash
npm run serve
```

然后在浏览器中访问: http://localhost:8081

### 桌面应用（Electron）

```bash
npm run electron:serve
```

### 生产环境构建

网页应用：
```bash
npm run build
```

桌面应用：
```bash
npm run electron:build
```

## 项目结构

```
txt2video/
├── public/              # 静态资源
│   ├── index.html       # HTML模板
│   ├── favicon.ico      # 网站图标
│   └── styles.css       # 全局样式
├── src/                 # 源代码
│   ├── components/      # Vue组件
│   │   ├── HomePage.vue     # 主页
│   │   └── CreationPage.vue # 创作页面
│   ├── utils/           # 工具函数
│   │   ├── srtParser.js     # SRT解析工具
│   │   └── comfyui.js       # AI集成工具
│   ├── assets/          # 项目资源
│   ├── App.vue          # 主应用组件
│   ├── main.js          # Vue应用入口
│   ├── background.js    # Electron主进程
│   └── preload.js       # Electron预加载脚本
├── server.js            # Express后端服务器
├── data/                # 数据存储目录
├── vue.config.js        # Vue配置
└── package.json         # 项目配置
```

## 使用方法

1. 启动应用后，在主页点击"开始创作"按钮创建新项目
2. 在创作界面，上传音频文件和字幕文件
3. 设置项目参数，然后点击"开始生成"
4. 等待处理完成，预览生成的有声漫画
5. 导出作品或继续编辑

## API参考

后端API提供以下端点：

- `GET /api/projects` - 获取所有项目
- `POST /api/projects` - 创建新项目
- `GET /api/projects/:id` - 获取特定项目
- `PUT /api/projects/:id` - 更新项目
- `DELETE /api/projects/:id` - 删除项目

## 开发指南

- 组件开发遵循Vue.js最佳实践
- 使用ESLint确保代码质量
- 遵循语义化版本控制进行版本管理

## 许可证

MIT

## 贡献

欢迎贡献代码、报告问题或提出改进建议。请查看[贡献指南](CONTRIBUTING.md)了解更多信息。 