/**
 * ComfyUI代理路由
 * 处理前端与ComfyUI服务器之间的通信，解决CORS问题
 */

const express = require('express');
const http = require('http');
const fs = require('fs');
const path = require('path');
const router = express.Router();

/**
 * 代理请求到ComfyUI服务器
 * @param {string} comfyuiUrl - ComfyUI服务器地址
 * @param {string} endpoint - API端点
 * @param {string} method - HTTP方法
 * @param {Object} data - 请求数据
 * @returns {Promise<Object>} 响应数据
 */
function proxyToComfyUI(comfyuiUrl, endpoint, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    try {
      const url = new URL(endpoint, comfyuiUrl);
      
      const options = {
        hostname: url.hostname,
        port: url.port || 80,
        path: url.pathname + url.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'txt2video-comfyui-client'
        },
        timeout: 10000 // 10秒超时
      };

      // 如果有数据，添加Content-Length
      let postData = null;
      if (data && (method === 'POST' || method === 'PUT')) {
        postData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      console.log(`[ComfyUI代理] ${method} ${comfyuiUrl}${endpoint}`);

      const req = http.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          try {
            // 尝试解析JSON响应
            let parsedData;
            if (res.headers['content-type']?.includes('application/json')) {
              parsedData = JSON.parse(responseData);
            } else {
              parsedData = responseData;
            }
            
            resolve({
              success: true,
              status: res.statusCode,
              headers: res.headers,
              data: parsedData
            });
          } catch (parseError) {
            console.error('[ComfyUI代理] JSON解析错误:', parseError);
            resolve({
              success: true,
              status: res.statusCode,
              headers: res.headers,
              data: responseData
            });
          }
        });
      });

      req.on('error', (error) => {
        console.error('[ComfyUI代理] 请求错误:', error);
        reject({
          success: false,
          error: error.message,
          code: 'CONNECTION_ERROR'
        });
      });

      req.on('timeout', () => {
        console.error('[ComfyUI代理] 请求超时');
        req.destroy();
        reject({
          success: false,
          error: '请求超时',
          code: 'TIMEOUT'
        });
      });

      // 发送数据
      if (postData) {
        req.write(postData);
      }
      
      req.end();
    } catch (error) {
      console.error('[ComfyUI代理] 设置错误:', error);
      reject({
        success: false,
        error: error.message,
        code: 'SETUP_ERROR'
      });
    }
  });
}

/**
 * 测试ComfyUI连接
 * POST /api/local/comfyui/test-connection
 */
router.post('/test-connection', async (req, res) => {
  try {
    const { serverUrl } = req.body;
    
    if (!serverUrl) {
      return res.status(400).json({
        success: false,
        error: '缺少服务器地址'
      });
    }

    console.log(`[ComfyUI] 测试连接: ${serverUrl}`);

    // 测试系统状态端点
    const result = await proxyToComfyUI(serverUrl, '/system_stats');
    
    if (result.success && result.status === 200) {
      console.log(`[ComfyUI] 连接成功:`, result.data);
      res.json({
        success: true,
        message: 'ComfyUI连接成功',
        systemStats: result.data,
        serverInfo: {
          url: serverUrl,
          status: result.status,
          headers: result.headers
        }
      });
    } else {
      console.log(`[ComfyUI] 连接失败:`, result);
      res.status(result.status || 500).json({
        success: false,
        error: `服务器响应错误: ${result.status}`,
        details: result
      });
    }
  } catch (error) {
    console.error('[ComfyUI] 连接测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message || '连接测试失败',
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
});

/**
 * 获取队列状态
 * GET /api/local/comfyui/queue
 */
router.get('/queue', async (req, res) => {
  try {
    const { serverUrl } = req.query;
    
    if (!serverUrl) {
      return res.status(400).json({
        success: false,
        error: '缺少服务器地址'
      });
    }

    const result = await proxyToComfyUI(serverUrl, '/queue');
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 获取队列状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

/**
 * 提交生成任务
 * POST /api/local/comfyui/prompt
 */
router.post('/prompt', async (req, res) => {
  try {
    const { serverUrl, workflow } = req.body;
    
    if (!serverUrl || !workflow) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      });
    }

    console.log(`[ComfyUI] 提交生成任务到: ${serverUrl}`);

    const result = await proxyToComfyUI(serverUrl, '/prompt', 'POST', {
      prompt: workflow
    });
    
    if (result.success) {
      console.log(`[ComfyUI] 任务提交成功:`, result.data);
      res.json(result);
    } else {
      console.log(`[ComfyUI] 任务提交失败:`, result);
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 提交任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

/**
 * 获取历史记录
 * GET /api/local/comfyui/history
 */
router.get('/history', async (req, res) => {
  try {
    const { serverUrl, limit } = req.query;

    if (!serverUrl) {
      return res.status(400).json({
        success: false,
        error: '缺少服务器地址'
      });
    }

    let endpoint = '/history';
    if (limit) {
      endpoint += `?limit=${limit}`;
    }

    const result = await proxyToComfyUI(serverUrl, endpoint);

    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 获取历史记录失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

/**
 * 🆕 获取特定prompt的历史记录
 * GET /api/local/comfyui/history/:prompt_id
 */
router.get('/history/:prompt_id', async (req, res) => {
  try {
    const { prompt_id } = req.params;
    const { serverUrl = 'http://localhost:8188' } = req.query;

    if (!prompt_id) {
      return res.status(400).json({
        success: false,
        error: '缺少prompt_id参数'
      });
    }

    console.log(`[ComfyUI] 获取特定prompt历史记录: ${prompt_id}`);

    const endpoint = `/history/${prompt_id}`;
    const result = await proxyToComfyUI(serverUrl, endpoint);

    if (result.success) {
      console.log(`[ComfyUI] 历史记录获取成功:`, Object.keys(result.data || {}));
      res.json(result);
    } else {
      console.log(`[ComfyUI] 历史记录获取失败:`, result);
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 获取特定prompt历史记录失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

/**
 * 🆕 获取ComfyUI可用的模型列表
 * GET /api/local/comfyui/models
 */
router.get('/models', async (req, res) => {
  try {
    console.log('[ComfyUI] 获取模型列表');

    // 从设置中获取ComfyUI服务器地址
    const settingsPath = path.join(__dirname, '../../userdata/comfyui-settings.json');
    let serverUrl = 'http://localhost:8188'; // 默认地址

    try {
      if (fs.existsSync(settingsPath)) {
        const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
        if (settings.comfyui?.serverUrl) {
          serverUrl = settings.comfyui.serverUrl;
        }
      }
    } catch (settingsError) {
      console.warn('[ComfyUI] 读取设置失败，使用默认服务器地址:', settingsError.message);
    }

    console.log(`[ComfyUI] 使用服务器地址: ${serverUrl}`);

    // 并行获取不同类型的模型列表
    const [checkpointsResult, lorasResult] = await Promise.all([
      // 获取Checkpoint模型列表
      proxyToComfyUI(serverUrl, '/object_info/CheckpointLoaderSimple').catch(err => {
        console.warn('[ComfyUI] 获取Checkpoint列表失败:', err.message);
        return { success: false, data: null };
      }),
      // 获取LoRA模型列表
      proxyToComfyUI(serverUrl, '/object_info/LoraLoader').catch(err => {
        console.warn('[ComfyUI] 获取LoRA列表失败:', err.message);
        return { success: false, data: null };
      })
    ]);

    // 解析Checkpoint列表
    let checkpoints = [];
    if (checkpointsResult.success && checkpointsResult.data) {
      const checkpointInfo = checkpointsResult.data.CheckpointLoaderSimple;
      if (checkpointInfo?.input?.required?.ckpt_name?.[0]) {
        checkpoints = checkpointInfo.input.required.ckpt_name[0];
      }
    }

    // 解析LoRA列表
    let loras = [];
    if (lorasResult.success && lorasResult.data) {
      const loraInfo = lorasResult.data.LoraLoader;
      if (loraInfo?.input?.required?.lora_name?.[0]) {
        loras = loraInfo.input.required.lora_name[0];
      }
    }

    console.log(`[ComfyUI] 找到 ${checkpoints.length} 个Checkpoint模型`);
    console.log(`[ComfyUI] 找到 ${loras.length} 个LoRA模型`);

    res.json({
      success: true,
      data: {
        checkpoints,
        loras,
        serverUrl,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[ComfyUI] 获取模型列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message || '获取模型列表失败'
    });
  }
});

/**
 * 获取生成的图像文件
 * GET /api/local/comfyui/view
 */
router.get('/view', async (req, res) => {
  try {
    const { filename, subfolder, type = 'output', serverUrl = 'http://localhost:8188' } = req.query;

    if (!filename) {
      return res.status(400).json({
        success: false,
        error: '缺少文件名参数'
      });
    }

    console.log(`[ComfyUI] 获取图像文件: ${filename}`);

    // 构建查询参数
    const params = new URLSearchParams({
      filename,
      type
    });

    if (subfolder) {
      params.append('subfolder', subfolder);
    }

    const endpoint = `/view?${params.toString()}`;

    // 🔧 新方案：统一从项目目录读取图像文件
    const path = require('path');
    const fs = require('fs').promises;

    // 🎯 从查询参数中获取项目信息，构建项目图像目录路径
    const { projectTitle, chapterTitle } = req.query;

    let filePath;
    if (projectTitle && chapterTitle) {
      // 优先从项目目录读取
      const projectImageDir = path.join(__dirname, '../../draft', projectTitle, chapterTitle, 'Img');
      filePath = path.join(projectImageDir, filename);
      console.log(`🎯 [ComfyUI] 尝试从项目目录读取: ${filePath}`);
    } else {
      // 回退到ComfyUI输出目录
      const comfyuiOutputDir = path.join(require('os').homedir(), 'Documents', 'ComfyUI', 'output');
      if (subfolder) {
        filePath = path.join(comfyuiOutputDir, subfolder, filename);
      } else {
        filePath = path.join(comfyuiOutputDir, filename);
      }
      console.log(`🔍 [ComfyUI] 回退到ComfyUI目录读取: ${filePath}`);
    }

    try {
      // 检查文件是否存在
      await fs.access(filePath);

      // 读取文件
      const fileBuffer = await fs.readFile(filePath);

      // 根据文件扩展名设置Content-Type
      const ext = path.extname(filename).toLowerCase();
      let contentType = 'image/png';
      if (ext === '.jpg' || ext === '.jpeg') {
        contentType = 'image/jpeg';
      } else if (ext === '.gif') {
        contentType = 'image/gif';
      } else if (ext === '.webp') {
        contentType = 'image/webp';
      }

      // 设置响应头并发送文件
      res.set({
        'Content-Type': contentType,
        'Content-Length': fileBuffer.length,
        'Cache-Control': 'public, max-age=3600'
      });

      res.send(fileBuffer);
      console.log(`✅ [ComfyUI] 图像文件读取成功: ${filename} (${fileBuffer.length} bytes)`);

    } catch (fileError) {
      console.log(`⚠️ [ComfyUI] 直接文件读取失败，尝试API代理: ${fileError.message}`);

      // 🔧 修复：检查serverUrl是否有效
      if (!serverUrl || serverUrl === 'undefined') {
        console.error('❌ [ComfyUI] serverUrl无效，无法进行API代理');
        return res.status(400).json({
          success: false,
          error: '服务器URL无效'
        });
      }

      // 如果直接文件读取失败，回退到API代理
      const url = new URL(endpoint, serverUrl);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'image/*',
          'User-Agent': 'txt2video-comfyui-client'
        }
      });

      if (response.ok) {
        // 🔧 修复：使用arrayBuffer而不是pipe
        const imageBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(imageBuffer);

        // 设置正确的响应头
        res.set({
          'Content-Type': response.headers.get('content-type') || 'image/png',
          'Content-Length': buffer.length,
          'Cache-Control': 'public, max-age=3600'
        });

        // 发送图像数据
        res.send(buffer);
        console.log(`✅ [ComfyUI] 图像文件传输成功: ${filename} (${buffer.length} bytes)`);
      } else {
        console.error(`❌ [ComfyUI] 图像文件获取失败: ${response.status} ${response.statusText}`);
        res.status(response.status).json({
          success: false,
          error: `图像文件获取失败: ${response.statusText}`
        });
      }
    }
  } catch (error) {
    console.error('[ComfyUI] 获取图像文件失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取图像文件失败'
    });
  }
});

/**
 * 中断当前执行的任务
 * POST /api/local/comfyui/interrupt
 */
router.post('/interrupt', async (req, res) => {
  try {
    const { serverUrl = 'http://localhost:8188' } = req.body;

    console.log(`[ComfyUI] 中断任务: ${serverUrl}`);

    const result = await proxyToComfyUI(serverUrl, '/interrupt', 'POST');

    if (result.success) {
      console.log(`[ComfyUI] 任务中断成功:`, result.data);
      res.json(result);
    } else {
      console.log(`[ComfyUI] 任务中断失败:`, result);
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 中断任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

/**
 * 获取队列状态
 * GET /api/local/comfyui/queue
 */
router.get('/queue', async (req, res) => {
  try {
    const { serverUrl = 'http://localhost:8188' } = req.query;

    const result = await proxyToComfyUI(serverUrl, '/queue');

    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 获取队列状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

/**
 * 通用代理端点
 * POST /api/local/comfyui/proxy
 */
router.post('/proxy', async (req, res) => {
  try {
    const { serverUrl, endpoint, method = 'GET', data } = req.body;

    if (!serverUrl || !endpoint) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      });
    }

    const result = await proxyToComfyUI(serverUrl, endpoint, method, data);

    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('[ComfyUI] 代理请求失败:', error);
    res.status(500).json({
      success: false,
      error: error.error || error.message
    });
  }
});

module.exports = router;
