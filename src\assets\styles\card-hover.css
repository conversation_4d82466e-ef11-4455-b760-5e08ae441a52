/* card-hover.css - 炫酷卡片悬浮动效 */

.card-hover-effect {
  transition: box-shadow 0.3s cubic-bezier(.4,2,.3,1),
              transform 0.25s cubic-bezier(.4,2,.3,1),
              background 0.3s;
  box-shadow: 0 2px 12px #0002;
  background: linear-gradient(135deg, #232136 60%, #a23a5a22 100%);
  position: relative;
  z-index: 1;
}

.card-hover-effect:hover, .card-hover-effect:focus-within {
  box-shadow: 0 8px 32px 0 #a23a5a66, 0 0 24px 4px #29908b55;
  background: linear-gradient(135deg, #a23a5a 0%, #29908b 100%);
  transform: scale(1.06) rotate(-1deg);
  border: 1.5px solid #fff2;
}

.card-hover-effect::after {
  content: '';
  display: block;
  position: absolute;
  inset: 0;
  border-radius: 16px;
  pointer-events: none;
  transition: opacity 0.3s;
  opacity: 0;
  box-shadow: 0 0 32px 8px #a23a5a55, 0 0 16px 4px #29908b44;
  z-index: 2;
}

.card-hover-effect:hover::after, .card-hover-effect:focus-within::after {
  opacity: 1;
} 