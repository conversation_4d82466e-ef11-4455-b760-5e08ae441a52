<template>
  <div class="timeline-editor">
    <div class="timeline-toolbar">
      <div class="timeline-title">
        时间轴编辑器
      </div>
      <div class="timeline-controls">
        <button
          class="timeline-btn"
          @click="zoomIn"
          title="放大"
        >
          <i class="ri-zoom-in-line" />
        </button>
        <button
          class="timeline-btn"
          @click="zoomOut"
          title="缩小"
        >
          <i class="ri-zoom-out-line" />
        </button>
        <button
          class="timeline-btn"
          @click="addMarker"
          title="添加标记点"
          :disabled="!isEditable"
        >
          <i class="ri-add-line" />
        </button>
      </div>
    </div>
    
    <div
      class="timeline-container"
      ref="container" 
      @wheel="handleWheel" 
      @mousedown="startDragTimeline" 
      @mousemove="dragTimeline" 
      @mouseup="stopDragTimeline"
      @mouseleave="stopDragTimeline"
    >
      <!-- 时间刻度 -->
      <div class="timeline-ruler">
        <div
          class="ruler-tick major"
          v-for="tick in majorTicks"
          :key="'major-' + tick.time" 
          :style="{ left: tick.position + '%' }"
        >
          <div class="tick-label">
            {{ formatTime(tick.time) }}
          </div>
        </div>
        <div
          class="ruler-tick minor"
          v-for="tick in minorTicks"
          :key="'minor-' + tick.time" 
          :style="{ left: tick.position + '%' }"
        />
      </div>
      
      <!-- 播放头 -->
      <div
        class="timeline-playhead"
        :style="{ left: playheadPosition + '%' }" 
        @mousedown.stop="startDragPlayhead"
      />
      
      <!-- 字幕轨道 -->
      <div class="timeline-track subtitle-track">
        <div class="track-label">
          字幕
        </div>
        <div class="track-content">
          <div 
            v-for="(subtitle, index) in subtitles" 
            :key="index"
            class="subtitle-segment"
            :class="{ 'selected': selectedSegmentIndex === index }"
            :style="{ 
              left: getPositionFromTime(subtitle.startTime) + '%', 
              width: getSegmentWidth(subtitle.startTime, subtitle.endTime) + '%' 
            }"
            @mousedown.stop="selectSegment(index, $event)"
          >
            <div
              class="segment-handle left"
              @mousedown.stop="startResizeSegment(index, 'start', $event)"
            />
            <div class="segment-text">
              {{ subtitle.text }}
            </div>
            <div
              class="segment-handle right"
              @mousedown.stop="startResizeSegment(index, 'end', $event)"
            />
          </div>
        </div>
      </div>
      
      <!-- 音频轨道 -->
      <div class="timeline-track audio-track">
        <div class="track-label">
          音频
        </div>
        <div class="track-content">
          <div
            class="audio-waveform"
            :style="{ width: '100%' }"
          >
            <div 
              v-for="(level, index) in waveformData" 
              :key="index" 
              class="waveform-bar"
              :style="{ 
                height: level + '%',
                left: (index / waveformData.length * 100) + '%'
              }"
            />
          </div>
        </div>
      </div>
      
      <!-- 标记点轨道 -->
      <div class="timeline-track marker-track">
        <div class="track-label">
          标记点
        </div>
        <div class="track-content">
          <div 
            v-for="(marker, index) in markers" 
            :key="index"
            class="timeline-marker"
            :style="{ left: getPositionFromTime(marker.time) + '%' }"
            @mousedown.stop="selectMarker(index)"
          >
            <div class="marker-line" />
            <div class="marker-label">
              {{ marker.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimelineEditor',
  props: {
    duration: {
      type: Number,
      default: 60 // 默认时长(秒)
    },
    currentTime: {
      type: Number,
      default: 0
    },
    subtitles: {
      type: Array,
      default: () => []
    },
    audioData: {
      type: Array,
      default: () => []
    },
    isEditable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 缩放级别(1-10)
      zoomLevel: 1,
      // 显示视图范围
      viewportStart: 0, // 开始时间(秒)
      viewportEnd: 60, // 结束时间(秒)
      // 内部状态
      isDraggingTimeline: false,
      isDraggingPlayhead: false,
      isDraggingSegment: false,
      isResizingSegment: false,
      dragStartX: 0,
      dragStartTime: 0,
      selectedSegmentIndex: -1,
      resizeDirection: null,
      // 标记点
      markers: [],
      // 生成的波形数据
      waveformData: []
    }
  },
  computed: {
    // 计算当前播放头位置的百分比
    playheadPosition() {
      return this.getPositionFromTime(this.currentTime);
    },
    // 计算主要刻度
    majorTicks() {
      const ticks = [];
      // 根据缩放级别调整刻度密度
      const interval = this.getMajorTickInterval();
      
      for (let t = Math.ceil(this.viewportStart / interval) * interval; t <= this.viewportEnd; t += interval) {
        ticks.push({
          time: t,
          position: this.getPositionFromTime(t)
        });
      }
      
      return ticks;
    },
    // 计算次要刻度
    minorTicks() {
      const ticks = [];
      // 根据缩放级别调整次要刻度密度
      const majorInterval = this.getMajorTickInterval();
      const minorInterval = majorInterval / 5;
      
      for (let t = Math.ceil(this.viewportStart / minorInterval) * minorInterval; t <= this.viewportEnd; t += minorInterval) {
        // 跳过主要刻度位置
        if (Math.abs(t % majorInterval) < 0.001) continue;
        
        ticks.push({
          time: t,
          position: this.getPositionFromTime(t)
        });
      }
      
      return ticks;
    }
  },
  watch: {
    audioData: {
      handler(newData) {
        this.generateWaveform(newData);
      },
      immediate: true
    },
    duration: {
      handler(newDuration) {
        // 如果持续时间变化，重置视口
        this.viewportEnd = Math.max(newDuration, 10);
      },
      immediate: true
    }
  },
  methods: {
    // 格式化时间为 mm:ss 格式
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },
    // 从时间计算位置百分比
    getPositionFromTime(time) {
      if (time < this.viewportStart) return 0;
      if (time > this.viewportEnd) return 100;
      
      return ((time - this.viewportStart) / (this.viewportEnd - this.viewportStart)) * 100;
    },
    // 从位置百分比计算时间
    getTimeFromPosition(position) {
      return this.viewportStart + (position / 100) * (this.viewportEnd - this.viewportStart);
    },
    // 计算段落宽度百分比
    getSegmentWidth(startTime, endTime) {
      const startPos = this.getPositionFromTime(startTime);
      const endPos = this.getPositionFromTime(endTime);
      return Math.max(0, endPos - startPos);
    },
    // 根据缩放级别获取主要刻度间隔
    getMajorTickInterval() {
      // 根据缩放级别动态调整刻度间隔
      const intervals = [60, 30, 15, 10, 5, 3, 2, 1, 0.5, 0.2];
      return intervals[Math.min(this.zoomLevel - 1, intervals.length - 1)];
    },
    // 缩放控制
    zoomIn() {
      if (this.zoomLevel < 10) {
        // 以当前播放头位置为中心进行缩放
        const centerTime = this.currentTime;
        const viewportRange = this.viewportEnd - this.viewportStart;
        
        this.zoomLevel += 1;
        
        // 计算新的视口范围，范围减小(缩放)
        const newRange = viewportRange * 0.8;
        
        // 设置新的视口，保持播放头在原位置
        this.viewportStart = Math.max(0, centerTime - newRange * (centerTime - this.viewportStart) / viewportRange);
        this.viewportEnd = this.viewportStart + newRange;
      }
    },
    zoomOut() {
      if (this.zoomLevel > 1) {
        // 以当前播放头位置为中心进行缩放
        const centerTime = this.currentTime;
        const viewportRange = this.viewportEnd - this.viewportStart;
        
        this.zoomLevel -= 1;
        
        // 计算新的视口范围，范围增大(缩小)
        const newRange = viewportRange / 0.8;
        
        // 设置新的视口，保持播放头在原位置
        this.viewportStart = Math.max(0, centerTime - newRange * (centerTime - this.viewportStart) / viewportRange);
        this.viewportEnd = Math.min(this.duration, this.viewportStart + newRange);
      }
    },
    // 鼠标滚轮控制缩放
    handleWheel(event) {
      // 阻止页面滚动
      event.preventDefault();
      
      // 根据滚轮方向缩放
      if (event.deltaY < 0) {
        this.zoomIn();
      } else {
        this.zoomOut();
      }
    },
    // 移动时间轴
    startDragTimeline(event) {
      if (event.target === this.$refs.container || event.target.classList.contains('track-content')) {
        this.isDraggingTimeline = true;
        this.dragStartX = event.clientX;
        this.dragStartViewportStart = this.viewportStart;
        this.dragStartViewportEnd = this.viewportEnd;
      }
    },
    dragTimeline(event) {
      if (!this.isDraggingTimeline) return;
      
      const deltaX = event.clientX - this.dragStartX;
      const containerWidth = this.$refs.container.offsetWidth;
      
      // 计算移动的时间量
      const timeRange = this.viewportEnd - this.viewportStart;
      const timeDelta = (deltaX / containerWidth) * -timeRange;
      
      // 更新视口
      const newStart = Math.max(0, this.dragStartViewportStart + timeDelta);
      const newEnd = Math.min(this.duration, this.dragStartViewportEnd + timeDelta);
      
      if (newEnd - newStart >= 0.1) { // 防止视口过小
        this.viewportStart = newStart;
        this.viewportEnd = newEnd;
      }
    },
    stopDragTimeline() {
      this.isDraggingTimeline = false;
    },
    // 拖动播放头
    startDragPlayhead(event) {
      this.isDraggingPlayhead = true;
      this.dragStartX = event.clientX;
      this.dragStartTime = this.currentTime;
      
      document.addEventListener('mousemove', this.dragPlayhead);
      document.addEventListener('mouseup', this.stopDragPlayhead);
    },
    dragPlayhead(event) {
      if (!this.isDraggingPlayhead) return;
      
      const containerRect = this.$refs.container.getBoundingClientRect();
      const positionPercent = (event.clientX - containerRect.left) / containerRect.width * 100;
      
      // 计算新的时间位置
      const newTime = this.getTimeFromPosition(positionPercent);
      
      // 触发时间更新事件
      this.$emit('update:currentTime', Math.max(0, Math.min(this.duration, newTime)));
    },
    stopDragPlayhead() {
      if (this.isDraggingPlayhead) {
        this.isDraggingPlayhead = false;
        document.removeEventListener('mousemove', this.dragPlayhead);
        document.removeEventListener('mouseup', this.stopDragPlayhead);
      }
    },
    // 选择字幕段落
    selectSegment(index, event) {
      if (!this.isEditable) return;
      
      this.selectedSegmentIndex = index;
      
      // 触发选择事件
      this.$emit('segment-selected', index);
      
      // 准备拖动段落
      this.isDraggingSegment = true;
      this.dragStartX = event.clientX;
      
      // 记录原始位置
      const segment = this.subtitles[index];
      this.dragStartSegmentStart = segment.startTime;
      this.dragStartSegmentEnd = segment.endTime;
      
      document.addEventListener('mousemove', this.dragSegment);
      document.addEventListener('mouseup', this.stopDragSegment);
    },
    dragSegment(event) {
      if (!this.isDraggingSegment || !this.isEditable) return;
      
      const containerRect = this.$refs.container.getBoundingClientRect();
      const deltaX = event.clientX - this.dragStartX;
      const timeDelta = (deltaX / containerRect.width) * (this.viewportEnd - this.viewportStart);
      
      // 计算新的开始和结束时间
      let newStart = Math.max(0, this.dragStartSegmentStart + timeDelta);
      let newEnd = Math.max(0, this.dragStartSegmentEnd + timeDelta);
      
      // 确保不超过时间轴范围
      if (newEnd > this.duration) {
        const overflow = newEnd - this.duration;
        newEnd = this.duration;
        newStart = Math.max(0, newStart - overflow);
      }
      
      // 更新字幕段落时间
      const updatedSubtitle = {
        ...this.subtitles[this.selectedSegmentIndex],
        startTime: newStart,
        endTime: newEnd
      };
      
      // 发出更新事件
      this.$emit('subtitle-updated', this.selectedSegmentIndex, updatedSubtitle);
    },
    stopDragSegment() {
      if (this.isDraggingSegment) {
        this.isDraggingSegment = false;
        document.removeEventListener('mousemove', this.dragSegment);
        document.removeEventListener('mouseup', this.stopDragSegment);
      }
    },
    // 调整字幕段落长度
    startResizeSegment(index, direction, event) {
      if (!this.isEditable) return;
      
      event.stopPropagation();
      this.selectedSegmentIndex = index;
      this.isResizingSegment = true;
      this.resizeDirection = direction;
      this.dragStartX = event.clientX;
      
      // 记录原始位置
      const segment = this.subtitles[index];
      this.dragStartSegmentStart = segment.startTime;
      this.dragStartSegmentEnd = segment.endTime;
      
      document.addEventListener('mousemove', this.resizeSegment);
      document.addEventListener('mouseup', this.stopResizeSegment);
    },
    resizeSegment(event) {
      if (!this.isResizingSegment || !this.isEditable) return;
      
      const containerRect = this.$refs.container.getBoundingClientRect();
      const deltaX = event.clientX - this.dragStartX;
      const timeDelta = (deltaX / containerRect.width) * (this.viewportEnd - this.viewportStart);
      
      const segment = {...this.subtitles[this.selectedSegmentIndex]};
      
      if (this.resizeDirection === 'start') {
        // 调整开始时间，但不能超过结束时间
        const newStart = Math.max(0, Math.min(segment.endTime - 0.1, this.dragStartSegmentStart + timeDelta));
        segment.startTime = newStart;
      } else if (this.resizeDirection === 'end') {
        // 调整结束时间，但不能小于开始时间
        const newEnd = Math.min(this.duration, Math.max(segment.startTime + 0.1, this.dragStartSegmentEnd + timeDelta));
        segment.endTime = newEnd;
      }
      
      // 发出更新事件
      this.$emit('subtitle-updated', this.selectedSegmentIndex, segment);
    },
    stopResizeSegment() {
      if (this.isResizingSegment) {
        this.isResizingSegment = false;
        document.removeEventListener('mousemove', this.resizeSegment);
        document.removeEventListener('mouseup', this.stopResizeSegment);
      }
    },
    // 添加标记点
    addMarker() {
      if (!this.isEditable) return;
      
      const newMarker = {
        time: this.currentTime,
        label: `标记 ${this.markers.length + 1}`
      };
      
      this.markers.push(newMarker);
      this.$emit('marker-added', newMarker);
    },
    // 选择标记点
    selectMarker(index) {
      if (!this.isEditable) return;
      
      // 触发选择事件
      this.$emit('marker-selected', index);
      
      // 更新当前时间到标记点位置
      this.$emit('update:currentTime', this.markers[index].time);
    },
    // 生成波形数据
    generateWaveform(audioData) {
      // 如果没有音频数据，生成随机波形
      if (!audioData || audioData.length === 0) {
        this.generateRandomWaveform();
        return;
      }
      
      // 处理真实音频数据...
      // 通常这里会压缩数据以适应显示
      this.waveformData = audioData.map(sample => {
        // 通常音频样本在 -1 到 1 范围内，映射到 0-100%
        return Math.abs(sample) * 100;
      });
    },
    // 生成随机波形数据(演示用)
    generateRandomWaveform() {
      const samples = 200;
      this.waveformData = Array(samples).fill(0).map(() => {
        return Math.random() * 60 + 5; // 5% - 65% 高度
      });
    }
  },
  created() {
    // 生成测试数据
    this.generateRandomWaveform();
  },
  beforeUnmount() {
    // 确保清理所有事件监听器
    document.removeEventListener('mousemove', this.dragPlayhead);
    document.removeEventListener('mouseup', this.stopDragPlayhead);
    document.removeEventListener('mousemove', this.dragSegment);
    document.removeEventListener('mouseup', this.stopDragSegment);
    document.removeEventListener('mousemove', this.resizeSegment);
    document.removeEventListener('mouseup', this.stopResizeSegment);
  }
}
</script>

<style scoped>
.timeline-editor {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 220px;
  background-color: #11111b;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #313244;
  user-select: none;
}

.timeline-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 10px;
  background-color: #181825;
  border-bottom: 1px solid #313244;
  height: 36px;
}

.timeline-title {
  font-size: 0.9rem;
  font-weight: bold;
  color: #cba6f7;
}

.timeline-controls {
  display: flex;
  gap: 4px;
}

.timeline-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background-color: #313244;
  border: none;
  color: #cdd6f4;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeline-btn:hover {
  background-color: #45475a;
  color: #f5c2e7;
}

.timeline-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.timeline-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.timeline-ruler {
  height: 24px;
  background-color: #1e1e2e;
  position: relative;
  border-bottom: 1px solid #313244;
}

.ruler-tick {
  position: absolute;
  top: 0;
  width: 1px;
  background-color: #45475a;
}

.ruler-tick.major {
  height: 16px;
  top: 0;
  background-color: #cba6f7;
}

.ruler-tick.minor {
  height: 8px;
  top: 8px;
}

.tick-label {
  position: absolute;
  top: 18px;
  left: 2px;
  font-size: 0.7rem;
  color: #a6adc8;
  transform: translateX(-50%);
  white-space: nowrap;
}

.timeline-playhead {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #f38ba8;
  z-index: 10;
  cursor: col-resize;
}

.timeline-playhead::before {
  content: '';
  position: absolute;
  top: 0;
  left: -5px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #f38ba8;
}

.timeline-track {
  height: 60px;
  display: flex;
  position: relative;
  border-bottom: 1px solid #313244;
}

.track-label {
  width: 70px;
  padding: 4px 8px;
  background-color: #181825;
  color: #cdd6f4;
  font-size: 0.8rem;
  border-right: 1px solid #313244;
  display: flex;
  align-items: center;
}

.track-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #1e1e2e;
}

/* 字幕段落 */
.subtitle-segment {
  position: absolute;
  top: 10px;
  height: 40px;
  background-color: rgba(203, 166, 247, 0.3);
  border: 1px solid #cba6f7;
  border-radius: 4px;
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  min-width: 10px;
}

.subtitle-segment.selected {
  background-color: rgba(203, 166, 247, 0.5);
  border: 2px solid #cba6f7;
  z-index: 5;
}

.segment-text {
  font-size: 0.8rem;
  color: #fff;
  text-align: center;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.segment-handle {
  position: absolute;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: col-resize;
  z-index: 2;
}

.segment-handle.left {
  left: 0;
}

.segment-handle.right {
  right: 0;
}

/* 音频波形 */
.audio-waveform {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.waveform-bar {
  position: absolute;
  bottom: 50%;
  width: 1px;
  background-color: #2dc0f0;
  transform: translateY(50%);
}

/* 标记点 */
.timeline-marker {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 3;
  cursor: pointer;
}

.marker-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #f5c2e7;
}

.marker-label {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 0.7rem;
  color: #f5c2e7;
  background-color: rgba(30, 30, 46, 0.8);
  padding: 2px 4px;
  border-radius: 3px;
  white-space: nowrap;
}

/* 添加特定轨道颜色 */
.subtitle-track .track-label {
  color: #cba6f7;
}

.audio-track .track-label {
  color: #2dc0f0;
}

.marker-track .track-label {
  color: #f5c2e7;
}
</style> 