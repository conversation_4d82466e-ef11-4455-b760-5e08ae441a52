/**
 * Google Gemini LLM 提供商配置
 * 支持Google Generative AI API
 */

export const googleConfig = {
  // 提供商基本信息
  name: 'Google Gemini',
  id: 'google',
  description: 'Google的Gemini系列大语言模型',
  
  // API配置
  api: {
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    endpoint: (model) => `/models/${model}:generateContent`,
    headers: {
      'Content-Type': 'application/json'
    },
    authParam: 'key', // Google使用查询参数而不是header
    authPrefix: ''
  },
  
  // 默认配置
  defaults: {
    model: 'gemini-2.5-flash-preview-05-20',
    temperature: 0.7,
    maxTokens: 35000,
    timeout: 30000
  },
  
  // 支持的模型列表
  models: [
    {
      id: 'gemini-2.5-flash-preview-05-20',
      name: 'Gemini 2.5 Flash Preview',
      description: '最新的Gemini 2.5 Flash预览版本',
      maxTokens: 1000000,
      pricing: { input: 0.00075, output: 0.003 }
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      description: '高性能的多模态模型',
      maxTokens: 2000000,
      pricing: { input: 0.00125, output: 0.005 }
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      description: '快速响应的轻量级模型',
      maxTokens: 1000000,
      pricing: { input: 0.000075, output: 0.0003 }
    },
    {
      id: 'gemini-pro',
      name: 'Gemini Pro',
      description: '经典的Gemini Pro模型',
      maxTokens: 32768,
      pricing: { input: 0.0005, output: 0.0015 }
    }
  ],
  
  // 模型名称处理
  formatModelName: (model) => {
    // 确保模型名称的正确性，有些模型名称可能不需要 'models/' 前缀
    return model.startsWith('models/') ? model : `models/${model}`;
  },
  
  // 请求格式化函数
  formatRequest: (prompt, settings) => {
    // 检查是否是结构化的prompt（包含instruction和content）
    let requestBody;
    if (typeof prompt === 'object' && prompt.instruction && prompt.content) {
      requestBody = {
        contents: [
          {
            parts: [
              { text: prompt.instruction + '\n\n' + prompt.content },
            ],
          },
        ],
        generationConfig: {
          temperature: settings.temperature || 0.7,
          maxOutputTokens: settings.maxOutputTokens || 4096,
        },
      };
    } else {
      // 向后兼容：如果是字符串prompt，直接使用
      requestBody = {
        contents: [
          {
            parts: [
              { text: prompt },
            ],
          },
        ],
        generationConfig: {
          temperature: settings.temperature || 0.7,
          maxOutputTokens: settings.maxOutputTokens || 4096,
        },
      };
    }
    
    return requestBody;
  },
  
  // 响应解析函数
  parseResponse: (data) => {
    if (!data.candidates || !data.candidates[0]) {
      throw new Error('Google API响应格式无效');
    }
    
    // 从不同响应格式中提取文本
    let content = '';
    if (data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
      content = data.candidates[0].content.parts[0].text;
    } else if (data.candidates[0].text) {
      content = data.candidates[0].text;
    }
    
    return {
      content: content.trim(),
      usage: data.usageMetadata,
      model: data.modelVersion
    };
  },
  
  // 错误处理
  handleError: (error, response) => {
    if (response && response.status === 401) {
      return new Error('Google API密钥无效或已过期');
    }
    if (response && response.status === 429) {
      return new Error('Google API请求频率限制，请稍后重试');
    }
    if (response && response.status === 403) {
      return new Error('Google API访问被拒绝，请检查API密钥权限');
    }
    return new Error(`Google API错误: ${error.message}`);
  },
  
  // 配置验证
  validateConfig: (config) => {
    const errors = [];
    
    if (!config.apiKey) {
      errors.push('缺少Google API密钥');
    }
    
    if (!config.model) {
      errors.push('未指定模型');
    }
    
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('温度值应在0-2之间');
    }
    
    return errors;
  },
  
  // 构建完整的API URL
  buildApiUrl: (model, apiKey) => {
    const modelName = googleConfig.formatModelName(model);
    return `${googleConfig.api.baseUrl}${googleConfig.api.endpoint(modelName)}?key=${apiKey}`;
  }
};

export default googleConfig;
