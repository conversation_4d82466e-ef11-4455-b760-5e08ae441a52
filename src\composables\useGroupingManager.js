/**
 * useGroupingManager.js
 * 统一管理字幕分组相关的逻辑，包括手动合并/分拆、AI分组、导入分组和导出文本
 */
import { ref } from 'vue';

export function useGroupingManager() {
  // 状态数据
  const isProcessing = ref(false);
  const error = ref('');
  const groupingResult = ref([]);
  const resultReady = ref(false);

  /**
   * 手动合并两个字幕项
   * @param {Array} subtitleItems 字幕项数组
   * @param {Number} targetIndex 目标索引（被合并到的项）
   * @param {Number} sourceIndex 源索引（要合并的项）
   * @returns {Object} 处理结果
   */
  function mergeItems(subtitleItems, targetIndex, sourceIndex) {
    if (!subtitleItems || subtitleItems.length === 0) {
      return { success: false, error: '没有可用的字幕项' };
    }

    if (targetIndex < 0 || targetIndex >= subtitleItems.length ||
        sourceIndex < 0 || sourceIndex >= subtitleItems.length) {
      return { success: false, error: '索引超出范围' };
    }

    if (targetIndex === sourceIndex) {
      return { success: false, error: '不能合并同一项' };
    }

    try {
      const targetItem = subtitleItems[targetIndex];
      const sourceItem = subtitleItems[sourceIndex];

      // 准备合并项的数据
      const mergedItem = {
        ...targetItem,
        content: targetItem.content + '\n' + sourceItem.content,
        isMerged: true
      };

      // 确保originalRow存在
      if (!mergedItem.originalRow) {
        mergedItem.originalRow = {};
      }

      // 设置originalState（如果从非合并项转为合并项）
      if (!targetItem.isMerged) {
        mergedItem.originalRow.originalState = JSON.parse(JSON.stringify({
          ...targetItem,
          isMerged: false
        }));

        // 初始化mergedRows数组
        mergedItem.originalRow.mergedRows = [
          JSON.parse(JSON.stringify(mergedItem.originalRow.originalState))
        ];
      }

      // 添加源项到mergedRows
      const sourceItemData = sourceItem.originalRow || sourceItem;
      if (!mergedItem.originalRow.mergedRows) {
        mergedItem.originalRow.mergedRows = [];
      }

      // 检查是否已存在（避免重复）
      const exists = mergedItem.originalRow.mergedRows.some(
        row => row.originalIndex === sourceItemData.originalIndex
      );

      if (!exists) {
        mergedItem.originalRow.mergedRows.push(JSON.parse(JSON.stringify({
          ...sourceItemData,
          isMerged: false
        })));
      }

      // 更新合并项的时间范围
      updateMergedItemTimeRange(mergedItem);

      // 创建新的字幕项数组（移除源项）
      const newSubtitleItems = [...subtitleItems];
      newSubtitleItems[targetIndex] = mergedItem;

      // 移除源项
      newSubtitleItems.splice(sourceIndex, 1);

      return {
        success: true,
        subtitleItems: newSubtitleItems,
        message: `已将第 ${sourceIndex + 1} 项合并到第 ${targetIndex + 1} 项`
      };
    } catch (error) {
      console.error('合并字幕项时出错:', error);
      return { success: false, error: `合并失败: ${error.message}` };
    }
  }

  /**
   * 分拆合并的字幕项
   * @param {Array} subtitleItems 字幕项数组
   * @param {Number} index 要分拆的项的索引
   * @returns {Object} 处理结果
   */
  function splitItem(subtitleItems, index) {
    if (!subtitleItems || subtitleItems.length === 0) {
      return { success: false, error: '没有可用的字幕项' };
    }

    if (index < 0 || index >= subtitleItems.length) {
      return { success: false, error: '索引超出范围' };
    }

    const itemToSplit = subtitleItems[index];

    if (!itemToSplit.isMerged) {
      return { success: false, error: '此项未合并，无法分拆' };
    }

    try {
      const originalData = itemToSplit.originalRow;

      // 验证数据完整性
      if (!originalData) {
        return { success: false, error: '分拆失败 - 无效的原始数据' };
      }

      // 检查mergedRows是否存在且有效
      if (!Array.isArray(originalData.mergedRows) || originalData.mergedRows.length === 0) {
        return { success: false, error: '分拆失败 - 无效的合并行数据' };
      }

      // 检查originalState是否存在
      if (!originalData.originalState) {
        // 尝试从第一个子行创建
        originalData.originalState = { ...originalData.mergedRows[0], isMerged: false };
      }

      // 准备恢复项目
      const restoredItems = [];

      // 恢复容器行自身（基于originalState）
      const containerRestored = createRestoredItem(originalData.originalState);
      restoredItems.push(containerRestored);

      // 恢复其他被合并的子项
      originalData.mergedRows.forEach(subRowData => {
        if (subRowData.originalIndex === originalData.originalState.originalIndex) {
          return; // 跳过容器行自身
        }
        const restoredSubItem = createRestoredItem(subRowData);
        restoredItems.push(restoredSubItem);
      });

      // 按原始索引排序
      restoredItems.sort((a, b) => a.originalIndex - b.originalIndex);

      // 创建新的字幕项数组
      const newSubtitleItems = [...subtitleItems];
      newSubtitleItems.splice(index, 1, ...restoredItems);

      return {
        success: true,
        subtitleItems: newSubtitleItems,
        message: `已将第 ${index + 1} 项分拆为 ${restoredItems.length} 个项目`
      };
    } catch (error) {
      console.error('分拆字幕项时出错:', error);
      return { success: false, error: `分拆失败: ${error.message}` };
    }
  }

  /**
   * 创建恢复项
   * @param {Object} rowData 行数据
   * @returns {Object} 恢复的字幕项
   */
  function createRestoredItem(rowData) {
    const restoredItem = {
      id: 0, // 将在后续重新编号
      content: rowData.description || rowData.content || '',
      isMerged: false,
      originalIndex: rowData.originalIndex,
      rowIndex: rowData.index,
      originalRow: JSON.parse(JSON.stringify(rowData)),
      startTime: rowData.startTime,
      endTime: rowData.endTime,
      duration: rowData.duration
    };

    // 移除可能导致循环引用的属性
    delete restoredItem.originalRow.mergedRows;
    // 🔥 修复：不要删除 originalState，它包含重要的原始数据
    // delete restoredItem.originalRow.originalState;  // 注释掉这行
    restoredItem.originalRow.isMerged = false;

    return restoredItem;
  }

  /**
   * 更新合并项的时间范围
   * @param {Object} item 字幕项
   */
  function updateMergedItemTimeRange(item) {
    if (item.originalRow && item.originalRow.mergedRows && item.originalRow.mergedRows.length > 0) {
      const startTimes = item.originalRow.mergedRows
        .map(row => row.startTime)
        .filter(time => time !== undefined && !isNaN(parseFloat(time)));

      const endTimes = item.originalRow.mergedRows
        .map(row => row.endTime)
        .filter(time => time !== undefined && !isNaN(parseFloat(time)));

      if (startTimes.length > 0) {
        item.startTime = Math.min(...startTimes);
        item.originalRow.startTime = item.startTime;
      }

      if (endTimes.length > 0) {
        item.endTime = Math.max(...endTimes);
        item.originalRow.endTime = item.endTime;
      }

      if (item.startTime !== undefined && item.endTime !== undefined) {
        item.duration = item.endTime - item.startTime;
        item.originalRow.duration = item.duration;
      }
    }
  }

  /**
   * 取消所有合并分组
   * @param {Array} subtitleItems 字幕项数组
   * @param {Array} rawSrtRows 原始SRT行数据
   * @returns {Object} 处理结果
   */
  function cancelAllMergedGroups(subtitleItems, rawSrtRows) {
    console.log('执行取消所有合并分组，当前字幕项数量:', subtitleItems?.length || 0, '原始行数量:', rawSrtRows?.length || 0);

    if (!rawSrtRows || rawSrtRows.length === 0) {
      return { success: false, error: '没有可用的原始数据' };
    }

    try {
      // 使用原始行数据重新构建字幕项
      const pristineRowsCopy = JSON.parse(JSON.stringify(rawSrtRows));

      // 检查原始数据是否有合并项（应该没有，但以防万一）
      const hasMergedRows = pristineRowsCopy.some(row => row.isMerged);
      if (hasMergedRows) {
        console.warn('原始数据中包含合并项，这可能导致取消分组不完全');

        // 强制设置所有行为未合并状态
        pristineRowsCopy.forEach(row => {
          row.isMerged = false;
          row.mergedRows = undefined;
          row.mergedRowsCount = undefined;
          // 🔥 修复：不要删除 originalState，它包含重要的原始数据
          // row.originalState = undefined;  // 注释掉这行
        });
      }

      // 确保所有行都有必要的属性
      pristineRowsCopy.forEach((row, index) => {
        // 确保有originalIndex
        if (row.originalIndex === undefined) {
          row.originalIndex = index;
        }

        // 确保有index
        if (row.index === undefined) {
          row.index = index + 1;
        }

        // 确保有description
        if (!row.description) {
          row.description = '';
        }
      });

      // 按照startTime排序
      pristineRowsCopy.sort((a, b) => {
        // 首先按时间排序（如果有）
        if (a.startTime !== undefined && b.startTime !== undefined &&
            !isNaN(a.startTime) && !isNaN(b.startTime)) {
          return a.startTime - b.startTime;
        }
        // 然后按originalIndex排序（如果有）
        if (a.originalIndex !== undefined && b.originalIndex !== undefined) {
          return a.originalIndex - b.originalIndex;
        }
        // 最后按index排序
        return (a.index || 0) - (b.index || 0);
      });

      // 重新编号
      pristineRowsCopy.forEach((row, index) => {
        row.index = index + 1;
      });

      // 重新构建字幕项，确保所有必要的属性都被正确设置
      const newSubtitleItems = pristineRowsCopy.map((row, index) => {
        // 创建基本字幕项
        const item = {
          id: index + 1,
          content: row.description || '',
          isMerged: false, // 强制设置为未合并状态
          originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
          rowIndex: row.index !== undefined ? row.index : index + 1,
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration
        };

        // 创建originalRow，确保它也是未合并状态
        item.originalRow = {
          ...JSON.parse(JSON.stringify(row)),
          isMerged: false,
          mergedRows: undefined,
          mergedRowsCount: undefined,
          originalState: undefined
        };

        // 确保originalRow有description
        if (!item.originalRow.description) {
          item.originalRow.description = item.content;
        }

        return item;
      });

      // 确保所有项都有唯一的id
      newSubtitleItems.forEach((item, index) => {
        item.id = index + 1;
      });

      console.log('取消分组完成，恢复后的字幕项数量:', newSubtitleItems.length);

      return {
        success: true,
        subtitleItems: newSubtitleItems,
        message: `已取消所有分组，恢复到原始状态`
      };
    } catch (error) {
      console.error('取消所有分组时出错:', error);
      return { success: false, error: `取消分组失败: ${error.message}` };
    }
  }

  return {
    // 状态
    isProcessing,
    error,
    groupingResult,
    resultReady,

    // 方法
    mergeItems,
    splitItem,
    cancelAllMergedGroups,
    updateMergedItemTimeRange
  };
}
