/**
 * contentValidation.js
 * 内容验证工具，确保分组操作不会覆盖原始字幕内容
 */

/**
 * 计算两个字符串的相似度（使用编辑距离算法）
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @returns {number} - 相似度（0-1之间，1表示完全相同）
 */
function calculateSimilarity(str1, str2) {
  if (!str1 || !str2) return 0;

  const len1 = str1.length;
  const len2 = str2.length;

  if (len1 === 0) return len2 === 0 ? 1 : 0;
  if (len2 === 0) return 0;

  // 创建编辑距离矩阵
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));

  // 初始化第一行和第一列
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // 计算编辑距离
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,     // 删除
        matrix[i][j - 1] + 1,     // 插入
        matrix[i - 1][j - 1] + cost // 替换
      );
    }
  }

  const editDistance = matrix[len1][len2];
  const maxLength = Math.max(len1, len2);

  return 1 - (editDistance / maxLength);
}

/**
 * 清理文本内容，移除标点符号和空格，用于内容比较
 * @param {string} text - 原始文本
 * @returns {string} - 清理后的文本
 */
function cleanTextForComparison(text) {
  if (!text) return '';
  return text
    .replace(/[，。！？；：""''（）【】《》、\s,.!?;:"'()<>-]/g, '')
    .toLowerCase()
    .trim();
}

/**
 * 验证分组结果是否与原始字幕内容匹配
 * @param {Array} originalSubtitles - 原始字幕数组
 * @param {Array} groupingResult - 分组结果（二维数组）
 * @param {Object} options - 验证选项
 * @returns {Object} - 验证结果
 */
export function validateGroupingContent(originalSubtitles, groupingResult, options = {}) {
  const {
    similarityThreshold = 0.8, // 相似度阈值
    allowPartialMatch = true,   // 是否允许部分匹配
    strictMode = false          // 严格模式：要求完全匹配
  } = options;

  console.log('开始验证分组内容...');
  console.log('原始字幕数量:', originalSubtitles.length);
  console.log('分组数量:', groupingResult.length);

  const validationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    matchedCount: 0,
    unmatchedCount: 0,
    details: []
  };

  // 计算分组中的总句子数
  const totalSentencesInGroups = groupingResult.reduce((total, group) => {
    return total + (Array.isArray(group) ? group.length : 1);
  }, 0);

  console.log('分组中的总句子数:', totalSentencesInGroups);

  // 如果分组中的句子数量远超过原始字幕数量，这是一个明显的错误
  if (totalSentencesInGroups > originalSubtitles.length * 1.5) {
    validationResult.isValid = false;
    validationResult.errors.push(
      `分组结果异常：分组包含${totalSentencesInGroups}个句子，但原始字幕只有${originalSubtitles.length}条。这表明AI可能生成了新内容而不是重新组织现有内容。`
    );
    return validationResult;
  }

  // 创建原始内容映射
  const originalContentMap = new Map();
  originalSubtitles.forEach((item, index) => {
    const content = item.content || item.description || '';
    const cleanContent = cleanTextForComparison(content);
    if (cleanContent) {
      originalContentMap.set(cleanContent, {
        originalIndex: index,
        content: content,
        used: false
      });
    }
  });

  // 验证每个分组
  groupingResult.forEach((group, groupIndex) => {
    if (!Array.isArray(group)) {
      validationResult.errors.push(`分组 ${groupIndex + 1} 不是有效的数组格式`);
      validationResult.isValid = false;
      return;
    }

    const groupDetail = {
      groupIndex: groupIndex + 1,
      sentences: [],
      allMatched: true,
      partialMatches: 0
    };

    group.forEach((sentence) => {
      const cleanSentence = cleanTextForComparison(sentence);
      let bestMatch = null;
      let bestSimilarity = 0;

      // 查找最佳匹配
      for (const [cleanOriginal, originalData] of originalContentMap.entries()) {
        if (originalData.used && !allowPartialMatch) continue;

        const similarity = calculateSimilarity(cleanSentence, cleanOriginal);
        if (similarity > bestSimilarity) {
          bestSimilarity = similarity;
          bestMatch = originalData;
        }
      }

      const sentenceDetail = {
        sentence: sentence,
        matched: false,
        similarity: bestSimilarity,
        originalContent: bestMatch ? bestMatch.content : null
      };

      if (strictMode) {
        // 严格模式：要求完全匹配
        if (bestSimilarity === 1.0 && bestMatch && !bestMatch.used) {
          sentenceDetail.matched = true;
          bestMatch.used = true;
          validationResult.matchedCount++;
        } else {
          sentenceDetail.matched = false;
          validationResult.unmatchedCount++;
          groupDetail.allMatched = false;
          validationResult.errors.push(
            `分组 ${groupIndex + 1} 中的句子 "${sentence}" 在原始字幕中找不到完全匹配`
          );
        }
      } else {
        // 宽松模式：使用相似度阈值
        if (bestSimilarity >= similarityThreshold && bestMatch) {
          sentenceDetail.matched = true;
          if (!bestMatch.used) {
            bestMatch.used = true;
          }
          validationResult.matchedCount++;
          if (bestSimilarity < 1.0) {
            groupDetail.partialMatches++;
            validationResult.warnings.push(
              `分组 ${groupIndex + 1} 中的句子 "${sentence}" 与原始内容 "${bestMatch.content}" 相似度为 ${(bestSimilarity * 100).toFixed(1)}%`
            );
          }
        } else {
          sentenceDetail.matched = false;
          validationResult.unmatchedCount++;
          groupDetail.allMatched = false;
          validationResult.errors.push(
            `分组 ${groupIndex + 1} 中的句子 "${sentence}" 在原始字幕中找不到匹配（最高相似度: ${(bestSimilarity * 100).toFixed(1)}%）`
          );
        }
      }

      groupDetail.sentences.push(sentenceDetail);
    });

    validationResult.details.push(groupDetail);
  });

  // 检查是否有未使用的原始内容
  const unusedOriginals = Array.from(originalContentMap.values()).filter(item => !item.used);
  if (unusedOriginals.length > 0) {
    validationResult.warnings.push(
      `有 ${unusedOriginals.length} 条原始字幕未在分组结果中使用`
    );
  }

  // 最终验证结果
  if (validationResult.errors.length > 0) {
    validationResult.isValid = false;
  }

  console.log('内容验证完成:', {
    isValid: validationResult.isValid,
    matchedCount: validationResult.matchedCount,
    unmatchedCount: validationResult.unmatchedCount,
    errorsCount: validationResult.errors.length,
    warningsCount: validationResult.warnings.length
  });

  return validationResult;
}

/**
 * 基于顺序的严格匹配校验
 * @param {string} importedText - 导入的文本内容
 * @param {Array} originalSubtitles - 原始字幕数组
 * @returns {Object} - 校验结果
 */
export function validateImportedText(importedText, originalSubtitles) {
  console.log('顺序校验导入的文本内容...');

  // 解析导入文本为分组
  const groupRegex = /\d+\.\s*([^\n]+)/g;
  const parsedGroups = [];
  let match;
  while ((match = groupRegex.exec(importedText)) !== null) {
    const groupContent = match[1].trim();
    // 支持逗号或换行分割
    const sentences = groupContent.split(/,|\n/).map(s => s.trim()).filter(s => s.length > 0);
    if (sentences.length > 0) parsedGroups.push(sentences);
  }
  if (parsedGroups.length === 0) {
    return {
      isValid: false,
      errors: ['导入的文本格式不正确，无法解析出有效的分组'],
      warnings: [],
      parsedGroups: []
    };
  }

  // 顺序比对
  let srtIndex = 0;
  const details = [];
  let matchedCount = 0;
  let unmatchedCount = 0;
  const errors = [];

  for (let g = 0; g < parsedGroups.length; g++) {
    const group = parsedGroups[g];
    const groupDetail = { groupIndex: g, sentences: [] };
    for (let s = 0; s < group.length; s++) {
      const sentence = group[s];
      const orig = originalSubtitles[srtIndex];
      let matched = false;
      let similarity = 0;
      let originalContent = orig ? (orig.content || orig.description || '') : '';
      if (orig) {
        matched = sentence === originalContent;
        similarity = matched ? 1 : (sentence && originalContent ? calculateSimilarity(sentence, originalContent) : 0);
      }
      if (matched) {
        matchedCount++;
      } else {
        unmatchedCount++;
        errors.push(`分组${g + 1}中的句子"${sentence}"与原始字幕第${srtIndex + 1}不匹配`);
      }
      groupDetail.sentences.push({
        sentence,
        matched,
        similarity,
        originalContent,
        srtIndex: srtIndex + 1
      });
      srtIndex++;
    }
    details.push(groupDetail);
  }

  return {
    isValid: errors.length === 0,
    matchedCount,
    unmatchedCount,
    errors,
    warnings: [],
    details,
    parsedGroups
  };
}

/**
 * 创建安全的分组操作，只允许合并/分割，不允许内容修改
 * @param {Array} originalSubtitles - 原始字幕数组
 * @param {Array} groupingResult - 分组结果
 * @param {Object} options - 选项
 * @returns {Object} - 安全分组结果
 */
export function createSafeGrouping(originalSubtitles, groupingResult, options = {}) {
  const {
    allowContentModification = false, // 是否允许内容修改
    fallbackToOriginal = true         // 验证失败时是否回退到原始内容
  } = options;

  console.log('创建安全分组操作...');

  // 首先验证内容
  const validation = validateGroupingContent(originalSubtitles, groupingResult, {
    strictMode: !allowContentModification,
    similarityThreshold: allowContentModification ? 0.7 : 0.95
  });

  if (!validation.isValid && !allowContentModification) {
    console.error('分组验证失败，拒绝应用分组:', validation.errors);

    if (fallbackToOriginal) {
      return {
        success: false,
        error: '分组内容与原始字幕不匹配，为保护数据完整性，已拒绝应用分组',
        details: validation,
        fallbackApplied: true,
        subtitleItems: originalSubtitles.map((item, index) => ({
          ...item,
          id: index + 1
        }))
      };
    } else {
      return {
        success: false,
        error: '分组内容验证失败',
        details: validation
      };
    }
  }

  // 如果验证通过或允许内容修改，则应用分组
  return {
    success: true,
    validation: validation,
    message: validation.warnings.length > 0
      ? `分组应用成功，但有 ${validation.warnings.length} 个警告`
      : '分组应用成功'
  };
}

export default {
  validateGroupingContent,
  validateImportedText,
  createSafeGrouping,
  calculateSimilarity,
  cleanTextForComparison
};
