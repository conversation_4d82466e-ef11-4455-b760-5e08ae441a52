<template>
  <div class="chapter-list-table">
    <div
      v-if="chapters && chapters.length"
      class="chapter-list-grid"
    >
      <!-- 表头 -->
      <div class="chapter-list-header">
        <div class="chapter-list-header-cell">
          章节标题
        </div>
        <div class="chapter-list-header-cell">
          操作
        </div>
        <div class="chapter-list-header-cell">
          去创作
        </div>
      </div>
      
      <!-- 章节行 -->
      <ChapterListItem
        v-for="chapter in [...chapters].slice().reverse()"
        :key="chapter.id || chapter.title"
        :chapter="chapter"
        :editing-chapter-id="editingChapterId"
        :editing-chapter-title="editingChapterTitle"
        :file-status="chaptersFilesStatus[chapter.id]"
        @edit="onEdit"
        @finish-edit="onFinishEdit"
        @delete="onDelete"
        @navigate="onNavigate"
      />
    </div>
    <div
      v-else
      class="empty-chapter-list"
    >
      暂无章节
    </div>
  </div>
</template>

<script>
import ChapterListItem from './ChapterListItem.vue';

export default {
  components: {
    ChapterListItem
  },
  props: {
    chapters: {
      type: Array,
      default: () => []
    },
    editingChapterId: {
      type: [String, Number, null],
      default: null
    },
    editingChapterTitle: {
      type: String,
      default: ''
    },
    chaptersFilesStatus: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['edit', 'finish-edit', 'delete', 'navigate'],
  setup(props, { emit }) {
    function onEdit(chapter) {
      emit('edit', chapter);
    }
    
    function onFinishEdit(updatedChapter) {
      // 确保把完整的更新后章节对象传递出去
      emit('finish-edit', updatedChapter);
    }
    
    function onDelete(chapter) {
      emit('delete', chapter);
    }
    
    function onNavigate(chapter) {
      emit('navigate', chapter);
    }
    
    return {
      onEdit,
      onFinishEdit,
      onDelete,
      onNavigate
    };
  }
};
</script>

<style scoped>
.empty-chapter-list {
  color: #888;
  text-align: center;
  padding: 2rem 0;
}

/* 网格布局 */
.chapter-list-table {
  width: 100%;
  margin-top: 2.2rem;
  overflow-x: visible;
}

.chapter-list-grid {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.chapter-list-header {
  display: grid;
  grid-template-columns: 60% 20% 20%;
  width: 100%;
  background: #2d2a45;
  color: #e0def4;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #393552;
}

.chapter-list-header-cell {
  padding: 0.7rem 1.2rem;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border: 1px solid #393552;
}
</style>
