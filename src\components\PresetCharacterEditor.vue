<template>
  <div
    class="character-editor-modal"
    v-if="show"
  >
    <div class="editor-card horizontal-layout">
      <button
        class="close-btn"
        @click="$emit('close')"
      >
        ×
      </button>
      <div class="avatar-section">
        <div
          class="avatar-upload"
          @click="triggerFileInput"
        >
          <img
            v-if="avatarUrl"
            :src="avatarUrl"
            alt="角色图片"
          >
          <span v-else>+</span>
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            @change="onFileChange"
            style="display:none;"
          >
        </div>
        <div class="avatar-tip">
          上传图片
        </div>
      </div>
      <div class="form-section">
        <div class="form-group">
          <label>角色名称</label>
          <input
            v-model="localCharacter.name"
            type="text"
            class="form-input"
            placeholder="请输入角色名称"
          >
        </div>
        <div class="form-group">
          <label>别名</label>
          <input
            v-model="localCharacter.alias"
            type="text"
            class="form-input"
            placeholder="多个别名用逗号分隔，如：兰姐,兰儿,小兰"
          >
          <!-- 显示当前别名标签 -->
          <div
            v-if="localCharacter.alias"
            class="alias-tags-container"
          >
            <span
              v-for="alias in getAliasArray(localCharacter.alias)"
              :key="alias"
              class="alias-tag"
            >
              {{ alias }}
              <button
                class="alias-remove-btn"
                @click="removeAlias(alias)"
                title="删除别名"
                type="button"
              >
                ×
              </button>
            </span>
          </div>
        </div>
        <div class="form-group">
          <label>描述</label>
          <textarea
            v-model="localCharacter.description"
            class="form-textarea"
            rows="3"
            placeholder="请输入描述"
          />
        </div>
        <div class="form-group">
          <label>Lora（待开发）</label>
          <input
            v-model="localCharacter.lora"
            type="text"
            class="form-input"
            placeholder="暂未开放，预留接口"
            disabled
          >
        </div>
        <button
          class="save-btn"
          @click="save"
        >
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PresetCharacterEditor',
  props: {
    show: { type: Boolean, default: false },
    character: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      avatarUrl: this.character.avatarUrl || '',
      localCharacter: { ...this.character }
    };
  },
  watch: {
    character: {
      handler(newVal) {
        this.localCharacter = { ...newVal };
        this.avatarUrl = newVal.avatarUrl || '';
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    onFileChange(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (ev) => {
          this.avatarUrl = ev.target.result;
          this.localCharacter.avatarUrl = this.avatarUrl;
        };
        reader.readAsDataURL(file);
      }
    },
    // 获取别名数组 - 支持逗号或空格分隔
    getAliasArray(alias) {
      if (!alias) return [];
      // 先尝试逗号分隔，如果没有逗号则用空格分隔
      const separator = alias.includes(',') ? ',' : ' ';
      return alias.split(separator).map(a => a.trim()).filter(a => a);
    },
    // 删除别名
    removeAlias(aliasToRemove) {
      const aliases = this.getAliasArray(this.localCharacter.alias);
      const updatedAliases = aliases.filter(alias => alias !== aliasToRemove);

      // 保持原来的分隔符格式
      const originalSeparator = this.localCharacter.alias && this.localCharacter.alias.includes(',') ? ', ' : ' ';
      this.localCharacter.alias = updatedAliases.join(originalSeparator);
    },
    save() {
      this.$emit('save', { ...this.localCharacter, avatarUrl: this.avatarUrl });
    }
  }
};
</script>

<style scoped>
.character-editor-modal {
  position: fixed;
  inset: 0;
  background: rgba(20, 22, 30, 0.92);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.editor-card.horizontal-layout {
  background: #232136;
  border-radius: 22px;
  min-width: 480px;
  max-width: 600px;
  width: 100%;
  padding: 36px 32px 28px 32px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  position: relative;
  box-shadow: 0 4px 24px rgba(62,143,176,0.10);
  gap: 32px;
}
.close-btn {
  position: absolute;
  right: 18px;
  top: 18px;
  background: none;
  border: none;
  font-size: 1.7rem;
  color: #3e8fb0;
  cursor: pointer;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s, color 0.18s;
}
.close-btn:hover {
  background: #3e8fb0;
  color: #fff;
}
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0;
  margin-right: 0;
  min-width: 110px;
}
.avatar-upload {
  width: 92px;
  height: 92px;
  border-radius: 50%;
  background: #2a273f;
  border: 2.5px solid #3e8fb0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  font-size: 2.2rem;
  color: #3e8fb0;
  margin-bottom: 6px;
  transition: border 0.18s, color 0.18s;
}
.avatar-upload:hover {
  border: 2.5px solid #4ea8c6;
  color: #4ea8c6;
}
.avatar-upload img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}
.avatar-tip {
  font-size: 0.92em;
  color: #908caa;
  margin-bottom: 2px;
}
.form-section {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-top: 0;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.form-group label {
  font-size: 0.98em;
  color: #908caa;
  font-weight: 500;
  margin-bottom: 2px;
}
.form-input, .form-textarea {
  background: #2a273f;
  border: 1.5px solid #3e8fb0;
  border-radius: 14px;
  color: #e0def4;
  padding: 12px 14px;
  font-size: 1.08em;
  outline: none;
  transition: border 0.18s;
}
.form-input:focus, .form-textarea:focus {
  border: 1.5px solid #4ea8c6;
}
.form-textarea {
  min-height: 60px;
  resize: vertical;
}
.form-input:disabled, .form-textarea:disabled {
  background: #393552;
  color: #908caa;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 别名标签样式 */
.alias-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.alias-tag {
  display: inline-flex;
  align-items: center;
  background: #44415a;
  color: #e0def4;
  font-size: 0.85em;
  padding: 4px 8px;
  border-radius: 14px;
  border: 1px solid #6e6a86;
  transition: background 0.2s, border-color 0.2s;
}

.alias-tag:hover {
  background: #524f67;
  border-color: #908caa;
}

.alias-remove-btn {
  background: none;
  border: none;
  color: #908caa;
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  margin-left: 6px;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: color 0.2s, background 0.2s;
}

.alias-remove-btn:hover {
  color: #eb6f92;
  background: rgba(235, 111, 146, 0.1);
}
.save-btn {
  width: 100%;
  background: #3e8fb0;
  color: #fff;
  border: none;
  border-radius: 14px;
  padding: 0.9em 0;
  font-size: 1.13em;
  font-weight: 600;
  cursor: pointer;
  margin-top: 6px;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 2px 8px rgba(62,143,176,0.10);
}
.save-btn:hover {
  background: #4ea8c6;
  color: #fff;
}
@media (max-width: 600px) {
  .editor-card.horizontal-layout {
    flex-direction: column;
    min-width: 0;
    max-width: 98vw;
    padding: 24px 8px 18px 8px;
    gap: 18px;
  }
  .avatar-section {
    min-width: 0;
    margin-bottom: 12px;
  }
}
</style>