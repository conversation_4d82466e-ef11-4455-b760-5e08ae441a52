/**
 * II. 核心模块: WebSocketManager
 * 
 * 职责：
 * - 管理底层的WebSocket连接生命周期（连接、断开、重连）
 * - 接收原始WebSocket消息
 * - 发送WebSocket消息（如果需要）
 * - 处理WebSocket级别的错误和关闭事件
 */

export class WebSocketManager {
  constructor(config = {}) {
    this.config = {
      ws_url: config.ws_url,
      reconnect_attempts: config.reconnect_attempts || 3, // 减少重连次数
      reconnect_delay: config.reconnect_delay || 5000,    // 增加重连延迟
      max_reconnect_delay: config.max_reconnect_delay || 30000
    };

    // WebSocket连接状态
    this.connection = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.isReconnecting = false;
    this.shouldStop = false; // 添加停止标志

    // 事件回调
    this.callbacks = {
      onOpen: null,
      onMessage: null,
      onError: null,
      onClose: null
    };

    console.log('🔌 [WebSocketManager] 初始化完成');
  }

  /**
   * 设置事件回调
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    const callbackName = `on${eventName.charAt(0).toUpperCase()}${eventName.slice(1)}`;
    if (Object.prototype.hasOwnProperty.call(this.callbacks, callbackName)) {
      this.callbacks[callbackName] = callback;
    } else {
      console.warn(`⚠️ [WebSocketManager] 未知的事件名称: ${eventName}`);
    }
  }

  /**
   * 触发事件回调
   * @param {string} callbackName - 回调名称
   * @param {...any} args - 参数
   */
  triggerCallback(callbackName, ...args) {
    const callback = this.callbacks[callbackName];
    if (callback && typeof callback === 'function') {
      try {
        callback(...args);
      } catch (error) {
        console.error(`❌ [WebSocketManager] 回调执行失败 ${callbackName}:`, error);
      }
    }
  }

  /**
   * 连接到WebSocket服务器
   */
  async connect() {
    if (this.isConnected) {
      console.log('🔌 [WebSocketManager] 已经连接，跳过连接');
      return;
    }

    if (this.isReconnecting) {
      console.log('🔌 [WebSocketManager] 正在重连中，跳过连接');
      return;
    }

    if (this.shouldStop) {
      console.log('🔌 [WebSocketManager] 已停止，跳过连接');
      return;
    }

    this.isReconnecting = true;

    // 生成客户端ID
    const clientId = this.generateClientId();

    // 使用代理WebSocket端点，并添加clientId参数
    const proxyWsUrl = `ws://localhost:8091/api/local/comfyui-ws?clientId=${clientId}`;
    console.log(`🔌 [WebSocketManager] 连接到代理: ${proxyWsUrl}`);

    return new Promise((resolve, reject) => {
      try {
        this.connection = new WebSocket(proxyWsUrl);

        // 设置连接超时
        const connectionTimeout = setTimeout(() => {
          if (this.connection.readyState === WebSocket.CONNECTING) {
            console.warn('⏰ [WebSocketManager] 连接超时');
            this.connection.close();
            reject(new Error('WebSocket连接超时'));
          }
        }, 10000); // 10秒超时

        this.connection.onopen = () => {
          clearTimeout(connectionTimeout);
          console.log('✅ [WebSocketManager] 连接成功');
          this.isConnected = true;
          this.isReconnecting = false;
          this.reconnectAttempts = 0;
          this.triggerCallback('onOpen');
          resolve();
        };

        this.connection.onmessage = async (event) => {
          // 🔧 优化：减少冗余日志，只在开发模式下显示详细信息
          const isDev = process.env.NODE_ENV === 'development';

          // 🔧 修复：处理Blob和字符串消息
          let messageData = event.data;

          if (event.data instanceof Blob) {
            // 🔧 完全禁用Blob消息日志，减少噪音
            try {
              // 将Blob转换为文本
              const text = await event.data.text();
              messageData = text;
            } catch (error) {
              console.error('❌ [WebSocketManager] Blob转换失败:', error);
              return;
            }
          }

          // 🔧 解析JSON消息 - 优化日志输出
          if (typeof messageData === 'string') {
            try {
              const parsed = JSON.parse(messageData);

              // 🔧 优化：只记录关键消息类型，减少冗余日志
              if (parsed.type === 'executed') {
                console.log('🎯 [WebSocketManager] 任务执行完成:', parsed.data?.prompt_id);
              } else if (parsed.type === 'execution_error') {
                console.error('❌ [WebSocketManager] 执行错误:', parsed.data?.exception_message);
              } else if (parsed.type === 'execution_interrupted') {
                console.warn('⚠️ [WebSocketManager] 执行中断:', parsed.data?.prompt_id);
              } else if (isDev && parsed.type === 'progress') {
                // 🔧 简洁日志：进度消息不显示任何日志，保持控制台安静
                // 进度信息通过UI状态更新，不需要控制台输出
              }
              // 其他消息类型在开发模式下才显示
              else if (isDev && parsed.type !== 'status') {
                console.log(`📨 [WebSocketManager] ${parsed.type}:`, parsed.data?.prompt_id || 'unknown');
              }
            } catch (e) {
              if (isDev) console.log('📨 [WebSocketManager] 非JSON消息:', messageData.substring(0, 100));
            }
          }

          this.triggerCallback('onMessage', messageData);
        };

        this.connection.onerror = (error) => {
          clearTimeout(connectionTimeout);
          console.error('❌ [WebSocketManager] 连接错误:', error);
          this.isConnected = false;
          this.isReconnecting = false;
          this.triggerCallback('onError', error);
          reject(new Error('WebSocket连接失败'));
        };

        this.connection.onclose = (event) => {
          clearTimeout(connectionTimeout);
          console.log(`🔌 [WebSocketManager] 连接关闭: ${event.code} - ${event.reason}`);
          this.isConnected = false;
          this.isReconnecting = false;
          this.triggerCallback('onClose', event.code, event.reason);

          // 自动重连逻辑
          if (!this.shouldStop && this.shouldReconnect(event.code)) {
            this.scheduleReconnect();
          }
        };

      } catch (error) {
        console.error('❌ [WebSocketManager] 创建连接失败:', error);
        this.isReconnecting = false;
        reject(new Error(`WebSocket创建失败: ${error.message}`));
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  async disconnect() {
    console.log('🔌 [WebSocketManager] 断开连接...');

    // 停止重连
    this.shouldStop = true;
    this.isReconnecting = false;

    // 清除重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 关闭连接
    if (this.connection) {
      this.connection.close(1000, '正常关闭');
      this.connection = null;
    }

    this.isConnected = false;
    this.reconnectAttempts = 0;

    console.log('✅ [WebSocketManager] 连接已断开，重连已停止');
  }

  /**
   * 发送WebSocket消息
   * @param {Object} payload - 消息内容
   */
  async sendMessage(payload) {
    if (!this.isConnected || !this.connection) {
      throw new Error('WebSocket未连接');
    }

    try {
      const message = JSON.stringify(payload);
      this.connection.send(message);
      // 🔧 优化：只在开发模式下显示发送成功日志
      if (process.env.NODE_ENV === 'development') {
        console.log('📤 [WebSocketManager] 消息发送成功');
      }
    } catch (error) {
      console.error('❌ [WebSocketManager] 消息发送失败:', error);
      throw error;
    }
  }

  /**
   * 判断是否应该重连
   * @param {number} closeCode - 关闭代码
   */
  shouldReconnect(closeCode) {
    // 不重连的情况
    const noReconnectCodes = [
      1000, // 正常关闭
      1001, // 端点离开
      1002, // 协议错误
      1003  // 不支持的数据类型
    ];

    return !noReconnectCodes.includes(closeCode);
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.shouldStop) {
      console.log('🔄 [WebSocketManager] 已停止，取消重连');
      return;
    }

    if (this.reconnectAttempts >= this.config.reconnect_attempts) {
      console.error(`❌ [WebSocketManager] 重连次数超限 (${this.reconnectAttempts}/${this.config.reconnect_attempts})，停止重连`);
      this.shouldStop = true;
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(
      this.config.reconnect_delay * Math.pow(2, this.reconnectAttempts - 1),
      this.config.max_reconnect_delay
    );

    console.log(`🔄 [WebSocketManager] 计划重连 (${this.reconnectAttempts}/${this.config.reconnect_attempts}) 延迟: ${delay}ms`);

    this.reconnectTimer = setTimeout(() => {
      if (!this.shouldStop) {
        this.connect().catch(error => {
          console.error('❌ [WebSocketManager] 重连失败:', error);
          // 如果重连失败，继续尝试下一次重连
          if (!this.shouldStop && this.reconnectAttempts < this.config.reconnect_attempts) {
            this.scheduleReconnect();
          }
        });
      }
    }, delay);
  }

  /**
   * 停止重连
   */
  stopReconnecting() {
    console.log('🛑 [WebSocketManager] 手动停止重连');
    this.shouldStop = true;
    this.isReconnecting = false;

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 重新启用重连
   */
  enableReconnecting() {
    console.log('🔄 [WebSocketManager] 重新启用重连');
    this.shouldStop = false;
    this.reconnectAttempts = 0;
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isReconnecting: this.isReconnecting,
      shouldStop: this.shouldStop,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.config.reconnect_attempts,
      connectionState: this.connection?.readyState,
      connectionStates: {
        0: 'CONNECTING',
        1: 'OPEN',
        2: 'CLOSING',
        3: 'CLOSED'
      }
    };
  }

  /**
   * 生成客户端ID
   * @returns {string} 客户端ID
   */
  generateClientId() {
    return 'client_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
  }
}
