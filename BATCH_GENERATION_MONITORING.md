# 批量生图监控日志指南

## 🎯 监控目标

为了解决以下问题添加了关键监控日志：
1. **批量生图任务完成时输出了什么？**
2. **为什么第一任务完结后，会取消了 selected row？**
3. **selected row 被什么机制清除了？**

## 📊 添加的监控日志

### 1. 批量任务完成监控
**文件**: `src/composables/image-generation/useImageExecution.js`

**日志内容**:
```javascript
// 批量任务完成时
console.log(`✅ [批量生成] 完成 ${batchSize} 张图像 - 行索引: ${rowIndex}`);
console.log(`📊 [批量生成] 任务详情:`, {
  rowIndex,
  batchSize,
  totalProgress: totalProgress.toFixed(1),
  currentBatchProgress,
  taskCompleted: true
});
```

### 2. 选择状态清除监控
**文件**: `src/composables/useBatchOperationUI.js`

**日志内容**:
```javascript
// 批量操作完成时
console.log(`📊 [批量生图完成] 结果:`, {
  cancelled: result.cancelled,
  hasContext: !!context,
  hasRows: !!(context && context.rows),
  rowCount: context && context.rows ? context.rows.length : 0
});

// 批量取消时的选择状态清除
console.log(`🔍 [选择状态清除] 批量取消时清除 ${allSelectedRows.length} 行的选择状态`);
allSelectedRows.forEach((row) => {
  const rowIndex = context.rows.indexOf(row);
  console.log(`🔍 [选择状态清除] 第${rowIndex + 1}行选择状态被清除 (批量取消)`);
  row.isSelected = false;
});
```

### 3. 单个任务完成后的选择状态监控
**文件**: `src/composables/useImageGenerationStudio.js`

**日志内容**:
```javascript
// 任务开始时记录选择状态
if (progressData.stage === 'starting' || progressData.progress === 0) {
  currentRow._wasSelected = currentRow.isSelected;
  console.log(`🔍 [选择状态记录] 第${rowIndex + 1}行任务开始时选择状态: ${currentRow.isSelected}`);
}

// 任务完成后监控选择状态
if (progressData.isCompleted) {
  console.log(`🔍 [选择状态监控] 任务完成后状态:`, {
    rowIndex: rowIndex + 1,
    isSelected: currentRow.isSelected,
    wasSelected: currentRow._wasSelected || '未记录',
    taskCompleted: true,
    isError: progressData.isError
  });
  
  // 记录选择状态变化
  if (currentRow.isSelected !== currentRow._wasSelected) {
    console.log(`🔍 [选择状态变化] 第${rowIndex + 1}行选择状态改变: ${currentRow._wasSelected} -> ${currentRow.isSelected} (任务完成后)`);
  }
}
```

### 4. 队列处理器监控
**文件**: `src/composables/image-generation/useQueueProcessor.js`

**日志内容**:
```javascript
// 任务完成后的状态监控
console.log(`🔍 [队列处理器] 任务完成后状态监控:`, {
  taskId: nextTask.id,
  rowIndex: nextTask.rowIndex + 1,
  taskType: nextTask.type || '图像生成'
});
```

## 🔍 监控重点

### **问题1: 批量生图任务完成输出**
现在会输出：
- ✅ 批量完成的确认信息
- 📊 详细的任务信息（行索引、批次大小、进度等）
- 🔍 队列处理器的任务完成状态

### **问题2: 选择状态被清除的原因**
监控以下可能的清除机制：
- **批量取消时的强制清除** - 在 `completeBatchImageGeneration` 中
- **任务完成后的自动清除** - 在单个任务完成回调中
- **状态同步时的清除** - 在各种状态更新过程中

### **问题3: 选择状态清除的具体机制**
通过以下日志追踪：
- 🔍 **任务开始时的状态记录** - 记录初始选择状态
- 🔍 **任务完成后的状态对比** - 对比前后状态变化
- 🔍 **批量操作的状态管理** - 监控批量操作对选择状态的影响
- 🔍 **队列处理的状态影响** - 监控队列处理对状态的影响

## 📋 使用方法

### **运行批量生图并观察日志**:
1. 选择多行进行批量生图
2. 观察控制台输出的监控日志
3. 重点关注以下标记的日志：
   - `✅ [批量生成]` - 批量任务完成
   - `📊 [批量生图完成]` - 批量操作结果
   - `🔍 [选择状态记录]` - 状态记录
   - `🔍 [选择状态监控]` - 状态监控
   - `🔍 [选择状态变化]` - 状态变化
   - `🔍 [选择状态清除]` - 状态清除

### **分析选择状态变化**:
1. 查找 `🔍 [选择状态记录]` 日志 - 了解任务开始时的状态
2. 查找 `🔍 [选择状态监控]` 日志 - 了解任务完成后的状态
3. 查找 `🔍 [选择状态变化]` 日志 - 了解状态是否发生了变化
4. 查找 `🔍 [选择状态清除]` 日志 - 了解是否有强制清除操作

## 🎯 预期发现

通过这些监控日志，您应该能够：
1. **确认批量任务的完成状态和输出内容**
2. **追踪选择状态在任务完成前后的变化**
3. **识别导致选择状态被清除的具体机制**
4. **定位问题发生的确切位置和原因**

## 🔧 故障排除

如果选择状态被意外清除，检查以下日志：
1. 是否有 `🔍 [选择状态清除]` 日志显示强制清除
2. 是否有 `🔍 [选择状态变化]` 日志显示状态改变
3. 批量操作是否被标记为 `cancelled: true`
4. 是否有其他组件或机制在任务完成后修改了选择状态

---

**添加时间**: 2025-01-29
**目的**: 监控批量生图完成状态和选择状态变化机制
**覆盖范围**: 批量执行、批量操作UI、图像生成工作室、队列处理器
