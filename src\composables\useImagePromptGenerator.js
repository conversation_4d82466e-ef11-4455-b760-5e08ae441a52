/**
 * 图像生成提示词处理服务
 * 基于现有LLM架构，专门处理图像生成提示词的生成
 */

import { ref, computed } from 'vue';
import { useLLMService } from './useLLMService.js';
import { IMAGE_PROMPT_GENERATION_TEMPLATE } from '../services/promptTemplates.js';

export function useImagePromptGenerator() {
  // 使用现有的LLM服务
  const { sendTextToLLMIndependent } = useLLMService();
  
  // 独立的状态管理，不影响全局LLM状态
  const isLoading = ref(false);
  const error = ref(null);
  const lastGeneratedPrompt = ref('');
  const processingStatus = ref('');
  
  // 输入数据状态
  const inputData = ref({
    synopsis: '',           // 总纲
    characters: [],         // 角色列表
    sceneAndTime: '',      // 本组场景与时间
    context: '',           // 上下文
    targetText: ''         // 需要生成画面的文本
  });
  
  // 生成的结果
  const generatedResults = ref([]);
  
  /**
   * 格式化角色信息为文本
   */
  function formatCharactersText(characters) {
    if (!characters || characters.length === 0) {
      return '暂无角色信息';
    }
    
    return characters.map(char => {
      const parts = [];
      if (char.name) parts.push(`姓名: ${char.name}`);
      if (char.englishName) parts.push(`英文名: ${char.englishName}`);
      if (char.gender) parts.push(`性别: ${char.gender}`);
      if (char.age) parts.push(`年龄: ${char.age}`);
      if (char.appearance) parts.push(`外貌: ${char.appearance}`);
      if (char.clothing) parts.push(`服装: ${char.clothing}`);
      if (char.hairStyle) parts.push(`发型: ${char.hairStyle}`);
      if (char.description) parts.push(`描述: ${char.description}`);
      
      return parts.join(', ');
    }).join('\n\n');
  }
  
  /**
   * 构建完整的提示词
   */
  function buildFullPrompt(data) {
    const charactersText = formatCharactersText(data.characters);
    
    const fullPrompt = `${IMAGE_PROMPT_GENERATION_TEMPLATE}

**总纲：** ${data.synopsis || '暂无总纲信息'}

**角色：** 
${charactersText}

**本分组的场景与时间：** ${data.sceneAndTime || '暂无场景时间信息'}

**上下文：** ${data.context || '暂无上下文信息'}

**需要给出画面的文本：** ${data.targetText || '暂无目标文本'}`;

    return fullPrompt;
  }
  
  /**
   * 生成图像提示词
   */
  async function generateImagePrompt(data) {
    if (!data.targetText || data.targetText.trim() === '') {
      throw new Error('请提供需要生成画面的目标文本');
    }
    
    isLoading.value = true;
    error.value = null;
    processingStatus.value = '正在生成图像提示词...';
    
    try {
      // 构建完整提示词
      const fullPrompt = buildFullPrompt(data);
      
      console.log('[useImagePromptGenerator] 发送提示词到LLM，长度:', fullPrompt.length);
      
      // 调用LLM服务
      const result = await sendTextToLLMIndependent(
        '', // 不传递原始文本，避免重复
        fullPrompt,
        {
          analyzeType: 'image-prompt-generation',
          skipLocalAPI: true // 强制使用远程LLM以获得更好的结果
        }
      );
      
      if (result && result.text) {
        // 处理返回的文本，提取关键词
        const generatedPrompt = extractImagePrompt(result.text);
        lastGeneratedPrompt.value = generatedPrompt;
        
        // 保存到结果历史
        const resultItem = {
          id: Date.now(),
          timestamp: new Date().toLocaleString(),
          inputData: { ...data },
          generatedPrompt,
          rawResponse: result.text
        };
        
        generatedResults.value.unshift(resultItem);
        
        // 限制历史记录数量
        if (generatedResults.value.length > 50) {
          generatedResults.value = generatedResults.value.slice(0, 50);
        }
        
        processingStatus.value = '图像提示词生成完成';
        
        return {
          success: true,
          prompt: generatedPrompt,
          rawResponse: result.text,
          resultId: resultItem.id
        };
      } else {
        throw new Error('LLM服务未返回有效响应');
      }
    } catch (err) {
      console.error('[useImagePromptGenerator] 生成失败:', err);
      error.value = err.message || '生成图像提示词时发生错误';
      processingStatus.value = '生成失败';
      
      return {
        success: false,
        error: error.value
      };
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * 从LLM响应中提取图像提示词
   */
  function extractImagePrompt(responseText) {
    if (!responseText) return '';
    
    // 尝试提取最后一行作为关键词列表
    const lines = responseText.trim().split('\n');
    
    // 查找包含逗号分隔关键词的行
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim();
      
      // 跳过空行和明显的标题行
      if (!line || line.startsWith('**') || line.startsWith('#') || line.startsWith('---')) {
        continue;
      }
      
      // 如果包含多个逗号，很可能是关键词列表
      if (line.includes(',') && line.split(',').length >= 3) {
        return line;
      }
    }
    
    // 如果没找到明显的关键词列表，返回最后一个非空行
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim();
      if (line && !line.startsWith('**') && !line.startsWith('#')) {
        return line;
      }
    }
    
    return responseText.trim();
  }
  
  /**
   * 批量生成多个文本的图像提示词
   */
  async function generateBatchImagePrompts(baseData, targetTexts) {
    const results = [];
    
    for (let i = 0; i < targetTexts.length; i++) {
      const targetText = targetTexts[i];
      processingStatus.value = `正在处理第 ${i + 1}/${targetTexts.length} 个文本...`;
      
      try {
        const data = {
          ...baseData,
          targetText
        };
        
        const result = await generateImagePrompt(data);
        results.push({
          targetText,
          ...result
        });
        
        // 添加延迟避免API限制
        if (i < targetTexts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (err) {
        results.push({
          targetText,
          success: false,
          error: err.message
        });
      }
    }
    
    processingStatus.value = `批量处理完成，共处理 ${results.length} 个文本`;
    return results;
  }
  
  /**
   * 清除错误状态
   */
  function clearError() {
    error.value = null;
  }
  
  /**
   * 清除历史记录
   */
  function clearHistory() {
    generatedResults.value = [];
  }
  
  /**
   * 更新输入数据
   */
  function updateInputData(newData) {
    inputData.value = { ...inputData.value, ...newData };
  }
  
  // 计算属性
  const hasError = computed(() => !!error.value);
  const hasResults = computed(() => generatedResults.value.length > 0);
  const isProcessing = computed(() => isLoading.value);
  
  return {
    // 状态
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    hasError,
    hasResults,
    isProcessing,
    processingStatus: computed(() => processingStatus.value),
    lastGeneratedPrompt: computed(() => lastGeneratedPrompt.value),
    generatedResults: computed(() => generatedResults.value),
    inputData: computed(() => inputData.value),
    
    // 方法
    generateImagePrompt,
    generateBatchImagePrompts,
    updateInputData,
    clearError,
    clearHistory,
    buildFullPrompt,
    extractImagePrompt
  };
}
