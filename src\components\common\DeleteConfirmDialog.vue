<template>
  <div
    class="delete-confirm-overlay"
    v-if="visible"
    @click="handleCancel"
  >
    <div
      class="delete-confirm-dialog"
      @click.stop
    >
      <div class="dialog-header">
        <h3>确认删除</h3>
      </div>
      <div class="dialog-content">
        <p>{{ message }}</p>
        <p class="warning-text">
          此操作不可撤销，将同时删除文件系统中的图片文件。
        </p>
      </div>
      <div class="dialog-actions">
        <button
          class="cancel-btn"
          @click="handleCancel"
        >
          取消
        </button>
        <button
          class="confirm-btn"
          @click="handleConfirm"
        >
          确认删除
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeleteConfirmDialog',
  emits: ['confirm', 'cancel'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: '确定要删除这张图片吗？'
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm');
    },
    handleCancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style scoped>
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-confirm-dialog {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  max-width: 500px;
  color: #e0e0e0;
}

.dialog-header h3 {
  margin: 0 0 15px 0;
  color: #ff6b6b;
}

.dialog-content p {
  margin: 10px 0;
}

.warning-text {
  color: #ffa500;
  font-size: 0.9em;
}

.dialog-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn, .confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn {
  background: #666;
  color: white;
}

.confirm-btn {
  background: #ff4757;
  color: white;
}

.cancel-btn:hover {
  background: #777;
}

.confirm-btn:hover {
  background: #ff3742;
}
</style>
