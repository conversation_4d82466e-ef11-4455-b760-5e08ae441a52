const fs = require('fs');
const path = require('path');

// 扫描目录中的所有.vue文件
function scanDirectory(directory) {
  const files = [];
  const items = fs.readdirSync(directory);
  
  for (const item of items) {
    const fullPath = path.join(directory, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 如果是目录，递归扫描
      files.push(...scanDirectory(fullPath));
    } else if (item.endsWith('.vue')) {
      // 如果是.vue文件，添加到结果
      files.push(fullPath);
    }
  }
  
  return files;
}

// 修复文件中的重复模板问题
function fixDuplicateStructure(fileContent) {
  // 检测是否有多个<template>
  const templateMatches = fileContent.match(/<template[\s\S]*?<\/template>/g);
  if (templateMatches && templateMatches.length > 1) {
    console.log(`  发现多个<template>标签`);
    // 只保留第一个<template>部分
    fileContent = fileContent.replace(/<template[\s\S]*?<\/template>/g, (match, index) => {
      return index === 0 ? match : '';
    });
  }
  
  // 检测是否有多个<script>
  const scriptMatches = fileContent.match(/<script[\s\S]*?<\/script>/g);
  if (scriptMatches && scriptMatches.length > 1) {
    console.log(`  发现多个<script>标签`);
    // 只保留第一个<script>部分
    fileContent = fileContent.replace(/<script[\s\S]*?<\/script>/g, (match, index) => {
      return index === 0 ? match : '';
    });
  }
  
  // 检测是否有多个<style>
  const styleMatches = fileContent.match(/<style[\s\S]*?<\/style>/g);
  if (styleMatches && styleMatches.length > 1) {
    console.log(`  发现多个<style>标签`);
    // 只保留第一个<style>部分
    fileContent = fileContent.replace(/<style[\s\S]*?<\/style>/g, (match, index) => {
      return index === 0 ? match : '';
    });
  }
  
  return fileContent;
}

// 修复文件中的中文乱码问题
function fixChineseEncoding(fileContent) {
  // 常见中文乱码替换
  const replacements = [
    { from: '创�?', to: '创建' },
    { from: '项�?', to: '项目' },
    { from: '名�?', to: '名称' },
    { from: '失�?', to: '失败' },
    { from: '返�?', to: '返利' },
    { from: '充�?', to: '充值' },
    { from: '角�?', to: '角色' },
    { from: '记�?', to: '记录' },
    { from: '事�?', to: '事件' },
    { from: '方�?', to: '方法' },
    { from: '（{{ projects.length }}�?', to: '（{{ projects.length }}）' }
  ];
  
  let fixed = false;
  for (const { from, to } of replacements) {
    if (fileContent.includes(from)) {
      fileContent = fileContent.replace(new RegExp(from, 'g'), to);
      console.log(`  修复中文乱码: ${from} -> ${to}`);
      fixed = true;
    }
  }
  
  if (fixed) {
    console.log('  检测并修复了中文乱码');
  }
  
  return fileContent;
}

// 处理单个文件
function processFile(filePath) {
  console.log(`处理文件: ${filePath}`);
  let fileContent = fs.readFileSync(filePath, 'utf8');
  const originalContent = fileContent;
  
  // 修复重复结构
  fileContent = fixDuplicateStructure(fileContent);
  
  // 修复中文乱码
  fileContent = fixChineseEncoding(fileContent);
  
  // 如果内容有变化，写回文件
  if (fileContent !== originalContent) {
    console.log(`  文件已修改，正在保存...`);
    fs.writeFileSync(filePath, fileContent, 'utf8');
  } else {
    console.log(`  没有需要修改的问题`);
  }
}

// 主函数
function main() {
  const componentsDir = path.join(__dirname, '../src/components');
  console.log(`扫描目录: ${componentsDir}`);
  
  const vueFiles = scanDirectory(componentsDir);
  console.log(`找到 ${vueFiles.length} 个 .vue 文件`);
  
  // 处理每个文件
  for (const filePath of vueFiles) {
    processFile(filePath);
  }
  
  console.log('所有文件处理完成！');
}

main(); 