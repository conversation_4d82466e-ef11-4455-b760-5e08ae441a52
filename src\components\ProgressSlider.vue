<template>
  <div class="progress-slider-container">
    <div
      class="progress-slider-track"
      ref="track"
      @click="handleTrackClick"
    >
      <div 
        class="progress-slider-fill" 
        :style="{ width: `${displayValue}%` }"
      />
      <div 
        class="progress-slider-handle" 
        :class="{ 'active': isDragging }"
        :style="{ left: `${displayValue}%` }"
        @mousedown="startDrag"
        @touchstart="startDrag"
      >
        <div
          class="handle-tooltip"
          v-if="showTooltip || isDragging"
        >
          {{ Math.round(displayValue) }}%
        </div>
      </div>
    </div>
    <div
      class="progress-slider-labels"
      v-if="showLabels"
    >
      <div class="progress-slider-label-start">
        {{ startLabel }}
      </div>
      <div class="progress-slider-label-end">
        {{ endLabel }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressSlider',
  props: {
    value: {
      type: Number,
      default: 0
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showTooltip: {
      type: Boolean,
      default: false
    },
    showLabels: {
      type: Boolean,
      default: false
    },
    startLabel: {
      type: String,
      default: '0%'
    },
    endLabel: {
      type: String,
      default: '100%'
    },
    // 动画效果：简单的线性进度或脉冲效果
    animationStyle: {
      type: String,
      default: 'linear' // 'linear', 'pulse'
    }
  },
  data() {
    return {
      isDragging: false,
      internalValue: this.value,
      displayValue: this.value,
      // 在动画模式下使用的属性
      isAnimating: false
    }
  },
  watch: {
    value(newVal) {
      this.internalValue = newVal;
      if (!this.isDragging) {
        this.updateDisplayValue();
      }
    }
  },
  mounted() {
    window.addEventListener('mousemove', this.handleDrag);
    window.addEventListener('mouseup', this.stopDrag);
    window.addEventListener('touchmove', this.handleDrag);
    window.addEventListener('touchend', this.stopDrag);
    
    this.updateDisplayValue();
    
    // 如果设置了脉冲效果，启动动画
    if (this.animationStyle === 'pulse') {
      this.startAnimation();
    }
  },
  unmounted() {
    window.removeEventListener('mousemove', this.handleDrag);
    window.removeEventListener('mouseup', this.stopDrag);
    window.removeEventListener('touchmove', this.handleDrag);
    window.removeEventListener('touchend', this.stopDrag);
    
    // 停止动画
    this.stopAnimation();
  },
  methods: {
    startDrag(event) {
      if (this.disabled) return;
      
      this.isDragging = true;
      this.handleDrag(event);
    },
    handleDrag(event) {
      if (!this.isDragging) return;
      
      // 阻止默认行为和事件冒泡
      event.preventDefault();
      
      const track = this.$refs.track;
      const trackRect = track.getBoundingClientRect();
      
      // 获取鼠标或触摸事件位置
      let clientX;
      if (event.touches && event.touches[0]) {
        clientX = event.touches[0].clientX;
      } else {
        clientX = event.clientX;
      }
      
      // 计算在轨道上的位置百分比
      let percentage = ((clientX - trackRect.left) / trackRect.width) * 100;
      
      // 限制在有效范围内
      percentage = Math.max(0, Math.min(100, percentage));
      
      // 更新内部值
      this.internalValue = this.min + (percentage / 100) * (this.max - this.min);
      this.displayValue = percentage;
      
      // 向父组件发出事件
      this.$emit('input', this.internalValue);
      this.$emit('change', this.internalValue);
    },
    stopDrag() {
      if (this.isDragging) {
        this.isDragging = false;
        // 最终确认值变化
        this.$emit('dragend', this.internalValue);
      }
    },
    handleTrackClick(event) {
      if (this.disabled) return;
      
      // 模拟拖动，直接设置到点击位置
      this.startDrag(event);
      this.stopDrag();
    },
    updateDisplayValue() {
      const percentage = ((this.internalValue - this.min) / (this.max - this.min)) * 100;
      this.displayValue = Math.max(0, Math.min(100, percentage));
    },
    startAnimation() {
      this.isAnimating = true;
      this.animateProgress();
    },
    stopAnimation() {
      this.isAnimating = false;
    },
    animateProgress() {
      if (!this.isAnimating) return;
      
      // 简单的脉冲效果，在实际进度附近小范围波动
      const baseProgress = this.displayValue;
      const pulseRange = 5; // 最大波动范围
      
      const pulse = () => {
        // 只在未拖动状态下进行动画
        if (!this.isDragging && this.isAnimating) {
          // 添加小的波动效果
          const variation = (Math.sin(Date.now() / 500) * pulseRange) / 2;
          this.displayValue = Math.max(0, Math.min(100, baseProgress + variation));
          
          requestAnimationFrame(pulse);
        }
      };
      
      requestAnimationFrame(pulse);
    }
  }
}
</script>

<style scoped>
.progress-slider-container {
  width: 100%;
  padding: 10px 0;
  user-select: none;
}

.progress-slider-track {
  height: 8px;
  background-color: #333344;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.progress-slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #2dc0f0;
  border-radius: 4px;
  transition: width 0.1s ease;
}

.progress-slider-handle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #f5c2e7;
  border: 2px solid #2dc0f0;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  z-index: 2;
}

.progress-slider-handle.active {
  cursor: grabbing;
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 0 0 6px rgba(45, 192, 240, 0.2);
}

.handle-tooltip {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background-color: #1e1e2e;
  color: #cdd6f4;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #2dc0f0;
}

.handle-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  border-width: 5px;
  border-style: solid;
  border-color: #2dc0f0 transparent transparent transparent;
  transform: translateX(-50%);
}

.progress-slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 0.8rem;
  color: #a6adc8;
}

/* 脉冲动画效果 */
@keyframes pulse-fill {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

.progress-slider-fill.pulse {
  animation: pulse-fill 1.5s infinite;
}
</style> 