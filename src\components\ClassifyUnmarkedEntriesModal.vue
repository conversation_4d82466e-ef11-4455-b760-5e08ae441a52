<template>
  <BaseDrawer
    :show="show"
    @update:show="handleUpdateShow"
    :drawer-width="'450px'"
    :show-close-button="true"
    :title="'分类未标记条目'"
  >
    <div class="classify-entries-modal-content">
      <p
        v-if="!parsedLines.length && linesToClassify.length > 0"
        class="info-message"
      >
        正在解析条目...
      </p>
      <p
        v-if="linesToClassify.length === 0 && !localLines.length"
        class="info-message"
      >
        没有需要分类的条目
      </p>

      <div
        v-if="parsedLines.length > 0"
        class="entries-list"
        ref="entriesListRef"
      >
        <div
          v-for="(item, index) in parsedLines"
          :key="index"
          class="entry-item"
        >
          <div class="entry-details">
            <span
              v-if="item.parseError"
              class="parse-error"
            >
              <i class="ri-error-warning-line" />
              此行无法解析为"名称 - 描述"格式，将被忽略："{{ item.originalLine }}"
            </span>
            <template v-else>
              <div class="entry-name">
                <strong>名称:</strong> {{ item.name }}
              </div>
              <div class="entry-description">
                <strong>描述:</strong> {{ item.description }}
              </div>
            </template>
          </div>
          <div
            v-if="!item.parseError"
            class="entry-actions"
          >
            <button
              @click="classifyItem(index, 'character')"
              :class="{'selected': item.type === 'character'}"
              class="classify-button character-button"
            >
              <i class="ri-user-line" /> 角色
            </button>
            <button
              @click="classifyItem(index, 'special')"
              :class="{'selected': item.type === 'special'}"
              class="classify-button special-button"
            >
              <i class="ri-treasure-map-line" /> 物品/场景
            </button>
          </div>
        </div>
      </div>
      <p
        v-if="completelyUnparsableCount > 0 && parsedLines.some(p => !p.parseError)"
        class="info-message bottom-info"
      >
        注意: {{ completelyUnparsableCount }} 行因格式不符已被自动忽略
      </p>

      <div class="modal-footer">
        <button
          @click="handleCancel"
          class="footer-button cancel-button"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          class="footer-button confirm-button"
          :disabled="!hasAnyClassification"
        >
          确认分类
        </button>
      </div>
    </div>
  </BaseDrawer>
</template>

<script>
import BaseDrawer from './BaseDrawer.vue';
import { ref, watch, computed, nextTick } from 'vue';

export default {
  name: 'ClassifyUnmarkedEntriesModal',
  components: {
    BaseDrawer,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    linesToClassify: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:show', 'classify-complete', 'classify-cancel'],
  setup(props, { emit }) {
    const localLines = ref([]);
    const parsedLines = ref([]);
    const completelyUnparsableCount = ref(0);
    const entriesListRef = ref(null);

    const extractItemsFromLine = (lineString) => {
      console.log("[ClassifyModal] Processing line:", JSON.stringify(lineString));
      lineString = lineString.replace(/^\uFEFF/, ''); 
      lineString = lineString.trim(); 
      if (!lineString) return [];
      const extracted = [];
      const nameContentChars = "\\u4E00-\\u9FA5A-Za-z0-9_ ";
      const namePatternGroup = `([${nameContentChars}]+)`;
      const descPatternGroup = String.raw`([\s\S]+)`;
      const separatorPattern = String.raw`\s*-\s*`;
      const lookaheadPattern = String.raw`(?=$)`;
      const finalRegexString = String.raw`${namePatternGroup}${separatorPattern}${descPatternGroup}${lookaheadPattern}`;
      let regex;
      try {
        regex = new RegExp(finalRegexString, "g");
      } catch (e) {
        console.error("[ClassifyModal] Regex Compilation Error:", e.message, "Faulty Regex:", finalRegexString, "Line:", JSON.stringify(lineString));
        extracted.push({ name: '', description: '', type: null, originalLine: lineString, parseError: true, errorMessage: `Regex compilation failed: ${e.message}` });
        return extracted;
      }
      let match;
      let lastIndex = 0;
      let foundAnyItems = false;
      while ((match = regex.exec(lineString)) !== null) {
        foundAnyItems = true;
        const name = match[1].trim();
        const description = match[2].trim();
        if (match.index > lastIndex) {
          const precedingText = lineString.substring(lastIndex, match.index).trim();
          if (precedingText) {
            extracted.push({ name: '', description: '', type: null, originalLine: precedingText, parseError: true });
          }
        }
        if (name) {
          extracted.push({ name: name, description: description, type: null, originalLine: `${name} - ${description}`, parseError: false });
        }
        lastIndex = regex.lastIndex;
      }
      if (!foundAnyItems) {
        extracted.push({ name: '', description: '', type: null, originalLine: lineString, parseError: true });
      } else if (lastIndex < lineString.length) {
        const trailingText = lineString.substring(lastIndex).trim();
        if (trailingText) {
          extracted.push({ name: '', description: '', type: null, originalLine: trailingText, parseError: true });
        }
      }
      return extracted;
    };

    watch(() => [props.show, props.linesToClassify], async ([newShow, newLinesArray]) => {
      if (newShow) {
        console.log("[ClassifyModal] Watch triggered. Show is true.");
        const useStaticTestData = false;
        if (useStaticTestData) {
          const staticTestData = [];
          for (let i = 0; i < 30; i++) {
              staticTestData.push({
                  name: `静态测试名称?${i + 1}`,
                  description: `这是一个静态的、比较长的描述文本，目的是为了确保内容能够溢出列表容器以便测试滚动功能。条目编? ${i + 1}`,
                  type: null,
                  originalLine: `静态测试名称?${i + 1} - 描述 ${i + 1}`,
                  parseError: false
              });
          }
          parsedLines.value = staticTestData;
          completelyUnparsableCount.value = 0;
          console.log("[ClassifyModal] Populated with static test data. Count:", parsedLines.value.length);
        } else {
          console.log("[ClassifyModal] Using dynamic data from props.linesToClassify:", JSON.stringify(newLinesArray));
          const allParsedItems = [];
          if (newLinesArray && newLinesArray.length > 0) {
            for (const singleLineString of newLinesArray) {
              if (typeof singleLineString === 'string') {
                const itemsFromThisLine = extractItemsFromLine(singleLineString);
                if (itemsFromThisLine && itemsFromThisLine.length > 0) {
                  allParsedItems.push(...itemsFromThisLine);
                } else if (singleLineString.trim()) { 
                  allParsedItems.push({
                    name: '', description: '', type: null, originalLine: singleLineString, parseError: true
                  });
                }
              }
            }
            parsedLines.value = allParsedItems;
          } else {
            parsedLines.value = [];
          }
          completelyUnparsableCount.value = parsedLines.value.filter(p => p.parseError).length;
          console.log("[ClassifyModal] Dynamic data processing complete. Parsed count:", parsedLines.value.length, "Unparsable:", completelyUnparsableCount.value);
        }
        
        await nextTick();
        if (entriesListRef.value) {
          console.log('[ClassifyModal] Scrolling attempt (nextTick). ClientHeight:', entriesListRef.value.clientHeight, 'ScrollHeight:', entriesListRef.value.scrollHeight);
          entriesListRef.value.scrollTop = entriesListRef.value.scrollHeight;
          
          setTimeout(() => {
            if (entriesListRef.value) {
              console.log('[ClassifyModal] Scrolling attempt (setTimeout). ClientHeight:', entriesListRef.value.clientHeight, 'ScrollHeight:', entriesListRef.value.scrollHeight);
              entriesListRef.value.scrollTop = entriesListRef.value.scrollHeight;
            }
          }, 50);
        } else {
          console.log('[ClassifyModal] entriesListRef.value is null after nextTick.');
        }

      } else {
        parsedLines.value = [];
        completelyUnparsableCount.value = 0;
        console.log("[ClassifyModal] Modal hidden or show is false, resetting state.");
      }
    }, { immediate: true, deep: true });

    const classifyItem = (index, type) => {
      if (parsedLines.value[index] && !parsedLines.value[index].parseError) {
        parsedLines.value[index].type = type;
      }
    };

    const handleConfirm = () => {
      const classifiedItems = parsedLines.value.filter(item => !item.parseError && item.type !== null);
      emit('classify-complete', classifiedItems);
      emit('update:show', false);
    };

    const handleCancel = () => {
      console.log('[ClassifyModal] handleCancel called');
      emit('classify-cancel');
      emit('update:show', false);
    };
    
    const handleUpdateShow = (value) => {
        console.log('[ClassifyModal] handleUpdateShow called with value:', value);
        emit('update:show', value);
    };

    const hasAnyClassification = computed(() => {
      return parsedLines.value.some(item => !item.parseError && item.type !== null);
    });

    return {
      localLines,
      parsedLines,
      classifyItem,
      handleConfirm,
      handleCancel,
      handleUpdateShow,
      hasAnyClassification,
      completelyUnparsableCount,
      entriesListRef,
    };
  },
};
</script>

<style scoped>
.classify-entries-modal-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.info-message {
  color: #908caa;
  margin-bottom: 16px;
  text-align: center;
}
.bottom-info {
  margin-top: 10px;
  font-size: 0.8rem;
}

.entries-list {
  flex-grow: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.entry-item {
  background-color: #2a273f;
  border: 1px solid #44415a;
  border-radius: 4px;
  padding: 12px;
}

.entry-details {
  margin-bottom: 10px;
}

.entry-name, .entry-description {
  font-size: 0.9rem;
  color: #e0def4;
  line-height: 1.4;
  word-break: break-word;
}
.entry-name strong, .entry-description strong {
  color: #c9c7e0;
}

.parse-error {
  color: #eb6f92;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}
.parse-error i {
  font-size: 1.1rem;
}


.entry-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
}

.classify-button {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #56526e;
  background-color: #3a3750;
  color: #e0def4;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.classify-button:hover {
  border-color: #7a759d;
  background-color: #433f5c;
}

.classify-button.selected {
  border-color: #3e8fb0;
  background-color: #3e8fb0;
  color: #ffffff;
}
.classify-button.character-button.selected {
  border-color: #3e8fb0; /* Vue Green */
  background-color: #3e8fb0;
}
.classify-button.special-button.selected {
  border-color: #ea9a97; /* A pinkish tone */
  background-color: #ea9a97;
}


.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #44415a;
  margin-top: auto; /* Pushes footer to the bottom */
}

.footer-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #908caa;
  color: #908caa;
}

.cancel-button:hover {
  border-color: #e0def4;
  color: #e0def4;
}

.confirm-button {
  background-color: #3e8fb0;
  border: 1px solid #3e8fb0;
  color: #232136;
  font-weight: 500;
}

.confirm-button:hover:not(:disabled) {
  background-color: #4ea8c6;
  border-color: #4ea8c6;
}

.confirm-button:disabled {
  background-color: #3a3750;
  border-color: #3a3750;
  color: #6e6a86;
  cursor: not-allowed;
}
</style> 