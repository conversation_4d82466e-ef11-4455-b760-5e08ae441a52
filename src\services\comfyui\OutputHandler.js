/**
 * V. 核心模块: OutputHandler
 * 
 * 职责：
 * - 处理 executed 消息中特定类型节点的输出（尤其是 SaveImage, PreviewImage 等）
 * - 提取文件信息（文件名、子文件夹、类型）
 * - （可选）通过HTTP /view API 自动获取图像数据
 */

export class OutputHandler {
  constructor(config = {}) {
    this.config = {
      auto_fetch_outputs: config.auto_fetch_outputs || false,
      output_directory: config.output_directory || null,
      httpClient: config.httpClient || null,
      serverUrl: config.serverUrl || 'http://localhost:8188' // 🔧 修复：添加serverUrl配置
    };

    // 事件回调
    this.callbacks = {
      onOutputReceived: null
    };

    // 支持的输出节点类型
    this.supportedNodeTypes = [
      'SaveImage',
      'PreviewImage',
      'SaveImageWebsocket',
      'VHS_VideoCombine'
    ];

    console.log('📤 [OutputHandler] 初始化完成');
  }

  /**
   * 设置事件回调
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    const callbackName = `on${eventName.charAt(0).toUpperCase()}${eventName.slice(1)}`;
    if (Object.prototype.hasOwnProperty.call(this.callbacks, callbackName)) {
      this.callbacks[callbackName] = callback;
    } else {
      console.warn(`⚠️ [OutputHandler] 未知的事件名称: ${eventName}`);
    }
  }

  /**
   * 触发事件回调
   * @param {string} callbackName - 回调名称
   * @param {...any} args - 参数
   */
  triggerCallback(callbackName, ...args) {
    const callback = this.callbacks[callbackName];
    if (callback && typeof callback === 'function') {
      try {
        callback(...args);
      } catch (error) {
        console.error(`❌ [OutputHandler] 回调执行失败 ${callbackName}:`, error);
      }
    }
  }

  /**
   * 处理节点输出
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Object} output_data - 输出数据
   */
  async processNodeOutput(prompt_id, node_id, output_data) {
    try {
      console.log(`📤 [OutputHandler] 处理节点输出: ${prompt_id} -> ${node_id}`);

      if (!output_data || typeof output_data !== 'object') {
        console.warn(`⚠️ [OutputHandler] 无效的输出数据: ${node_id}`);
        return;
      }

      // 处理不同类型的输出
      for (const [outputKey, outputValue] of Object.entries(output_data)) {
        await this.processOutputType(prompt_id, node_id, outputKey, outputValue);
      }

    } catch (error) {
      console.error(`❌ [OutputHandler] 处理节点输出失败: ${error.message}`);
    }
  }

  /**
   * 处理特定类型的输出
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {string} output_type - 输出类型
   * @param {any} output_value - 输出值
   */
  async processOutputType(prompt_id, node_id, output_type, output_value) {
    console.log(`📤 [OutputHandler] 处理输出类型: ${output_type}`);

    switch (output_type) {
      case 'images':
        await this.processImageOutput(prompt_id, node_id, output_value);
        break;
      case 'videos':
        await this.processVideoOutput(prompt_id, node_id, output_value);
        break;
      case 'audio':
        await this.processAudioOutput(prompt_id, node_id, output_value);
        break;
      default:
        console.log(`📤 [OutputHandler] 未处理的输出类型: ${output_type}`);
    }
  }

  /**
   * 处理图像输出
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Array} images - 图像数组
   */
  async processImageOutput(prompt_id, node_id, images) {
    if (!Array.isArray(images)) {
      console.warn(`⚠️ [OutputHandler] 图像输出不是数组: ${node_id}`);
      return;
    }

    console.log(`🖼️ [OutputHandler] 处理图像输出: ${images.length} 张图片`);

    for (const imageInfo of images) {
      await this.processImageFile(prompt_id, node_id, imageInfo);
    }
  }

  /**
   * 处理单个图像文件
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Object} imageInfo - 图像信息
   */
  async processImageFile(prompt_id, node_id, imageInfo) {
    try {
      const { filename, subfolder, type } = imageInfo;
      
      console.log(`🖼️ [OutputHandler] 处理图像文件: ${filename}`);

      const outputDetails = {
        filename,
        subfolder: subfolder || '',
        type: type || 'output',
        node_id,
        prompt_id
      };

      // 触发输出接收事件
      this.triggerCallback('onOutputReceived', {
        prompt_id,
        node_id,
        output_type: 'image',
        output_details: outputDetails
      });

      // 可选：自动获取图像数据
      if (this.config.auto_fetch_outputs && this.config.httpClient) {
        await this.fetchImageData(outputDetails);
      }

    } catch (error) {
      console.error(`❌ [OutputHandler] 处理图像文件失败: ${error.message}`);
    }
  }

  /**
   * 处理视频输出
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Array} videos - 视频数组
   */
  async processVideoOutput(prompt_id, node_id, videos) {
    if (!Array.isArray(videos)) {
      console.warn(`⚠️ [OutputHandler] 视频输出不是数组: ${node_id}`);
      return;
    }

    console.log(`🎥 [OutputHandler] 处理视频输出: ${videos.length} 个视频`);

    for (const videoInfo of videos) {
      const { filename, subfolder, type } = videoInfo;
      
      const outputDetails = {
        filename,
        subfolder: subfolder || '',
        type: type || 'output',
        node_id,
        prompt_id
      };

      this.triggerCallback('onOutputReceived', {
        prompt_id,
        node_id,
        output_type: 'video',
        output_details: outputDetails
      });
    }
  }

  /**
   * 处理音频输出
   * @param {string} prompt_id - 任务ID
   * @param {string} node_id - 节点ID
   * @param {Array} audios - 音频数组
   */
  async processAudioOutput(prompt_id, node_id, audios) {
    if (!Array.isArray(audios)) {
      console.warn(`⚠️ [OutputHandler] 音频输出不是数组: ${node_id}`);
      return;
    }

    console.log(`🔊 [OutputHandler] 处理音频输出: ${audios.length} 个音频`);

    for (const audioInfo of audios) {
      const { filename, subfolder, type } = audioInfo;
      
      const outputDetails = {
        filename,
        subfolder: subfolder || '',
        type: type || 'output',
        node_id,
        prompt_id
      };

      this.triggerCallback('onOutputReceived', {
        prompt_id,
        node_id,
        output_type: 'audio',
        output_details: outputDetails
      });
    }
  }

  /**
   * 获取图像数据
   * @param {Object} outputDetails - 输出详情
   */
  async fetchImageData(outputDetails) {
    try {
      if (!this.config.httpClient) {
        console.warn('⚠️ [OutputHandler] 没有配置HTTP客户端，跳过图像数据获取');
        return;
      }

      console.log(`📥 [OutputHandler] 获取图像数据: ${outputDetails.filename}`);

      // 🆕 构建图像URL，使用新的后端代理
      const imageUrl = this.buildImageUrl(
        outputDetails.filename,
        outputDetails.subfolder,
        outputDetails.type
      );

      // 🆕 使用HTTP客户端获取图像数据
      const imageData = await this.config.httpClient.getView(
        outputDetails.filename,
        outputDetails.subfolder,
        outputDetails.type
      );

      if (imageData) {
        console.log(`✅ [OutputHandler] 图像数据获取成功: ${outputDetails.filename}`);

        // 🆕 触发图像数据接收事件，包含URL信息
        this.triggerCallback('onImageDataReceived', {
          prompt_id: outputDetails.prompt_id,
          node_id: outputDetails.node_id,
          filename: outputDetails.filename,
          imageData,
          imageUrl
        });

        // 可选：保存到本地目录
        if (this.config.output_directory) {
          await this.saveImageToDirectory(outputDetails, imageData);
        }
      }

    } catch (error) {
      console.error(`❌ [OutputHandler] 获取图像数据失败: ${error.message}`);

      // 🆕 触发图像获取失败事件
      this.triggerCallback('onImageFetchError', {
        prompt_id: outputDetails.prompt_id,
        node_id: outputDetails.node_id,
        filename: outputDetails.filename,
        error: error.message
      });
    }
  }

  /**
   * 🆕 构建图像URL
   * @param {string} filename - 文件名
   * @param {string} subfolder - 子文件夹
   * @param {string} type - 文件类型
   * @returns {string} 图像URL
   */
  buildImageUrl(filename, subfolder = '', type = 'output') {
    const params = new URLSearchParams({
      filename,
      type,
      serverUrl: this.config.serverUrl || 'http://localhost:8188'
    });

    if (subfolder) {
      params.append('subfolder', subfolder);
    }

    // 使用新的后端代理路由
    const imageUrl = `http://localhost:8091/api/local/comfyui/view?${params.toString()}`;
    console.log(`🖼️ [OutputHandler] 构建图像URL: ${imageUrl}`);

    return imageUrl;
  }

  /**
   * 保存图像到目录
   * @param {Object} outputDetails - 输出详情
   * @param {Uint8Array} imageData - 图像数据
   */
  async saveImageToDirectory(outputDetails, imageData) {
    try {
      // 这里可以实现保存到本地目录的逻辑
      // 由于浏览器环境限制，通常需要通过下载API或其他方式实现
      console.log(`💾 [OutputHandler] 保存图像: ${outputDetails.filename}`);
      
      // 创建下载链接
      const blob = new Blob([imageData], { type: 'image/png' });
      const url = URL.createObjectURL(blob);
      
      // 触发下载
      const a = document.createElement('a');
      a.href = url;
      a.download = outputDetails.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error(`❌ [OutputHandler] 保存图像失败: ${error.message}`);
    }
  }

  /**
   * 检查节点类型是否支持
   * @param {string} nodeType - 节点类型
   * @returns {boolean} 是否支持
   */
  isSupportedNodeType(nodeType) {
    return this.supportedNodeTypes.includes(nodeType);
  }

  /**
   * 获取支持的节点类型列表
   * @returns {Array<string>} 支持的节点类型
   */
  getSupportedNodeTypes() {
    return [...this.supportedNodeTypes];
  }
}
