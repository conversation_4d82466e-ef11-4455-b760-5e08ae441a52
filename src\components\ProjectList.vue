<template>
  <section
    v-if="projects.length"
    style="margin-top: 2.5rem;"
  >
    <h3 style="color:#fff;font-size:1.15rem;margin-bottom:0.7rem;">
      我的项目（{{ projects.length }}）
    </h3>
    <div style="display:flex;gap:2.2rem;flex-wrap:wrap;">
      <div
        v-for="(project, idx) in projects"
        :key="project.title+project.date" 
        class="card-hover-effect"
        style="width:110px;background:#111;border-radius:12px;padding:1.1rem 0.5rem 0.7rem 0.5rem;display:flex;flex-direction:column;align-items:center;position:relative;"
        @click.self="handleCardClick(project)"
      >
        <div
          style="width:70px;height:70px;background:#000;border-radius:8px;margin-bottom:0.7rem;"
          @click.stop="handleCardClick(project)"
        />
        <div style="color:#fff;font-size:1rem;font-weight:600;word-break:break-all;min-height:1.5em;">
          <span
            v-if="editingIndex !== idx"
            @click.stop="startEditTitle(idx, project)"
            @mouseenter="hoveredIndex = idx"
            @mouseleave="hoveredIndex = null"
            :class="{ 'text-hover-bling': hoveredIndex === idx }"
            style="cursor:pointer;"
          >{{ project.title }}</span>
          <input
            v-else
            :id="'edit-title-input-' + idx"
            v-model="editingTitle"
            @blur="finishEditTitle(idx, project.title)"
            @keyup.enter="finishEditTitle(idx, project.title)"
            @keyup.tab="finishEditTitle(idx, project.title)"
            style="width:90px;background:#222;color:#fff;border:1px solid #a23a5a;border-radius:6px;padding:2px 6px;font-size:1em;"
          >
        </div>
        <button
          @click.stop.prevent="handleDelete(project.title)"
          title="删除"
          style="position:absolute;top:8px;right:8px;z-index:10;background:none;border:none;cursor:pointer;padding:0;"
        >
          <i
            class="ri-delete-bin-line"
            style="color:#f38ba8;font-size:1.1em;"
          />
        </button>
        <div style="color:#b5bfe2;font-size:0.85rem;margin-top:0.2rem;">
          {{ project.date }}
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { ref } from 'vue';
import { useLocalCommunication } from '@/utils/localCommunication';

export default {
  name: 'ProjectList',
  
  props: {
    projects: {
      type: Array,
      required: true
    }
  },

  emits: ['select', 'delete', 'update'],

  setup(props, { emit }) {
    const { renameFolder } = useLocalCommunication();
    
    const editingIndex = ref(null);
    const editingTitle = ref('');
    const hoveredIndex = ref(null);

    function handleCardClick(project) {
      emit('select', project);
    }

    function startEditTitle(idx, project) {
      editingIndex.value = idx;
      editingTitle.value = project.title;
      setTimeout(() => {
        const input = document.getElementById('edit-title-input-' + idx);
        if (input) {
          input.focus();
          input.select();
        }
      });
    }

    async function finishEditTitle(idx, oldTitle) {
      const newTitle = editingTitle.value.trim();
      if (!newTitle || newTitle === oldTitle) {
        editingIndex.value = null;
        editingTitle.value = '';
        return;
      }

      try {
        await renameFolder(oldTitle, newTitle);
        emit('update');
      } catch (error) {
        alert('重命名失败: ' + error.message);
      }
      
      editingIndex.value = null;
      editingTitle.value = '';
    }

    function handleDelete(title) {
      console.log('点击删除', title);
      emit('delete', title);
    }

    return {
      editingIndex,
      editingTitle,
      hoveredIndex,
      handleCardClick,
      startEditTitle,
      finishEditTitle,
      handleDelete
    };
  }
};
</script>

<style scoped>
@import '../assets/styles/text-hover.css';
@import '../assets/styles/card-hover.css';
</style> 
