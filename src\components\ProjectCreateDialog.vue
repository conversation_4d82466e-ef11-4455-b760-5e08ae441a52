<template>
  <BaseDialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="创建新项目"
    width="500px"
  >
    <div class="form-content">
      <div class="form-item">
        <label>项目名称</label>
        <input
          v-model="title"
          placeholder="请输入项目名称"
        >
      </div>
    </div>
    <template #footer>
      <button
        class="cancel-btn"
        @click="$emit('update:modelValue', false)"
      >
        取消
      </button>
      <button
        class="confirm-btn"
        @click="handleCreate"
        :disabled="!title.trim()"
      >
        创建
      </button>
    </template>
  </BaseDialog>
</template>

<script>
import { ref } from 'vue';
import BaseDialog from './BaseDialog.vue';
import { useLocalCommunication } from '@/utils/localCommunication';

export default {
  name: 'ProjectCreateDialog',
  
  components: {
    BaseDialog
  },

  props: {
    modelValue: {
      type: Boolean,
      required: true
    }
  },

  emits: ['update:modelValue', 'created'],

  setup(props, { emit }) {
    const { createFolder } = useLocalCommunication();
    
    const title = ref('');

    async function handleCreate() {
      const trimmedTitle = title.value.trim();
      if (!trimmedTitle) return;

      try {
        await createFolder(trimmedTitle);
        emit('created');
        emit('update:modelValue', false);
        title.value = '';
      } catch (error) {
        alert('创建失败: ' + error.message);
      }
    }

    return {
      title,
      handleCreate
    };
  }
};
</script>

<style scoped>
.form-content {
  padding: 1rem 0;
}

.form-item {
  margin-bottom: 1rem;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item label {
  display: block;
  color: #fff;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-item input,
.form-item textarea {
  width: 100%;
  background: #1e1e2e;
  border: 1px solid #313244;
  border-radius: 6px;
  padding: 0.5rem;
  color: #fff;
  font-size: 0.95rem;
  outline: none;
  transition: border-color 0.2s;
}

.form-item textarea {
  resize: vertical;
}

.form-item input:focus,
.form-item textarea:focus {
  border-color: #89b4fa;
}

.cancel-btn,
.confirm-btn {
  padding: 0.5rem 1.2rem;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: transparent;
  border: 1px solid #45475a;
  color: #cdd6f4;
  margin-right: 1rem;
}

.cancel-btn:hover {
  background: #313244;
}

.confirm-btn {
  background: #89b4fa;
  border: none;
  color: #1e1e2e;
}

.confirm-btn:hover {
  background: #74c7ec;
}

.confirm-btn:disabled {
  background: #45475a;
  cursor: not-allowed;
}
</style> 
