/**
 * OpenAI LLM 提供商配置
 * 支持OpenAI GPT系列模型
 */

export const openaiConfig = {
  // 提供商基本信息
  name: 'OpenAI',
  id: 'openai',
  description: 'OpenAI的GPT系列大语言模型',
  
  // API配置
  api: {
    baseUrl: 'https://api.openai.com/v1',
    endpoint: '/chat/completions',
    headers: {
      'Content-Type': 'application/json'
    },
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  
  // 默认配置
  defaults: {
    model: 'gpt-4o-mini',
    temperature: 0.7,
    maxTokens: 4096,
    timeout: 30000
  },
  
  // 支持的模型列表
  models: [
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      description: '最新的GPT-4 Omni模型，支持多模态',
      maxTokens: 128000,
      pricing: { input: 0.005, output: 0.015 }
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      description: '轻量级的GPT-4o模型，性价比高',
      maxTokens: 128000,
      pricing: { input: 0.00015, output: 0.0006 }
    },
    {
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      description: '高性能的GPT-4 Turbo模型',
      maxTokens: 128000,
      pricing: { input: 0.01, output: 0.03 }
    },
    {
      id: 'gpt-4',
      name: 'GPT-4',
      description: '经典的GPT-4模型',
      maxTokens: 8192,
      pricing: { input: 0.03, output: 0.06 }
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      description: '快速且经济的GPT-3.5模型',
      maxTokens: 16385,
      pricing: { input: 0.0005, output: 0.0015 }
    }
  ],
  
  // 请求格式化函数
  formatRequest: (prompt, settings) => {
    // 检查是否是结构化的prompt（包含instruction和content）
    let messages;
    if (typeof prompt === 'object' && prompt.instruction && prompt.content) {
      messages = [
        { role: 'system', content: prompt.instruction },
        { role: 'user', content: prompt.content }
      ];
    } else {
      // 向后兼容：如果是字符串prompt，直接使用
      messages = [{ role: 'user', content: prompt }];
    }

    return {
      model: settings.model,
      messages: messages,
      temperature: settings.temperature || 0.7,
      max_tokens: settings.maxOutputTokens || 4096,
    };
  },
  
  // 响应解析函数
  parseResponse: (data) => {
    if (!data.choices || !data.choices[0]) {
      throw new Error('OpenAI API响应格式无效');
    }
    
    return {
      content: data.choices[0].message.content.trim(),
      usage: data.usage,
      model: data.model
    };
  },
  
  // 错误处理
  handleError: (error, response) => {
    if (response && response.status === 401) {
      return new Error('OpenAI API密钥无效或已过期');
    }
    if (response && response.status === 429) {
      return new Error('OpenAI API请求频率限制，请稍后重试');
    }
    if (response && response.status === 402) {
      return new Error('OpenAI账户余额不足');
    }
    if (response && response.status === 403) {
      return new Error('OpenAI API访问被拒绝，请检查API密钥权限');
    }
    return new Error(`OpenAI API错误: ${error.message}`);
  },
  
  // 配置验证
  validateConfig: (config) => {
    const errors = [];
    
    if (!config.apiKey) {
      errors.push('缺少OpenAI API密钥');
    }
    
    if (!config.model) {
      errors.push('未指定模型');
    }
    
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('温度值应在0-2之间');
    }
    
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 128000)) {
      errors.push('最大token数应在1-128000之间');
    }
    
    return errors;
  },
  
  // 构建完整的API URL
  buildApiUrl: () => {
    return `${openaiConfig.api.baseUrl}${openaiConfig.api.endpoint}`;
  }
};

export default openaiConfig;
