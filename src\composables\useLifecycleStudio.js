/**
 * 生命周期管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的生命周期管理相关功能
 * 
 * 功能：
 * - 组件初始化流程管理
 * - 状态验证和重置
 * - 服务初始化
 * - 清理和销毁逻辑
 */

export function useLifecycleStudio() {

  /**
   * 组件创建时的初始化流程
   * @param {Object} context - 组件上下文
   */
  const handleCreated = async (context) => {
    console.log('🔄 [生命周期] 组件创建阶段开始');

    try {
      // 设置初始加载状态
      if (context.uiStateStudio && context.uiStateStudio.setLoadingState) {
        context.uiStateStudio.setLoadingState(true, '正在初始化项目...');
      }

      // 初始化项目信息
      await context.initializeProjectInfo();

      // 加载项目数据
      await context.loadSrtContent();

      // 加载角色数据
      await context.loadCharacterData();

      // 加载完成后自动检测SRT文件更新
      context.$nextTick(async () => {
        // 延迟一下，确保UI已渲染
        setTimeout(async () => {
          // 自动检测SRT变更
          const hasChanges = await context.autoDetectSrtChanges();
          if (hasChanges) {
            console.log('🔄 [生命周期] 已自动检测到SRT文件变更并已刷新');
          }
        }, 500);
      });

      console.log('✅ [生命周期] 组件创建阶段完成');

      // 确保加载状态被正确设置为false
      if (context.uiStateStudio && context.uiStateStudio.setLoadingState) {
        context.uiStateStudio.setLoadingState(false);
      }
    } catch (error) {
      console.error('❌ [生命周期] 组件创建阶段出错:', error);

      // 确保错误时也结束加载状态
      if (context.uiStateStudio && context.uiStateStudio.setLoadingState) {
        context.uiStateStudio.setLoadingState(false);
      }

      if (context.showErrorMessage) {
        context.showErrorMessage('初始化失败', error.message);
      }
    }
  };

  /**
   * 组件挂载时的初始化流程
   * @param {Object} context - 组件上下文
   */
  const handleMounted = async (context) => {
    console.log('🔄 [生命周期] 组件挂载阶段开始');

    try {
      // 0. 设置Toast组件引用和全局Toast系统
      context.$nextTick(() => {
        if (context.$refs.toast) {
          context.toastNotification.setToastRef(context.$refs.toast);
          context.toastNotification.setupGlobalToastSystem();
          console.log('🔔 [Toast] Toast组件引用已设置，全局Toast系统已初始化');
        }
      });

      // 1. 确保角色数据结构正确初始化
      context.ensureCharacterDataStructure();

      // 2. 重置批量操作状态，确保干净的初始状态
      context.resetBatchOperationStates();

      // 3. 初始化项目上下文
      if (context.projectData && context.projectContextTools) {
        context.projectContextTools.setProjectContext({
          projectTitle: context.projectData.title || '',
          chapterTitle: context.projectData.currentChapter || '',
          currentStep: context.currentStep || '',
          projectData: context.projectData
        });
      }

      // 4. 初始化图像生成服务
      await context.initializeImageGeneration();

      // 5. 页面刷新后的状态检查和重置
      await context.performStateValidationAndReset();

      // 6. 设置全局toast通知系统
      context.setupGlobalToastSystem();

      // 7. 强制重置队列状态，解决持久化问题
      context.forceResetQueueState();

      // 8. 清理遗留的临时状态
      if (context.hasLegacyTemporaryStates()) {
        console.log('🧹 [状态清理] 检测到遗留的临时状态，执行清理');
        context.cleanLoadedQueueState();
      }

      // 9. 🔧 修复：启动批量图像生成状态监控
      if (context.batchOperationUI && context.batchOperationUI.startStateMonitoring) {
        context.batchOperationUI.startStateMonitoring(context);
        console.log('🔧 [生命周期] 批量图像生成状态监控已启动');
      }

      // 10. 强制触发canGenerateImage重新计算
      context.$nextTick(() => {
        // 检查imageGeneration对象
        if (context.imageGeneration && context.imageGeneration.comfyuiImageGeneration) {
          const isAvailable = context.imageGeneration.comfyuiImageGeneration.isAvailable();
          if (!isAvailable) {
            console.log('🎨 [图像生成] ComfyUI服务不可用，请检查配置');
          }
        }

        context.$forceUpdate();
      });

      console.log('✅ [生命周期] 组件挂载阶段完成');
    } catch (error) {
      console.error('❌ [生命周期] 组件挂载阶段出错:', error);
      if (context.showErrorMessage) {
        context.showErrorMessage('挂载失败', error.message);
      }
    }
  };

  /**
   * 组件销毁前的清理流程
   * @param {Object} context - 组件上下文
   */
  const handleBeforeUnmount = (context) => {
    console.log('🔄 [生命周期] 组件销毁前清理开始');

    try {
      // 清除虚假状态监控定时器
      if (context.uiStateStudio && context.uiStateStudio.stopPhantomStateMonitoring) {
        context.uiStateStudio.stopPhantomStateMonitoring();
      }

      // 🔧 修复：停止批量图像生成状态监控
      if (context.batchOperationUI && context.batchOperationUI.stopStateMonitoring) {
        context.batchOperationUI.stopStateMonitoring();
        console.log('🔧 [生命周期] 批量图像生成状态监控已停止');
      }

      // 清理图像生成相关资源
      if (context.imageGeneration && context.imageGeneration.cleanup) {
        context.imageGeneration.cleanup();
      }

      // 清理批量操作相关资源
      if (context.batchOperationsStudio && context.batchOperationsStudio.cleanup) {
        context.batchOperationsStudio.cleanup();
      }

      // 重置UI状态
      if (context.uiStateStudio && context.uiStateStudio.resetAllUIStates) {
        context.uiStateStudio.resetAllUIStates();
      }

      console.log('✅ [生命周期] 组件销毁前清理完成');
    } catch (error) {
      console.error('❌ [生命周期] 组件销毁前清理出错:', error);
    }
  };

  /**
   * 页面刷新后的状态验证和重置
   * @param {Object} context - 组件上下文
   */
  const performStateValidationAndReset = async (context) => {
    try {
      console.log('🔍 [状态验证] 开始页面刷新后的状态检查...');

      if (!context.imageGeneration) {
        console.warn('⚠️ [状态验证] 图像生成服务未初始化');
        return;
      }

      // 步骤1：验证状态一致性
      const validation = context.imageGeneration.validateStateConsistency();

      if (!validation.isConsistent) {
        // 步骤2：强制重置状态
        const resetResult = context.imageGeneration.forceResetState();

        // 步骤3：通知用户（如果有重要的状态重置）
        if (resetResult.resetActions.length > 0) {
          console.log('📢 [状态验证] 已自动修复状态同步问题:', resetResult.resetActions);
        }
      } else {
        console.log('✅ [状态验证] 状态一致性检查通过');
      }

      // 步骤4：清理可能的僵尸任务
      const backendStatus = context.imageGeneration.comfyuiImageGeneration.getStatus();
      if (backendStatus.taskStates.total > 0) {
        console.log('🧹 [状态验证] 发现活跃任务，进行清理:', backendStatus.taskStates);
        context.imageGeneration.comfyuiImageGeneration.forceStateReset();
      }

    } catch (error) {
      console.error('❌ [状态验证] 状态验证过程出错:', error);
      // 出错时也要尝试重置状态
      if (context.imageGeneration && context.imageGeneration.forceResetState) {
        context.imageGeneration.forceResetState();
      }
    }
  };

  /**
   * 初始化图像生成服务
   * @param {Object} context - 组件上下文
   */
  const initializeImageGeneration = async (context) => {
    try {
      console.log('🎨 [图像生成] 初始化服务...');

      // 强制重置状态，确保干净的开始
      if (context.imageGeneration && context.imageGeneration.forceResetState) {
        context.imageGeneration.forceResetState();
        console.log('🎨 [图像生成] 已强制重置状态');
      }

      // 直接设置状态为false，确保初始状态正确
      if (context.imageGeneration && context.imageGeneration.isGenerating) {
        if (context.imageGeneration.isGenerating.value !== undefined) {
          context.imageGeneration.isGenerating.value = false;
        } else {
          context.imageGeneration.isGenerating = false;
        }
        console.log('🎨 [图像生成] 直接设置 isGenerating = false');
      }

      // 清空当前任务
      if (context.imageGeneration && context.imageGeneration.currentTask) {
        if (context.imageGeneration.currentTask.value !== undefined) {
          context.imageGeneration.currentTask.value = null;
        } else {
          context.imageGeneration.currentTask = null;
        }
        console.log('🎨 [图像生成] 清空 currentTask');
      }

      if (context.imageGeneration && context.imageGeneration.initializeService) {
        const success = await context.imageGeneration.initializeService();

        if (success) {
          console.log('🎨 [图像生成] 服务初始化成功');
        } else {
          console.warn('🎨 [图像生成] 服务初始化失败，请检查ComfyUI设置');
        }
      } else {
        console.error('🎨 [图像生成] 图像生成服务未正确初始化');
      }
    } catch (error) {
      console.error('🎨 [图像生成] 服务初始化出错:', error);
    }
  };

  /**
   * 确保角色数据结构正确初始化
   * @param {Object} context - 组件上下文
   */
  const ensureCharacterDataStructure = (context) => {
    // 确保 currentSelectedCharacters 是数组
    if (!Array.isArray(context.currentSelectedCharacters)) {
      context.currentSelectedCharacters = [];
    }

    // 确保 availableCharacters 是数组
    if (!Array.isArray(context.availableCharacters)) {
      context.availableCharacters = [];
    }

    // 确保项目数据中的角色字段存在
    if (context.projectData && context.projectData.data) {
      if (!Array.isArray(context.projectData.data.currentSelectedCharacters)) {
        context.projectData.data.currentSelectedCharacters = [];
      }
    }
  };

  return {
    // 生命周期处理
    handleCreated,
    handleMounted,
    handleBeforeUnmount,
    
    // 状态管理
    performStateValidationAndReset,
    initializeImageGeneration,
    ensureCharacterDataStructure
  };
}
