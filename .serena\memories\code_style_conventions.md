# 代码风格和约定

## ESLint配置
项目使用ESLint进行代码质量检查，配置在package.json中：
- 基于Vue 3强烈推荐规则
- 使用Babel解析器
- 支持最新ECMAScript特性
- 禁用多词组件名称规则

## 代码风格特点
- **语言**: 主要使用中文注释和变量名
- **组件命名**: 使用PascalCase（如HomePage、CreationPage）
- **文件命名**: Vue组件使用PascalCase，工具文件使用camelCase
- **函数命名**: 使用camelCase
- **常量命名**: 使用UPPER_SNAKE_CASE

## Vue 3特性使用
- 使用Composition API（组合式API）
- 大量使用composables进行逻辑复用
- 使用`<script setup>`语法糖
- 响应式数据使用ref和reactive

## 项目特定约定
- 组合式函数以`use`开头（如useImageGeneration、useProjectContext）
- 服务类文件放在services目录
- 工具函数放在utils目录
- 配置文件放在config目录
- 类型定义放在types目录

## 注释风格
- 使用中文注释
- 重要功能有详细的注释说明
- API接口有清晰的注释