const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// 暴露给渲染进程的API
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件系统操作
  selectFiles: (options) => ipcRenderer.invoke('select-files', options),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  
  // 项目管理
  getProjects: () => ipcRenderer.invoke('get-projects'),
  createProject: (project) => ipcRenderer.invoke('create-project', project),
  getProject: (id) => ipcRenderer.invoke('get-project', id),
  updateProject: (project) => ipcRenderer.invoke('update-project', project),
  deleteProject: (id) => ipcRenderer.invoke('delete-project', id)
}) 