/**
 * 数据持久化 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的数据持久化相关功能
 * 
 * 功能：
 * - 项目数据保存操作（立即保存、防抖保存）
 * - 项目数据加载操作
 * - 本地存储管理（合并状态等）
 * - 数据同步状态管理
 * - SRT文件处理
 */

import { ref, reactive } from 'vue';

export function useDataPersistenceStudio() {
  // 响应式状态
  const isSaving = ref(false);
  const isLoading = ref(false);
  const saveState = reactive({
    lastSaveTime: 0,
    saveCount: 0,
    pendingSaveOperations: new Set()
  });

  /**
   * 防抖保存项目数据
   * @param {Object} context - 组件上下文
   * @returns {Promise<void>}
   */
  const debouncedSaveProject = async (context) => {
    if (context.isBatchUpdating) {
      context.pendingSaveOperations.add('project');
      // console.log('[Performance] 批量更新模式中，延迟保存操作');
      return;
    }

    // 使用统一的数据持久化系统
    if (context.projectData) {
      // console.log('[Performance] 执行统一防抖保存');
      try {
        const { saveProject } = await import('../composables/useDataPersistence.js').then(module => module.useDataPersistence());
        await saveProject(context.projectData, {
          showMessages: false, // ContentCreationStudio有自己的消息系统
          debounceDelay: 100   // 使用更快的防抖延迟
        });
      } catch (error) {
        console.error('[Performance] 统一保存失败:', error);
        context.showErrorMessage('保存失败: ' + error.message);
      }
    }
  };

  /**
   * 立即保存项目数据（不使用防抖）
   * @param {Object} context - 组件上下文
   * @returns {Promise<void>}
   */
  const saveProjectDataImmediately = async (context) => {
    if (!context.projectData) {
      console.warn('🎨 [实时保存] 项目数据不存在，跳过保存');
      return;
    }

    try {
      console.log('🎨 [实时保存] 开始立即保存项目数据');

      // 🔍 监控：记录保存前所有行的选择状态
      const beforeSaveStates = context.rows.map((r, i) => ({ index: i + 1, isSelected: r.isSelected }));
      console.log(`🔍 [保存前监控] 所有行选择状态:`, beforeSaveStates);

      // 🔧 修复：创建行数据的副本，避免引用问题
      if (context.projectData.data) {
        // 🔧 修复：使用深拷贝避免引用问题，防止数据清理时影响原始数据
        context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));
      }

      // 使用统一的数据持久化系统，但不使用防抖
      const { saveProject } = await import('../composables/useDataPersistence.js').then(module => module.useDataPersistence());
      await saveProject(context.projectData, {
        showMessages: false, // 不显示保存消息，避免干扰用户
        debounceDelay: 0     // 立即保存，不使用防抖
      });

      // 🔍 监控：记录保存后所有行的选择状态
      const afterSaveStates = context.rows.map((r, i) => ({ index: i + 1, isSelected: r.isSelected }));
      console.log(`🔍 [保存后监控] 所有行选择状态:`, afterSaveStates);

      // 🔍 检查保存过程是否影响了选择状态
      const changedRows = [];
      for (let i = 0; i < beforeSaveStates.length; i++) {
        if (beforeSaveStates[i].isSelected !== afterSaveStates[i].isSelected) {
          changedRows.push({
            rowIndex: i + 1,
            before: beforeSaveStates[i].isSelected,
            after: afterSaveStates[i].isSelected
          });
        }
      }

      if (changedRows.length > 0) {
        console.error(`🚨 [保存异常] 保存过程中选择状态被意外改变:`, changedRows);
      }

      console.log('🎨 [实时保存] 项目数据保存成功');
    } catch (error) {
      console.error('🎨 [实时保存] 保存项目数据失败:', error);
    }
  };

  /**
   * 从文件系统加载项目数据
   * @param {Object} context - 组件上下文
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @returns {Promise<boolean>} 加载是否成功
   */
  const loadProjectDataFromFile = async (context, projectTitle, chapterTitle) => {
    try {
      console.log('从文件系统加载项目数据:', projectTitle, chapterTitle);

      // 构建项目数据文件路径
      const projectDataPath = `draft/${projectTitle}/${chapterTitle}/project-data.json`;

      const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(projectDataPath)}`);

      if (!response.ok) {
        console.error('读取项目数据文件失败:', response.status, response.statusText);
        return false;
      }

      const result = await response.json();

      if (!result.success || !result.content) {
        console.error('项目数据文件读取结果无效:', result);
        return false;
      }

      let projectData;
      try {
        projectData = JSON.parse(result.content);
      } catch (parseError) {
        console.error('解析项目数据JSON失败:', parseError);
        return false;
      }

      if (!projectData || !projectData.data) {
        console.error('项目数据格式无效');
        return false;
      }

      console.log('成功加载项目数据，行数:', projectData.data.rows ? projectData.data.rows.length : 0);

      // 保存现有的 currentSelectedCharacters（如果存在）
      const existingSelectedCharacters = context.projectData?.data?.currentSelectedCharacters;

      // 更新项目数据
      context.projectData = {
        title: projectTitle,
        chapter: chapterTitle,
        data: projectData.data
      };

      // 恢复 currentSelectedCharacters（如果之前存在且新数据中没有）
      if (existingSelectedCharacters && !context.projectData.data.currentSelectedCharacters) {
        context.projectData.data.currentSelectedCharacters = existingSelectedCharacters;
        console.log('loadProjectDataFromFile - 恢复现有的 currentSelectedCharacters:', existingSelectedCharacters);
      }

      // 更新URL
      const url = new URL(window.location.href);
      url.searchParams.set('project', projectTitle);
      url.searchParams.set('chapter', chapterTitle);
      window.history.replaceState({}, '', url.toString());

      // 更新本地标题
      context.localProjectTitle = projectTitle;
      context.localChapterTitle = chapterTitle;

      // 更新行数据
      if (projectData.data.rows && Array.isArray(projectData.data.rows)) {
        console.log('更新行数据，行数:', projectData.data.rows.length);

        // 确保每行都有必要的字段
        const processedRows = projectData.data.rows.map((row, index) => {
          // 🔍 监控选择状态重置
          const originalSelected = row.isSelected;
          const newSelected = row.isSelected !== undefined ? row.isSelected : false;

          if (originalSelected !== newSelected) {
            console.warn(`🚨 [数据重置] 第${index + 1}行选择状态: ${originalSelected} -> ${newSelected}`);
          }

          return {
            ...row,
            index: index,
            isSelected: newSelected, // 🔧 修复：只在undefined时设为false
            isLocked: row.isLocked !== undefined ? row.isLocked : false,
            // 确保图片字段存在
            imageSrc: row.imageSrc || 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
            imageAlt: row.imageAlt || `第${index + 1}行图片`,
            thumbnails: row.thumbnails || [],
            // 🔧 修复：确保图像生成相关的状态属性存在且为响应式
            isGenerating: row.isGenerating || false,
            isQueued: row.isQueued || false,
            isInferring: row.isInferring || false,
            generationProgress: row.generationProgress || 0,
            generationMessage: row.generationMessage || '',
            queueTaskId: row.queueTaskId || '',
            queuePosition: row.queuePosition || 0,
            _isCancelling: row._isCancelling || false
          };
        });

        // 🔍 监控选择状态统计变化
        const oldSelectedCount = context.rows ? context.rows.filter(row => row.isSelected).length : 0;
        const newSelectedCount = processedRows.filter(row => row.isSelected).length;

        if (oldSelectedCount !== newSelectedCount) {
          console.warn(`🚨 [数据加载] 选择统计变化: ${oldSelectedCount} -> ${newSelectedCount}`);
        }

        context.rows = processedRows;

        // 🔧 修复：更新项目数据中的行数据，使用深拷贝避免引用问题
        if (context.projectData.data) {
          context.projectData.data.rows = JSON.parse(JSON.stringify(context.rows));
        }

        context.isLoading = false;

        // 记录合并状态
        if (context.rows.some(row => row.isMerged)) {
          setMergeFlag(projectTitle, chapterTitle, true);
        }

        // 加载全局选择的角色状态
        if (context.loadGlobalSelectedCharacters) {
          context.loadGlobalSelectedCharacters();
        }
      }

      return true;
    } catch (error) {
      console.error('加载项目数据失败:', error);
      return false;
    }
  };

  /**
   * 设置合并状态标志
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @param {boolean} hasMerged - 是否有合并
   */
  const setMergeFlag = (projectTitle, chapterTitle, hasMerged) => {
    const key = `project_${projectTitle}_${chapterTitle}_hasMerged`;
    if (hasMerged) {
      localStorage.setItem(key, 'true');
    } else {
      localStorage.removeItem(key);
    }
  };

  /**
   * 获取合并状态标志
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @returns {boolean} 是否有合并
   */
  const getMergeFlag = (projectTitle, chapterTitle) => {
    const key = `project_${projectTitle}_${chapterTitle}_hasMerged`;
    return localStorage.getItem(key) === 'true';
  };

  /**
   * 清除合并状态标志
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   */
  const clearMergeFlag = (projectTitle, chapterTitle) => {
    const key = `project_${projectTitle}_${chapterTitle}_hasMerged`;
    localStorage.removeItem(key);
  };

  /**
   * 设置SRT更新标志
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   */
  const setSrtUpdateFlag = (projectTitle, chapterTitle) => {
    const key = `srt_updated_${projectTitle}_${chapterTitle}`;
    localStorage.setItem(key, Date.now().toString());
  };

  /**
   * 获取并清除SRT更新标志
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @returns {string|null} 更新时间戳
   */
  const getSrtUpdateFlag = (projectTitle, chapterTitle) => {
    const key = `srt_updated_${projectTitle}_${chapterTitle}`;
    const timestamp = localStorage.getItem(key);
    if (timestamp) {
      localStorage.removeItem(key);
    }
    return timestamp;
  };

  /**
   * 更新合并状态
   * @param {Object} context - 组件上下文
   * @param {Array} processedRows - 处理后的行数据
   */
  const updateMergeState = (context, processedRows) => {
    // 检查是否有合并行
    const mergedRows = processedRows.filter(row => row.isMerged);

    if (mergedRows.length > 0) {
      // 设置合并标志到localStorage
      setMergeFlag(context.localProjectTitle, context.localChapterTitle, true);
    } else {
      clearMergeFlag(context.localProjectTitle, context.localChapterTitle);
    }

    // 更新行数据
    context.rows = processedRows;
    console.log('ContentCreationStudio - 行数据更新完成，当前行数:', context.rows.length);
  };

  return {
    // 响应式状态
    isSaving,
    isLoading,
    saveState,
    
    // 保存操作
    debouncedSaveProject,
    saveProjectDataImmediately,
    
    // 加载操作
    loadProjectDataFromFile,
    
    // 本地存储管理
    setMergeFlag,
    getMergeFlag,
    clearMergeFlag,
    setSrtUpdateFlag,
    getSrtUpdateFlag,
    
    // 状态管理
    updateMergeState
  };
}
