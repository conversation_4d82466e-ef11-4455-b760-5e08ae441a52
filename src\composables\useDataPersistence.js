/**
 * 统一数据持久化系统
 * 整合所有保存操作，提供防抖机制和保存队列管理
 * 解决重复保存和性能问题
 */

import { reactive } from 'vue';
import logger from '../utils/logger.js';

// 全局保存状态管理
const saveState = reactive({
  isSaving: false,
  saveQueue: new Map(), // 使用Map来管理不同类型的保存操作
  lastSaveTime: 0,
  saveCount: 0
});

// 防抖定时器管理
const debounceTimers = new Map();

// 保存操作类型枚举
const SAVE_TYPES = {
  PROJECT_DATA: 'project_data',
  PROJECT_FILE: 'project_file',
  TEXT_FILE: 'text_file',
  USER_SETTINGS: 'user_settings',
  PROMPTS: 'prompts'
};

// 默认配置 - 优化防抖延迟
const DEFAULT_CONFIG = {
  debounceDelay: 100, // 减少防抖延迟时间（毫秒）
  maxRetries: 3, // 最大重试次数
  retryDelay: 1000, // 重试延迟时间（毫秒）
  enableLogging: true // 是否启用详细日志
};

/**
 * 统一数据持久化组合式函数
 * @param {Object} config - 配置选项
 * @returns {Object} 持久化相关的方法和状态
 */
export function useDataPersistence(config = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // 消息显示函数（可以被外部覆盖）
  let showSuccessMessage = (title, message) => console.log(`✅ ${title}: ${message}`);
  let showErrorMessage = (message) => console.error(`❌ ${message}`);
  let showInfoMessage = (title, message) => console.log(`ℹ️ ${title}: ${message}`);

  /**
   * 设置消息显示函数
   * @param {Object} messageFunctions - 消息显示函数对象
   */
  const setMessageFunctions = (messageFunctions) => {
    if (messageFunctions.showSuccessMessage) showSuccessMessage = messageFunctions.showSuccessMessage;
    if (messageFunctions.showErrorMessage) showErrorMessage = messageFunctions.showErrorMessage;
    if (messageFunctions.showInfoMessage) showInfoMessage = messageFunctions.showInfoMessage;
  };

  /**
   * 生成保存操作的唯一键
   * @param {string} type - 保存类型
   * @param {Object} params - 保存参数
   * @returns {string} 唯一键
   */
  const generateSaveKey = (type, params) => {
    switch (type) {
      case SAVE_TYPES.PROJECT_DATA:
        return `${type}_${params.projectTitle}_${params.chapterTitle}`;
      case SAVE_TYPES.PROJECT_FILE:
        return `${type}_${params.projectTitle}_${params.chapterTitle}_${params.fileName}`;
      case SAVE_TYPES.TEXT_FILE:
        return `${type}_${params.filePath}`;
      case SAVE_TYPES.USER_SETTINGS:
        return `${type}_${params.filename}`;
      case SAVE_TYPES.PROMPTS:
        return `${type}_prompts`;
      default:
        return `${type}_${Date.now()}`;
    }
  };

  /**
   * 记录性能日志
   * @param {string} operation - 操作名称
   * @param {number} startTime - 开始时间
   * @param {Object} params - 操作参数
   */
  const logPerformance = (operation, startTime, params = {}) => {
    if (!finalConfig.enableLogging) return;

    const duration = Date.now() - startTime;
    const logData = {
      operation,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      saveCount: ++saveState.saveCount,
      ...params
    };

    console.log(`[数据持久化] ${operation} 完成:`, logData);

    // 性能警告
    if (duration > 1000) {
      console.warn(`[数据持久化] 性能警告: ${operation} 耗时 ${duration}ms，超过1秒`);
    }
  };

  /**
   * 执行实际的保存操作
   * @param {string} type - 保存类型
   * @param {Object} params - 保存参数
   * @returns {Promise<Object>} 保存结果
   */
  const executeSave = async (type, params) => {
    const startTime = Date.now();

    try {
      let result;

      switch (type) {
        case SAVE_TYPES.PROJECT_DATA:
          result = await saveProjectData(params);
          break;
        case SAVE_TYPES.PROJECT_FILE:
          result = await saveProjectFile(params);
          break;
        case SAVE_TYPES.TEXT_FILE:
          result = await saveTextFile(params);
          break;
        case SAVE_TYPES.USER_SETTINGS:
          result = await saveUserSettings(params);
          break;
        case SAVE_TYPES.PROMPTS:
          result = await savePrompts(params);
          break;
        default:
          throw new Error(`不支持的保存类型: ${type}`);
      }

      logPerformance(`保存${type}`, startTime, {
        success: true,
        dataSize: JSON.stringify(params).length
      });

      return result;
    } catch (error) {
      logPerformance(`保存${type}`, startTime, {
        success: false,
        error: error.message
      });
      throw error;
    }
  };

  /**
   * 清理行数据，移除临时UI状态字段
   * @param {Object} row - 行数据
   * @returns {Object} 清理后的行数据
   */
  const cleanRowData = (row) => {
    if (!row || typeof row !== 'object') return row;

    // 🔥 完整的临时UI状态字段列表 - 这些字段不应该被持久化保存
    const temporaryUIFields = [
      // 🔸 UI动画和加载状态
      'isInferring',        // 推理按钮动画状态
      'isGenerating',       // 生成按钮动画状态
      'isUploading',        // 上传状态
      'isProcessing',       // 处理状态
      'isLoading',          // 加载状态
      'loadingState',       // 加载状态对象
      'animationState',     // 动画状态
      'uiState',            // UI状态
      'tempState',          // 临时状态

      // 🔸 图像生成过程中的临时状态
      'generationProgress', // 生成进度 (0-100)
      'generationStage',    // 生成阶段 ('preparing', 'generating', 'completed')
      'generationError',    // 生成错误信息
      'currentTaskId',      // 当前任务ID
      'taskId',             // 任务ID (别名)

      // 🔸 排队系统临时状态字段
      'isQueued',           // 排队状态
      'queueTaskId',        // 队列任务ID
      'queuePosition',      // 队列位置
      'generationMessage',  // 生成消息（包含排队信息）

      // 🔸 用户交互临时状态
      'isSelected',         // 选中状态 (批量操作时的临时选择状态) - 正确：不应保存到磁盘
      '_wasSelected',       // 🔧 修复：任务开始时的选择状态记录 - 临时字段，不应保存到磁盘
      '_isCancelling',      // 🔧 修复：取消操作状态标志 - 临时字段，不应保存到磁盘
      'isHovered',          // 悬停状态
      'isFocused',          // 焦点状态
      'isEditing',          // 编辑状态
      'isDragging',         // 拖拽状态
      'isResizing',         // 调整大小状态

      // 🔸 表单和输入临时状态
      'inputValue',         // 临时输入值
      'tempValue',          // 临时值
      'originalValue',      // 原始值备份
      'hasChanges',         // 是否有变更
      'isDirty',            // 脏数据标记

      // 🔸 网络请求和API临时状态
      'isSubmitting',       // 提交状态
      'isSaving',           // 保存状态
      'isDeleting',         // 删除状态
      'requestId',          // 请求ID
      'lastRequestTime',    // 最后请求时间

      // 🔸 错误和验证临时状态
      'validationErrors',   // 验证错误
      'hasErrors',          // 是否有错误
      'errorMessage',       // 错误消息
      'warningMessage',     // 警告消息
      'successMessage',     // 成功消息

      // 🔸 缓存和性能相关临时状态
      'cacheKey',           // 缓存键
      'lastUpdateTime',     // 最后更新时间
      'renderTime',         // 渲染时间
      'computedHash',       // 计算哈希

      // 🔸 调试和开发临时状态
      'debugInfo',          // 调试信息
      'performanceMetrics', // 性能指标
      'traceId',            // 追踪ID
      '_internal',          // 内部状态
      '_temp',              // 临时前缀
      '_cache'             // 缓存前缀
    ];

    // 🔥 临时状态字段的模式匹配规则
    const temporaryFieldPatterns = [
      /^temp[A-Z]/,           // temp开头的驼峰命名字段
      /^_/,                   // 🔧 修复：所有以下划线开头的字段（内部/临时字段约定）
      /^is[A-Z].*ing$/,       // is开头ing结尾的状态字段 (如isGenerating, isProcessing)
      /^has[A-Z].*Error$/,    // has开头Error结尾的错误状态字段
      /^last[A-Z].*Time$/,    // last开头Time结尾的时间字段
      /^current[A-Z].*Id$/,   // current开头Id结尾的ID字段
      /Progress$/,            // Progress结尾的进度字段
      /Stage$/,               // Stage结尾的阶段字段
      /Message$/,             // Message结尾的消息字段
      /Error$/,               // Error结尾的错误字段
      /^queue[A-Z]/,          // queue开头的队列相关字段
      /^generation[A-Z]/,     // generation开头的生成相关字段
      /^ui[A-Z]/,             // ui开头的UI状态字段
      /^animation[A-Z]/,      // animation开头的动画字段
      /^loading[A-Z]/,        // loading开头的加载字段
      /^cache[A-Z]/,          // cache开头的缓存字段
      /^debug[A-Z]/,          // debug开头的调试字段
      /^trace[A-Z]/,          // trace开头的追踪字段
      /^performance[A-Z]/     // performance开头的性能字段
    ];

    // 🔥 检查字段是否为临时状态字段
    const isTemporaryField = (fieldName) => {
      // 1. 检查明确的字段名列表
      if (temporaryUIFields.includes(fieldName)) {
        return true;
      }

      // 2. 检查模式匹配
      return temporaryFieldPatterns.some(pattern => pattern.test(fieldName));
    };

    // 创建清理后的对象
    const cleanedRow = {};

    for (const [key, value] of Object.entries(row)) {
      // 🔥 跳过所有临时UI状态字段（包括模式匹配的字段）
      if (isTemporaryField(key)) {
        continue;
      }

      // 递归清理嵌套对象
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        cleanedRow[key] = cleanRowData(value);
      } else if (Array.isArray(value)) {
        // 清理数组中的对象
        cleanedRow[key] = value.map(item =>
          item && typeof item === 'object' ? cleanRowData(item) : item
        );
      } else {
        cleanedRow[key] = value;
      }
    }

    return cleanedRow;
  };

  /**
   * 🆕 专门清理排队状态的函数
   * @param {Object} row - 行数据
   * @returns {Object} 清理后的行数据
   */
  const cleanQueueState = (row) => {
    if (!row || typeof row !== 'object') return row;

    const cleanedRow = { ...row };

    // 强制重置排队相关状态为默认值
    if ('isQueued' in cleanedRow) {
      cleanedRow.isQueued = false;
    }
    if ('queueTaskId' in cleanedRow) {
      cleanedRow.queueTaskId = '';
    }
    if ('queuePosition' in cleanedRow) {
      cleanedRow.queuePosition = 0;
    }
    if ('generationMessage' in cleanedRow) {
      // 只清理包含排队信息的消息
      if (typeof cleanedRow.generationMessage === 'string' &&
          (cleanedRow.generationMessage.includes('队列') ||
           cleanedRow.generationMessage.includes('排队'))) {
        cleanedRow.generationMessage = '';
      }
    }

    return cleanedRow;
  };

  /**
   * 清理项目数据，移除所有临时UI状态字段
   * @param {Object} data - 项目数据
   * @returns {Object} 清理后的项目数据
   */
  const cleanProjectData = (data) => {
    if (!data || typeof data !== 'object') return data;

    const cleanedData = JSON.parse(JSON.stringify(data)); // 深拷贝

    // 清理行数据中的临时UI状态字段
    if (cleanedData.rows && Array.isArray(cleanedData.rows)) {
      cleanedData.rows = cleanedData.rows.map(row => {
        // 先清理一般的临时状态字段
        const cleanedRow = cleanRowData(row);
        // 再专门清理排队状态
        return cleanQueueState(cleanedRow);
      });
    }

    // 清理其他可能包含临时状态的字段
    if (cleanedData.mergedRows && Array.isArray(cleanedData.mergedRows)) {
      cleanedData.mergedRows = cleanedData.mergedRows.map(row => {
        const cleanedRow = cleanRowData(row);
        return cleanQueueState(cleanedRow);
      });
    }

    return cleanedData;
  };

  /**
   * 保存项目数据的核心实现
   * @param {Object} params - 保存参数
   * @returns {Promise<Object>} 保存结果
   */
  const saveProjectData = async (params) => {
    const { projectTitle, chapterTitle, data, enableSRTOptimization = true } = params;

    // 🔥 修复：清理项目数据，移除所有临时UI状态字段（如 isInferring 等）
    const cleanData = cleanProjectData(data);

    // 🔥 性能优化：记录清理效果，确保临时状态不被保存
    if (process.env.NODE_ENV === 'development') {
      const originalSize = JSON.stringify(data).length;
      const cleanedSize = JSON.stringify(cleanData).length;
      const sizeDiff = originalSize - cleanedSize;

      if (sizeDiff > 0) {
        console.log(`🧹 [数据清理] 移除了${sizeDiff}字节的临时状态数据`);
      }
    }

    if (!projectTitle || !chapterTitle) {
      throw new Error('项目标题或章节标题缺失，无法保存');
    }

    if (!data) {
      throw new Error('项目数据不存在，无法保存');
    }

    // SRT内容优化：基于哈希的快速检查
    let dataToSave = { ...data };

    if (enableSRTOptimization && dataToSave.srtContent) {
      const srtHash = dataToSave.srtContent.length + '_' +
                     dataToSave.srtContent.substring(0, 100) + '_' +
                     dataToSave.srtContent.substring(dataToSave.srtContent.length - 100);

      const cacheKey = `srt_hash_${projectTitle}_${chapterTitle}`;
      const cachedHash = localStorage.getItem(cacheKey);

      if (cachedHash === srtHash) {
        delete dataToSave.srtContent;
        // SRT内容未变化，跳过传输以提升性能
      } else {
        localStorage.setItem(cacheKey, srtHash);
        // SRT内容已变化，更新哈希缓存
      }
    }

    // 保留完整的合并行数据，确保split操作能正常工作
    // 注意：mergedRows字段对于split操作是必需的，不能删除

    const saveData = {
      projectTitle,
      chapterTitle,
      data: cleanData // 🔥 使用清理后的数据，移除了所有 originalState
    };

    const response = await fetch('/api/local/save-project-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(saveData)
    });

    if (!response.ok) {
      throw new Error(`保存项目数据失败: HTTP ${response.status}`);
    }

    const result = await response.json();
    return result;
  };

  /**
   * 保存项目文件的核心实现
   * @param {Object} params - 保存参数
   * @returns {Promise<Object>} 保存结果
   */
  const saveProjectFile = async (params) => {
    const { projectTitle, chapterTitle, fileName, content, filePath } = params;

    if (!fileName || content === undefined) {
      throw new Error('缺少 fileName 或 content 参数');
    }

    // 验证文件名
    if (!fileName.endsWith('.json') || fileName.includes('/') || fileName.includes('\\')) {
      throw new Error('无效的文件名或类型。只允许 .json 文件，且文件名不能包含路径分隔符。');
    }

    const requestBody = {
      projectTitle,
      chapterTitle,
      fileName,
      content,
      filePath
    };

    const response = await fetch('/api/local/save-project-file', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();
    if (!response.ok || !result.success) {
      throw new Error(result.error || `保存 ${fileName} 失败`);
    }

    return result;
  };

  /**
   * 保存文本文件的核心实现
   * @param {Object} params - 保存参数
   * @returns {Promise<Object>} 保存结果
   */
  const saveTextFile = async (params) => {
    const { filePath, content } = params;

    if (!filePath || content === undefined) {
      throw new Error('缺少 filePath 或 content 参数');
    }

    const response = await fetch('/api/local/save-text-file', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ filePath, content })
    });

    const result = await response.json();
    if (!response.ok || !result.success) {
      throw new Error(result.error || '保存文本文件失败');
    }

    return result;
  };

  /**
   * 保存用户设置的核心实现
   * @param {Object} params - 保存参数
   * @returns {Promise<Object>} 保存结果
   */
  const saveUserSettings = async (params) => {
    const { filename, settings } = params;

    if (!filename || !settings) {
      throw new Error('缺少 filename 或 settings 参数');
    }

    const response = await fetch('/api/local/save-user-settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ filename, settings })
    });

    const result = await response.json();
    if (!response.ok || !result.success) {
      throw new Error(result.error || '保存用户设置失败');
    }

    return result;
  };

  /**
   * 保存提示词的核心实现
   * @param {Object} params - 保存参数
   * @returns {Promise<Object>} 保存结果
   */
  const savePrompts = async (params) => {
    const { content } = params;

    if (!content) {
      throw new Error('缺少 content 参数');
    }

    const response = await fetch('/api/local/save-userdata-prompts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(content)
    });

    const result = await response.json();
    if (!response.ok || !result.success) {
      throw new Error(result.error || '保存提示词失败');
    }

    return result;
  };

  /**
   * 带重试机制的保存操作
   * @param {string} type - 保存类型
   * @param {Object} params - 保存参数
   * @param {number} retryCount - 当前重试次数
   * @returns {Promise<Object>} 保存结果
   */
  const saveWithRetry = async (type, params, retryCount = 0) => {
    try {
      return await executeSave(type, params);
    } catch (error) {
      if (retryCount < finalConfig.maxRetries) {
        console.warn(`[数据持久化] 保存失败，${finalConfig.retryDelay}ms后进行第${retryCount + 1}次重试:`, error.message);

        await new Promise(resolve => setTimeout(resolve, finalConfig.retryDelay));
        return saveWithRetry(type, params, retryCount + 1);
      } else {
        console.error(`[数据持久化] 保存失败，已达到最大重试次数(${finalConfig.maxRetries}):`, error.message);
        throw error;
      }
    }
  };

  /**
   * 防抖保存操作
   * @param {string} type - 保存类型
   * @param {Object} params - 保存参数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 保存结果
   */
  const debouncedSave = (type, params, options = {}) => {
    const {
      debounceDelay = finalConfig.debounceDelay,
      enableRetry = true,
      showMessages = true
    } = options;

    const saveKey = generateSaveKey(type, params);

    // 清除之前的防抖定时器
    if (debounceTimers.has(saveKey)) {
      clearTimeout(debounceTimers.get(saveKey));
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(async () => {
        // 检查是否已经在保存队列中
        if (saveState.saveQueue.has(saveKey)) {
          logger.verbose(`保存操作已在队列中，跳过重复保存: ${saveKey}`);
          resolve(null);
          return;
        }

        // 检查全局保存状态
        if (saveState.isSaving) {
          logger.verbose(`系统正在保存中，延迟保存: ${saveKey}`);
          // 延迟50ms后重试
          setTimeout(() => {
            debouncedSave(type, params, { ...options, debounceDelay: 50 })
              .then(resolve)
              .catch(reject);
          }, 50);
          return;
        }

        // 添加到保存队列
        saveState.saveQueue.set(saveKey, { type, params, timestamp: Date.now() });

        try {
          const result = enableRetry
            ? await saveWithRetry(type, params)
            : await executeSave(type, params);

          if (showMessages && result) {
            showSuccessMessage('保存成功', '数据已成功保存');
          }

          resolve(result);
        } catch (error) {
          if (showMessages) {
            showErrorMessage(`保存失败: ${error.message}`);
          }
          reject(error);
        } finally {
          // 从保存队列中移除
          saveState.saveQueue.delete(saveKey);
          debounceTimers.delete(saveKey);
        }
      }, debounceDelay);

      debounceTimers.set(saveKey, timer);
    });
  };

  /**
   * 便捷的项目数据保存方法
   * @param {Object} projectData - 项目数据对象
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 保存结果
   */
  const saveProject = async (projectData, options = {}) => {
    if (!projectData) {
      throw new Error('项目数据不存在，无法保存');
    }

    const params = {
      projectTitle: projectData.title || '',
      chapterTitle: projectData.currentChapter || '',
      data: projectData.data || {},
      enableSRTOptimization: options.enableSRTOptimization !== false
    };

    return debouncedSave(SAVE_TYPES.PROJECT_DATA, params, options);
  };

  /**
   * 便捷的项目文件保存方法
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   * @param {string} fileName - 文件名
   * @param {Object} content - 文件内容
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 保存结果
   */
  const saveProjectFileConvenient = async (projectTitle, chapterTitle, fileName, content, options = {}) => {
    const params = {
      projectTitle,
      chapterTitle,
      fileName,
      content,
      filePath: options.filePath
    };

    return debouncedSave(SAVE_TYPES.PROJECT_FILE, params, options);
  };

  /**
   * 批量保存操作
   * @param {Array} saveOperations - 保存操作数组
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 保存结果数组
   */
  const batchSave = async (saveOperations, options = {}) => {
    const { concurrent = false, showProgress = false } = options;

    if (showProgress) {
      console.log(`[数据持久化] 开始批量保存 ${saveOperations.length} 个操作`);
    }

    const results = [];

    if (concurrent) {
      // 并发执行
      const promises = saveOperations.map(async (operation, index) => {
        try {
          const result = await debouncedSave(operation.type, operation.params, {
            ...options,
            showMessages: false // 批量操作时不显示单个消息
          });
          if (showProgress) {
            console.log(`[数据持久化] 批量保存进度: ${index + 1}/${saveOperations.length}`);
          }
          return { success: true, result, operation };
        } catch (error) {
          return { success: false, error: error.message, operation };
        }
      });

      results.push(...await Promise.all(promises));
    } else {
      // 顺序执行
      for (let i = 0; i < saveOperations.length; i++) {
        const operation = saveOperations[i];
        try {
          const result = await debouncedSave(operation.type, operation.params, {
            ...options,
            showMessages: false
          });
          results.push({ success: true, result, operation });

          if (showProgress) {
            console.log(`[数据持久化] 批量保存进度: ${i + 1}/${saveOperations.length}`);
          }
        } catch (error) {
          results.push({ success: false, error: error.message, operation });
        }
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    if (showProgress || options.showMessages !== false) {
      if (failureCount === 0) {
        showSuccessMessage('批量保存完成', `成功保存 ${successCount} 个操作`);
      } else {
        showErrorMessage(`批量保存完成，成功 ${successCount} 个，失败 ${failureCount} 个`);
      }
    }

    return results;
  };

  /**
   * 清理保存队列和定时器
   */
  const cleanup = () => {
    // 清理所有防抖定时器
    debounceTimers.forEach(timer => clearTimeout(timer));
    debounceTimers.clear();

    // 清空保存队列
    saveState.saveQueue.clear();

    console.log('[数据持久化] 已清理所有保存队列和定时器');
  };

  /**
   * 获取保存状态信息
   * @returns {Object} 状态信息
   */
  const getSaveStatus = () => {
    return {
      isSaving: saveState.isSaving,
      queueSize: saveState.saveQueue.size,
      activeTimers: debounceTimers.size,
      lastSaveTime: saveState.lastSaveTime,
      totalSaveCount: saveState.saveCount,
      queueItems: Array.from(saveState.saveQueue.keys())
    };
  };

  // 导出保存类型常量和所有方法
  return {
    // 常量
    SAVE_TYPES,

    // 状态
    saveState,

    // 配置方法
    setMessageFunctions,

    // 消息函数（供外部使用）
    showSuccessMessage: (title, message) => showSuccessMessage(title, message),
    showErrorMessage: (message) => showErrorMessage(message),
    showInfoMessage: (title, message) => showInfoMessage(title, message),

    // 核心方法
    generateSaveKey,
    executeSave,
    cleanRowData,
    cleanProjectData,
    saveProjectData,
    saveProjectFile,
    saveTextFile,
    saveUserSettings,
    savePrompts,
    saveWithRetry,
    debouncedSave,

    // 便捷方法
    saveProject,
    saveProjectFileConvenient,
    batchSave,

    // 工具方法
    cleanup,
    getSaveStatus
  };
}
