/**
 * Anthropic <PERSON> LLM 提供商配置
 * 支持Anthropic Claude系列模型
 */

export const anthropicConfig = {
  // 提供商基本信息
  name: 'Anthropic <PERSON>',
  id: 'anthropic',
  description: 'Anthropic的Claude系列大语言模型',
  
  // API配置
  api: {
    baseUrl: 'https://api.anthropic.com/v1',
    endpoint: '/messages',
    headers: {
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    },
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  
  // 默认配置
  defaults: {
    model: 'claude-3-haiku-20240307',
    temperature: 0.7,
    maxTokens: 4000,
    timeout: 30000
  },
  
  // 支持的模型列表
  models: [
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'Claude 3.5 Sonnet',
      description: '最新的Claude 3.5 Sonnet模型，平衡性能和成本',
      maxTokens: 200000,
      pricing: { input: 0.003, output: 0.015 }
    },
    {
      id: 'claude-3-5-haiku-20241022',
      name: '<PERSON> 3.5 <PERSON><PERSON>',
      description: '最新的Claude 3.5 Haiku模型，快速且经济',
      maxTokens: 200000,
      pricing: { input: 0.0008, output: 0.004 }
    },
    {
      id: 'claude-3-opus-20240229',
      name: 'Claude 3 Opus',
      description: '最强大的Claude 3模型，适合复杂任务',
      maxTokens: 200000,
      pricing: { input: 0.015, output: 0.075 }
    },
    {
      id: 'claude-3-sonnet-20240229',
      name: 'Claude 3 Sonnet',
      description: '平衡性能和成本的Claude 3模型',
      maxTokens: 200000,
      pricing: { input: 0.003, output: 0.015 }
    },
    {
      id: 'claude-3-haiku-20240307',
      name: 'Claude 3 Haiku',
      description: '快速响应的轻量级Claude 3模型',
      maxTokens: 200000,
      pricing: { input: 0.00025, output: 0.00125 }
    }
  ],
  
  // 请求格式化函数
  formatRequest: (prompt, settings) => {
    // 检查是否是结构化的prompt（包含instruction和content）
    let messages;
    if (typeof prompt === 'object' && prompt.instruction && prompt.content) {
      messages = [
        { role: 'user', content: `${prompt.instruction}\n\n${prompt.content}` }
      ];
    } else {
      // 向后兼容：如果是字符串prompt，直接使用
      messages = [{ role: 'user', content: prompt }];
    }

    return {
      model: settings.model || 'claude-3-haiku-20240307',
      max_tokens: settings.maxOutputTokens || 4000,
      messages: messages,
      temperature: settings.temperature || 0.7,
    };
  },
  
  // 响应解析函数
  parseResponse: (data) => {
    if (!data.content || !data.content[0]) {
      throw new Error('Anthropic API响应格式无效');
    }
    
    return {
      content: data.content[0].text || '',
      usage: data.usage,
      model: data.model
    };
  },
  
  // 错误处理
  handleError: (error, response) => {
    if (response && response.status === 401) {
      return new Error('Anthropic API密钥无效或已过期');
    }
    if (response && response.status === 429) {
      return new Error('Anthropic API请求频率限制，请稍后重试');
    }
    if (response && response.status === 402) {
      return new Error('Anthropic账户余额不足');
    }
    if (response && response.status === 403) {
      return new Error('Anthropic API访问被拒绝，请检查API密钥权限');
    }
    return new Error(`Anthropic API错误: ${error.message}`);
  },
  
  // 配置验证
  validateConfig: (config) => {
    const errors = [];
    
    if (!config.apiKey) {
      errors.push('缺少Anthropic API密钥');
    }
    
    if (!config.model) {
      errors.push('未指定模型');
    }
    
    if (config.temperature < 0 || config.temperature > 1) {
      errors.push('温度值应在0-1之间');
    }
    
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 200000)) {
      errors.push('最大token数应在1-200000之间');
    }
    
    return errors;
  },
  
  // 构建完整的API URL
  buildApiUrl: () => {
    return `${anthropicConfig.api.baseUrl}${anthropicConfig.api.endpoint}`;
  }
};

export default anthropicConfig;
