/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #1e1e2e;
  color: #cdd6f4;
  height: 100vh;
  overflow: hidden;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1.5rem;
  height: 60px;
  background-color: #181825;
  border-bottom: 1px solid #313244;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.header-logo {
  font-size: 1.5rem;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #cba6f7;
}

.header-right {
  display: flex;
  gap: 0.8rem;
}

.header-button {
  width: 32px;
  height: 32px;
  background-color: #313244;
  border: none;
  font-size: 1rem;
  color: #cdd6f4;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.header-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

/* 主要内容区 */
.main-content {
  height: calc(100vh - 60px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
}

/* 英雄区域 */
.hero-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.create-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #cba6f7;
  color: #11111b;
  border: none;
  border-radius: 12px;
  padding: 1.2rem 3rem;
  font-size: 1.3rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(203, 166, 247, 0.2);
}

.create-button:hover {
  background-color: #f5c2e7;
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(203, 166, 247, 0.3);
}

.create-button span:first-child {
  font-size: 1.5rem;
  margin-top: -2px;
}

/* 功能卡片区 */
.features-section {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.feature-card {
  background-color: #313244;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.feature-card:hover {
  background-color: #45475a;
  transform: translateY(-5px);
  border: 1px solid #cba6f7;
  box-shadow: 0 10px 20px rgba(203, 166, 247, 0.1);
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f5c2e7;
}

.feature-desc {
  color: #bac2de;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 项目区 */
.projects-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.projects-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #cba6f7;
}

.refresh-button {
  background-color: #313244;
  border: none;
  color: #cdd6f4;
  font-size: 1rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #45475a;
  color: #f5c2e7;
  transform: translateY(-2px);
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem;
}

.project-card {
  background-color: #313244;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  border: 1px solid #45475a;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-card:hover {
  transform: translateY(-3px);
  border-color: #cba6f7;
  background-color: #45475a;
}

.project-title {
  font-weight: 600;
  color: #f5c2e7;
}

.project-date {
  font-size: 0.8rem;
  color: #a6adc8;
}

/* 创作页面 */
.creation-container {
  display: none;
  flex-direction: column;
  height: calc(100vh - 60px);
  background-color: #1e1e2e;
  padding: 1.5rem;
  gap: 1.5rem;
}

.creation-container.active {
  display: flex;
}

/* 创作页面工具栏 */
.creation-toolbar {
  display: flex;
  justify-content: space-between;
  background-color: #181825;
  padding: 0.8rem 1rem;
  border-radius: 10px;
}

.toolbar-left {
  display: flex;
  gap: 1rem;
}

.toolbar-right {
  display: flex;
  gap: 0.8rem;
}

.toolbar-button {
  padding: 0.5rem 1.2rem;
  background-color: #313244;
  border: none;
  border-radius: 6px;
  color: #cdd6f4;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

.toolbar-button.primary {
  background-color: #cba6f7;
  color: #181825;
}

.toolbar-button.primary:hover {
  background-color: #f5c2e7;
}

/* 创作区域布局 */
.creation-content {
  display: flex;
  flex: 1;
  gap: 1.5rem;
  height: calc(100% - 120px);
}

.creation-sidebar {
  width: 300px;
  background-color: #181825;
  border-radius: 12px;
  padding: 1.2rem;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  border: 1px solid #313244;
}

.creation-sidebar h3 {
  color: #cba6f7;
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #313244;
}

.creation-main {
  flex: 1;
  background-color: #181825;
  border-radius: 12px;
  padding: 1.2rem;
  overflow: auto;
  border: 1px solid #313244;
}

.creation-main h3 {
  color: #cba6f7;
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #313244;
  margin-bottom: 1rem;
}

/* 日志区域 */
.log-container {
  background-color: #181825;
  border-radius: 12px;
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  border: 1px solid #313244;
}

.log-entry {
  padding: 0.3rem 0;
  border-bottom: 1px solid #313244;
  color: #a6adc8;
}

.log-error {
  color: #f38ba8;
}

.log-success {
  color: #a6e3a1;
}

/* 状态类 */
.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.hidden {
  display: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #181825;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #45475a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #cba6f7;
}

/* 加载状态 */
.loading {
  text-align: center;
  color: #a6adc8;
  padding: 2rem;
  font-style: italic;
}

.empty-state {
  grid-column: 1/-1;
  text-align: center;
  color: #a6adc8;
  padding: 2rem;
  background-color: #313244;
  border-radius: 12px;
  border: 1px dashed #45475a;
}

/* 项目卡片动作 */
.project-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.project-action {
  width: 28px;
  height: 28px;
  background-color: #45475a;
  border: none;
  border-radius: 4px;
  color: #cdd6f4;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.project-action:hover {
  background-color: #cba6f7;
  color: #11111b;
  transform: translateY(-2px);
}

/* 设置表单 */
.settings-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-item label {
  font-size: 0.9rem;
  color: #bac2de;
}

input, select, textarea {
  padding: 0.8rem;
  background-color: #1e1e2e;
  border: 1px solid #45475a;
  border-radius: 6px;
  color: #cdd6f4;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #cba6f7;
  box-shadow: 0 0 0 2px rgba(203, 166, 247, 0.2);
}

/* 预览容器 */
.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  background-color: #1e1e2e;
  border-radius: 8px;
  overflow: hidden;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #45475a;
}

.preview-placeholder i {
  font-size: 4rem;
}

.preview-placeholder p {
  font-size: 1rem;
} 