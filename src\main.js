import { createApp } from 'vue'
import App from './App.vue'
import './assets/styles/button-hover.css'
import logger from './utils/logger'
import router from './router'

// 全局 fetch 日志挂件 - 静默版（只记录错误）
const originalFetch = window.fetch;
window.fetch = async (...args) => {
  try {
    const response = await originalFetch(...args);
    // 只在出现错误状态时记录日志
    if (!response.ok && response.status >= 400) {
      logger.error('[FETCH 错误]', response.status, response.url);
    }
    return response;
  } catch (e) {
    logger.error('[FETCH 错误]', e);
    throw e;
  }
};

const app = createApp(App);
app.config.globalProperties.$logger = logger;
app.use(router);

app.mount('#app')
