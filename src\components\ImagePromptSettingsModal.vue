<template>
  <!-- 设置弹窗遮罩 -->
  <div
    v-if="showProp"
    class="modal-overlay"
    @click.self="closeModal"
  >
    <div class="settings-modal">
      <!-- 现代化头部 -->
      <header class="modal-header">
        <div class="header-content">
          <div class="header-title">
            <div class="title-icon">
              <i class="ri-settings-3-line" />
            </div>
            <div class="title-text">
              <h2>提示词设置</h2>
              <p class="subtitle">
                配置图像提示词生成参数
              </p>
            </div>
          </div>
          <div class="header-actions">
            <button
              @click="openPromptEditor"
              class="action-btn edit-btn"
              title="编辑提示词模板"
            >
              <i class="ri-edit-line" />
              <span>编辑</span>
            </button>
            <button
              @click="closeModal"
              class="action-btn close-btn"
              title="关闭设置"
            >
              <i class="ri-close-line" />
              <span>关闭</span>
            </button>
          </div>
        </div>
      </header>

      <!-- 设置内容区域 -->
      <main class="modal-main">
        <!-- 输入配置面板 -->
        <section class="settings-panel">
          <div class="panel-header">
            <h3 class="panel-title">
              <i class="ri-edit-box-line" />
              输入配置
            </h3>
          </div>
          <div class="panel-content">
            <!-- 上下文设置 -->
            <div class="input-card context-settings-card">
              <div class="card-header">
                <div class="header-left">
                  <i class="ri-settings-3-line" />
                  <span class="card-title">上下文设置</span>
                </div>
                <div class="header-right">
                  <label
                    for="context-lines-select-main"
                    class="context-lines-label"
                  >选择行数:</label>
                  <select
                    id="context-lines-select-main"
                    v-model.number="tempContextLines"
                    class="context-lines-select"
                  >
                    <option :value="5">
                      5行
                    </option>
                    <option :value="10">
                      10行
                    </option>
                    <option :value="15">
                      15行
                    </option>
                    <option :value="20">
                      20行
                    </option>
                  </select>
                  <button
                    class="confirm-settings-btn"
                    @click="confirmContextSettings"
                    :disabled="tempContextLines === currentContextLines"
                  >
                    确认
                  </button>
                </div>
              </div>
              <div class="card-body">
                <p class="context-description">
                  设置在生成图像提示词时显示的上下文行数。上下文将从目标文本周围提取，帮助AI更好地理解场景。
                </p>
              </div>
            </div>

            <!-- 总纲输入卡片 -->
            <div class="input-card">
              <div class="card-header">
                <i class="ri-book-open-line" />
                <span class="card-title">故事总纲</span>
              </div>
              <div class="card-body">
                <textarea
                  v-model="localInputData.synopsis"
                  class="modern-textarea"
                  placeholder="请输入整体故事背景和主要冲突..."
                  rows="3"
                />
              </div>
            </div>

            <!-- 角色显示卡片 -->
            <div class="input-card">
              <div class="card-header">
                <div class="header-left">
                  <i class="ri-team-line" />
                  <span class="card-title">角色信息</span>
                </div>
                <div class="header-right">
                  <span
                    class="character-count"
                    v-if="currentData.characters.length > 0"
                  >
                    共 {{ currentData.characters.length }} 个角色
                  </span>
                  <button
                    class="refresh-button"
                    @click="refreshCurrentData"
                    title="刷新Current.json数据"
                  >
                    <i class="ri-refresh-line" />
                  </button>
                </div>
              </div>
              <div class="card-body">
                <!-- 数据缺失提醒 -->
                <div
                  v-if="!hasCurrentData"
                  class="data-missing-alert"
                >
                  <i class="ri-alert-line" />
                  <div class="alert-content">
                    <div class="alert-title">
                      Current.json 数据缺失
                    </div>
                    <div class="alert-message">
                      未找到 <code>draft/{{ props.projectTitle }}/{{ props.chapterTitle }}/Current.json</code> 文件或文件中缺少必要数据。
                      <br><br>
                      <strong>调试信息：</strong>
                      <ul>
                        <li>项目标题: {{ props.projectTitle || '未设置' }}</li>
                        <li>章节标题: {{ props.chapterTitle || '未设置' }}</li>
                        <li>角色数量: {{ currentData.characters.length }}</li>
                        <li>场景数量: {{ currentData.scenes.length }}</li>
                        <li>大纲数量: {{ currentData.outlines.length }}</li>
                        <li>hasCurrentData: {{ hasCurrentData }}</li>
                      </ul>
                      <br>
                      <strong>需要的数据包括：</strong>
                      <ul>
                        <li><strong>故事大纲 (outlines)</strong> - 用于生成故事总纲</li>
                        <li>角色信息 (characters) - 用于角色展示</li>
                        <li>场景设置 (scenes) - 用于场景与时间</li>
                        <li>时代背景 (eraBackground) - 用于故事上下文</li>
                      </ul>
                      <strong>特别说明：</strong>故事总纲将直接使用 <code>outlines[].content</code> 字段的内容。
                      <br><br>
                      请先在全局推理中分析内容以生成这些配置数据。
                    </div>
                    <div class="alert-actions">
                      <button
                        class="alert-button primary"
                        @click="closeModal"
                      >
                        前往全局推理
                      </button>
                      <button
                        class="alert-button secondary"
                        @click="refreshCurrentData"
                      >
                        重新检查
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 角色列表 -->
                <div
                  v-else-if="currentData.characters.length > 0"
                  class="character-display-grid"
                >
                  <div
                    v-for="character in currentData.characters"
                    :key="character.name"
                    class="character-display-card"
                  >
                    <div class="character-avatar">
                      <i class="ri-user-3-line" />
                    </div>
                    <div class="character-info">
                      <div class="character-name">
                        {{ character.name }}
                        <span
                          v-if="character.alias"
                          class="character-alias"
                        >({{ character.alias }})</span>
                      </div>
                      <div class="character-meta">
                        {{ character.description || '无详细信息' }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div
                  v-else
                  class="empty-state"
                >
                  <i class="ri-user-add-line" />
                  <p>暂无可用角色</p>
                  <small>请先在全局推理中分析角色</small>
                </div>
              </div>
            </div>

            <!-- 当前特名列表卡片 -->
            <div class="input-card">
              <div class="card-header">
                <div class="header-left">
                  <i class="ri-bookmark-3-line" />
                  <span class="card-title">当前特名列表</span>
                </div>
                <div class="header-right">
                  <span
                    class="special-names-count"
                    v-if="specialNames.length > 0"
                  >
                    共 {{ specialNames.length }} 个特名
                  </span>
                </div>
              </div>
              <div class="card-body">
                <!-- 特名卡片网格 -->
                <div
                  v-if="specialNames.length > 0"
                  class="special-names-grid"
                >
                  <div
                    v-for="(item, index) in specialNames"
                    :key="index"
                    class="special-name-card"
                  >
                    <div class="special-name-avatar">
                      <i class="ri-bookmark-line" />
                    </div>
                    <div class="special-name-info">
                      <div class="special-name-title">
                        {{ item.name }}
                      </div>
                      <div class="special-name-description">
                        {{ item.description }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div
                  v-else
                  class="empty-state"
                >
                  <i class="ri-bookmark-line" />
                  <p>暂无特名</p>
                  <small>请先在全局推理中分析特殊名词</small>
                </div>
              </div>
            </div>

            <!-- 场景时间卡片 -->
            <div class="input-card">
              <div class="card-header">
                <i class="ri-map-pin-line" />
                <span class="card-title">场景与时间</span>
              </div>
              <div class="card-body">
                <!-- 显示所有场景信息 -->
                <div
                  v-if="currentData.scenes.length > 0"
                  class="scenes-display"
                >
                  <div
                    v-for="(scene, index) in currentData.scenes"
                    :key="index"
                    class="scene-item"
                  >
                    <div class="scene-header">
                      <span class="scene-range">第{{ scene.range }}行</span>
                      <span class="scene-index">场景 {{ index + 1 }}</span>
                    </div>
                    <div class="scene-content">
                      <div class="scene-field">
                        <span class="scene-label">地点:</span>
                        <span class="scene-value">{{ scene.location }}</span>
                      </div>
                      <div class="scene-field">
                        <span class="scene-label">时间:</span>
                        <span class="scene-value">{{ scene.time }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- 提示词编辑模态框 -->
    <PromptEditorModal
      v-if="showPromptEditor"
      :show="showPromptEditor"
      @update:show="showPromptEditor = $event"
      @save-prompt="handleSavePrompt"
      :prompts-map="promptsMap"
      :default-prompts-map="defaultPromptsMap"
      :current-type="'imagePrompt'"
      :prompt-type-options="promptTypeOptions"
    />
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, onUnmounted, watch } from 'vue';
import PromptEditorModal from './PromptEditorModal.vue';
import { useUserImageSettings } from '../composables/useUserImageSettings.js';

// Props
const props = defineProps({
  show: Boolean,
  projectTitle: {
    type: String,
    default: ''
  },
  chapterTitle: {
    type: String,
    default: ''
  },
  availableCharacters: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:show']);

// 本地状态
const showProp = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const localInputData = ref({
  synopsis: ''
});

// 使用用户图像设置 composable
const {
  getContextLines,
  setContextLines
} = useUserImageSettings();

// 临时设置（用于确认前的选择）
const tempContextLines = ref(10);
// 当前保存的上下文行数
const currentContextLines = ref(10);

// Current.json 数据
const currentData = ref({
  eraBackground: '',
  characters: [],
  scenes: [],
  outlines: [],
  specialItems: [],
  techniques: []
});

// 数据状态
const hasCurrentData = computed(() => {
  return currentData.value.characters.length > 0 ||
         currentData.value.scenes.length > 0 ||
         currentData.value.outlines.length > 0;
});

// 🔥 新增：特名列表
const specialNames = computed(() => {
  return currentData.value.specialItems
    .filter(item => item.name && item.name.trim())
    .map(item => ({
      name: item.name,
      description: item.description || '无描述'
    }))
    .sort((a, b) => a.name.localeCompare(b.name));
});

// 文件监听状态
const isFileWatching = ref(false);
const fileWatchInterval = ref(null);
const lastModified = ref(null);

const showPromptEditor = ref(false);

// 提示词相关数据
const promptTypeOptions = ref({
  imagePrompt: '图像提示词'
});

const promptsMap = reactive({
  imagePrompt: ''
});

const defaultPromptsMap = reactive({
  imagePrompt: ''
});

// 加载 prompts.json
async function loadAllPrompts() {
  try {
    const res = await fetch('/userdata/prompts.json');
    if (!res.ok) return;
    const data = await res.json();

    // 图像提示词
    promptsMap.imagePrompt = Array.isArray(data.imagePrompt?.user) ? data.imagePrompt.user.join('\n') : '';
    defaultPromptsMap.imagePrompt = Array.isArray(data.imagePrompt?.default) ? data.imagePrompt.default.join('\n') : '';
  } catch (error) {
    console.error('加载提示词失败:', error);
  }
}

// 加载 Current.json 数据
async function loadCurrentData(forceReload = false) {
  try {
    const currentPath = `draft/${props.projectTitle}/${props.chapterTitle}/Current.json`;
    console.log(`正在加载 Current.json: ${currentPath}`);

    // 使用后端API读取文件
    const res = await fetch(`/api/local/read-file?path=${encodeURIComponent(currentPath)}&t=${Date.now()}`);
    if (!res.ok) {
      console.warn('Current.json 文件不存在或无法访问，状态码:', res.status);
      resetCurrentData();
      return false;
    }

    const response = await res.json();
    console.log('Current.json API响应:', response);

    // 检查API响应格式并提取实际数据
    let data;
    if (response.success && response.content) {
      // 后端返回的是包装格式，需要解析content字段
      try {
        data = JSON.parse(response.content);
        console.log('解析后的Current.json数据:', data);
      } catch (parseError) {
        console.error('解析Current.json内容失败:', parseError);
        resetCurrentData();
        return false;
      }
    } else if (response.eraBackground !== undefined || response.characters !== undefined) {
      // 直接返回的JSON数据
      data = response;
    } else {
      console.error('无效的API响应格式:', response);
      resetCurrentData();
      return false;
    }

    // 验证数据完整性
    const isValidData = validateCurrentData(data);
    console.log('数据验证结果:', isValidData);
    if (!isValidData && !forceReload) {
      console.warn('Current.json 数据不完整，但继续处理');
    }

    // 更新数据
    currentData.value = {
      eraBackground: data.eraBackground || '',
      characters: data.characters || [],
      scenes: data.scenes || [],
      outlines: data.outlines || [],
      specialItems: data.specialItems || [],
      techniques: data.techniques || []
    };

    console.log('更新后的currentData:', {
      characters: currentData.value.characters.length,
      scenes: currentData.value.scenes.length,
      outlines: currentData.value.outlines.length,
      eraBackground: currentData.value.eraBackground ? '已设置' : '未设置'
    });

    // 记录文件修改时间
    const lastModifiedHeader = res.headers.get('last-modified');
    if (lastModifiedHeader) {
      lastModified.value = new Date(lastModifiedHeader).getTime();
    }

    // 自动填充数据
    autoFillFromCurrentData();

    console.log('Current.json 加载成功', {
      characters: currentData.value.characters.length,
      scenes: currentData.value.scenes.length,
      outlines: currentData.value.outlines.length,
      hasCurrentData: hasCurrentData.value
    });

    return true;
  } catch (error) {
    console.error('加载Current.json失败:', error);
    resetCurrentData();
    return false;
  }
}

// 验证Current.json数据完整性
function validateCurrentData(data) {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // 检查必要字段 - outlines是最重要的，因为它提供故事总纲
  const hasOutlines = Array.isArray(data.outlines) && data.outlines.length > 0 &&
                     data.outlines.some(outline => outline.content && outline.content.trim());
  const hasCharacters = Array.isArray(data.characters) && data.characters.length > 0;
  const hasScenes = Array.isArray(data.scenes) && data.scenes.length > 0;
  const hasEraBackground = typeof data.eraBackground === 'string' && data.eraBackground.length > 0;

  // outlines是核心数据，优先级最高
  return hasOutlines || hasCharacters || hasScenes || hasEraBackground;
}

// 重置Current.json数据
function resetCurrentData() {
  currentData.value = {
    eraBackground: '',
    characters: [],
    scenes: [],
    outlines: [],
    specialItems: [],
    techniques: []
  };
}

// 从Current.json自动填充数据
function autoFillFromCurrentData() {
  const data = currentData.value;

  console.log('开始自动填充数据', {
    characters: data.characters.length,
    scenes: data.scenes.length,
    outlines: data.outlines.length,
    eraBackground: data.eraBackground ? '已设置' : '未设置'
  });

  // 填充故事总纲（直接使用outlines的content）
  if (data.outlines.length > 0 && !localInputData.value.synopsis.trim()) {
    const latestOutline = data.outlines[data.outlines.length - 1];
    localInputData.value.synopsis = latestOutline.content;
    console.log('已填充故事总纲:', latestOutline.content.substring(0, 50) + '...');
  }

  // 场景与时间现在通过专门的显示区域展示，不再自动填充到文本框
  // 用户可以在手动输入区域自行输入需要的场景信息
  console.log('场景数据已加载，共', data.scenes.length, '个场景');

  // 上下文设置已移到顶部，不再需要自动填充上下文内容
  console.log('上下文设置已配置，行数:', currentContextLines.value);
}

// 保存提示词
async function handleSavePrompt({ type, content }) {
  try {
    // 获取当前的 prompts.json 数据
    const currentData = await (await fetch('/userdata/prompts.json')).json();

    // 确保类型存在
    if (!currentData[type]) {
      currentData[type] = {};
    }

    // 只更新 user 部分，保持 default 不变
    currentData[type].user = content.split('\n');

    // 如果 default 不存在，保持为空或使用现有值
    if (!currentData[type].default) {
      currentData[type].default = defaultPromptsMap[type] ? defaultPromptsMap[type].split('\n') : [];
    }

    // 保存到 prompts.json
    const res = await fetch('/api/local/save-userdata-prompts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(currentData)
    });

    if (res.ok) {
      // 只更新内存中的 user prompt
      promptsMap[type] = content;
      console.log(`已保存 ${type} 的用户自定义prompt`);
    } else {
      throw new Error('保存失败');
    }
  } catch (error) {
    console.error('保存提示词失败:', error);
  }
}

// 开始监听Current.json文件变化
function startFileWatching() {
  // 暂时禁用文件监听功能，避免频繁读取文件
  console.log('文件监听功能已暂时禁用，避免频繁读取文件');
}

// 停止监听Current.json文件变化
function stopFileWatching() {
  if (fileWatchInterval.value) {
    clearInterval(fileWatchInterval.value);
    fileWatchInterval.value = null;
  }
  isFileWatching.value = false;
  console.log('停止监听Current.json文件变化');
}

// 手动刷新数据
async function refreshCurrentData() {
  console.log('手动刷新Current.json数据');
  const success = await loadCurrentData(true);
  if (success) {
    // 可以添加成功提示
    console.log('数据刷新成功');
  }
}

// 监听props变化，当项目或章节信息更新时重新加载数据
watch([() => props.projectTitle, () => props.chapterTitle], async ([newProjectTitle, newChapterTitle], [oldProjectTitle, oldChapterTitle]) => {
  console.log('ImagePromptSettingsModal props changed:', {
    oldProjectTitle,
    oldChapterTitle,
    newProjectTitle,
    newChapterTitle
  });

  // 如果项目或章节信息发生变化，重新加载数据
  if (newProjectTitle && newChapterTitle && (newProjectTitle !== oldProjectTitle || newChapterTitle !== oldChapterTitle)) {
    console.log('重新加载Current.json数据');
    stopFileWatching(); // 停止之前的监听
    const success = await loadCurrentData(true);
    if (success) {
      startFileWatching(); // 开始新的监听
    }
  }
}, { immediate: false });

// 加载设置的函数
async function loadSettings() {
  console.log('开始加载设置，项目:', props.projectTitle, '章节:', props.chapterTitle);

  // 加载用户设置
  if (props.projectTitle && props.chapterTitle) {
    try {
      const contextLines = await getContextLines(props.projectTitle, props.chapterTitle);
      currentContextLines.value = contextLines;
      tempContextLines.value = contextLines;
      console.log('加载上下文设置成功:', contextLines);
    } catch (error) {
      console.error('加载上下文设置失败:', error);
      currentContextLines.value = 10;
      tempContextLines.value = 10;
    }
  } else {
    // 如果没有项目信息，使用默认值
    currentContextLines.value = 10;
    tempContextLines.value = 10;
  }

  await loadAllPrompts();

  // 只有在有项目和章节信息时才加载Current.json
  if (props.projectTitle && props.chapterTitle) {
    console.log('开始加载Current.json数据，项目:', props.projectTitle, '章节:', props.chapterTitle);
    const success = await loadCurrentData();

    if (success) {
      console.log('Current.json数据加载成功');
      startFileWatching();
    } else {
      console.error('Current.json数据加载失败');
    }
  } else {
    console.warn('ImagePromptSettingsModal: 缺少项目或章节信息，无法加载Current.json', {
      projectTitle: props.projectTitle,
      chapterTitle: props.chapterTitle
    });
  }
}

onMounted(async () => {
  console.log('ImagePromptSettingsModal mounted with props:', {
    projectTitle: props.projectTitle,
    chapterTitle: props.chapterTitle,
    show: props.show
  });

  await loadSettings();
});

// 组件卸载时清理
onUnmounted(() => {
  stopFileWatching();
});

// 方法
function closeModal() {
  emit('update:show', false);
}

function openPromptEditor() {
  showPromptEditor.value = true;
}

// 确认上下文设置
async function confirmContextSettings() {
  try {
    // 确保传递的是整数类型
    const contextLines = parseInt(tempContextLines.value, 10);
    console.log('准备保存上下文行数:', contextLines, '类型:', typeof contextLines);

    const success = await setContextLines(contextLines, props.projectTitle, props.chapterTitle);
    if (success) {
      currentContextLines.value = contextLines;
      console.log('上下文行数设置已确认:', contextLines);
    } else {
      console.error('保存上下文设置失败');
    }
  } catch (error) {
    console.error('确认上下文设置时出错:', error);
  }
}

// 监听 show prop 的变化，当模态框显示时重新加载设置
watch(() => props.show, async (newShow) => {
  if (newShow) {
    console.log('模态框显示，重新加载设置');
    await loadSettings();
  }
}, { immediate: false });

// 监听 currentContextLines 的变化，同步到 tempContextLines
watch(currentContextLines, (newValue) => {
  if (newValue !== undefined && newValue !== null) {
    tempContextLines.value = newValue;
  }
}, { immediate: true });


</script>

<style scoped>
/* 现代Windows风格样式 */

/* 模态框遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

/* 设置弹窗主容器 */
.settings-modal {
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

/* 头部样式 */
.modal-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  max-width: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.title-text h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  letter-spacing: -0.025em;
}

.subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #718096;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  color: #4a5568;
}

.action-btn:hover:not(:disabled) {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.edit-btn:hover:not(:disabled) {
  color: #3182ce;
  border-color: #3182ce;
}

.close-btn:hover:not(:disabled) {
  color: #e53e3e;
  border-color: #e53e3e;
}

/* 主体内容 */
.modal-main {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 设置面板样式 */
.settings-panel {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(248, 250, 252, 0.8);
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-title i {
  font-size: 20px;
  color: #667eea;
}

.panel-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 主体内容滚动条样式 */
.modal-main::-webkit-scrollbar {
  width: 8px;
}

.modal-main::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.modal-main::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.modal-main::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 输入卡片样式 */
.input-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.input-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.required-card {
  border-color: #fed7d7;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(248, 250, 252, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-header i {
  font-size: 18px;
  color: #667eea;
}

.card-title {
  font-weight: 600;
  color: #2d3748;
  font-size: 15px;
}

.character-count {
  margin-left: auto;
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.required-badge {
  margin-left: auto;
  background: #e53e3e;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 上下文行数选择器样式 */
.context-lines-label {
  font-size: 12px;
  color: #718096;
  margin-right: 8px;
  font-weight: 500;
}

.context-lines-select {
  padding: 4px 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  background: white;
  font-size: 12px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.context-lines-select:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.context-lines-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 确认设置按钮样式 */
.confirm-settings-btn {
  margin-left: 8px;
  padding: 4px 12px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-settings-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.confirm-settings-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  opacity: 0.6;
}

.confirm-settings-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(102, 126, 234, 0.3);
}

/* 场景显示样式 */
.scenes-display {
  margin-bottom: 16px;
}

.scene-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.scene-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e0;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.scene-range {
  background: #667eea;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.scene-index {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.scene-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.scene-field {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.scene-label {
  font-size: 13px;
  color: #1a202c;
  font-weight: 700;
  min-width: 40px;
  flex-shrink: 0;
  background: rgba(26, 32, 44, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.scene-value {
  font-size: 12px;
  color: #2d3748;
  line-height: 1.4;
  flex: 1;
}



/* 上下文设置卡片样式 */
.context-settings-card {
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f3ff 100%);
  border: 1px solid #c3dafe;
}

.context-description {
  font-size: 13px;
  color: #4a5568;
  line-height: 1.5;
  margin: 0;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.card-body {
  padding: 20px;
}

/* 现代文本域样式 */
.modern-textarea {
  width: 100%;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  color: #2d3748;
  background: #fafafa;
  transition: all 0.2s ease;
  resize: vertical;
  font-family: inherit;
}

.modern-textarea:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.required-field {
  border-color: #fc8181;
}

.required-field:focus {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

/* 角色显示网格样式 */
.character-display-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

/* 🔥 新增：特名卡片网格样式 */
.special-names-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  padding: 8px 0;
}

.special-name-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
  cursor: default;
}

.special-name-card:hover {
  border-color: #f093fb;
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.15);
  transform: translateY(-1px);
}

.special-name-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.special-name-info {
  flex: 1;
  min-width: 0;
}

.special-name-title {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  line-height: 1.2;
}

.special-name-description {
  font-size: 12px;
  color: #718096;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.special-names-count {
  margin-left: auto;
  background: #f093fb;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.character-display-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: #fafafa;
}

.character-display-card:hover {
  border-color: #cbd5e0;
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.character-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.character-info {
  flex: 1;
  min-width: 0;
}

.character-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
  margin-bottom: 4px;
}

.character-alias {
  font-weight: 400;
  color: #667eea;
  font-size: 12px;
  margin-left: 4px;
}

.character-meta {
  font-size: 12px;
  color: #718096;
  line-height: 1.3;
}

/* 数据缺失提醒样式 */
.data-missing-alert {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 1px solid #feb2b2;
  border-radius: 12px;
  margin: 16px 0;
}

.data-missing-alert i {
  font-size: 24px;
  color: #e53e3e;
  margin-top: 2px;
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #c53030;
  font-size: 16px;
  margin-bottom: 8px;
}

.alert-message {
  color: #742a2a;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.alert-button {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.alert-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}

/* 刷新按钮样式 */
.refresh-button {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  padding: 8px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.refresh-button:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.refresh-button:active {
  transform: translateY(0);
}

/* 更新的数据缺失提醒样式 */
.alert-message ul {
  margin: 12px 0;
  padding-left: 20px;
}

.alert-message li {
  margin: 4px 0;
  color: #742a2a;
}

.alert-message code {
  background: rgba(197, 48, 48, 0.1);
  color: #c53030;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
}

.alert-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.alert-button.primary {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
}

.alert-button.secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.alert-button.secondary:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.empty-state i {
  font-size: 48px;
  color: #cbd5e0;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 8px 0 4px 0;
  font-weight: 500;
  color: #4a5568;
}

.empty-state small {
  font-size: 12px;
  color: #a0aec0;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-modal {
    width: 95vw;
    max-height: 95vh;
  }

  .header-content {
    padding: 16px 20px;
  }

  .modal-main {
    padding: 16px 20px;
  }

  .character-display-grid {
    grid-template-columns: 1fr;
  }
}
</style>
