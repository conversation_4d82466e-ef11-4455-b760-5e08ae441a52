<template>
  <div
    v-if="show"
    class="text-viewer-modal-overlay"
    @click.self="closeModal"
  >
    <div
      class="text-viewer-modal-content"
      @click.stop
    >
      <div class="modal-header">
        <h3>查看文本</h3>
        <button
          @click="closeModal"
          class="close-button"
          title="关闭"
        >
          <i class="ri-close-line" />
        </button>
      </div>
      <div class="modal-body">
        <pre class="text-content">{{ textToShow }}</pre>
      </div>
      <div class="modal-footer">
        <button
          @click="closeModal"
          class="action-button"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  show: {
    type: Boolean,
    default: false
  },
  textToShow: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};
</script>

<style scoped>
.text-viewer-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-viewer-modal-content {
  background-color: #1e1e2e;
  border-radius: 8px;
  width: 80%;
  max-width: 900px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1.2rem;
  background-color: #181825;
  border-bottom: 1px solid #313244;
}

.modal-header h3 {
  color: #cdd6f4;
  font-size: 1.1rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #cdd6f4;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #313244;
}

.modal-body {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.text-content {
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #cdd6f4;
  margin: 0;
  padding: 0.5rem;
  background-color: #11111b;
  border-radius: 6px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 0.8rem 1.2rem;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #313244;
}

.action-button {
  padding: 0.5rem 1.2rem;
  background-color: #45475a;
  color: #cdd6f4;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #585b70;
}
</style>



 




 
