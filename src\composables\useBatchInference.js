/**
 * 批量推理功能模块
 * 提供顺序处理选中行的推理功能
 */

import { ref, computed } from 'vue';

export function useBatchInference() {
  // 状态管理
  const isProcessing = ref(false);
  const currentRowIndex = ref(-1);
  const totalRows = ref(0);
  const processedRows = ref(0);
  const failedRows = ref([]);
  const processingMessage = ref('');
  const processingError = ref('');

  // 计算属性
  const progress = computed(() => {
    if (totalRows.value === 0) return 0;
    return Math.round((processedRows.value / totalRows.value) * 100);
  });

  const isCompleted = computed(() => {
    return processedRows.value === totalRows.value && totalRows.value > 0;
  });

  const hasErrors = computed(() => {
    return failedRows.value.length > 0;
  });

  /**
   * 重置状态
   */
  function resetState() {
    isProcessing.value = false;
    currentRowIndex.value = -1;
    totalRows.value = 0;
    processedRows.value = 0;
    failedRows.value = [];
    processingMessage.value = '';
    processingError.value = '';
  }

  /**
   * 批量推理主函数
   * @param {Array} selectedRows - 选中的行数据
   * @param {Function} inferenceFunction - 推理函数
   * @param {Function} onProgress - 进度回调
   * @param {Function} onRowComplete - 单行完成回调
   */
  async function processBatchInference(selectedRows, inferenceFunction, onProgress, onRowComplete) {
    if (!selectedRows || selectedRows.length === 0) {
      throw new Error('没有选中的行数据');
    }

    if (typeof inferenceFunction !== 'function') {
      throw new Error('推理函数无效');
    }

    console.log('🧠 [批量推理] 开始处理:', {
      totalRows: selectedRows.length,
      selectedRowsData: selectedRows.map(row => ({ index: row.index, keywords: row.keywords?.substring(0, 50) }))
    });

    // 初始化状态
    resetState();
    isProcessing.value = true;
    totalRows.value = selectedRows.length;
    processingMessage.value = '准备开始批量推理...';

    try {
      // 顺序处理每一行
      for (let i = 0; i < selectedRows.length; i++) {
        // 检查是否被取消
        if (!isProcessing.value) {
          console.log('🧠 [批量推理] 处理被用户取消');
          break;
        }

        const row = selectedRows[i];
        currentRowIndex.value = i;
        processingMessage.value = `正在处理第 ${i + 1}/${selectedRows.length} 行...`;

        console.log(`🧠 [批量推理] 处理第 ${i + 1}/${selectedRows.length} 行:`, {
          rowIndex: row.index,
          keywords: row.keywords?.substring(0, 100)
        });

        try {
          // 🔧 添加详细的状态跟踪
          console.log(`🧠 [批量推理] 开始调用推理函数，第 ${i + 1} 行`);

          // 调用推理函数
          const result = await inferenceFunction(row, i);

          console.log(`🧠 [批量推理] 推理函数返回结果，第 ${i + 1} 行:`, result);

          // 更新进度
          processedRows.value++;

          // 调用单行完成回调
          if (onRowComplete) {
            console.log(`🧠 [批量推理] 调用单行完成回调，第 ${i + 1} 行`);
            onRowComplete(row, i, result, null);
          }

          // 调用进度回调
          if (onProgress) {
            console.log(`🧠 [批量推理] 调用进度回调，第 ${i + 1} 行`);
            onProgress({
              current: processedRows.value,
              total: totalRows.value,
              progress: progress.value,
              currentRow: row,
              result: result
            });
          }

          console.log(`✅ [批量推理] 第 ${i + 1} 行处理完成，准备处理下一行`);

          // 🔧 检查处理状态是否仍然有效
          if (!isProcessing.value) {
            console.log('🧠 [批量推理] 检测到处理状态已被取消，停止处理');
            break;
          }

        } catch (error) {
          console.error(`❌ [批量推理] 第 ${i + 1} 行处理失败:`, error);
          
          // 记录失败的行
          failedRows.value.push({
            index: i,
            row: row,
            error: error.message || '未知错误'
          });

          // 调用单行完成回调（包含错误）
          if (onRowComplete) {
            onRowComplete(row, i, null, error);
          }

          // 调用进度回调（包含错误）
          if (onProgress) {
            onProgress({
              current: processedRows.value,
              total: totalRows.value,
              progress: progress.value,
              currentRow: row,
              error: error
            });
          }

          // 继续处理下一行，不中断整个批量处理
          processedRows.value++;
        }

        // 添加短暂延迟，避免过快的请求
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // 处理完成
      processingMessage.value = isCompleted.value ? 
        `批量推理完成！成功处理 ${processedRows.value - failedRows.value.length}/${totalRows.value} 行` :
        '批量推理已停止';

      console.log('🧠 [批量推理] 处理完成:', {
        total: totalRows.value,
        processed: processedRows.value,
        failed: failedRows.value.length,
        success: processedRows.value - failedRows.value.length
      });

      return {
        success: true,
        total: totalRows.value,
        processed: processedRows.value,
        failed: failedRows.value.length,
        failedRows: failedRows.value
      };

    } catch (error) {
      console.error('❌ [批量推理] 批量处理失败:', error);
      processingError.value = error.message || '批量推理失败';
      throw error;
    } finally {
      isProcessing.value = false;
    }
  }

  /**
   * 取消批量处理
   */
  function cancelBatchInference() {
    console.log('🛑 [批量推理] 用户取消批量处理');
    isProcessing.value = false;
    processingMessage.value = '批量推理已取消';
  }

  /**
   * 获取处理状态摘要
   */
  function getProcessingSummary() {
    return {
      isProcessing: isProcessing.value,
      progress: progress.value,
      currentRow: currentRowIndex.value + 1,
      totalRows: totalRows.value,
      processedRows: processedRows.value,
      failedRows: failedRows.value.length,
      message: processingMessage.value,
      error: processingError.value,
      isCompleted: isCompleted.value,
      hasErrors: hasErrors.value
    };
  }

  return {
    // 状态
    isProcessing,
    currentRowIndex,
    totalRows,
    processedRows,
    failedRows,
    processingMessage,
    processingError,

    // 计算属性
    progress,
    isCompleted,
    hasErrors,

    // 方法
    processBatchInference,
    cancelBatchInference,
    resetState,
    getProcessingSummary
  };
}
